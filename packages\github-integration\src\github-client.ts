/**
 * GitHub client for GitHub integration
 */

import * as dotenv from 'dotenv';
import { Octokit } from '@octokit/rest';
import { GitHubClientOptions } from './types';
import { GitHubAPI } from './github-api';
import { GitHubActions } from './github-actions';
import { GitHubPages } from './github-pages';
import { GitHubReleases } from './github-releases';
import { EventBus, EventType } from '@qawolf/core';

// Load environment variables
dotenv.config();

/**
 * GitHub client
 */
export class GitHubClient {
  /**
   * API token
   */
  private token: string;
  
  /**
   * Owner
   */
  private owner: string;
  
  /**
   * Repository
   */
  private repo: string;
  
  /**
   * Base URL
   */
  private baseUrl: string;
  
  /**
   * Timeout in milliseconds
   */
  private timeout: number;
  
  /**
   * Octokit instance
   */
  private octokit: Octokit;
  
  /**
   * GitHub API
   */
  private api: GitHubAPI;
  
  /**
   * GitHub actions
   */
  private actions: GitHubActions;
  
  /**
   * GitHub pages
   */
  private pages: GitHubPages;
  
  /**
   * GitHub releases
   */
  private releases: GitHubReleases;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options GitHub client options
   */
  constructor(options: GitHubClientOptions = {}) {
    this.token = options.token || process.env.GITHUB_TOKEN || process.env.GITHUB_API_TOKEN || '';
    this.owner = options.owner || process.env.GITHUB_OWNER || '';
    this.repo = options.repo || process.env.GITHUB_REPO || '';
    this.baseUrl = options.baseUrl || process.env.GITHUB_API_URL || 'https://api.github.com';
    this.timeout = options.timeout || 30000;
    
    if (!this.token) {
      throw new Error('GitHub token is required');
    }
    
    if (!this.owner) {
      throw new Error('GitHub owner is required');
    }
    
    if (!this.repo) {
      throw new Error('GitHub repository is required');
    }
    
    this.octokit = new Octokit({
      auth: this.token,
      baseUrl: this.baseUrl,
      request: {
        timeout: this.timeout
      }
    });
    
    this.api = new GitHubAPI({
      token: this.token,
      owner: this.owner,
      repo: this.repo,
      baseUrl: this.baseUrl,
      timeout: this.timeout
    });
    
    this.actions = new GitHubActions({
      token: this.token,
      owner: this.owner,
      repo: this.repo,
      baseUrl: this.baseUrl,
      timeout: this.timeout
    });
    
    this.pages = new GitHubPages({
      token: this.token,
      owner: this.owner,
      repo: this.repo,
      baseUrl: this.baseUrl,
      timeout: this.timeout
    });
    
    this.releases = new GitHubReleases({
      token: this.token,
      owner: this.owner,
      repo: this.repo,
      baseUrl: this.baseUrl,
      timeout: this.timeout
    });
    
    this.eventBus = EventBus.getInstance();
    
    // Emit event
    this.eventBus.emit(EventType.GITHUB_CLIENT_INITIALIZED, {
      token: this.token,
      owner: this.owner,
      repo: this.repo,
      baseUrl: this.baseUrl
    });
  }
  
  /**
   * Get Octokit instance
   * @returns Octokit instance
   */
  getOctokit(): Octokit {
    return this.octokit;
  }
  
  /**
   * Get API
   * @returns GitHub API
   */
  getAPI(): GitHubAPI {
    return this.api;
  }
  
  /**
   * Get actions
   * @returns GitHub actions
   */
  getActions(): GitHubActions {
    return this.actions;
  }
  
  /**
   * Get pages
   * @returns GitHub pages
   */
  getPages(): GitHubPages {
    return this.pages;
  }
  
  /**
   * Get releases
   * @returns GitHub releases
   */
  getReleases(): GitHubReleases {
    return this.releases;
  }
  
  /**
   * Get token
   * @returns API token
   */
  getToken(): string {
    return this.token;
  }
  
  /**
   * Get owner
   * @returns Owner
   */
  getOwner(): string {
    return this.owner;
  }
  
  /**
   * Get repository
   * @returns Repository
   */
  getRepo(): string {
    return this.repo;
  }
  
  /**
   * Get base URL
   * @returns Base URL
   */
  getBaseUrl(): string {
    return this.baseUrl;
  }
  
  /**
   * Get timeout
   * @returns Timeout in milliseconds
   */
  getTimeout(): number {
    return this.timeout;
  }
  
  /**
   * Set token
   * @param token API token
   * @returns This instance for chaining
   */
  setToken(token: string): GitHubClient {
    this.token = token;
    
    this.octokit = new Octokit({
      auth: this.token,
      baseUrl: this.baseUrl,
      request: {
        timeout: this.timeout
      }
    });
    
    this.api.setToken(token);
    this.actions.setToken(token);
    this.pages.setToken(token);
    this.releases.setToken(token);
    
    return this;
  }
  
  /**
   * Set owner
   * @param owner Owner
   * @returns This instance for chaining
   */
  setOwner(owner: string): GitHubClient {
    this.owner = owner;
    
    this.api.setOwner(owner);
    this.actions.setOwner(owner);
    this.pages.setOwner(owner);
    this.releases.setOwner(owner);
    
    return this;
  }
  
  /**
   * Set repository
   * @param repo Repository
   * @returns This instance for chaining
   */
  setRepo(repo: string): GitHubClient {
    this.repo = repo;
    
    this.api.setRepo(repo);
    this.actions.setRepo(repo);
    this.pages.setRepo(repo);
    this.releases.setRepo(repo);
    
    return this;
  }
  
  /**
   * Set base URL
   * @param baseUrl Base URL
   * @returns This instance for chaining
   */
  setBaseUrl(baseUrl: string): GitHubClient {
    this.baseUrl = baseUrl;
    
    this.octokit = new Octokit({
      auth: this.token,
      baseUrl: this.baseUrl,
      request: {
        timeout: this.timeout
      }
    });
    
    this.api.setBaseUrl(baseUrl);
    this.actions.setBaseUrl(baseUrl);
    this.pages.setBaseUrl(baseUrl);
    this.releases.setBaseUrl(baseUrl);
    
    return this;
  }
  
  /**
   * Set timeout
   * @param timeout Timeout in milliseconds
   * @returns This instance for chaining
   */
  setTimeout(timeout: number): GitHubClient {
    this.timeout = timeout;
    
    this.octokit = new Octokit({
      auth: this.token,
      baseUrl: this.baseUrl,
      request: {
        timeout: this.timeout
      }
    });
    
    this.api.setTimeout(timeout);
    this.actions.setTimeout(timeout);
    this.pages.setTimeout(timeout);
    this.releases.setTimeout(timeout);
    
    return this;
  }
}

/**
 * Create GitHub client
 * @param options GitHub client options
 * @returns GitHub client
 */
export function createGitHubClient(options: GitHubClientOptions = {}): GitHubClient {
  return new GitHubClient(options);
}
