/**
 * @qawolf/mcp-optimizer
 * MCP optimization and orchestration for QA Wolf testing
 */

// Import shared utilities
const { config, screenshot, test } = require('@qawolf/shared-utils');

// Core modules
const analyzer = require('./core/analyzer');
const optimizer = require('./core/optimizer');
const selector = require('./core/selector');

// Reporting modules
const reporter = require('./reporting/reporter');

// Tool integrations
const playwrightMcp = require('./tools/playwright-mcp');
const browserTools = require('./tools/browser-tools');
const desktopCommander = require('./tools/desktop-commander');
const quillopy = require('./tools/quillopy');
const sequentialThinking = require('./tools/sequential-thinking');

// Export all modules
module.exports = {
  // Core modules
  analyzer,
  optimizer,
  selector,
  
  // Reporting modules
  reporter,
  
  // Tool integrations
  tools: {
    playwrightMcp,
    browserTools,
    desktopCommander,
    quillopy,
    sequentialThinking
  },
  
  // Convenience exports for common functions
  analyzeScreenshot: analyzer.analyzeScreenshot,
  optimizeSelectors: optimizer.optimizeSelectors,
  selectMcpTool: selector.selectMcpTool,
  generateReport: reporter.generateReport,
  
  // Shared utilities
  utils: {
    config,
    screenshot,
    test
  },
  
  // Version
  version: require('../package.json').version
};