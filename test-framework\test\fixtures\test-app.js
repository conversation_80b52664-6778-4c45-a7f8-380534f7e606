/**
 * Test Application Fixture
 * 
 * This module provides a fixture for testing with a controlled test application.
 */

const { chromium } = require('@playwright/test');

/**
 * Create a test application fixture
 * @param {Object} options - Options for the test application
 * @returns {Object} - Test application fixture
 */
async function createTestApp(options = {}) {
  const {
    baseUrl = 'https://app.lidostaging.com',
    credentials = {
      email: '<EMAIL>',
      password: 'vhc!tGK289IS&'
    },
    headless = true
  } = options;
  
  // Launch browser
  const browser = await chromium.launch({
    headless
  });
  
  // Create context
  const context = await browser.newContext({
    viewport: { width: 1280, height: 720 },
    acceptDownloads: true
  });
  
  // Create page
  const page = await context.newPage();
  
  // Create test app object
  const testApp = {
    browser,
    context,
    page,
    baseUrl,
    credentials,
    
    /**
     * Navigate to the app
     * @returns {Promise<void>}
     */
    async navigate() {
      await page.goto(baseUrl);
    },
    
    /**
     * Log in to the app
     * @param {Object} [customCredentials] - Custom credentials
     * @returns {Promise<void>}
     */
    async login(customCredentials) {
      const { email, password } = customCredentials || credentials;
      
      await this.navigate();
      await page.fill('[data-test-id="SignInEmail"]', email);
      await page.fill('[data-test-id="SignInPassword"]', password);
      await page.click(':text("Log in with email")');
      
      // Wait for login to complete
      await page.waitForSelector('div[class*="FilesTable"]', { timeout: 15000 });
    },
    
    /**
     * Create a new file
     * @returns {Promise<string>} - File name
     */
    async createFile() {
      // Click on the "New file" button
      await page.click('div[class*="pages__NewFileButton"]');
      
      // Wait for the file to be created
      await page.waitForTimeout(3000);
      
      // Get the file name
      const fileName = await page.evaluate(() => {
        const fileElements = document.querySelectorAll('div[class*="FilesTable"] div[class*="FilesTable__row"]');
        return fileElements[0]?.textContent || 'Unknown';
      });
      
      return fileName;
    },
    
    /**
     * Delete a file
     * @param {string} fileName - File name
     * @returns {Promise<void>}
     */
    async deleteFile(fileName) {
      // Find the file
      const fileRow = page.locator(`div[class*="FilesTable"] div[class*="FilesTable__row"]:has-text("${fileName}")`);
      
      // Click on the file to select it
      await fileRow.click();
      
      // Click on the delete button
      await page.click('button:has-text("Delete")');
      
      // Confirm deletion
      await page.click('button:has-text("Delete file")');
      
      // Wait for the file to be deleted
      await page.waitForTimeout(3000);
    },
    
    /**
     * Clean up resources
     * @returns {Promise<void>}
     */
    async cleanup() {
      await browser.close();
    }
  };
  
  return testApp;
}

module.exports = {
  createTestApp
};