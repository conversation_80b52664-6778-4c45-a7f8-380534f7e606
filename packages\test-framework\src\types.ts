/**
 * Types for QA Wolf Test Framework
 */

import { Page, BrowserContext, Browser } from 'playwright';

/**
 * Test status
 */
export enum TestStatus {
  PASSED = 'passed',
  FAILED = 'failed',
  SKIPPED = 'skipped',
  PENDING = 'pending',
  RUNNING = 'running'
}

/**
 * Test step status
 */
export enum TestStepStatus {
  PASSED = 'passed',
  FAILED = 'failed',
  SKIPPED = 'skipped'
}

/**
 * Test step type
 */
export enum TestStepType {
  ARRANGE = 'arrange',
  ACT = 'act',
  ASSERT = 'assert',
  SETUP = 'setup',
  TEARDOWN = 'teardown'
}

/**
 * Test step
 */
export interface TestStep {
  name: string;
  type: TestStepType;
  status: TestStepStatus;
  duration: number;
  startTime: number;
  endTime: number;
  error?: Error;
  screenshot?: string;
}

/**
 * Test result
 */
export interface TestResult {
  id: string;
  name: string;
  status: TestStatus;
  duration: number;
  startTime: number;
  endTime: number;
  steps: TestStep[];
  error?: Error;
  screenshots: string[];
  metadata: Record<string, any>;
}

/**
 * Test options
 */
export interface TestOptions {
  name: string;
  timeout?: number;
  retries?: number;
  skip?: boolean;
  only?: boolean;
  tags?: string[];
  metadata?: Record<string, any>;
}

/**
 * Test function
 */
export type TestFunction = (context: TestContext) => Promise<void>;

/**
 * Test context
 */
export interface TestContext {
  page: Page;
  browser: Browser;
  context: BrowserContext;
  testInfo: {
    id: string;
    name: string;
    startTime: number;
    metadata: Record<string, any>;
  };
  step: (name: string, type: TestStepType, fn: () => Promise<void>) => Promise<void>;
  arrange: (name: string, fn: () => Promise<void>) => Promise<void>;
  act: (name: string, fn: () => Promise<void>) => Promise<void>;
  assert: (name: string, fn: () => Promise<void>) => Promise<void>;
  screenshot: (name: string) => Promise<string>;
}

/**
 * Test suite options
 */
export interface TestSuiteOptions {
  name: string;
  timeout?: number;
  retries?: number;
  skip?: boolean;
  only?: boolean;
  tags?: string[];
  metadata?: Record<string, any>;
}

/**
 * Test suite
 */
export interface TestSuite {
  name: string;
  options: TestSuiteOptions;
  tests: Test[];
  beforeAll?: () => Promise<void>;
  afterAll?: () => Promise<void>;
  beforeEach?: () => Promise<void>;
  afterEach?: () => Promise<void>;
}

/**
 * Test
 */
export interface Test {
  name: string;
  options: TestOptions;
  fn: TestFunction;
}

/**
 * Test runner options
 */
export interface TestRunnerOptions {
  browser?: 'chromium' | 'firefox' | 'webkit';
  headless?: boolean;
  timeout?: number;
  retries?: number;
  workers?: number;
  reporter?: string[];
  outputDir?: string;
  testMatch?: string[];
  testIgnore?: string[];
  metadata?: Record<string, any>;
}

/**
 * Test reporter
 */
export interface TestReporter {
  onTestStart: (test: Test) => void;
  onTestEnd: (result: TestResult) => void;
  onStepStart: (test: Test, step: TestStep) => void;
  onStepEnd: (test: Test, step: TestStep) => void;
  onRunStart: (tests: Test[]) => void;
  onRunEnd: (results: TestResult[]) => void;
}

/**
 * AAA pattern
 */
export interface AAAPattern {
  arrange: string[];
  act: string[];
  assert: string[];
}

/**
 * AAA validation result
 */
export interface AAAValidationResult {
  valid: boolean;
  score: number;
  errors: string[];
  warnings: string[];
  pattern: AAAPattern;
}

/**
 * AAA validator options
 */
export interface AAAValidatorOptions {
  minScore: number;
  strictMode: boolean;
  ignoreComments: boolean;
  ignoreImports: boolean;
}

/**
 * AAA validator
 */
export interface AAAValidator {
  validate: (code: string, options?: Partial<AAAValidatorOptions>) => AAAValidationResult;
}

/**
 * Test fixture
 */
export interface TestFixture<T> {
  setup: () => Promise<T>;
  teardown: (value: T) => Promise<void>;
}

/**
 * Test fixtures
 */
export interface TestFixtures {
  browser: TestFixture<Browser>;
  context: TestFixture<BrowserContext>;
  page: TestFixture<Page>;
  [key: string]: TestFixture<any>;
}
