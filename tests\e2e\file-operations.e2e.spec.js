/**
 * File Operations End-to-End Tests
 * 
 * This file contains end-to-end tests for file operations.
 * It validates that users can create, view, and delete files.
 */

const { test, expect, login, navigateToFilesPage, createFile, deleteFile } = require('../utils/test-helpers');
const { PerformanceTracker } = require('../utils/performance-tracker');
const { getConfig } = require('../config/test.config');

// Test configuration
const config = getConfig();

test.describe('File Operations', () => {
  // Before each test, log in
  test.beforeEach(async ({ page }) => {
    await login(page);
  });
  
  /**
   * Test: Create a new file
   * Purpose: Verify that users can create a new file
   * Input: None
   * Expected: New file is created
   */
  test('should create a new file', async ({ page }) => {
    // Performance tracking
    const performanceTracker = new PerformanceTracker({
      thresholds: config.performance.thresholds
    }).start();
    
    try {
      // ARRANGE: Navigate to the files page
      await navigateToFilesPage(page);
      
      // Take a screenshot before creating a file
      await page.takeScreenshot({
        action: 'before-create-file',
        description: 'Before creating a file'
      });
      
      // Count existing "untitled" files before creating a new one
      const existingUntitledCount1 = await page.locator('span[class*="FileTitle"]:has-text("untitled")').count();
      const existingUntitledCount2 = await page.locator('div[class*="styled_FileName"]:has-text("untitled"), div.jHdmZQ:has-text("untitled")').count();
      const existingUntitledCount = Math.max(existingUntitledCount1, existingUntitledCount2);
      
      // ACT: Create a new file
      const fileName = await createFile(page);
      
      // Track the operation
      performanceTracker.trackOperation({
        name: 'Create File',
        type: 'file-operation',
        duration: 1000 // Placeholder value
      });
      
      // Navigate back to the main menu
      try {
        await page.click('button[aria-label="Back"], a[href="/"]');
        await page.waitForTimeout(1000);
      } catch {
        // If that fails, try direct navigation
        await page.goto(config.baseUrl);
      }
      
      // Wait for the files table to appear
      await page.waitForSelector('div[class*="FilesTable"]', { timeout: 5000 });
      
      // Take a screenshot after creating a file
      await page.takeScreenshot({
        action: 'after-create-file',
        description: 'After creating a file'
      });
      
      // ASSERT: Verify the file was created
      // After creating a new file, there should be one more "untitled" file than before
      const newUntitledCount1 = await page.locator('span[class*="FileTitle"]:has-text("untitled")').count();
      const newUntitledCount2 = await page.locator('div[class*="styled_FileName"]:has-text("untitled"), div.jHdmZQ:has-text("untitled")').count();
      const newUntitledCount = Math.max(newUntitledCount1, newUntitledCount2);
      
      expect(newUntitledCount).toBeGreaterThan(existingUntitledCount);
      
      // Clean up: Delete the file we just created
      await deleteFile(page, { fileName });
    } catch (error) {
      // Take a screenshot on error
      await page.takeErrorScreenshot({
        error
      });
      
      throw error;
    } finally {
      // Stop performance tracking
      const metrics = performanceTracker.stop();
      console.log('Performance metrics:', JSON.stringify(metrics, null, 2));
    }
  });
  
  /**
   * Test: Delete a file
   * Purpose: Verify that users can delete a file
   * Input: None
   * Expected: File is deleted
   */
  test('should delete a file', async ({ page }) => {
    // Performance tracking
    const performanceTracker = new PerformanceTracker({
      thresholds: config.performance.thresholds
    }).start();
    
    try {
      // ARRANGE: Navigate to the files page and create a file to delete
      await navigateToFilesPage(page);
      
      // Take a screenshot before creating a file to delete
      await page.takeScreenshot({
        action: 'before-create-file-to-delete',
        description: 'Before creating a file to delete'
      });
      
      // Create a file to delete
      const fileName = await createFile(page);
      
      // Track the operation
      performanceTracker.trackOperation({
        name: 'Create File to Delete',
        type: 'file-operation',
        duration: 1000 // Placeholder value
      });
      
      // Navigate back to the main menu
      try {
        await page.click('button[aria-label="Back"], a[href="/"]');
        await page.waitForTimeout(1000);
      } catch {
        // If that fails, try direct navigation
        await page.goto(config.baseUrl);
      }
      
      // Wait for the files table to appear
      await page.waitForSelector('div[class*="FilesTable"]', { timeout: 5000 });
      
      // Count files with the specified name before deletion
      const existingCount1 = await page.locator(`span[class*="FileTitle"]:has-text("${fileName}")`).count();
      const existingCount2 = await page.locator(`div[class*="styled_FileName"]:has-text("${fileName}"), div.jHdmZQ:has-text("${fileName}")`).count();
      const existingCount = Math.max(existingCount1, existingCount2);
      
      // Take a screenshot before deleting the file
      await page.takeScreenshot({
        action: 'before-delete-file',
        description: 'Before deleting the file'
      });
      
      // ACT: Delete the file
      await deleteFile(page, { fileName });
      
      // Track the operation
      performanceTracker.trackOperation({
        name: 'Delete File',
        type: 'file-operation',
        duration: 1000 // Placeholder value
      });
      
      // Take a screenshot after deleting the file
      await page.takeScreenshot({
        action: 'after-delete-file',
        description: 'After deleting the file'
      });
      
      // ASSERT: Verify the file was deleted
      // After deleting the file, there should be one less file with the specified name
      const newCount1 = await page.locator(`span[class*="FileTitle"]:has-text("${fileName}")`).count();
      const newCount2 = await page.locator(`div[class*="styled_FileName"]:has-text("${fileName}"), div.jHdmZQ:has-text("${fileName}")`).count();
      const newCount = Math.max(newCount1, newCount2);
      
      expect(newCount).toBeLessThan(existingCount);
    } catch (error) {
      // Take a screenshot on error
      await page.takeErrorScreenshot({
        error
      });
      
      throw error;
    } finally {
      // Stop performance tracking
      const metrics = performanceTracker.stop();
      console.log('Performance metrics:', JSON.stringify(metrics, null, 2));
    }
  });
  
  /**
   * Test: Create and delete multiple files
   * Purpose: Verify that users can create and delete multiple files
   * Input: None
   * Expected: Multiple files are created and deleted
   */
  test('should create and delete multiple files', async ({ page }) => {
    // Performance tracking
    const performanceTracker = new PerformanceTracker({
      thresholds: config.performance.thresholds
    }).start();
    
    try {
      // ARRANGE: Navigate to the files page
      await navigateToFilesPage(page);
      
      // Take a screenshot before creating multiple files
      await page.takeScreenshot({
        action: 'before-create-multiple-files',
        description: 'Before creating multiple files'
      });
      
      // Count existing "untitled" files before creating new ones
      const existingUntitledCount1 = await page.locator('span[class*="FileTitle"]:has-text("untitled")').count();
      const existingUntitledCount2 = await page.locator('div[class*="styled_FileName"]:has-text("untitled"), div.jHdmZQ:has-text("untitled")').count();
      const existingUntitledCount = Math.max(existingUntitledCount1, existingUntitledCount2);
      
      // ACT: Create multiple files
      const fileNames = [];
      
      // Create first file
      fileNames.push(await createFile(page));
      
      // Track the operation
      performanceTracker.trackOperation({
        name: 'Create First File',
        type: 'file-operation',
        duration: 1000 // Placeholder value
      });
      
      // Navigate back to the main menu
      try {
        await page.click('button[aria-label="Back"], a[href="/"]');
        await page.waitForTimeout(1000);
      } catch {
        // If that fails, try direct navigation
        await page.goto(config.baseUrl);
      }
      
      // Wait for the files table to appear
      await page.waitForSelector('div[class*="FilesTable"]', { timeout: 5000 });
      
      // Create second file
      fileNames.push(await createFile(page));
      
      // Track the operation
      performanceTracker.trackOperation({
        name: 'Create Second File',
        type: 'file-operation',
        duration: 1000 // Placeholder value
      });
      
      // Navigate back to the main menu
      try {
        await page.click('button[aria-label="Back"], a[href="/"]');
        await page.waitForTimeout(1000);
      } catch {
        // If that fails, try direct navigation
        await page.goto(config.baseUrl);
      }
      
      // Wait for the files table to appear
      await page.waitForSelector('div[class*="FilesTable"]', { timeout: 5000 });
      
      // Take a screenshot after creating multiple files
      await page.takeScreenshot({
        action: 'after-create-multiple-files',
        description: 'After creating multiple files'
      });
      
      // Count "untitled" files after creating new ones
      const newUntitledCount1 = await page.locator('span[class*="FileTitle"]:has-text("untitled")').count();
      const newUntitledCount2 = await page.locator('div[class*="styled_FileName"]:has-text("untitled"), div.jHdmZQ:has-text("untitled")').count();
      const newUntitledCount = Math.max(newUntitledCount1, newUntitledCount2);
      
      // ASSERT: Verify the files were created
      expect(newUntitledCount).toBeGreaterThan(existingUntitledCount + 1);
      
      // Take a screenshot before deleting multiple files
      await page.takeScreenshot({
        action: 'before-delete-multiple-files',
        description: 'Before deleting multiple files'
      });
      
      // Delete the files we just created
      for (const fileName of fileNames) {
        await deleteFile(page, { fileName });
        
        // Track the operation
        performanceTracker.trackOperation({
          name: `Delete File ${fileName}`,
          type: 'file-operation',
          duration: 1000 // Placeholder value
        });
      }
      
      // Take a screenshot after deleting multiple files
      await page.takeScreenshot({
        action: 'after-delete-multiple-files',
        description: 'After deleting multiple files'
      });
      
      // Count "untitled" files after deleting
      const finalUntitledCount1 = await page.locator('span[class*="FileTitle"]:has-text("untitled")').count();
      const finalUntitledCount2 = await page.locator('div[class*="styled_FileName"]:has-text("untitled"), div.jHdmZQ:has-text("untitled")').count();
      const finalUntitledCount = Math.max(finalUntitledCount1, finalUntitledCount2);
      
      // ASSERT: Verify the files were deleted
      expect(finalUntitledCount).toBeLessThanOrEqual(existingUntitledCount);
    } catch (error) {
      // Take a screenshot on error
      await page.takeErrorScreenshot({
        error
      });
      
      throw error;
    } finally {
      // Stop performance tracking
      const metrics = performanceTracker.stop();
      console.log('Performance metrics:', JSON.stringify(metrics, null, 2));
    }
  });
});
