name: Test

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    env:
      SCREENSHOT_DIR: screenshots
      CI_BUILD_NUMBER: ${{ github.run_id }}
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '20'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Run tests
      run: npm test
      
    - name: Verify screenshot structure
      run: npm run screenshots:verify
      
    - name: Upload screenshots
      uses: actions/upload-artifact@v3
      with:
        name: screenshots
        path: screenshots/
        
    - name: Clean up screenshots
      run: npm run screenshots:cleanup
