/**
 * Simplified Login End-to-End Test
 *
 * This is a simplified version of our login end-to-end test that doesn't rely on the MCP or self-healing functionality.
 * It follows the Arrange-Act-Assert (AAA) pattern for test structure.
 *
 * The AAA pattern consists of three main sections:
 * - Arrange: Set up the test environment and prerequisites
 * - Act: Perform the action being tested
 * - Assert: Verify the expected outcome
 */

const { test, expect } = require('@playwright/test');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Create a simple screenshot utility
const screenshot = {
  takeScreenshot: async (page, options) => {
    const { testName, action, fullPage = false } = options;

    // Create screenshots directory if it doesn't exist
    const screenshotDir = path.join(process.cwd(), 'screenshots');
    if (!fs.existsSync(screenshotDir)) {
      fs.mkdirSync(screenshotDir, { recursive: true });
    }

    // Create a dated directory for today's screenshots
    const dateDir = path.join(screenshotDir, new Date().toISOString().split('T')[0]);
    if (!fs.existsSync(dateDir)) {
      fs.mkdirSync(dateDir, { recursive: true });
    }

    // Create a directory for this specific test
    const testDir = path.join(dateDir, testName);
    if (!fs.existsSync(testDir)) {
      fs.mkdirSync(testDir, { recursive: true });
    }

    // Create a timestamp for the filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

    // Build the filename
    const filename = `${timestamp}_${action}.png`;
    const screenshotPath = path.join(testDir, filename);

    // Take the screenshot
    await page.screenshot({
      path: screenshotPath,
      fullPage
    });

    console.log(`Screenshot saved: ${screenshotPath}`);
    return screenshotPath;
  },

  takeErrorScreenshot: async (page, options) => {
    const { testName, error, fullPage = true } = options;

    const errorDescription = error ? `${error.name}-${error.message.substring(0, 20).replace(/[^a-zA-Z0-9]/g, '_')}` : 'unknown-error';

    return screenshot.takeScreenshot(page, {
      testName,
      action: `error-${errorDescription}`,
      fullPage
    });
  }
};

// Performance tracking utility
class PerformanceTracker {
  constructor() {
    this.metrics = {
      executionTime: {
        start: null,
        end: null,
        duration: null
      },
      operations: []
    };
  }

  start() {
    this.metrics.executionTime.start = Date.now();
    return this;
  }

  stop() {
    this.metrics.executionTime.end = Date.now();
    this.metrics.executionTime.duration = this.metrics.executionTime.end - this.metrics.executionTime.start;
    return this.getMetrics();
  }

  trackOperation(operation) {
    const { name, type, duration } = operation;

    this.metrics.operations.push({
      name,
      type,
      duration,
      timestamp: Date.now()
    });

    return this;
  }

  getMetrics() {
    return this.metrics;
  }
}

test.describe('Login Functionality', () => {
  /**
   * Test: Login with valid credentials
   * Purpose: Verify that users can log in with valid credentials
   * Input: Valid email and password
   * Expected: User is logged in successfully
   *
   * This test follows the AAA pattern:
   * - Arrange: Set up test environment with valid credentials
   * - Act: Navigate to login page, fill in credentials, and submit
   * - Assert: Verify successful login by checking URL and visible elements
   */
  test('should login with valid credentials', async ({ page }) => {
    // Performance tracking
    const performanceTracker = new PerformanceTracker().start();

    try {
      // ARRANGE: Set up the test environment
      const email = process.env.EMAIL || '<EMAIL>';
      const password = process.env.PASSWORD || 'vhc!tGK289IS&';
      const url = process.env.URL || 'https://app.lidostaging.com';

      // Take a screenshot before login
      await screenshot.takeScreenshot(page, {
        testName: 'login-valid-credentials',
        action: 'before-login',
        fullPage: true
      });

      // ACT: Log in with valid credentials
      await page.goto(url);

      // Fill in login credentials and submit
      await page.fill('[data-test-id="SignInEmail"]', email);
      await page.fill('[data-test-id="SignInPassword"]', password);

      // Take a screenshot before clicking login button
      await screenshot.takeScreenshot(page, {
        testName: 'login-valid-credentials',
        action: 'filled-login-form',
        fullPage: true
      });

      await page.locator(':text("Log in with email")').click();

      // Wait for navigation to complete
      await page.waitForNavigation();

      // Track the operation
      performanceTracker.trackOperation({
        name: 'Login with Valid Credentials',
        type: 'login',
        duration: 2000 // Placeholder value
      });

      // Take a screenshot after login
      await screenshot.takeScreenshot(page, {
        testName: 'login-valid-credentials',
        action: 'after-login',
        fullPage: true
      });

      // ASSERT: Verify successful login
      // Check that we're redirected to the dashboard or home page
      await expect(page).not.toHaveURL(/login/);

      // Check for elements that should be visible after login
      await expect(page.locator('div[class*="FilesTable__Wrapper"]')).toBeVisible();
    } catch (error) {
      // Take a screenshot on error
      await screenshot.takeErrorScreenshot(page, {
        testName: 'login-valid-credentials',
        error
      });

      throw error;
    } finally {
      // Stop performance tracking
      const metrics = performanceTracker.stop();
      console.log('Performance metrics:', JSON.stringify(metrics, null, 2));
    }
  });

  /**
   * Test: Login with invalid credentials
   * Purpose: Verify that appropriate error messages are shown for invalid credentials
   * Input: Invalid email and password
   * Expected: Error message is shown
   *
   * This test follows the AAA pattern:
   * - Arrange: Set up test environment with invalid credentials
   * - Act: Navigate to login page, fill in invalid credentials, and submit
   * - Assert: Verify error message is displayed and we remain on login page
   */
  test('should show error with invalid credentials', async ({ page }) => {
    // Performance tracking
    const performanceTracker = new PerformanceTracker().start();

    try {
      // ARRANGE: Set up the test environment
      const email = '<EMAIL>';
      const password = 'invalidpassword';
      const url = process.env.URL || 'https://app.lidostaging.com';

      // Take a screenshot before login
      await screenshot.takeScreenshot(page, {
        testName: 'login-invalid-credentials',
        action: 'before-invalid-login',
        fullPage: true
      });

      // ACT: Log in with invalid credentials
      await page.goto(url);

      // Fill in invalid login credentials and submit
      await page.fill('[data-test-id="SignInEmail"]', email);
      await page.fill('[data-test-id="SignInPassword"]', password);

      // Take a screenshot before clicking login button
      await screenshot.takeScreenshot(page, {
        testName: 'login-invalid-credentials',
        action: 'filled-invalid-login-form',
        fullPage: true
      });

      await page.locator(':text("Log in with email")').click();

      // Track the operation
      performanceTracker.trackOperation({
        name: 'Login with Invalid Credentials',
        type: 'login',
        duration: 1000 // Placeholder value
      });

      // Take a screenshot after login attempt
      await screenshot.takeScreenshot(page, {
        testName: 'login-invalid-credentials',
        action: 'after-invalid-login',
        fullPage: true
      });

      // ASSERT: Verify error message
      // Wait for the error message to appear
      await page.waitForSelector('text=Email or password incorrect', { timeout: 5000 });

      // Check that we're still on the login page
      await expect(page).toHaveURL(/login/);

      // Check that the error message is visible
      await expect(page.locator('text=Email or password incorrect')).toBeVisible();
    } catch (error) {
      // Take a screenshot on error
      await screenshot.takeErrorScreenshot(page, {
        testName: 'login-invalid-credentials',
        error
      });

      throw error;
    } finally {
      // Stop performance tracking
      const metrics = performanceTracker.stop();
      console.log('Performance metrics:', JSON.stringify(metrics, null, 2));
    }
  });
});
