name: Continuous Integration

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  lint:
    name: <PERSON><PERSON>
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: 20
        cache: 'npm'
    
    - name: Install dependencies
      run: |
        npm ci
        npm run bootstrap
    
    - name: Lint
      run: npm run lint
  
  test:
    name: Test
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: 20
        cache: 'npm'
    
    - name: Install dependencies
      run: |
        npm ci
        npm run bootstrap
    
    - name: Build
      run: npm run build
    
    - name: Test
      run: npm test
    
    - name: Upload test results
      uses: actions/upload-artifact@v3
      with:
        name: test-results
        path: reports/
  
  integration:
    name: Integration Tests
    runs-on: ubuntu-latest
    needs: [lint, test]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: 20
        cache: 'npm'
    
    - name: Install dependencies
      run: |
        npm ci
        npm run bootstrap
    
    - name: Build
      run: npm run build
    
    - name: Install Playwright
      run: npx playwright install --with-deps
    
    - name: Run integration tests
      run: npm run test:integration
    
    - name: Upload integration test results
      uses: actions/upload-artifact@v3
      with:
        name: integration-test-results
        path: reports/
  
  e2e:
    name: End-to-End Tests
    runs-on: ubuntu-latest
    needs: [lint, test]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: 20
        cache: 'npm'
    
    - name: Install dependencies
      run: |
        npm ci
        npm run bootstrap
    
    - name: Build
      run: npm run build
    
    - name: Install Playwright
      run: npx playwright install --with-deps
    
    - name: Run E2E tests
      run: npm run test:e2e
    
    - name: Upload E2E test results
      uses: actions/upload-artifact@v3
      with:
        name: e2e-test-results
        path: reports/
  
  performance:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: [lint, test]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: 20
        cache: 'npm'
    
    - name: Install dependencies
      run: |
        npm ci
        npm run bootstrap
    
    - name: Build
      run: npm run build
    
    - name: Install Playwright
      run: npx playwright install --with-deps
    
    - name: Run performance tests
      run: npm run test:performance
    
    - name: Upload performance test results
      uses: actions/upload-artifact@v3
      with:
        name: performance-test-results
        path: reports/
  
  reliability:
    name: Reliability Tests
    runs-on: ubuntu-latest
    needs: [lint, test]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: 20
        cache: 'npm'
    
    - name: Install dependencies
      run: |
        npm ci
        npm run bootstrap
    
    - name: Build
      run: npm run build
    
    - name: Install Playwright
      run: npx playwright install --with-deps
    
    - name: Run reliability tests
      run: npm run test:reliability
    
    - name: Upload reliability test results
      uses: actions/upload-artifact@v3
      with:
        name: reliability-test-results
        path: reports/
  
  dashboard:
    name: Generate Dashboard
    runs-on: ubuntu-latest
    needs: [integration, e2e, performance, reliability]
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: 20
        cache: 'npm'
    
    - name: Install dependencies
      run: |
        npm ci
        npm run bootstrap
    
    - name: Download all test results
      uses: actions/download-artifact@v3
      with:
        path: reports/
    
    - name: Generate dashboard
      run: node test-framework/test/dashboard/index.js
    
    - name: Upload dashboard
      uses: actions/upload-artifact@v3
      with:
        name: test-dashboard
        path: dashboard/