/**
 * Linear projects for Linear integration
 */

import { LinearClient, Project, ProjectConnection } from '@linear/sdk';
import { LinearProjectsOptions, LinearProjectFilter, LinearProjectCreateInput, LinearProjectUpdateInput } from './types';
import { EventBus, EventType } from '@qawolf/core';

/**
 * Linear projects
 */
export class LinearProjects {
  /**
   * Linear client
   */
  private client: LinearClient;
  
  /**
   * Team ID
   */
  private teamId: string;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options Linear projects options
   */
  constructor(options: LinearProjectsOptions = {}) {
    if (!options.client && !options.apiKey) {
      throw new Error('Linear client or API key is required');
    }
    
    if (options.client) {
      this.client = options.client;
    } else {
      this.client = new LinearClient({
        apiKey: options.apiKey
      });
    }
    
    this.teamId = options.teamId || '';
    this.eventBus = EventBus.getInstance();
  }
  
  /**
   * Get projects
   * @param filter Project filter
   * @returns Projects
   */
  async getProjects(filter?: LinearProjectFilter): Promise<Project[]> {
    try {
      // Build filter
      const queryFilter: Record<string, any> = {};
      
      if (filter?.id) {
        queryFilter.id = { eq: filter.id };
      }
      
      if (filter?.name) {
        queryFilter.name = { contains: filter.name };
      }
      
      if (filter?.description) {
        queryFilter.description = { contains: filter.description };
      }
      
      if (filter?.state) {
        queryFilter.state = { eq: filter.state };
      }
      
      if (filter?.team) {
        queryFilter.team = { id: { eq: filter.team } };
      } else if (this.teamId) {
        queryFilter.team = { id: { eq: this.teamId } };
      }
      
      if (filter?.lead) {
        queryFilter.lead = { id: { eq: filter.lead } };
      }
      
      if (filter?.members && filter.members.length > 0) {
        queryFilter.members = { id: { in: filter.members } };
      }
      
      if (filter?.createdAfter) {
        queryFilter.createdAt = { ...queryFilter.createdAt, gt: filter.createdAfter };
      }
      
      if (filter?.createdBefore) {
        queryFilter.createdAt = { ...queryFilter.createdAt, lt: filter.createdBefore };
      }
      
      if (filter?.updatedAfter) {
        queryFilter.updatedAt = { ...queryFilter.updatedAt, gt: filter.updatedAfter };
      }
      
      if (filter?.updatedBefore) {
        queryFilter.updatedAt = { ...queryFilter.updatedAt, lt: filter.updatedBefore };
      }
      
      if (filter?.startDate) {
        queryFilter.startDate = { eq: filter.startDate };
      }
      
      if (filter?.startDateAfter) {
        queryFilter.startDate = { ...queryFilter.startDate, gt: filter.startDateAfter };
      }
      
      if (filter?.startDateBefore) {
        queryFilter.startDate = { ...queryFilter.startDate, lt: filter.startDateBefore };
      }
      
      if (filter?.targetDate) {
        queryFilter.targetDate = { eq: filter.targetDate };
      }
      
      if (filter?.targetDateAfter) {
        queryFilter.targetDate = { ...queryFilter.targetDate, gt: filter.targetDateAfter };
      }
      
      if (filter?.targetDateBefore) {
        queryFilter.targetDate = { ...queryFilter.targetDate, lt: filter.targetDateBefore };
      }
      
      // Build order by
      const orderBy = filter?.sortBy ? `${filter.sortBy}_${filter.sortOrder || 'ASC'}` : undefined;
      
      // Get projects
      const projects = await this.client.projects({
        filter: queryFilter,
        orderBy
      });
      
      // Get all projects
      const allProjects = await this.getAllProjects(projects);
      
      // Emit event
      this.eventBus.emit(EventType.LINEAR_PROJECTS_GET_PROJECTS, {
        projects: allProjects
      });
      
      return allProjects;
    } catch (error) {
      this.handleError(error, 'Failed to get projects');
      return [];
    }
  }
  
  /**
   * Get project by ID
   * @param id Project ID
   * @returns Project
   */
  async getProjectById(id: string): Promise<Project | null> {
    try {
      const project = await this.client.project(id);
      
      // Emit event
      this.eventBus.emit(EventType.LINEAR_PROJECTS_GET_PROJECT_BY_ID, {
        project
      });
      
      return project;
    } catch (error) {
      this.handleError(error, `Failed to get project with ID ${id}`);
      return null;
    }
  }
  
  /**
   * Create project
   * @param input Project create input
   * @returns Project
   */
  async createProject(input: LinearProjectCreateInput): Promise<Project | null> {
    try {
      const teamId = input.teamId || this.teamId;
      
      if (!teamId) {
        throw new Error('Team ID is required');
      }
      
      const project = await this.client.projectCreate({
        name: input.name,
        description: input.description,
        state: input.state,
        teamIds: [teamId],
        leadId: input.leadId,
        memberIds: input.memberIds,
        startDate: input.startDate ? input.startDate.toISOString() : undefined,
        targetDate: input.targetDate ? input.targetDate.toISOString() : undefined,
        icon: input.icon,
        color: input.color
      });
      
      // Emit event
      this.eventBus.emit(EventType.LINEAR_PROJECTS_CREATE_PROJECT, {
        project: project.project
      });
      
      return project.project;
    } catch (error) {
      this.handleError(error, 'Failed to create project');
      return null;
    }
  }
  
  /**
   * Update project
   * @param id Project ID
   * @param input Project update input
   * @returns Project
   */
  async updateProject(id: string, input: LinearProjectUpdateInput): Promise<Project | null> {
    try {
      const project = await this.client.projectUpdate(id, {
        name: input.name,
        description: input.description,
        state: input.state,
        teamIds: input.teamId ? [input.teamId] : undefined,
        leadId: input.leadId,
        memberIds: input.memberIds,
        startDate: input.startDate ? input.startDate.toISOString() : undefined,
        targetDate: input.targetDate ? input.targetDate.toISOString() : undefined,
        icon: input.icon,
        color: input.color
      });
      
      // Emit event
      this.eventBus.emit(EventType.LINEAR_PROJECTS_UPDATE_PROJECT, {
        project: project.project
      });
      
      return project.project;
    } catch (error) {
      this.handleError(error, `Failed to update project with ID ${id}`);
      return null;
    }
  }
  
  /**
   * Delete project
   * @param id Project ID
   * @returns True if deleted, false otherwise
   */
  async deleteProject(id: string): Promise<boolean> {
    try {
      const result = await this.client.projectDelete(id);
      
      // Emit event
      this.eventBus.emit(EventType.LINEAR_PROJECTS_DELETE_PROJECT, {
        id,
        success: result.success
      });
      
      return result.success;
    } catch (error) {
      this.handleError(error, `Failed to delete project with ID ${id}`);
      return false;
    }
  }
  
  /**
   * Get all projects
   * @param projects Project connection
   * @returns Projects
   */
  private async getAllProjects(projects: ProjectConnection): Promise<Project[]> {
    const allProjects: Project[] = [];
    let currentPage = projects;
    
    while (true) {
      const nodes = await currentPage.nodes;
      allProjects.push(...nodes);
      
      if (!(await currentPage.hasNextPage)) {
        break;
      }
      
      currentPage = await currentPage.next();
    }
    
    return allProjects;
  }
  
  /**
   * Handle error
   * @param error Error
   * @param message Error message
   */
  private handleError(error: any, message: string): void {
    console.error(message, error);
    
    // Emit event
    this.eventBus.emit(EventType.LINEAR_PROJECTS_ERROR, {
      error,
      message
    });
  }
  
  /**
   * Get team ID
   * @returns Team ID
   */
  getTeamId(): string {
    return this.teamId;
  }
  
  /**
   * Set team ID
   * @param teamId Team ID
   * @returns This instance for chaining
   */
  setTeamId(teamId: string): LinearProjects {
    this.teamId = teamId;
    return this;
  }
}

/**
 * Create Linear projects
 * @param options Linear projects options
 * @returns Linear projects
 */
export function createLinearProjects(options: LinearProjectsOptions = {}): LinearProjects {
  return new LinearProjects(options);
}
