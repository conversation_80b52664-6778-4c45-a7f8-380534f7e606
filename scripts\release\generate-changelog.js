#!/usr/bin/env node

/**
 * Generate Changelog Script
 * 
 * This script generates a changelog from git commits.
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Parse command line arguments
const args = process.argv.slice(2);
const fromVersion = args[0] || '';
const toVersion = args[1] || 'HEAD';

/**
 * Generate a changelog
 * @param {string} fromVersion - From version
 * @param {string} toVersion - To version
 * @returns {string} - Changelog
 */
function generateChangelog(fromVersion, toVersion) {
  console.log(`Generating changelog from ${fromVersion || 'beginning'} to ${toVersion}...`);
  
  // Get commits
  const gitLogCommand = fromVersion
    ? `git log --pretty=format:"%h %s" ${fromVersion}..${toVersion}`
    : `git log --pretty=format:"%h %s" ${toVersion}`;
  
  const commits = execSync(gitLogCommand, { encoding: 'utf8' }).trim().split('\n');
  
  // Group commits by type
  const commitsByType = {
    feat: [],
    fix: [],
    docs: [],
    style: [],
    refactor: [],
    perf: [],
    test: [],
    build: [],
    ci: [],
    chore: [],
    revert: [],
    other: []
  };
  
  for (const commit of commits) {
    const match = commit.match(/^([a-f0-9]+) (feat|fix|docs|style|refactor|perf|test|build|ci|chore|revert)(\(.+\))?:\s(.+)$/);
    
    if (match) {
      const [, hash, type, scope, message] = match;
      commitsByType[type].push({ hash, scope, message });
    } else {
      const [hash, ...messageParts] = commit.split(' ');
      const message = messageParts.join(' ');
      commitsByType.other.push({ hash, message });
    }
  }
  
  // Generate changelog
  let changelog = `# Changelog\n\n`;
  
  // Features
  if (commitsByType.feat.length > 0) {
    changelog += '## Features\n\n';
    
    for (const commit of commitsByType.feat) {
      const scope = commit.scope ? ` ${commit.scope}` : '';
      changelog += `- ${commit.message} ([${commit.hash}](https://github.com/sneezyxl/QAWolfeesojc/commit/${commit.hash}))\n`;
    }
    
    changelog += '\n';
  }
  
  // Bug Fixes
  if (commitsByType.fix.length > 0) {
    changelog += '## Bug Fixes\n\n';
    
    for (const commit of commitsByType.fix) {
      const scope = commit.scope ? ` ${commit.scope}` : '';
      changelog += `- ${commit.message} ([${commit.hash}](https://github.com/sneezyxl/QAWolfeesojc/commit/${commit.hash}))\n`;
    }
    
    changelog += '\n';
  }
  
  // Documentation
  if (commitsByType.docs.length > 0) {
    changelog += '## Documentation\n\n';
    
    for (const commit of commitsByType.docs) {
      const scope = commit.scope ? ` ${commit.scope}` : '';
      changelog += `- ${commit.message} ([${commit.hash}](https://github.com/sneezyxl/QAWolfeesojc/commit/${commit.hash}))\n`;
    }
    
    changelog += '\n';
  }
  
  // Performance
  if (commitsByType.perf.length > 0) {
    changelog += '## Performance\n\n';
    
    for (const commit of commitsByType.perf) {
      const scope = commit.scope ? ` ${commit.scope}` : '';
      changelog += `- ${commit.message} ([${commit.hash}](https://github.com/sneezyxl/QAWolfeesojc/commit/${commit.hash}))\n`;
    }
    
    changelog += '\n';
  }
  
  // Other Changes
  const otherTypes = ['style', 'refactor', 'test', 'build', 'ci', 'chore', 'revert', 'other'];
  const otherCommits = otherTypes.flatMap(type => commitsByType[type]);
  
  if (otherCommits.length > 0) {
    changelog += '## Other Changes\n\n';
    
    for (const commit of otherCommits) {
      if (commit.message) {
        changelog += `- ${commit.message} ([${commit.hash}](https://github.com/sneezyxl/QAWolfeesojc/commit/${commit.hash}))\n`;
      } else {
        changelog += `- ${commit.hash}\n`;
      }
    }
    
    changelog += '\n';
  }
  
  return changelog;
}

/**
 * Save changelog to file
 * @param {string} changelog - Changelog content
 * @param {string} [filePath] - File path
 * @returns {void}
 */
function saveChangelog(changelog, filePath = 'CHANGELOG.md') {
  const fullPath = path.join(process.cwd(), filePath);
  fs.writeFileSync(fullPath, changelog, 'utf8');
  console.log(`Changelog saved to ${fullPath}`);
}

// Generate changelog
const changelog = generateChangelog(fromVersion, toVersion);

// Save changelog
saveChangelog(changelog);

// Print changelog
console.log(changelog);