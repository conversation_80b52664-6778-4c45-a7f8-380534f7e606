#!/usr/bin/env node

/**
 * QA Wolf CI Greenlight CLI
 * 
 * This script polls the QA Wolf API for CI greenlight status.
 */

const { createCIGreenlight } = require('../dist/ci-greenlight');

// Parse command line arguments
const args = process.argv.slice(2);
const options = {};

for (let i = 0; i < args.length; i++) {
  const arg = args[i];
  
  if (arg === '--api-key' && i + 1 < args.length) {
    options.apiKey = args[++i];
  } else if (arg === '--team-id' && i + 1 < args.length) {
    options.teamId = args[++i];
  } else if (arg === '--api-url' && i + 1 < args.length) {
    options.apiUrl = args[++i];
  } else if (arg === '--reports-dir' && i + 1 < args.length) {
    options.reportsDir = args[++i];
  } else if (arg === '--max-polls' && i + 1 < args.length) {
    options.maxPolls = parseInt(args[++i], 10);
  } else if (arg === '--poll-interval' && i + 1 < args.length) {
    options.pollInterval = parseInt(args[++i], 10);
  } else if (arg === '--environment' && i + 1 < args.length) {
    options.environment = args[++i];
  } else if (arg === '--branch' && i + 1 < args.length) {
    options.branch = args[++i];
  }
}

// Create CI greenlight instance
const ciGreenlight = createCIGreenlight(options);

// Poll for greenlight
ciGreenlight.pollForGreenlight()
  .then(greenlight => {
    if (greenlight) {
      console.log('QA Wolf CI greenlight: PASSED');
      process.exit(0);
    } else {
      console.error('QA Wolf CI greenlight: FAILED');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Error polling for QA Wolf CI greenlight:', error.message);
    process.exit(1);
  });
