/**
 * Testmo automation for Testmo integration
 */

import axios, { AxiosInstance } from 'axios';
import { TestmoAutomationOptions, TestmoAPIResponse, TestmoAutomation as TestmoAutomationType, TestmoAutomationResult } from './types';
import { EventBus, EventType } from '@qawolf/core';

/**
 * Testmo automation
 */
export class TestmoAutomation {
  /**
   * API key
   */
  private apiKey: string;
  
  /**
   * Host URL
   */
  private host: string;
  
  /**
   * Project ID
   */
  private projectId: number;
  
  /**
   * Source
   */
  private source: string;
  
  /**
   * Timeout in milliseconds
   */
  private timeout: number;
  
  /**
   * Axios instance
   */
  private axios: AxiosInstance;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options Testmo automation options
   */
  constructor(options: TestmoAutomationOptions = {}) {
    this.apiKey = options.apiKey || '';
    this.host = options.host || 'https://api.testmo.io';
    this.projectId = options.projectId || 0;
    this.source = options.source || 'qawolf';
    this.timeout = options.timeout || 30000;
    
    this.axios = axios.create({
      baseURL: this.host,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      }
    });
    
    this.eventBus = EventBus.getInstance();
  }
  
  /**
   * Get automations
   * @param projectId Project ID
   * @returns Automations
   */
  async getAutomations(projectId?: number): Promise<TestmoAutomationType[]> {
    try {
      const id = projectId || this.projectId;
      
      if (!id) {
        throw new Error('Project ID is required');
      }
      
      const response = await this.axios.get<TestmoAPIResponse<TestmoAutomationType[]>>(`/api/v1/projects/${id}/automations`);
      
      // Emit event
      this.eventBus.emit(EventType.TESTMO_AUTOMATION_GET_AUTOMATIONS, {
        automations: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, `Failed to get automations for project with ID ${projectId || this.projectId}`);
      return [];
    }
  }
  
  /**
   * Get automation by ID
   * @param automationId Automation ID
   * @param projectId Project ID
   * @returns Automation
   */
  async getAutomationById(automationId: number, projectId?: number): Promise<TestmoAutomationType | null> {
    try {
      const id = projectId || this.projectId;
      
      if (!id) {
        throw new Error('Project ID is required');
      }
      
      const response = await this.axios.get<TestmoAPIResponse<TestmoAutomationType>>(`/api/v1/projects/${id}/automations/${automationId}`);
      
      // Emit event
      this.eventBus.emit(EventType.TESTMO_AUTOMATION_GET_AUTOMATION_BY_ID, {
        automation: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, `Failed to get automation with ID ${automationId}`);
      return null;
    }
  }
  
  /**
   * Create automation
   * @param name Automation name
   * @param sourceId Automation source ID
   * @param caseId Automation case ID
   * @param source Automation source
   * @param projectId Project ID
   * @returns Automation
   */
  async createAutomation(
    name: string,
    sourceId: string,
    caseId?: number,
    source?: string,
    projectId?: number
  ): Promise<TestmoAutomationType | null> {
    try {
      const id = projectId || this.projectId;
      
      if (!id) {
        throw new Error('Project ID is required');
      }
      
      const response = await this.axios.post<TestmoAPIResponse<TestmoAutomationType>>(`/api/v1/projects/${id}/automations`, {
        name,
        source_id: sourceId,
        case_id: caseId,
        source: source || this.source
      });
      
      // Emit event
      this.eventBus.emit(EventType.TESTMO_AUTOMATION_CREATE_AUTOMATION, {
        automation: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, `Failed to create automation with name ${name}`);
      return null;
    }
  }
  
  /**
   * Update automation
   * @param automationId Automation ID
   * @param name Automation name
   * @param sourceId Automation source ID
   * @param caseId Automation case ID
   * @param source Automation source
   * @param projectId Project ID
   * @returns Automation
   */
  async updateAutomation(
    automationId: number,
    name?: string,
    sourceId?: string,
    caseId?: number,
    source?: string,
    projectId?: number
  ): Promise<TestmoAutomationType | null> {
    try {
      const id = projectId || this.projectId;
      
      if (!id) {
        throw new Error('Project ID is required');
      }
      
      const response = await this.axios.put<TestmoAPIResponse<TestmoAutomationType>>(`/api/v1/projects/${id}/automations/${automationId}`, {
        name,
        source_id: sourceId,
        case_id: caseId,
        source: source || this.source
      });
      
      // Emit event
      this.eventBus.emit(EventType.TESTMO_AUTOMATION_UPDATE_AUTOMATION, {
        automation: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, `Failed to update automation with ID ${automationId}`);
      return null;
    }
  }
  
  /**
   * Delete automation
   * @param automationId Automation ID
   * @param projectId Project ID
   * @returns True if deleted, false otherwise
   */
  async deleteAutomation(automationId: number, projectId?: number): Promise<boolean> {
    try {
      const id = projectId || this.projectId;
      
      if (!id) {
        throw new Error('Project ID is required');
      }
      
      await this.axios.delete<TestmoAPIResponse<void>>(`/api/v1/projects/${id}/automations/${automationId}`);
      
      // Emit event
      this.eventBus.emit(EventType.TESTMO_AUTOMATION_DELETE_AUTOMATION, {
        automationId
      });
      
      return true;
    } catch (error) {
      this.handleError(error, `Failed to delete automation with ID ${automationId}`);
      return false;
    }
  }
  
  /**
   * Get automation results
   * @param automationId Automation ID
   * @param projectId Project ID
   * @returns Automation results
   */
  async getAutomationResults(automationId: number, projectId?: number): Promise<TestmoAutomationResult[]> {
    try {
      const id = projectId || this.projectId;
      
      if (!id) {
        throw new Error('Project ID is required');
      }
      
      const response = await this.axios.get<TestmoAPIResponse<TestmoAutomationResult[]>>(`/api/v1/projects/${id}/automations/${automationId}/results`);
      
      // Emit event
      this.eventBus.emit(EventType.TESTMO_AUTOMATION_GET_AUTOMATION_RESULTS, {
        automationResults: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, `Failed to get automation results for automation with ID ${automationId}`);
      return [];
    }
  }
  
  /**
   * Add automation result
   * @param automationId Automation ID
   * @param statusId Status ID
   * @param comment Comment
   * @param version Version
   * @param elapsed Elapsed
   * @param defects Defects
   * @param projectId Project ID
   * @returns Automation result
   */
  async addAutomationResult(
    automationId: number,
    statusId: number,
    comment?: string,
    version?: string,
    elapsed?: string,
    defects?: string,
    projectId?: number
  ): Promise<TestmoAutomationResult | null> {
    try {
      const id = projectId || this.projectId;
      
      if (!id) {
        throw new Error('Project ID is required');
      }
      
      const response = await this.axios.post<TestmoAPIResponse<TestmoAutomationResult>>(`/api/v1/projects/${id}/automations/${automationId}/results`, {
        status_id: statusId,
        comment,
        version,
        elapsed,
        defects
      });
      
      // Emit event
      this.eventBus.emit(EventType.TESTMO_AUTOMATION_ADD_AUTOMATION_RESULT, {
        automationResult: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, `Failed to add automation result for automation with ID ${automationId}`);
      return null;
    }
  }
  
  /**
   * Handle error
   * @param error Error
   * @param message Error message
   */
  private handleError(error: any, message: string): void {
    console.error(message, error);
    
    // Emit event
    this.eventBus.emit(EventType.TESTMO_AUTOMATION_ERROR, {
      error,
      message
    });
  }
  
  /**
   * Get API key
   * @returns API key
   */
  getAPIKey(): string {
    return this.apiKey;
  }
  
  /**
   * Get host URL
   * @returns Host URL
   */
  getHost(): string {
    return this.host;
  }
  
  /**
   * Get project ID
   * @returns Project ID
   */
  getProjectId(): number {
    return this.projectId;
  }
  
  /**
   * Get source
   * @returns Source
   */
  getSource(): string {
    return this.source;
  }
  
  /**
   * Get timeout
   * @returns Timeout in milliseconds
   */
  getTimeout(): number {
    return this.timeout;
  }
  
  /**
   * Set API key
   * @param apiKey API key
   * @returns This instance for chaining
   */
  setAPIKey(apiKey: string): TestmoAutomation {
    this.apiKey = apiKey;
    
    this.axios = axios.create({
      baseURL: this.host,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      }
    });
    
    return this;
  }
  
  /**
   * Set host URL
   * @param host Host URL
   * @returns This instance for chaining
   */
  setHost(host: string): TestmoAutomation {
    this.host = host;
    
    this.axios = axios.create({
      baseURL: this.host,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      }
    });
    
    return this;
  }
  
  /**
   * Set project ID
   * @param projectId Project ID
   * @returns This instance for chaining
   */
  setProjectId(projectId: number): TestmoAutomation {
    this.projectId = projectId;
    return this;
  }
  
  /**
   * Set source
   * @param source Source
   * @returns This instance for chaining
   */
  setSource(source: string): TestmoAutomation {
    this.source = source;
    return this;
  }
  
  /**
   * Set timeout
   * @param timeout Timeout in milliseconds
   * @returns This instance for chaining
   */
  setTimeout(timeout: number): TestmoAutomation {
    this.timeout = timeout;
    
    this.axios = axios.create({
      baseURL: this.host,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      }
    });
    
    return this;
  }
}

/**
 * Create Testmo automation
 * @param options Testmo automation options
 * @returns Testmo automation
 */
export function createTestmoAutomation(options: TestmoAutomationOptions = {}): TestmoAutomation {
  return new TestmoAutomation(options);
}
