# Creating a Login Test

This tutorial will guide you through creating a login test using the QA Wolf testing framework.

## Prerequisites

Before you begin, make sure you have:

- Installed the QA Wolf testing framework
- Configured your environment

If you haven't done these steps yet, see the [Getting Started Guide](../guides/getting-started.md).

## Step 1: Create a Test File

Create a new file called `login-test.spec.js` in your `tests` directory:

```javascript
const { test, expect } = require('@playwright/test');
const { createMcpController, createSelfHealingController, test: testUtils } = require('@qawolf/test-framework');
const { config } = require('@qawolf/shared-utils');

test('login test', async ({ page }) => {
  // We'll add our test code here
});
```

## Step 2: Set Up Controllers

Add code to create the MCP controller and self-healing controller:

```javascript
const { test, expect } = require('@playwright/test');
const { createMcpController, createSelfHealingController, test: testUtils } = require('@qawolf/test-framework');
const { config } = require('@qawolf/shared-utils');

test('login test', async ({ page }) => {
  // Create an MCP controller
  const mcpController = createMcpController();
  
  // Create a self-healing controller
  const selfHealingController = createSelfHealingController();
  
  try {
    // We'll add our test code here
  } finally {
    // Clean up resources
    await mcpController.cleanup();
    await selfHealingController.cleanup();
  }
});
```

## Step 3: Start the Test Run

Add code to start the test run:

```javascript
const { test, expect } = require('@playwright/test');
const { createMcpController, createSelfHealingController, test: testUtils } = require('@qawolf/test-framework');
const { config } = require('@qawolf/shared-utils');

test('login test', async ({ page }) => {
  // Create an MCP controller
  const mcpController = createMcpController();
  
  // Create a self-healing controller
  const selfHealingController = createSelfHealingController();
  
  try {
    // Start the test run
    await selfHealingController.startTest({
      testId: 'login-test',
      testName: 'Login Test'
    });
    
    // Create a self-healing page
    const selfHealingPage = selfHealingController.createPage(page);
    
    // Create a performance tracker
    const performanceTracker = new testUtils.PerformanceTracker();
    
    // We'll add our test code here
    
    // End the test run
    await selfHealingController.endTest({
      success: true
    });
  } catch (error) {
    // End the test run with failure
    await selfHealingController.endTest({
      success: false,
      error
    });
    
    throw error;
  } finally {
    // Clean up resources
    await mcpController.cleanup();
    await selfHealingController.cleanup();
  }
});
```

## Step 4: Add Login Logic

Now, let's add the login logic:

```javascript
const { test, expect } = require('@playwright/test');
const { createMcpController, createSelfHealingController, test: testUtils } = require('@qawolf/test-framework');
const { config } = require('@qawolf/shared-utils');

test('login test', async ({ page }) => {
  // Create an MCP controller
  const mcpController = createMcpController();
  
  // Create a self-healing controller
  const selfHealingController = createSelfHealingController();
  
  try {
    // Start the test run
    await selfHealingController.startTest({
      testId: 'login-test',
      testName: 'Login Test'
    });
    
    // Create a self-healing page
    const selfHealingPage = selfHealingController.createPage(page);
    
    // Create a performance tracker
    const performanceTracker = new testUtils.PerformanceTracker();
    
    // Get credentials from environment variables
    const email = config.getEnv('QAWOLF_EMAIL', '<EMAIL>');
    const password = config.getEnv('QAWOLF_PASSWORD', 'vhc!tGK289IS&');
    const baseUrl = config.getEnv('QAWOLF_BASE_URL', 'https://app.lidostaging.com');
    
    // Navigate to the app
    performanceTracker.startOperation('navigation_to_app');
    await selfHealingPage.goto(baseUrl);
    performanceTracker.endOperation();
    
    // Fill in login form
    performanceTracker.startOperation('login');
    await selfHealingPage.fill('[data-test-id="SignInEmail"]', email);
    await selfHealingPage.fill('[data-test-id="SignInPassword"]', password);
    
    // Click the login button
    await selfHealingPage.click(':text("Log in with email")');
    
    // Wait for login to complete
    await selfHealingPage.waitForSelector('div[class*="FilesTable"]', { timeout: 15000 });
    performanceTracker.endOperation();
    
    // Verify login was successful
    const isLoggedIn = await selfHealingPage.isVisible('div[class*="FilesTable"]');
    expect(isLoggedIn).toBe(true);
    
    // Track performance metrics
    await selfHealingController.trackPerformance(performanceTracker.getMetrics());
    
    // End the test run
    await selfHealingController.endTest({
      success: true
    });
  } catch (error) {
    // End the test run with failure
    await selfHealingController.endTest({
      success: false,
      error
    });
    
    throw error;
  } finally {
    // Clean up resources
    await mcpController.cleanup();
    await selfHealingController.cleanup();
  }
});
```

## Step 5: Add Selector Optimization

Let's optimize the selectors using the MCP controller:

```javascript
const { test, expect } = require('@playwright/test');
const { createMcpController, createSelfHealingController, test: testUtils } = require('@qawolf/test-framework');
const { config } = require('@qawolf/shared-utils');

test('login test', async ({ page }) => {
  // Create an MCP controller
  const mcpController = createMcpController();
  
  // Create a self-healing controller
  const selfHealingController = createSelfHealingController();
  
  try {
    // Start the test run
    await selfHealingController.startTest({
      testId: 'login-test',
      testName: 'Login Test'
    });
    
    // Create a self-healing page
    const selfHealingPage = selfHealingController.createPage(page);
    
    // Create a performance tracker
    const performanceTracker = new testUtils.PerformanceTracker();
    
    // Get credentials from environment variables
    const email = config.getEnv('QAWOLF_EMAIL', '<EMAIL>');
    const password = config.getEnv('QAWOLF_PASSWORD', 'vhc!tGK289IS&');
    const baseUrl = config.getEnv('QAWOLF_BASE_URL', 'https://app.lidostaging.com');
    
    // Navigate to the app
    performanceTracker.startOperation('navigation_to_app');
    await selfHealingPage.goto(baseUrl);
    performanceTracker.endOperation();
    
    // Optimize selectors
    performanceTracker.startOperation('selector_optimization');
    const selectors = await mcpController.optimizeSelectors([
      '[data-test-id="SignInEmail"]',
      '[data-test-id="SignInPassword"]',
      ':text("Log in with email")'
    ]);
    performanceTracker.endOperation();
    
    // Fill in login form
    performanceTracker.startOperation('login');
    await selfHealingPage.fill(selectors.selectors[0].optimized, email);
    await selfHealingPage.fill(selectors.selectors[1].optimized, password);
    
    // Click the login button
    await selfHealingPage.click(selectors.selectors[2].optimized);
    
    // Wait for login to complete
    await selfHealingPage.waitForSelector('div[class*="FilesTable"]', { timeout: 15000 });
    performanceTracker.endOperation();
    
    // Verify login was successful
    const isLoggedIn = await selfHealingPage.isVisible('div[class*="FilesTable"]');
    expect(isLoggedIn).toBe(true);
    
    // Track performance metrics
    await selfHealingController.trackPerformance(performanceTracker.getMetrics());
    
    // End the test run
    await selfHealingController.endTest({
      success: true
    });
  } catch (error) {
    // End the test run with failure
    await selfHealingController.endTest({
      success: false,
      error
    });
    
    throw error;
  } finally {
    // Clean up resources
    await mcpController.cleanup();
    await selfHealingController.cleanup();
  }
});
```

## Step 6: Add Screenshot Capture

Let's add screenshot capture to help with debugging:

```javascript
const { test, expect } = require('@playwright/test');
const { createMcpController, createSelfHealingController, test: testUtils } = require('@qawolf/test-framework');
const { config, screenshot } = require('@qawolf/shared-utils');

test('login test', async ({ page }) => {
  // Create an MCP controller
  const mcpController = createMcpController();
  
  // Create a self-healing controller
  const selfHealingController = createSelfHealingController();
  
  try {
    // Start the test run
    await selfHealingController.startTest({
      testId: 'login-test',
      testName: 'Login Test'
    });
    
    // Create a self-healing page
    const selfHealingPage = selfHealingController.createPage(page);
    
    // Create a performance tracker
    const performanceTracker = new testUtils.PerformanceTracker();
    
    // Get credentials from environment variables
    const email = config.getEnv('QAWOLF_EMAIL', '<EMAIL>');
    const password = config.getEnv('QAWOLF_PASSWORD', 'vhc!tGK289IS&');
    const baseUrl = config.getEnv('QAWOLF_BASE_URL', 'https://app.lidostaging.com');
    
    // Navigate to the app
    performanceTracker.startOperation('navigation_to_app');
    await selfHealingPage.goto(baseUrl);
    performanceTracker.endOperation();
    
    // Take a screenshot
    await screenshot.takeScreenshot(selfHealingPage, {
      testName: 'login-test',
      action: 'navigation',
      fullPage: true
    });
    
    // Optimize selectors
    performanceTracker.startOperation('selector_optimization');
    const selectors = await mcpController.optimizeSelectors([
      '[data-test-id="SignInEmail"]',
      '[data-test-id="SignInPassword"]',
      ':text("Log in with email")'
    ]);
    performanceTracker.endOperation();
    
    // Fill in login form
    performanceTracker.startOperation('login');
    await selfHealingPage.fill(selectors.selectors[0].optimized, email);
    await selfHealingPage.fill(selectors.selectors[1].optimized, password);
    
    // Take a screenshot
    await screenshot.takeScreenshot(selfHealingPage, {
      testName: 'login-test',
      action: 'form-filled',
      fullPage: true
    });
    
    // Click the login button
    await selfHealingPage.click(selectors.selectors[2].optimized);
    
    // Wait for login to complete
    await selfHealingPage.waitForSelector('div[class*="FilesTable"]', { timeout: 15000 });
    performanceTracker.endOperation();
    
    // Take a screenshot
    await screenshot.takeScreenshot(selfHealingPage, {
      testName: 'login-test',
      action: 'logged-in',
      fullPage: true
    });
    
    // Verify login was successful
    const isLoggedIn = await selfHealingPage.isVisible('div[class*="FilesTable"]');
    expect(isLoggedIn).toBe(true);
    
    // Track performance metrics
    await selfHealingController.trackPerformance(performanceTracker.getMetrics());
    
    // End the test run
    await selfHealingController.endTest({
      success: true
    });
  } catch (error) {
    // Take a screenshot on error
    await screenshot.takeScreenshot(page, {
      testName: 'login-test',
      action: 'error',
      fullPage: true
    });
    
    // End the test run with failure
    await selfHealingController.endTest({
      success: false,
      error
    });
    
    throw error;
  } finally {
    // Clean up resources
    await mcpController.cleanup();
    await selfHealingController.cleanup();
  }
});
```

## Step 7: Run the Test

Now, let's run the test:

```bash
npx playwright test tests/login-test.spec.js
```

You should see output indicating that the test passed.

## Step 8: View the Test Report

You can view the test report by running:

```bash
npx playwright show-report
```

This will open the Playwright HTML report in your browser, showing detailed information about your test run.

## Step 9: Generate a Self-Healing Report

You can generate a self-healing report to see how the self-healing automation performed:

```javascript
// Generate a report
const report = await selfHealingController.generateReport({
  format: 'json'
});

console.log(report);
```

Add this code before the `endTest` call in your test.

## Conclusion

Congratulations! You've created a login test using the QA Wolf testing framework. This test:

1. Navigates to the app
2. Optimizes selectors using MCP
3. Fills in the login form
4. Clicks the login button
5. Verifies that login was successful
6. Tracks performance metrics
7. Takes screenshots for debugging
8. Uses self-healing automation

You can use this as a starting point for more complex tests. For more information, see:

- [MCP Integration Guide](../guides/mcp-integration.md)
- [Self-Healing Automation Guide](../guides/self-healing.md)
- [Performance Optimization Guide](../guides/performance-optimization.md)