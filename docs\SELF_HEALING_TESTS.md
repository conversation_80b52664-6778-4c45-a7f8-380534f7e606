# Self-Healing Tests

This document describes our implementation of self-healing tests, which automatically adapt to changes in the application UI to reduce test flakiness.

## Overview

Self-healing tests are designed to automatically recover from common test failures, such as:

- Changed selectors
- UI element relocations
- Timing issues
- Dynamic content loading

Our implementation uses a combination of techniques to achieve this, including:

- Alternative selector strategies
- Intelligent waiting mechanisms
- Retry logic
- Learning from previous test runs

## Self-Healing Page Class

The core of our self-healing implementation is the `SelfHealingPage` class, which wraps <PERSON><PERSON>'s `Page` class and adds self-healing capabilities.

```javascript
class SelfHealingPage {
  constructor(page) {
    this.page = page;
    this.selectorHistory = new SelectorHistory();
    this.healingStrategies = [
      new AttributeHealingStrategy(),
      new TextContentHealingStrategy(),
      new PositionHealingStrategy(),
      new FuzzyMatchHealingStrategy()
    ];
  }

  async goto(url) {
    return this.page.goto(url);
  }

  async locator(selector) {
    try {
      // Try the original selector first
      const element = this.page.locator(selector);
      const count = await element.count();
      
      if (count > 0) {
        // Selector worked, return the element
        return element;
      }
      
      // Selector didn't work, try to heal it
      return await this.healSelector(selector);
    } catch (error) {
      console.error(`Error locating element with selector "${selector}":`, error);
      
      // Try to heal the selector
      return await this.healSelector(selector);
    }
  }

  async healSelector(selector) {
    console.log(`Attempting to heal selector: ${selector}`);
    
    // Check if we have a known alternative for this selector
    const alternativeSelector = this.selectorHistory.getAlternative(selector);
    
    if (alternativeSelector) {
      console.log(`Using known alternative selector: ${alternativeSelector}`);
      
      const element = this.page.locator(alternativeSelector);
      const count = await element.count();
      
      if (count > 0) {
        // Alternative selector worked, return the element
        return element;
      }
    }
    
    // Try each healing strategy
    for (const strategy of this.healingStrategies) {
      try {
        const healedSelector = await strategy.heal(this.page, selector);
        
        if (healedSelector) {
          console.log(`Healed selector using ${strategy.constructor.name}: ${healedSelector}`);
          
          // Save the healed selector for future use
          this.selectorHistory.addAlternative(selector, healedSelector);
          
          return this.page.locator(healedSelector);
        }
      } catch (error) {
        console.error(`Error healing selector with ${strategy.constructor.name}:`, error);
      }
    }
    
    // If all healing strategies failed, return the original selector
    // This will likely fail, but it's better than returning null
    console.error(`Failed to heal selector: ${selector}`);
    return this.page.locator(selector);
  }
}
```

## Healing Strategies

We implement several healing strategies to recover from different types of selector failures:

### 1. Attribute Healing Strategy

This strategy tries to find elements with similar attributes to the original selector.

```javascript
class AttributeHealingStrategy {
  async heal(page, selector) {
    // Parse the selector to extract attributes
    const attributes = this.parseAttributes(selector);
    
    if (Object.keys(attributes).length === 0) {
      return null;
    }
    
    // Try different combinations of attributes
    for (let i = Object.keys(attributes).length; i > 0; i--) {
      const attributeCombinations = this.getCombinations(Object.entries(attributes), i);
      
      for (const combination of attributeCombinations) {
        const newSelector = this.buildSelector(combination);
        
        const element = page.locator(newSelector);
        const count = await element.count();
        
        if (count > 0) {
          return newSelector;
        }
      }
    }
    
    return null;
  }
  
  parseAttributes(selector) {
    // Implementation details...
  }
  
  getCombinations(array, size) {
    // Implementation details...
  }
  
  buildSelector(attributes) {
    // Implementation details...
  }
}
```

### 2. Text Content Healing Strategy

This strategy tries to find elements with the same text content.

```javascript
class TextContentHealingStrategy {
  async heal(page, selector) {
    try {
      // Try to get the text content of the element using a similar selector
      const similarSelector = this.getSimilarSelector(selector);
      
      if (!similarSelector) {
        return null;
      }
      
      const elements = await page.$$(similarSelector);
      
      if (elements.length === 0) {
        return null;
      }
      
      // Get the text content of the first element
      const textContent = await elements[0].textContent();
      
      if (!textContent || textContent.trim() === '') {
        return null;
      }
      
      // Try to find an element with the same text content
      return `:text("${textContent.trim()}")`;
    } catch (error) {
      console.error('Error in TextContentHealingStrategy:', error);
      return null;
    }
  }
  
  getSimilarSelector(selector) {
    // Implementation details...
  }
}
```

### 3. Position Healing Strategy

This strategy tries to find elements in a similar position on the page.

```javascript
class PositionHealingStrategy {
  async heal(page, selector) {
    try {
      // Try to get the position of the element using a similar selector
      const similarSelector = this.getSimilarSelector(selector);
      
      if (!similarSelector) {
        return null;
      }
      
      const elements = await page.$$(similarSelector);
      
      if (elements.length === 0) {
        return null;
      }
      
      // Get the bounding box of the first element
      const boundingBox = await elements[0].boundingBox();
      
      if (!boundingBox) {
        return null;
      }
      
      // Try to find an element at a similar position
      const elementsAtPosition = await page.$$eval('*', (els, x, y) => {
        return els
          .filter(el => {
            const rect = el.getBoundingClientRect();
            return (
              rect.left <= x && x <= rect.right &&
              rect.top <= y && y <= rect.bottom
            );
          })
          .map(el => {
            return {
              tagName: el.tagName,
              id: el.id,
              className: el.className,
              textContent: el.textContent
            };
          });
      }, boundingBox.x + boundingBox.width / 2, boundingBox.y + boundingBox.height / 2);
      
      if (elementsAtPosition.length === 0) {
        return null;
      }
      
      // Build a selector for the first element at the position
      return this.buildSelector(elementsAtPosition[0]);
    } catch (error) {
      console.error('Error in PositionHealingStrategy:', error);
      return null;
    }
  }
  
  getSimilarSelector(selector) {
    // Implementation details...
  }
  
  buildSelector(element) {
    // Implementation details...
  }
}
```

### 4. Fuzzy Match Healing Strategy

This strategy uses fuzzy matching to find elements with similar selectors.

```javascript
class FuzzyMatchHealingStrategy {
  async heal(page, selector) {
    try {
      // Get all elements on the page
      const allElements = await page.$$('*');
      
      // Calculate similarity scores for each element
      const scores = [];
      
      for (let i = 0; i < allElements.length; i++) {
        const element = allElements[i];
        
        // Get element attributes
        const attributes = await element.evaluate(el => {
          const attrs = {};
          for (const attr of el.attributes) {
            attrs[attr.name] = attr.value;
          }
          return attrs;
        });
        
        // Calculate similarity score
        const score = this.calculateSimilarityScore(selector, attributes);
        
        scores.push({
          element,
          score,
          attributes
        });
      }
      
      // Sort by score (descending)
      scores.sort((a, b) => b.score - a.score);
      
      // If the best match has a score above threshold, use it
      if (scores.length > 0 && scores[0].score > 0.7) {
        return this.buildSelector(scores[0].attributes);
      }
      
      return null;
    } catch (error) {
      console.error('Error in FuzzyMatchHealingStrategy:', error);
      return null;
    }
  }
  
  calculateSimilarityScore(selector, attributes) {
    // Implementation details...
  }
  
  buildSelector(attributes) {
    // Implementation details...
  }
}
```

## Selector History

The `SelectorHistory` class keeps track of selector alternatives that have worked in the past.

```javascript
class SelectorHistory {
  constructor() {
    this.alternatives = {};
    this.loadHistory();
  }
  
  loadHistory() {
    try {
      const historyPath = path.join(process.cwd(), 'selector-history.json');
      
      if (fs.existsSync(historyPath)) {
        const historyData = fs.readFileSync(historyPath, 'utf8');
        this.alternatives = JSON.parse(historyData);
      }
    } catch (error) {
      console.error('Error loading selector history:', error);
    }
  }
  
  saveHistory() {
    try {
      const historyPath = path.join(process.cwd(), 'selector-history.json');
      fs.writeFileSync(historyPath, JSON.stringify(this.alternatives, null, 2));
    } catch (error) {
      console.error('Error saving selector history:', error);
    }
  }
  
  getAlternative(selector) {
    return this.alternatives[selector];
  }
  
  addAlternative(selector, alternative) {
    this.alternatives[selector] = alternative;
    this.saveHistory();
  }
}
```

## Using Self-Healing Tests

To use self-healing tests in your test files, wrap the Playwright `Page` object with the `SelfHealingPage` class:

```javascript
const { test, expect } = require('@playwright/test');
const { SelfHealingPage } = require('../utils/self-healing');

test('should heal CSS selectors', async ({ page }) => {
  // Create a self-healing page
  const selfHealingPage = new SelfHealingPage(page);
  
  // Navigate to the login page
  await selfHealingPage.goto('https://app.lidostaging.com');
  
  // Try to find an element with an invalid CSS selector
  // The self-healing page should try to heal the selector
  const element = await selfHealingPage.locator('[data-testid="SignInEmail"]');
  
  // Verify the selector was healed and the element was found
  const count = await element.count();
  expect(count).toBeGreaterThan(0);
});
```

## Benefits

Self-healing tests provide several benefits:

1. **Reduced Flakiness**: Tests are more resilient to UI changes
2. **Lower Maintenance**: Less need to update selectors when the UI changes
3. **Improved Reliability**: Tests continue to work even when the application evolves
4. **Better Developer Experience**: Less time spent debugging failing tests

## Limitations

While self-healing tests are powerful, they have some limitations:

1. **Performance Impact**: Healing selectors can slow down test execution
2. **False Positives**: Healed selectors might find the wrong element
3. **Complexity**: Self-healing logic adds complexity to the test framework
4. **Learning Curve**: Developers need to understand how self-healing works

## Future Improvements

We plan to improve our self-healing tests in the following ways:

1. **Machine Learning**: Use ML to improve selector healing accuracy
2. **Performance Optimization**: Reduce the performance impact of healing
3. **Better Reporting**: Provide more detailed reports of healed selectors
4. **Integration with CI/CD**: Automatically update selectors in the codebase
