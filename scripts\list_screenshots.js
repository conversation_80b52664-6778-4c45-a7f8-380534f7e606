/**
 * List Screenshots
 * 
 * This script lists all screenshots in the screenshots directory.
 * 
 * Usage:
 * node scripts/list_screenshots.js [--date=YYYY-MM-DD] [--run=run_id] [--test=test_name]
 * 
 * @module list_screenshots
 * <AUTHOR> Wolf Team
 * @date 2025-01-01
 */

const fs = require('fs');
const path = require('path');
const { format } = require('date-fns');
const { SCREENSHOTS_BASE_DIR } = require('../src/utils/screenshot-utils');

// Parse command line arguments
const args = process.argv.slice(2);
let date = null;
let runId = null;
let testName = null;

args.forEach(arg => {
  if (arg.startsWith('--date=')) {
    date = arg.substring('--date='.length);
  } else if (arg.startsWith('--run=')) {
    runId = arg.substring('--run='.length);
  } else if (arg.startsWith('--test=')) {
    testName = arg.substring('--test='.length);
  }
});

// If no date is specified, use today's date
if (!date) {
  date = format(new Date(), 'yyyy-MM-dd');
}

/**
 * List all screenshots
 * 
 * @param {string} date - Date in YYYY-MM-DD format
 * @param {string} [runId] - Run ID
 * @param {string} [testName] - Test name
 */
function listScreenshots(date, runId, testName) {
  try {
    console.log(`Listing screenshots for date ${date}...`);
    
    // Check if the date directory exists
    const dateDir = path.join(SCREENSHOTS_BASE_DIR, date);
    if (!fs.existsSync(dateDir)) {
      console.error(`Error: No screenshots found for date ${date}`);
      process.exit(1);
    }
    
    // Get all run directories for this date
    let runDirs = fs.readdirSync(dateDir)
      .filter(dir => dir.startsWith('run-'))
      .map(dir => path.join(dateDir, dir));
    
    if (runDirs.length === 0) {
      console.error(`Error: No runs found for date ${date}`);
      process.exit(1);
    }
    
    // Filter by run ID if specified
    if (runId) {
      runDirs = runDirs.filter(dir => path.basename(dir) === runId);
      
      if (runDirs.length === 0) {
        console.error(`Error: No runs found for date ${date} with run ID ${runId}`);
        process.exit(1);
      }
    }
    
    console.log(`Found ${runDirs.length} runs for date ${date}`);
    
    // Get all test directories across all runs
    let testDirs = [];
    runDirs.forEach(runDir => {
      const dirs = fs.readdirSync(runDir)
        .filter(dir => fs.statSync(path.join(runDir, dir)).isDirectory())
        .map(dir => ({
          path: path.join(runDir, dir),
          name: dir,
          runId: path.basename(runDir)
        }));
      
      testDirs.push(...dirs);
    });
    
    if (testDirs.length === 0) {
      console.error(`Error: No tests found for date ${date}`);
      process.exit(1);
    }
    
    // Filter by test name if specified
    if (testName) {
      testDirs = testDirs.filter(dir => dir.name === testName);
      
      if (testDirs.length === 0) {
        console.error(`Error: No tests found for date ${date} with name ${testName}`);
        process.exit(1);
      }
    }
    
    console.log(`Found ${testDirs.length} test directories`);
    
    // List all screenshots
    let totalScreenshots = 0;
    testDirs.forEach(testDir => {
      const screenshots = fs.readdirSync(testDir.path)
        .filter(file => file.endsWith('.png'))
        .map(file => path.join(testDir.path, file));
      
      totalScreenshots += screenshots.length;
      
      console.log(`\nTest: ${testDir.name}`);
      console.log(`Run: ${testDir.runId}`);
      console.log(`Screenshots: ${screenshots.length}`);
      
      if (screenshots.length > 0) {
        console.log('Files:');
        screenshots.forEach(screenshot => {
          console.log(`  ${path.basename(screenshot)}`);
        });
      }
    });
    
    console.log(`\nTotal screenshots: ${totalScreenshots}`);
  } catch (error) {
    console.error('Error listing screenshots:', error);
    process.exit(1);
  }
}

// List all screenshots
listScreenshots(date, runId, testName);
