{"timestamp": "2025-05-19T06:37:41.516Z", "summary": {"totalFiles": 23, "passedFiles": 0, "failedFiles": 23, "errorFiles": 0, "overallScore": 0.31891304347826077}, "results": [{"filePath": "tests\\e2e\\file-operations.e2e.spec.js", "results": {"overallScore": 0.09000000000000001, "testResults": [{"testName": "should create a new file", "score": 0.09, "aspects": {"arrangementClarity": 0.3, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should delete a file", "score": 0.09, "aspects": {"arrangementClarity": 0.3, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should create and delete multiple files", "score": 0.09, "aspects": {"arrangementClarity": 0.3, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}], "recommendations": [{"testName": "should create a new file", "score": 0.09, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should delete a file", "score": 0.09, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should create and delete multiple files", "score": 0.09, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}], "passesThreshold": false}}, {"filePath": "tests\\e2e\\login.e2e.spec.js", "results": {"overallScore": 0.09, "testResults": [{"testName": "should login with valid credentials", "score": 0.09, "aspects": {"arrangementClarity": 0.3, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should show error with invalid credentials", "score": 0.09, "aspects": {"arrangementClarity": 0.3, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should show error with empty credentials", "score": 0.09, "aspects": {"arrangementClarity": 0.3, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should login and logout successfully", "score": 0.09, "aspects": {"arrangementClarity": 0.3, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}], "recommendations": [{"testName": "should login with valid credentials", "score": 0.09, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should show error with invalid credentials", "score": 0.09, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should show error with empty credentials", "score": 0.09, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should login and logout successfully", "score": 0.09, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}], "passesThreshold": false}}, {"filePath": "tests\\integration\\cross-component.integration.spec.js", "results": {"overallScore": 0.09, "testResults": [{"testName": "should integrate all components in an end-to-end scenario", "score": 0.09, "aspects": {"arrangementClarity": 0.3, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should integrate MCP and self-healing controllers", "score": 0.09, "aspects": {"arrangementClarity": 0.3, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}], "recommendations": [{"testName": "should integrate all components in an end-to-end scenario", "score": 0.09, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should integrate MCP and self-healing controllers", "score": 0.09, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}], "passesThreshold": false}}, {"filePath": "tests\\integration\\mcp-optimizer.integration.spec.js", "results": {"overallScore": 0.09, "testResults": [{"testName": "should create and initialize MCP controller", "score": 0.09, "aspects": {"arrangementClarity": 0.3, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should optimize selectors", "score": 0.09, "aspects": {"arrangementClarity": 0.3, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should select appropriate MCP tool for a task", "score": 0.09, "aspects": {"arrangementClarity": 0.3, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should analyze screenshots", "score": 0.09, "aspects": {"arrangementClarity": 0.3, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should generate reports", "score": 0.09, "aspects": {"arrangementClarity": 0.3, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}], "recommendations": [{"testName": "should create and initialize MCP controller", "score": 0.09, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should optimize selectors", "score": 0.09, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should select appropriate MCP tool for a task", "score": 0.09, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should analyze screenshots", "score": 0.09, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should generate reports", "score": 0.09, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}], "passesThreshold": false}}, {"filePath": "tests\\integration\\self-healing.integration.spec.js", "results": {"overallScore": 0.09, "testResults": [{"testName": "should create and initialize self-healing controller", "score": 0.09, "aspects": {"arrangementClarity": 0.3, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should create a self-healing page", "score": 0.09, "aspects": {"arrangementClarity": 0.3, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should track test runs", "score": 0.09, "aspects": {"arrangementClarity": 0.3, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should track performance metrics", "score": 0.09, "aspects": {"arrangementClarity": 0.3, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should generate reports", "score": 0.09, "aspects": {"arrangementClarity": 0.3, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}], "recommendations": [{"testName": "should create and initialize self-healing controller", "score": 0.09, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should create a self-healing page", "score": 0.09, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should track test runs", "score": 0.09, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should track performance metrics", "score": 0.09, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should generate reports", "score": 0.09, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}], "passesThreshold": false}}, {"filePath": "tests\\integration\\shared-utils.integration.spec.js", "results": {"overallScore": 0.09, "testResults": [{"testName": "should integrate screenshot utilities correctly", "score": 0.09, "aspects": {"arrangementClarity": 0.3, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should integrate error screenshot utilities correctly", "score": 0.09, "aspects": {"arrangementClarity": 0.3, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should integrate configuration utilities correctly", "score": 0.09, "aspects": {"arrangementClarity": 0.3, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should integrate self-healing with shared-utils correctly", "score": 0.09, "aspects": {"arrangementClarity": 0.3, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}], "recommendations": [{"testName": "should integrate screenshot utilities correctly", "score": 0.09, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should integrate error screenshot utilities correctly", "score": 0.09, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should integrate configuration utilities correctly", "score": 0.09, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should integrate self-healing with shared-utils correctly", "score": 0.09, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}], "passesThreshold": false}}, {"filePath": "tests\\performance\\execution-time.spec.js", "results": {"overallScore": 0.09000000000000001, "testResults": [{"testName": "should measure login performance", "score": 0.09, "aspects": {"arrangementClarity": 0.3, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should measure file creation performance with and without MCP optimization", "score": 0.09, "aspects": {"arrangementClarity": 0.3, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should measure self-healing performance", "score": 0.09, "aspects": {"arrangementClarity": 0.3, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}], "recommendations": [{"testName": "should measure login performance", "score": 0.09, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should measure file creation performance with and without MCP optimization", "score": 0.09, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should measure self-healing performance", "score": 0.09, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}], "passesThreshold": false}}, {"filePath": "tests\\performance\\token-usage.spec.js", "results": {"overallScore": 0.09000000000000001, "testResults": [{"testName": "should measure selector optimization token usage", "score": 0.09, "aspects": {"arrangementClarity": 0.3, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should measure screenshot analysis token usage", "score": 0.09, "aspects": {"arrangementClarity": 0.3, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should measure tool selection token usage", "score": 0.09, "aspects": {"arrangementClarity": 0.3, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}], "recommendations": [{"testName": "should measure selector optimization token usage", "score": 0.09, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should measure screenshot analysis token usage", "score": 0.09, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should measure tool selection token usage", "score": 0.09, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}], "passesThreshold": false}}, {"filePath": "tests\\playwright-mcp\\examples\\create_delete.spec.js", "results": {"overallScore": 0.3, "testResults": [{"testName": "should create and delete a file", "score": 0.3, "aspects": {"arrangementClarity": 0.7, "actionFocus": 0.3, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add screenshot capture for better debugging and documentation."]}], "recommendations": [{"testName": "should create and delete a file", "score": 0.3, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add screenshot capture for better debugging and documentation."]}], "passesThreshold": false}}, {"filePath": "tests\\playwright-mcp\\examples\\login.spec.js", "results": {"overallScore": 0.675, "testResults": [{"testName": "should login with valid credentials", "score": 0.72, "aspects": {"arrangementClarity": 0.7, "actionFocus": 0.7, "assertionCompleteness": 1, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should show error with invalid credentials", "score": 0.63, "aspects": {"arrangementClarity": 0.7, "actionFocus": 0.7, "assertionCompleteness": 0.7, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}], "recommendations": [{"testName": "should login with valid credentials", "score": 0.72, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should show error with invalid credentials", "score": 0.63, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}], "passesThreshold": false}}, {"filePath": "tests\\playwright-mcp\\generated\\login_create_delete.spec.js", "results": {"overallScore": 0.72, "testResults": [{"testName": "should login, create a file, and delete it", "score": 0.72, "aspects": {"arrangementClarity": 0.7, "actionFocus": 0.7, "assertionCompleteness": 1, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}], "recommendations": [{"testName": "should login, create a file, and delete it", "score": 0.72, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}], "passesThreshold": false}}, {"filePath": "tests\\playwright-mcp\\generated\\login_create_delete_debug.spec.js", "results": {"overallScore": 0.72, "testResults": [{"testName": "should login, create a file, and delete it", "score": 0.72, "aspects": {"arrangementClarity": 0.7, "actionFocus": 0.7, "assertionCompleteness": 1, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}], "recommendations": [{"testName": "should login, create a file, and delete it", "score": 0.72, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}], "passesThreshold": false}}, {"filePath": "tests\\playwright-mcp\\generated\\login_create_delete_enhanced.spec.js", "results": {"overallScore": 0.72, "testResults": [{"testName": "should login, create a file, and delete it", "score": 0.72, "aspects": {"arrangementClarity": 0.7, "actionFocus": 0.7, "assertionCompleteness": 1, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}], "recommendations": [{"testName": "should login, create a file, and delete it", "score": 0.72, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}], "passesThreshold": false}}, {"filePath": "tests\\playwright-mcp\\generated\\login_create_delete_enhanced_prod.spec.js", "results": {"overallScore": 0.72, "testResults": [{"testName": "should login, create a file, and delete it", "score": 0.72, "aspects": {"arrangementClarity": 0.7, "actionFocus": 0.7, "assertionCompleteness": 1, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}], "recommendations": [{"testName": "should login, create a file, and delete it", "score": 0.72, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}], "passesThreshold": false}}, {"filePath": "tests\\playwright-mcp\\generated\\login_create_file_production.spec.js", "results": {"overallScore": 0.5549999999999999, "testResults": [{"testName": "should login, create a file, and delete it", "score": 0.5549999999999999, "aspects": {"arrangementClarity": 0.7, "actionFocus": 0.7, "assertionCompleteness": 0.44999999999999996, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}], "recommendations": [{"testName": "should login, create a file, and delete it", "score": 0.5549999999999999, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}], "passesThreshold": false}}, {"filePath": "tests\\self-healing\\recovery-strategies.spec.js", "results": {"overallScore": 0.09000000000000001, "testResults": [{"testName": "should recover using retry strategy", "score": 0.09, "aspects": {"arrangementClarity": 0.3, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should recover using wait strategy", "score": 0.09, "aspects": {"arrangementClarity": 0.3, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should recover using selector strategy", "score": 0.09, "aspects": {"arrangementClarity": 0.3, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}], "recommendations": [{"testName": "should recover using retry strategy", "score": 0.09, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should recover using wait strategy", "score": 0.09, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should recover using selector strategy", "score": 0.09, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}], "passesThreshold": false}}, {"filePath": "tests\\self-healing\\selector-healing.spec.js", "results": {"overallScore": 0.09000000000000001, "testResults": [{"testName": "should heal CSS selectors", "score": 0.09, "aspects": {"arrangementClarity": 0.3, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should heal XPath selectors", "score": 0.09, "aspects": {"arrangementClarity": 0.3, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should heal text-based selectors", "score": 0.09, "aspects": {"arrangementClarity": 0.3, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}], "recommendations": [{"testName": "should heal CSS selectors", "score": 0.09, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should heal XPath selectors", "score": 0.09, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}, {"testName": "should heal text-based selectors", "score": 0.09, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}], "passesThreshold": false}}, {"filePath": "tests\\simple-test.spec.js", "results": {"overallScore": 0.675, "testResults": [{"testName": "should pass", "score": 0.675, "aspects": {"arrangementClarity": 0.7, "actionFocus": 0.7, "assertionCompleteness": 0.85, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}], "recommendations": [{"testName": "should pass", "score": 0.675, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add proper error handling with try/catch blocks.", "Add screenshot capture for better debugging and documentation."]}], "passesThreshold": false}}, {"filePath": "tests\\simplified-file-operations.spec.js", "results": {"overallScore": 0.21, "testResults": [{"testName": "should create a new file", "score": 0.21, "aspects": {"arrangementClarity": 0.7, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs."]}], "recommendations": [{"testName": "should create a new file", "score": 0.21, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs."]}], "passesThreshold": false}}, {"filePath": "tests\\simplified-integration.spec.js", "results": {"overallScore": 0.42, "testResults": [{"testName": "should take screenshots correctly", "score": 0.42, "aspects": {"arrangementClarity": 0.7, "actionFocus": 0.7, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs."]}, {"testName": "should take error screenshots correctly", "score": 0.42, "aspects": {"arrangementClarity": 0.7, "actionFocus": 0.7, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add screenshot capture for better debugging and documentation."]}], "recommendations": [{"testName": "should take screenshots correctly", "score": 0.42, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs."]}, {"testName": "should take error screenshots correctly", "score": 0.42, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.", "Add screenshot capture for better debugging and documentation."]}], "passesThreshold": false}}, {"filePath": "tests\\simplified-login.spec.js", "results": {"overallScore": 0.21, "testResults": [{"testName": "should login with valid credentials", "score": 0.21, "aspects": {"arrangementClarity": 0.7, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs."]}, {"testName": "should show error with invalid credentials", "score": 0.21, "aspects": {"arrangementClarity": 0.7, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs."]}], "recommendations": [{"testName": "should login with valid credentials", "score": 0.21, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs."]}, {"testName": "should show error with invalid credentials", "score": 0.21, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs."]}], "passesThreshold": false}}, {"filePath": "tests\\simplified-performance.spec.js", "results": {"overallScore": 0.21, "testResults": [{"testName": "should measure login performance", "score": 0.21, "aspects": {"arrangementClarity": 0.7, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs."]}, {"testName": "should measure selector optimization performance", "score": 0.21, "aspects": {"arrangementClarity": 0.7, "actionFocus": 0, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs."]}], "recommendations": [{"testName": "should measure login performance", "score": 0.21, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs."]}, {"testName": "should measure selector optimization performance", "score": 0.21, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs."]}], "passesThreshold": false}}, {"filePath": "tests\\simplified-self-healing.spec.js", "results": {"overallScore": 0.3, "testResults": [{"testName": "should heal CSS selectors", "score": 0.3, "aspects": {"arrangementClarity": 0.7, "actionFocus": 0.3, "assertionCompleteness": 0, "documentation": 0}, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs."]}], "recommendations": [{"testName": "should heal CSS selectors", "score": 0.3, "recommendations": ["Improve arrangement clarity by adding a clear \"// Arrange\" comment and ensuring proper setup code.", "Improve action focus by adding a clear \"// Act\" comment and ensuring the action is focused and separated from arrangement and assertion.", "Improve assertion completeness by adding a clear \"// Assert\" comment and ensuring comprehensive verification of expected outcomes.", "Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs."]}], "passesThreshold": false}}]}