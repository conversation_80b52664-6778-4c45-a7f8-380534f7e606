{"timestamp": "2025-05-19T06:24:02.431Z", "simplifiedTests": {"passed": 3, "failed": 3, "skipped": 0, "total": 6, "tests": [{"name": "tests/simple-test.spec.js", "passed": true, "output": "\nRunning 1 test using 1 worker\n\n  ok 1 tests\\simple-test.spec.js:16:3 › Simple Test › should pass (619ms)\n\n  1 passed (3.5s)\n"}, {"name": "tests/simplified-integration.spec.js", "passed": false, "output": "\nRunning 2 tests using 1 worker\n\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\screenshot-test\\2025-05-19T06-20-12-134Z_test-action.png\n  ok 1 tests\\simplified-integration.spec.js:71:3 › Simplified Integration Test › should take screenshots correctly (443ms)\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\error-screenshot-test\\2025-05-19T06-20-12-513Z_error-Error-Test_error.png\n  ok 2 tests\\simplified-integration.spec.js:112:3 › Simplified Integration Test › should take error screenshots correctly (349ms)\n\n  2 passed (2.7s)\n"}, {"name": "tests/simplified-login.spec.js", "passed": false, "output": "\nRunning 2 tests using 1 worker\n\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\login-valid-credentials\\2025-05-19T06-20-17-206Z_before-login.png\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\login-valid-credentials\\2025-05-19T06-20-18-826Z_filled-login-form.png\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\login-valid-credentials\\2025-05-19T06-20-19-841Z_after-login.png\nPerformance metrics: {\n  \"executionTime\": {\n    \"start\": 1747635617204,\n    \"end\": 1747635619956,\n    \"duration\": 2752\n  },\n  \"operations\": [\n    {\n      \"name\": \"Login with Valid Credentials\",\n      \"type\": \"login\",\n      \"duration\": 2000,\n      \"timestamp\": 1747635619840\n    }\n  ]\n}\n  ok 1 tests\\simplified-login.spec.js:114:3 › Login Functionality › should login with valid credentials (2.9s)\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\login-invalid-credentials\\2025-05-19T06-20-20-009Z_before-invalid-login.png\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\login-invalid-credentials\\2025-05-19T06-20-21-370Z_filled-invalid-login-form.png\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\login-invalid-credentials\\2025-05-19T06-20-21-475Z_after-invalid-login.png\nPerformance metrics: {\n  \"executionTime\": {\n    \"start\": 1747635620008,\n    \"end\": 1747635621598,\n    \"duration\": 1590\n  },\n  \"operations\": [\n    {\n      \"name\": \"Login with Invalid Credentials\",\n      \"type\": \"login\",\n      \"duration\": 1000,\n      \"timestamp\": 1747635621474\n    }\n  ]\n}\n  ok 2 tests\\simplified-login.spec.js:191:3 › Login Functionality › should show error with invalid credentials (1.6s)\n\n  2 passed (6.4s)\n"}, {"name": "tests/simplified-file-operations.spec.js", "passed": true, "output": "\nRunning 1 test using 1 worker\n\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\create-file\\2025-05-19T06-20-29-233Z_before-create-file.png\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\create-file\\2025-05-19T06-20-32-482Z_after-create-file.png\nPerformance metrics: {\n  \"executionTime\": {\n    \"start\": 1747635628165,\n    \"end\": 1747635640740,\n    \"duration\": 12575\n  },\n  \"operations\": [\n    {\n      \"name\": \"Create File\",\n      \"type\": \"file-operation\",\n      \"duration\": 1000,\n      \"timestamp\": 1747635631401\n    }\n  ]\n}\n  ok 1 tests\\simplified-file-operations.spec.js:296:3 › File Operations › should create a new file (14.8s)\n\n  1 passed (16.7s)\n"}, {"name": "tests/simplified-self-healing.spec.js", "passed": false, "output": "\nRunning 1 test using 1 worker\n\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\heal-css-selectors\\2025-05-19T06-20-46-814Z_before-heal-css-selectors.png\nAttempting to locate element with selector: [data-testid=\"SignInEmail\"]\nElement not found with original selector: [data-testid=\"SignInEmail\"]. Attempting to heal...\nHealing attempt 1: Trying alternative selector: [data-test-id=\"SignInEmail\"]\nHealing attempt 2: Trying alternative selector: [data-test=\"SignInEmail\"]\nHealing attempt 3: Trying alternative selector: [data-test-id=\"SignInEmail\"]\nMaximum healing attempts reached (3). Giving up.\nElement not found with any alternative selectors. Returning original selector: [data-testid=\"SignInEmail\"]\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\heal-css-selectors\\2025-05-19T06-20-47-074Z_after-heal-css-selectors.png\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\heal-css-selectors\\2025-05-19T06-20-47-163Z_error-Error-__2mexpect___22m__31.png\nPerformance metrics: {\n  \"executionTime\": {\n    \"start\": 1747635645625,\n    \"end\": 1747635647239,\n    \"duration\": 1614\n  },\n  \"operations\": [\n    {\n      \"name\": \"Heal CSS Selector\",\n      \"type\": \"self-healing\",\n      \"duration\": 500,\n      \"timestamp\": 1747635647073\n    }\n  ]\n}\n  x  1 tests\\simplified-self-healing.spec.js:257:3 › Self-Healing Capabilities › should heal CSS selectors (1.8s)\n\n\n  1) tests\\simplified-self-healing.spec.js:257:3 › Self-Healing Capabilities › should heal CSS selectors \n\n    Error: \u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBeGreaterThan\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\n    Expected: > \u001b[32m0\u001b[39m\n    Received:   \u001b[31m0\u001b[39m\n\n      302 |       // ASSERT: Verify the selector was healed and the element was found\n      303 |       const count = await element.count();\n    > 304 |       expect(count).toBeGreaterThan(0);\n          |                     ^\n      305 |     } catch (error) {\n      306 |       // Take a screenshot on error\n      307 |       await screenshot.takeErrorScreenshot(page, {\n        at C:\\Users\\<USER>\\QAWolfeesojc\\tests\\simplified-self-healing.spec.js:304:21\n\n    Error Context: test-results\\tests-simplified-self-heal-25020-s-should-heal-CSS-selectors\\error-context.md\n\n  1 failed\n    tests\\simplified-self-healing.spec.js:257:3 › Self-Healing Capabilities › should heal CSS selectors \n"}, {"name": "tests/simplified-performance.spec.js", "passed": true, "output": "\nRunning 2 tests using 1 worker\n\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\login-performance\\2025-05-19T06-20-52-037Z_before-login-performance.png\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\login-performance\\2025-05-19T06-20-54-152Z_after-login-performance.png\nLogin duration: 2022ms\nPerformance metrics: {\n  \"executionTime\": {\n    \"start\": 1747635652035,\n    \"end\": 1747635654270,\n    \"duration\": 2235\n  },\n  \"operations\": [\n    {\n      \"name\": \"Login\",\n      \"type\": \"performance\",\n      \"duration\": 2022,\n      \"timestamp\": 1747635654151\n    }\n  ]\n}\n  ok 1 tests\\simplified-performance.spec.js:175:3 › Performance Measurements › should measure login performance (2.4s)\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\selector-optimization-performance\\2025-05-19T06-20-54-337Z_before-optimization.png\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\selector-optimization-performance\\2025-05-19T06-20-54-526Z_after-optimization.png\nOptimization without MCP duration: 0ms\nOptimization with MCP duration: 103ms\nPerformance metrics: {\n  \"executionTime\": {\n    \"start\": 1747635654336,\n    \"end\": 1747635654589,\n    \"duration\": 253\n  },\n  \"operations\": [\n    {\n      \"name\": \"Optimization Without MCP\",\n      \"type\": \"performance\",\n      \"duration\": 0,\n      \"timestamp\": 1747635654423\n    },\n    {\n      \"name\": \"Optimization With MCP\",\n      \"type\": \"performance\",\n      \"duration\": 103,\n      \"timestamp\": 1747635654526\n    }\n  ]\n}\n  ok 2 tests\\simplified-performance.spec.js:251:3 › Performance Measurements › should measure selector optimization performance (314ms)\n\n  2 passed (4.7s)\n"}]}, "fullTests": {"passed": 0, "failed": 10, "skipped": 0, "total": 10, "tests": [{"name": "tests/integration/shared-utils.integration.spec.js", "passed": false, "output": "\nRunning 4 tests using 1 worker\n\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\run-local-1747635660733\\screenshot-integration-test\\012101_test-action.png\nPerformance metrics: {\n  \"executionTime\": {\n    \"start\": 1747635661692,\n    \"end\": 1747635661793,\n    \"duration\": 101,\n    \"score\": 0.9932666666666666\n  },\n  \"tokenUsage\": {\n    \"total\": 0,\n    \"byOperation\": {},\n    \"score\": 1\n  },\n  \"resourceUsage\": {\n    \"cpu\": {\n      \"values\": [],\n      \"average\": 0,\n      \"score\": 1\n    },\n    \"memory\": {\n      \"values\": [],\n      \"average\": 0,\n      \"score\": 1\n    }\n  },\n  \"operations\": [\n    {\n      \"name\": \"Take Screenshot\",\n      \"type\": \"screenshot\",\n      \"duration\": 100,\n      \"timestamp\": 1747635661788\n    }\n  ],\n  \"overallScore\": 0.9983166666666666\n}\n  x  1 tests\\integration\\shared-utils.integration.spec.js:25:3 › Shared Utils Integration with Test Framework › should integrate screenshot utilities correctly (268ms)\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\run-local-1747635663306\\error-screenshot-integration-test\\012104_error_Error-Test_error.png\nPerformance metrics: {\n  \"executionTime\": {\n    \"start\": 1747635664300,\n    \"end\": 1747635664398,\n    \"duration\": 98,\n    \"score\": 0.9934666666666667\n  },\n  \"tokenUsage\": {\n    \"total\": 0,\n    \"byOperation\": {},\n    \"score\": 1\n  },\n  \"resourceUsage\": {\n    \"cpu\": {\n      \"values\": [],\n      \"average\": 0,\n      \"score\": 1\n    },\n    \"memory\": {\n      \"values\": [],\n      \"average\": 0,\n      \"score\": 1\n    }\n  },\n  \"operations\": [\n    {\n      \"name\": \"Take Error Screenshot\",\n      \"type\": \"screenshot\",\n      \"duration\": 100,\n      \"timestamp\": 1747635664393\n    }\n  ],\n  \"overallScore\": 0.9983666666666666\n}\n  x  2 tests\\integration\\shared-utils.integration.spec.js:72:3 › Shared Utils Integration with Test Framework › should integrate error screenshot utilities correctly (262ms)\nPerformance metrics: {\n  \"executionTime\": {\n    \"start\": 1747635666766,\n    \"end\": 1747635666770,\n    \"duration\": 4,\n    \"score\": 0.9997333333333334\n  },\n  \"tokenUsage\": {\n    \"total\": 0,\n    \"byOperation\": {},\n    \"score\": 1\n  },\n  \"resourceUsage\": {\n    \"cpu\": {\n      \"values\": [],\n      \"average\": 0,\n      \"score\": 1\n    },\n    \"memory\": {\n      \"values\": [],\n      \"average\": 0,\n      \"score\": 1\n    }\n  },\n  \"operations\": [\n    {\n      \"name\": \"Create MCP Controller\",\n      \"type\": \"mcp\",\n      \"duration\": 100,\n      \"timestamp\": 1747635666766\n    }\n  ],\n  \"overallScore\": 0.9999333333333333\n}\n  x  3 tests\\integration\\shared-utils.integration.spec.js:119:3 › Shared Utils Integration with Test Framework › should integrate configuration utilities correctly (191ms)\nPerformance metrics: {\n  \"executionTime\": {\n    \"start\": 1747635669283,\n    \"end\": 1747635669322,\n    \"duration\": 39,\n    \"score\": 0.9974\n  },\n  \"tokenUsage\": {\n    \"total\": 0,\n    \"byOperation\": {},\n    \"score\": 1\n  },\n  \"resourceUsage\": {\n    \"cpu\": {\n      \"values\": [],\n      \"average\": 0,\n      \"score\": 1\n    },\n    \"memory\": {\n      \"values\": [],\n      \"average\": 0,\n      \"score\": 1\n    }\n  },\n  \"operations\": [\n    {\n      \"name\": \"Create Self-Healing Controller\",\n      \"type\": \"self-healing\",\n      \"duration\": 100,\n      \"timestamp\": 1747635669283\n    }\n  ],\n  \"overallScore\": 0.99935\n}\n  x  4 tests\\integration\\shared-utils.integration.spec.js:164:3 › Shared Utils Integration with Test Framework › should integrate self-healing with shared-utils correctly (232ms)\n\n\n  1) tests\\integration\\shared-utils.integration.spec.js:25:3 › Shared Utils Integration with Test Framework › should integrate screenshot utilities correctly \n\n    TypeError: feedbackCollector.exportFeedback is not a function\n\n       at test-framework\\src\\self-healing\\index.js:90\n\n      88 |       // Learn from feedback data if auto-learning is enabled\n      89 |       if (mergedConfig.learning.enabled && mergedConfig.learning.autoOptimize) {\n    > 90 |         await learningEngine.learn(await feedbackCollector.exportFeedback());\n         |                                                            ^\n      91 |       }\n      92 |     },\n      93 |     \n        at Object.endTest (C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\self-healing\\index.js:90:60)\n        at Object.page (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:94:5)\n\n  2) tests\\integration\\shared-utils.integration.spec.js:72:3 › Shared Utils Integration with Test Framework › should integrate error screenshot utilities correctly \n\n    TypeError: feedbackCollector.exportFeedback is not a function\n\n       at test-framework\\src\\self-healing\\index.js:90\n\n      88 |       // Learn from feedback data if auto-learning is enabled\n      89 |       if (mergedConfig.learning.enabled && mergedConfig.learning.autoOptimize) {\n    > 90 |         await learningEngine.learn(await feedbackCollector.exportFeedback());\n         |                                                            ^\n      91 |       }\n      92 |     },\n      93 |     \n        at Object.endTest (C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\self-healing\\index.js:90:60)\n        at Object.page (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:94:5)\n\n  3) tests\\integration\\shared-utils.integration.spec.js:119:3 › Shared Utils Integration with Test Framework › should integrate configuration utilities correctly \n\n    TypeError: feedbackCollector.exportFeedback is not a function\n\n       at test-framework\\src\\self-healing\\index.js:90\n\n      88 |       // Learn from feedback data if auto-learning is enabled\n      89 |       if (mergedConfig.learning.enabled && mergedConfig.learning.autoOptimize) {\n    > 90 |         await learningEngine.learn(await feedbackCollector.exportFeedback());\n         |                                                            ^\n      91 |       }\n      92 |     },\n      93 |     \n        at Object.endTest (C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\self-healing\\index.js:90:60)\n        at Object.page (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:94:5)\n\n  4) tests\\integration\\shared-utils.integration.spec.js:164:3 › Shared Utils Integration with Test Framework › should integrate self-healing with shared-utils correctly \n\n    TypeError: feedbackCollector.exportFeedback is not a function\n\n       at test-framework\\src\\self-healing\\index.js:90\n\n      88 |       // Learn from feedback data if auto-learning is enabled\n      89 |       if (mergedConfig.learning.enabled && mergedConfig.learning.autoOptimize) {\n    > 90 |         await learningEngine.learn(await feedbackCollector.exportFeedback());\n         |                                                            ^\n      91 |       }\n      92 |     },\n      93 |     \n        at Object.endTest (C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\self-healing\\index.js:90:60)\n        at Object.page (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:94:5)\n\n  4 failed\n    tests\\integration\\shared-utils.integration.spec.js:25:3 › Shared Utils Integration with Test Framework › should integrate screenshot utilities correctly \n    tests\\integration\\shared-utils.integration.spec.js:72:3 › Shared Utils Integration with Test Framework › should integrate error screenshot utilities correctly \n    tests\\integration\\shared-utils.integration.spec.js:119:3 › Shared Utils Integration with Test Framework › should integrate configuration utilities correctly \n    tests\\integration\\shared-utils.integration.spec.js:164:3 › Shared Utils Integration with Test Framework › should integrate self-healing with shared-utils correctly \n"}, {"name": "tests/integration/mcp-optimizer.integration.spec.js", "passed": false, "output": "\nRunning 5 tests using 1 worker\n\nStarting Playwright MCP...\nPerformance metrics: {\n  \"executionTime\": {\n    \"start\": 1747635677167,\n    \"end\": 1747635677186,\n    \"duration\": 19,\n    \"score\": 0.9987333333333334\n  },\n  \"tokenUsage\": {\n    \"total\": 0,\n    \"byOperation\": {},\n    \"score\": 1\n  },\n  \"resourceUsage\": {\n    \"cpu\": {\n      \"values\": [],\n      \"average\": 0,\n      \"score\": 1\n    },\n    \"memory\": {\n      \"values\": [],\n      \"average\": 0,\n      \"score\": 1\n    }\n  },\n  \"operations\": [],\n  \"overallScore\": 0.9996833333333334\n}\n  x  1 tests\\integration\\mcp-optimizer.integration.spec.js:24:3 › MCP Optimizer Integration with Test Framework › should create and initialize MCP controller (231ms)\nStarting Playwright MCP...\nPerformance metrics: {\n  \"executionTime\": {\n    \"start\": 1747635679800,\n    \"end\": 1747635679820,\n    \"duration\": 20,\n    \"score\": 0.9986666666666667\n  },\n  \"tokenUsage\": {\n    \"total\": 0,\n    \"byOperation\": {},\n    \"score\": 1\n  },\n  \"resourceUsage\": {\n    \"cpu\": {\n      \"values\": [],\n      \"average\": 0,\n      \"score\": 1\n    },\n    \"memory\": {\n      \"values\": [],\n      \"average\": 0,\n      \"score\": 1\n    }\n  },\n  \"operations\": [],\n  \"overallScore\": 0.9996666666666667\n}\n  x  2 tests\\integration\\mcp-optimizer.integration.spec.js:74:3 › MCP Optimizer Integration with Test Framework › should optimize selectors (218ms)\nStarting Playwright MCP...\nPerformance metrics: {\n  \"executionTime\": {\n    \"start\": 1747635682433,\n    \"end\": 1747635682449,\n    \"duration\": 16,\n    \"score\": 0.9989333333333333\n  },\n  \"tokenUsage\": {\n    \"total\": 0,\n    \"byOperation\": {},\n    \"score\": 1\n  },\n  \"resourceUsage\": {\n    \"cpu\": {\n      \"values\": [],\n      \"average\": 0,\n      \"score\": 1\n    },\n    \"memory\": {\n      \"values\": [],\n      \"average\": 0,\n      \"score\": 1\n    }\n  },\n  \"operations\": [],\n  \"overallScore\": 0.9997333333333334\n}\n  x  3 tests\\integration\\mcp-optimizer.integration.spec.js:132:3 › MCP Optimizer Integration with Test Framework › should select appropriate MCP tool for a task (203ms)\nStarting Playwright MCP...\nPerformance metrics: {\n  \"executionTime\": {\n    \"start\": 1747635685031,\n    \"end\": 1747635685048,\n    \"duration\": 17,\n    \"score\": 0.9988666666666667\n  },\n  \"tokenUsage\": {\n    \"total\": 0,\n    \"byOperation\": {},\n    \"score\": 1\n  },\n  \"resourceUsage\": {\n    \"cpu\": {\n      \"values\": [],\n      \"average\": 0,\n      \"score\": 1\n    },\n    \"memory\": {\n      \"values\": [],\n      \"average\": 0,\n      \"score\": 1\n    }\n  },\n  \"operations\": [],\n  \"overallScore\": 0.9997166666666667\n}\n  x  4 tests\\integration\\mcp-optimizer.integration.spec.js:189:3 › MCP Optimizer Integration with Test Framework › should analyze screenshots (205ms)\nStarting Playwright MCP...\nPerformance metrics: {\n  \"executionTime\": {\n    \"start\": 1747635687722,\n    \"end\": 1747635687739,\n    \"duration\": 17,\n    \"score\": 0.9988666666666667\n  },\n  \"tokenUsage\": {\n    \"total\": 0,\n    \"byOperation\": {},\n    \"score\": 1\n  },\n  \"resourceUsage\": {\n    \"cpu\": {\n      \"values\": [],\n      \"average\": 0,\n      \"score\": 1\n    },\n    \"memory\": {\n      \"values\": [],\n      \"average\": 0,\n      \"score\": 1\n    }\n  },\n  \"operations\": [],\n  \"overallScore\": 0.9997166666666667\n}\n  x  5 tests\\integration\\mcp-optimizer.integration.spec.js:242:3 › MCP Optimizer Integration with Test Framework › should generate reports (202ms)\n\n\n  1) tests\\integration\\mcp-optimizer.integration.spec.js:24:3 › MCP Optimizer Integration with Test Framework › should create and initialize MCP controller \n\n    Error: Config file not found: null\n\n       at mcp-optimizer\\src\\tools\\playwright-mcp.js:32\n\n      30 |   // Check if config file exists\n      31 |   if (!fs.existsSync(serverOptions.configPath)) {\n    > 32 |     throw new Error(`Config file not found: ${serverOptions.configPath}`);\n         |           ^\n      33 |   }\n      34 |\n      35 |   // Build the command arguments\n        at Object.startPlaywrightMCP (C:\\Users\\<USER>\\QAWolfeesojc\\mcp-optimizer\\src\\tools\\playwright-mcp.js:32:11)\n        at C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\mcp\\controller.js:50:79\n        at C:\\Users\\<USER>\\QAWolfeesojc\\tests\\integration\\mcp-optimizer.integration.spec.js:42:7\n\n    TypeError: feedbackCollector.exportFeedback is not a function\n\n       at test-framework\\src\\self-healing\\index.js:90\n\n      88 |       // Learn from feedback data if auto-learning is enabled\n      89 |       if (mergedConfig.learning.enabled && mergedConfig.learning.autoOptimize) {\n    > 90 |         await learningEngine.learn(await feedbackCollector.exportFeedback());\n         |                                                            ^\n      91 |       }\n      92 |     },\n      93 |     \n        at Object.endTest (C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\self-healing\\index.js:90:60)\n        at Object.page (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:94:5)\n\n  2) tests\\integration\\mcp-optimizer.integration.spec.js:74:3 › MCP Optimizer Integration with Test Framework › should optimize selectors \n\n    Error: Config file not found: null\n\n       at mcp-optimizer\\src\\tools\\playwright-mcp.js:32\n\n      30 |   // Check if config file exists\n      31 |   if (!fs.existsSync(serverOptions.configPath)) {\n    > 32 |     throw new Error(`Config file not found: ${serverOptions.configPath}`);\n         |           ^\n      33 |   }\n      34 |\n      35 |   // Build the command arguments\n        at Object.startPlaywrightMCP (C:\\Users\\<USER>\\QAWolfeesojc\\mcp-optimizer\\src\\tools\\playwright-mcp.js:32:11)\n        at C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\mcp\\controller.js:50:79\n        at C:\\Users\\<USER>\\QAWolfeesojc\\tests\\integration\\mcp-optimizer.integration.spec.js:90:7\n\n    TypeError: feedbackCollector.exportFeedback is not a function\n\n       at test-framework\\src\\self-healing\\index.js:90\n\n      88 |       // Learn from feedback data if auto-learning is enabled\n      89 |       if (mergedConfig.learning.enabled && mergedConfig.learning.autoOptimize) {\n    > 90 |         await learningEngine.learn(await feedbackCollector.exportFeedback());\n         |                                                            ^\n      91 |       }\n      92 |     },\n      93 |     \n        at Object.endTest (C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\self-healing\\index.js:90:60)\n        at Object.page (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:94:5)\n\n  3) tests\\integration\\mcp-optimizer.integration.spec.js:132:3 › MCP Optimizer Integration with Test Framework › should select appropriate MCP tool for a task \n\n    Error: Config file not found: null\n\n       at mcp-optimizer\\src\\tools\\playwright-mcp.js:32\n\n      30 |   // Check if config file exists\n      31 |   if (!fs.existsSync(serverOptions.configPath)) {\n    > 32 |     throw new Error(`Config file not found: ${serverOptions.configPath}`);\n         |           ^\n      33 |   }\n      34 |\n      35 |   // Build the command arguments\n        at Object.startPlaywrightMCP (C:\\Users\\<USER>\\QAWolfeesojc\\mcp-optimizer\\src\\tools\\playwright-mcp.js:32:11)\n        at C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\mcp\\controller.js:50:79\n        at C:\\Users\\<USER>\\QAWolfeesojc\\tests\\integration\\mcp-optimizer.integration.spec.js:149:7\n\n    TypeError: feedbackCollector.exportFeedback is not a function\n\n       at test-framework\\src\\self-healing\\index.js:90\n\n      88 |       // Learn from feedback data if auto-learning is enabled\n      89 |       if (mergedConfig.learning.enabled && mergedConfig.learning.autoOptimize) {\n    > 90 |         await learningEngine.learn(await feedbackCollector.exportFeedback());\n         |                                                            ^\n      91 |       }\n      92 |     },\n      93 |     \n        at Object.endTest (C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\self-healing\\index.js:90:60)\n        at Object.page (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:94:5)\n\n  4) tests\\integration\\mcp-optimizer.integration.spec.js:189:3 › MCP Optimizer Integration with Test Framework › should analyze screenshots \n\n    Error: Config file not found: null\n\n       at mcp-optimizer\\src\\tools\\playwright-mcp.js:32\n\n      30 |   // Check if config file exists\n      31 |   if (!fs.existsSync(serverOptions.configPath)) {\n    > 32 |     throw new Error(`Config file not found: ${serverOptions.configPath}`);\n         |           ^\n      33 |   }\n      34 |\n      35 |   // Build the command arguments\n        at Object.startPlaywrightMCP (C:\\Users\\<USER>\\QAWolfeesojc\\mcp-optimizer\\src\\tools\\playwright-mcp.js:32:11)\n        at C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\mcp\\controller.js:50:79\n        at C:\\Users\\<USER>\\QAWolfeesojc\\tests\\integration\\mcp-optimizer.integration.spec.js:205:7\n\n    TypeError: feedbackCollector.exportFeedback is not a function\n\n       at test-framework\\src\\self-healing\\index.js:90\n\n      88 |       // Learn from feedback data if auto-learning is enabled\n      89 |       if (mergedConfig.learning.enabled && mergedConfig.learning.autoOptimize) {\n    > 90 |         await learningEngine.learn(await feedbackCollector.exportFeedback());\n         |                                                            ^\n      91 |       }\n      92 |     },\n      93 |     \n        at Object.endTest (C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\self-healing\\index.js:90:60)\n        at Object.page (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:94:5)\n\n  5) tests\\integration\\mcp-optimizer.integration.spec.js:242:3 › MCP Optimizer Integration with Test Framework › should generate reports \n\n    Error: Config file not found: null\n\n       at mcp-optimizer\\src\\tools\\playwright-mcp.js:32\n\n      30 |   // Check if config file exists\n      31 |   if (!fs.existsSync(serverOptions.configPath)) {\n    > 32 |     throw new Error(`Config file not found: ${serverOptions.configPath}`);\n         |           ^\n      33 |   }\n      34 |\n      35 |   // Build the command arguments\n        at Object.startPlaywrightMCP (C:\\Users\\<USER>\\QAWolfeesojc\\mcp-optimizer\\src\\tools\\playwright-mcp.js:32:11)\n        at C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\mcp\\controller.js:50:79\n        at C:\\Users\\<USER>\\QAWolfeesojc\\tests\\integration\\mcp-optimizer.integration.spec.js:261:7\n\n    TypeError: feedbackCollector.exportFeedback is not a function\n\n       at test-framework\\src\\self-healing\\index.js:90\n\n      88 |       // Learn from feedback data if auto-learning is enabled\n      89 |       if (mergedConfig.learning.enabled && mergedConfig.learning.autoOptimize) {\n    > 90 |         await learningEngine.learn(await feedbackCollector.exportFeedback());\n         |                                                            ^\n      91 |       }\n      92 |     },\n      93 |     \n        at Object.endTest (C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\self-healing\\index.js:90:60)\n        at Object.page (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:94:5)\n\n  5 failed\n    tests\\integration\\mcp-optimizer.integration.spec.js:24:3 › MCP Optimizer Integration with Test Framework › should create and initialize MCP controller \n    tests\\integration\\mcp-optimizer.integration.spec.js:74:3 › MCP Optimizer Integration with Test Framework › should optimize selectors \n    tests\\integration\\mcp-optimizer.integration.spec.js:132:3 › MCP Optimizer Integration with Test Framework › should select appropriate MCP tool for a task \n    tests\\integration\\mcp-optimizer.integration.spec.js:189:3 › MCP Optimizer Integration with Test Framework › should analyze screenshots \n    tests\\integration\\mcp-optimizer.integration.spec.js:242:3 › MCP Optimizer Integration with Test Framework › should generate reports \n"}, {"name": "tests/integration/self-healing.integration.spec.js", "passed": false, "output": "\nRunning 5 tests using 1 worker\n\nPerformance metrics: {\n  \"executionTime\": {\n    \"start\": 1747635694033,\n    \"end\": 1747635694044,\n    \"duration\": 11,\n    \"score\": 0.9992666666666666\n  },\n  \"tokenUsage\": {\n    \"total\": 0,\n    \"byOperation\": {},\n    \"score\": 1\n  },\n  \"resourceUsage\": {\n    \"cpu\": {\n      \"values\": [],\n      \"average\": 0,\n      \"score\": 1\n    },\n    \"memory\": {\n      \"values\": [],\n      \"average\": 0,\n      \"score\": 1\n    }\n  },\n  \"operations\": [\n    {\n      \"name\": \"Create and Initialize Self-Healing Controller\",\n      \"type\": \"self-healing\",\n      \"duration\": 100,\n      \"timestamp\": 1747635694035\n    }\n  ],\n  \"overallScore\": 0.9998166666666667\n}\n  x  1 tests\\integration\\self-healing.integration.spec.js:23:3 › Self-Healing Integration with Test Framework › should create and initialize self-healing controller (192ms)\nPerformance metrics: {\n  \"executionTime\": {\n    \"start\": 1747635696558,\n    \"end\": 1747635696567,\n    \"duration\": 9,\n    \"score\": 0.9994\n  },\n  \"tokenUsage\": {\n    \"total\": 0,\n    \"byOperation\": {},\n    \"score\": 1\n  },\n  \"resourceUsage\": {\n    \"cpu\": {\n      \"values\": [],\n      \"average\": 0,\n      \"score\": 1\n    },\n    \"memory\": {\n      \"values\": [],\n      \"average\": 0,\n      \"score\": 1\n    }\n  },\n  \"operations\": [\n    {\n      \"name\": \"Create Self-Healing Page\",\n      \"type\": \"self-healing\",\n      \"duration\": 50,\n      \"timestamp\": 1747635696560\n    }\n  ],\n  \"overallScore\": 0.99985\n}\n  x  2 tests\\integration\\self-healing.integration.spec.js:73:3 › Self-Healing Integration with Test Framework › should create a self-healing page (190ms)\nPerformance metrics: {\n  \"executionTime\": {\n    \"start\": 1747635699102,\n    \"end\": 1747635699107,\n    \"duration\": 5,\n    \"score\": 0.9996666666666667\n  },\n  \"tokenUsage\": {\n    \"total\": 0,\n    \"byOperation\": {},\n    \"score\": 1\n  },\n  \"resourceUsage\": {\n    \"cpu\": {\n      \"values\": [],\n      \"average\": 0,\n      \"score\": 1\n    },\n    \"memory\": {\n      \"values\": [],\n      \"average\": 0,\n      \"score\": 1\n    }\n  },\n  \"operations\": [\n    {\n      \"name\": \"Start Test Run\",\n      \"type\": \"self-healing\",\n      \"duration\": 50,\n      \"timestamp\": 1747635699104\n    }\n  ],\n  \"overallScore\": 0.9999166666666667\n}\n  x  3 tests\\integration\\self-healing.integration.spec.js:123:3 › Self-Healing Integration with Test Framework › should track test runs (205ms)\nPerformance metrics: {\n  \"executionTime\": {\n    \"start\": 1747635701764,\n    \"end\": 1747635701773,\n    \"duration\": 9,\n    \"score\": 0.9994\n  },\n  \"tokenUsage\": {\n    \"total\": 0,\n    \"byOperation\": {},\n    \"score\": 1\n  },\n  \"resourceUsage\": {\n    \"cpu\": {\n      \"values\": [],\n      \"average\": 0,\n      \"score\": 1\n    },\n    \"memory\": {\n      \"values\": [],\n      \"average\": 0,\n      \"score\": 1\n    }\n  },\n  \"operations\": [\n    {\n      \"name\": \"Track Performance Metrics\",\n      \"type\": \"self-healing\",\n      \"duration\": 50,\n      \"timestamp\": 1747635701767\n    }\n  ],\n  \"overallScore\": 0.99985\n}\n  x  4 tests\\integration\\self-healing.integration.spec.js:194:3 › Self-Healing Integration with Test Framework › should track performance metrics (213ms)\nPerformance metrics: {\n  \"executionTime\": {\n    \"start\": 1747635704341,\n    \"end\": 1747635704345,\n    \"duration\": 4,\n    \"score\": 0.9997333333333334\n  },\n  \"tokenUsage\": {\n    \"total\": 0,\n    \"byOperation\": {},\n    \"score\": 1\n  },\n  \"resourceUsage\": {\n    \"cpu\": {\n      \"values\": [],\n      \"average\": 0,\n      \"score\": 1\n    },\n    \"memory\": {\n      \"values\": [],\n      \"average\": 0,\n      \"score\": 1\n    }\n  },\n  \"operations\": [],\n  \"overallScore\": 0.9999333333333333\n}\n  x  5 tests\\integration\\self-healing.integration.spec.js:254:3 › Self-Healing Integration with Test Framework › should generate reports (208ms)\n\n\n  1) tests\\integration\\self-healing.integration.spec.js:23:3 › Self-Healing Integration with Test Framework › should create and initialize self-healing controller \n\n    TypeError: feedbackCollector.exportFeedback is not a function\n\n       at test-framework\\src\\self-healing\\index.js:90\n\n      88 |       // Learn from feedback data if auto-learning is enabled\n      89 |       if (mergedConfig.learning.enabled && mergedConfig.learning.autoOptimize) {\n    > 90 |         await learningEngine.learn(await feedbackCollector.exportFeedback());\n         |                                                            ^\n      91 |       }\n      92 |     },\n      93 |     \n        at Object.endTest (C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\self-healing\\index.js:90:60)\n        at Object.page (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:94:5)\n\n  2) tests\\integration\\self-healing.integration.spec.js:73:3 › Self-Healing Integration with Test Framework › should create a self-healing page \n\n    TypeError: feedbackCollector.exportFeedback is not a function\n\n       at test-framework\\src\\self-healing\\index.js:90\n\n      88 |       // Learn from feedback data if auto-learning is enabled\n      89 |       if (mergedConfig.learning.enabled && mergedConfig.learning.autoOptimize) {\n    > 90 |         await learningEngine.learn(await feedbackCollector.exportFeedback());\n         |                                                            ^\n      91 |       }\n      92 |     },\n      93 |     \n        at Object.endTest (C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\self-healing\\index.js:90:60)\n        at Object.page (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:94:5)\n\n  3) tests\\integration\\self-healing.integration.spec.js:123:3 › Self-Healing Integration with Test Framework › should track test runs \n\n    TypeError: feedbackCollector.exportFeedback is not a function\n\n       at test-framework\\src\\self-healing\\index.js:90\n\n      88 |       // Learn from feedback data if auto-learning is enabled\n      89 |       if (mergedConfig.learning.enabled && mergedConfig.learning.autoOptimize) {\n    > 90 |         await learningEngine.learn(await feedbackCollector.exportFeedback());\n         |                                                            ^\n      91 |       }\n      92 |     },\n      93 |     \n        at Object.endTest (C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\self-healing\\index.js:90:60)\n        at C:\\Users\\<USER>\\QAWolfeesojc\\tests\\integration\\self-healing.integration.spec.js:160:7\n\n    TypeError: feedbackCollector.exportFeedback is not a function\n\n       at test-framework\\src\\self-healing\\index.js:90\n\n      88 |       // Learn from feedback data if auto-learning is enabled\n      89 |       if (mergedConfig.learning.enabled && mergedConfig.learning.autoOptimize) {\n    > 90 |         await learningEngine.learn(await feedbackCollector.exportFeedback());\n         |                                                            ^\n      91 |       }\n      92 |     },\n      93 |     \n        at Object.endTest (C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\self-healing\\index.js:90:60)\n        at Object.page (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:94:5)\n\n  4) tests\\integration\\self-healing.integration.spec.js:194:3 › Self-Healing Integration with Test Framework › should track performance metrics \n\n    TypeError: feedbackCollector.exportFeedback is not a function\n\n       at test-framework\\src\\self-healing\\index.js:90\n\n      88 |       // Learn from feedback data if auto-learning is enabled\n      89 |       if (mergedConfig.learning.enabled && mergedConfig.learning.autoOptimize) {\n    > 90 |         await learningEngine.learn(await feedbackCollector.exportFeedback());\n         |                                                            ^\n      91 |       }\n      92 |     },\n      93 |     \n        at Object.endTest (C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\self-healing\\index.js:90:60)\n        at Object.page (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:94:5)\n\n  5) tests\\integration\\self-healing.integration.spec.js:254:3 › Self-Healing Integration with Test Framework › should generate reports \n\n    TypeError: feedbackCollector.exportFeedback is not a function\n\n       at test-framework\\src\\self-healing\\index.js:90\n\n      88 |       // Learn from feedback data if auto-learning is enabled\n      89 |       if (mergedConfig.learning.enabled && mergedConfig.learning.autoOptimize) {\n    > 90 |         await learningEngine.learn(await feedbackCollector.exportFeedback());\n         |                                                            ^\n      91 |       }\n      92 |     },\n      93 |     \n        at Object.endTest (C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\self-healing\\index.js:90:60)\n        at C:\\Users\\<USER>\\QAWolfeesojc\\tests\\integration\\self-healing.integration.spec.js:284:7\n\n    TypeError: feedbackCollector.exportFeedback is not a function\n\n       at test-framework\\src\\self-healing\\index.js:90\n\n      88 |       // Learn from feedback data if auto-learning is enabled\n      89 |       if (mergedConfig.learning.enabled && mergedConfig.learning.autoOptimize) {\n    > 90 |         await learningEngine.learn(await feedbackCollector.exportFeedback());\n         |                                                            ^\n      91 |       }\n      92 |     },\n      93 |     \n        at Object.endTest (C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\self-healing\\index.js:90:60)\n        at Object.page (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:94:5)\n\n  5 failed\n    tests\\integration\\self-healing.integration.spec.js:23:3 › Self-Healing Integration with Test Framework › should create and initialize self-healing controller \n    tests\\integration\\self-healing.integration.spec.js:73:3 › Self-Healing Integration with Test Framework › should create a self-healing page \n    tests\\integration\\self-healing.integration.spec.js:123:3 › Self-Healing Integration with Test Framework › should track test runs \n    tests\\integration\\self-healing.integration.spec.js:194:3 › Self-Healing Integration with Test Framework › should track performance metrics \n    tests\\integration\\self-healing.integration.spec.js:254:3 › Self-Healing Integration with Test Framework › should generate reports \n"}, {"name": "tests/integration/cross-component.integration.spec.js", "passed": false, "output": "\nRunning 2 tests using 1 worker\n\nStarting Playwright MCP...\nPerformance metrics: {\n  \"executionTime\": {\n    \"start\": 1747635710679,\n    \"end\": 1747635710696,\n    \"duration\": 17,\n    \"score\": 0.9988666666666667\n  },\n  \"tokenUsage\": {\n    \"total\": 0,\n    \"byOperation\": {},\n    \"score\": 1\n  },\n  \"resourceUsage\": {\n    \"cpu\": {\n      \"values\": [],\n      \"average\": 0,\n      \"score\": 1\n    },\n    \"memory\": {\n      \"values\": [],\n      \"average\": 0,\n      \"score\": 1\n    }\n  },\n  \"operations\": [],\n  \"overallScore\": 0.9997166666666667\n}\n  x  1 tests\\integration\\cross-component.integration.spec.js:24:3 › Cross-Component Integration › should integrate all components in an end-to-end scenario (199ms)\nStarting Playwright MCP...\nPerformance metrics: {\n  \"executionTime\": {\n    \"start\": 1747635713078,\n    \"end\": 1747635713095,\n    \"duration\": 17,\n    \"score\": 0.9988666666666667\n  },\n  \"tokenUsage\": {\n    \"total\": 0,\n    \"byOperation\": {},\n    \"score\": 1\n  },\n  \"resourceUsage\": {\n    \"cpu\": {\n      \"values\": [],\n      \"average\": 0,\n      \"score\": 1\n    },\n    \"memory\": {\n      \"values\": [],\n      \"average\": 0,\n      \"score\": 1\n    }\n  },\n  \"operations\": [],\n  \"overallScore\": 0.9997166666666667\n}\n  x  2 tests\\integration\\cross-component.integration.spec.js:180:3 › Cross-Component Integration › should integrate MCP and self-healing controllers (194ms)\n\n\n  1) tests\\integration\\cross-component.integration.spec.js:24:3 › Cross-Component Integration › should integrate all components in an end-to-end scenario \n\n    Error: Config file not found: null\n\n       at mcp-optimizer\\src\\tools\\playwright-mcp.js:32\n\n      30 |   // Check if config file exists\n      31 |   if (!fs.existsSync(serverOptions.configPath)) {\n    > 32 |     throw new Error(`Config file not found: ${serverOptions.configPath}`);\n         |           ^\n      33 |   }\n      34 |\n      35 |   // Build the command arguments\n        at Object.startPlaywrightMCP (C:\\Users\\<USER>\\QAWolfeesojc\\mcp-optimizer\\src\\tools\\playwright-mcp.js:32:11)\n        at C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\mcp\\controller.js:50:79\n        at C:\\Users\\<USER>\\QAWolfeesojc\\tests\\integration\\cross-component.integration.spec.js:50:7\n\n    TypeError: feedbackCollector.exportFeedback is not a function\n\n       at test-framework\\src\\self-healing\\index.js:90\n\n      88 |       // Learn from feedback data if auto-learning is enabled\n      89 |       if (mergedConfig.learning.enabled && mergedConfig.learning.autoOptimize) {\n    > 90 |         await learningEngine.learn(await feedbackCollector.exportFeedback());\n         |                                                            ^\n      91 |       }\n      92 |     },\n      93 |     \n        at Object.endTest (C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\self-healing\\index.js:90:60)\n        at Object.page (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:94:5)\n\n  2) tests\\integration\\cross-component.integration.spec.js:180:3 › Cross-Component Integration › should integrate MCP and self-healing controllers \n\n    Error: Config file not found: null\n\n       at mcp-optimizer\\src\\tools\\playwright-mcp.js:32\n\n      30 |   // Check if config file exists\n      31 |   if (!fs.existsSync(serverOptions.configPath)) {\n    > 32 |     throw new Error(`Config file not found: ${serverOptions.configPath}`);\n         |           ^\n      33 |   }\n      34 |\n      35 |   // Build the command arguments\n        at Object.startPlaywrightMCP (C:\\Users\\<USER>\\QAWolfeesojc\\mcp-optimizer\\src\\tools\\playwright-mcp.js:32:11)\n        at C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\mcp\\controller.js:50:79\n        at C:\\Users\\<USER>\\QAWolfeesojc\\tests\\integration\\cross-component.integration.spec.js:206:7\n\n    TypeError: feedbackCollector.exportFeedback is not a function\n\n       at test-framework\\src\\self-healing\\index.js:90\n\n      88 |       // Learn from feedback data if auto-learning is enabled\n      89 |       if (mergedConfig.learning.enabled && mergedConfig.learning.autoOptimize) {\n    > 90 |         await learningEngine.learn(await feedbackCollector.exportFeedback());\n         |                                                            ^\n      91 |       }\n      92 |     },\n      93 |     \n        at Object.endTest (C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\self-healing\\index.js:90:60)\n        at Object.page (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:94:5)\n\n  2 failed\n    tests\\integration\\cross-component.integration.spec.js:24:3 › Cross-Component Integration › should integrate all components in an end-to-end scenario \n    tests\\integration\\cross-component.integration.spec.js:180:3 › Cross-Component Integration › should integrate MCP and self-healing controllers \n"}, {"name": "tests/e2e/login.e2e.spec.js", "passed": false, "output": "\nRunning 4 tests using 1 worker\n\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\run-local-1747635718367\\should login with valid credentials\\012159_before-login_Before login.png\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\run-local-1747635718367\\should login with valid credentials\\012200_filled-login-form_After filling login form.png\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\run-local-1747635718367\\should login with valid credentials\\012200_error_TypeError-page_locator______cl.png\nPerformance metrics: {\n  \"executionTime\": {\n    \"start\": 1747635719391,\n    \"end\": 1747635721017,\n    \"duration\": 1626,\n    \"score\": 0.8916\n  },\n  \"tokenUsage\": {\n    \"total\": 0,\n    \"byOperation\": {},\n    \"score\": 1\n  },\n  \"resourceUsage\": {\n    \"cpu\": {\n      \"values\": [\n        57.20155954613142\n      ],\n      \"average\": 57.20155954613142,\n      \"score\": 0.2849805056733572\n    },\n    \"memory\": {\n      \"values\": [\n        981.5850457187214\n      ],\n      \"average\": 981.5850457187214,\n      \"score\": 0\n    }\n  },\n  \"operations\": [],\n  \"overallScore\": 0.5441451264183392\n}\n  x  1 tests\\e2e\\login.e2e.spec.js:23:3 › Login Functionality › should login with valid credentials (1.8s)\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\run-local-1747635722389\\should show error with invalid credentials\\012203_before-invalid-login_Before invalid login.png\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\run-local-1747635722389\\should show error with invalid credentials\\012205_filled-invalid-login-form_After filling invalid login form.png\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\run-local-1747635722389\\should show error with invalid credentials\\012205_error_TypeError-page_locator______cl.png\nPerformance metrics: {\n  \"executionTime\": {\n    \"start\": 1747635723344,\n    \"end\": 1747635725319,\n    \"duration\": 1975,\n    \"score\": 0.8683333333333334\n  },\n  \"tokenUsage\": {\n    \"total\": 0,\n    \"byOperation\": {},\n    \"score\": 1\n  },\n  \"resourceUsage\": {\n    \"cpu\": {\n      \"values\": [\n        70.36699872140318\n      ],\n      \"average\": 70.36699872140318,\n      \"score\": 0.12041251598246028\n    },\n    \"memory\": {\n      \"values\": [\n        715.1760582784501\n      ],\n      \"average\": 715.1760582784501,\n      \"score\": 0\n    }\n  },\n  \"operations\": [],\n  \"overallScore\": 0.4971864623289484\n}\n  x  2 tests\\e2e\\login.e2e.spec.js:98:3 › Login Functionality › should show error with invalid credentials (2.1s)\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\run-local-1747635726658\\should show error with empty credentials\\012207_before-empty-login_Before empty login.png\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\run-local-1747635726658\\should show error with empty credentials\\012208_error_TypeError-page_locator______cl.png\nPerformance metrics: {\n  \"executionTime\": {\n    \"start\": 1747635727625,\n    \"end\": 1747635729072,\n    \"duration\": 1447,\n    \"score\": 0.9035333333333333\n  },\n  \"tokenUsage\": {\n    \"total\": 0,\n    \"byOperation\": {},\n    \"score\": 1\n  },\n  \"resourceUsage\": {\n    \"cpu\": {\n      \"values\": [\n        11.521557514489466\n      ],\n      \"average\": 11.521557514489466,\n      \"score\": 0.8559805310688817\n    },\n    \"memory\": {\n      \"values\": [\n        84.05096421375634\n      ],\n      \"average\": 84.05096421375634,\n      \"score\": 0.8318980715724873\n    }\n  },\n  \"operations\": [],\n  \"overallScore\": 0.8978529839936755\n}\n  x  3 tests\\e2e\\login.e2e.spec.js:173:3 › Login Functionality › should show error with empty credentials (1.6s)\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\run-local-1747635730499\\should login and logout successfully\\012211_before-login-logout_Before login for logout test.png\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\run-local-1747635730499\\should login and logout successfully\\012212_error_TypeError-page_locator______cl.png\nPerformance metrics: {\n  \"executionTime\": {\n    \"start\": 1747635731446,\n    \"end\": 1747635733071,\n    \"duration\": 1625,\n    \"score\": 0.8916666666666666\n  },\n  \"tokenUsage\": {\n    \"total\": 0,\n    \"byOperation\": {},\n    \"score\": 1\n  },\n  \"resourceUsage\": {\n    \"cpu\": {\n      \"values\": [\n        14.195827293445685\n      ],\n      \"average\": 14.195827293445685,\n      \"score\": 0.8225521588319289\n    },\n    \"memory\": {\n      \"values\": [\n        284.2598315223672\n      ],\n      \"average\": 284.2598315223672,\n      \"score\": 0.43148033695526555\n    }\n  },\n  \"operations\": [],\n  \"overallScore\": 0.7864247906134653\n}\n  x  4 tests\\e2e\\login.e2e.spec.js:236:3 › Login Functionality › should login and logout successfully (1.8s)\n\n\n  1) tests\\e2e\\login.e2e.spec.js:23:3 › Login Functionality › should login with valid credentials ──\n\n    TypeError: page.locator(...).click is not a function\n\n      52 |       });\n      53 |       \n    > 54 |       await page.locator(':text(\"Log in with email\")').click();\n         |                                                        ^\n      55 |       \n      56 |       // Wait for navigation to complete\n      57 |       await page.waitForNavigation();\n        at C:\\Users\\<USER>\\QAWolfeesojc\\tests\\e2e\\login.e2e.spec.js:54:56\n\n    TypeError: feedbackCollector.exportFeedback is not a function\n\n       at test-framework\\src\\self-healing\\index.js:90\n\n      88 |       // Learn from feedback data if auto-learning is enabled\n      89 |       if (mergedConfig.learning.enabled && mergedConfig.learning.autoOptimize) {\n    > 90 |         await learningEngine.learn(await feedbackCollector.exportFeedback());\n         |                                                            ^\n      91 |       }\n      92 |     },\n      93 |     \n        at Object.endTest (C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\self-healing\\index.js:90:60)\n        at Object.page (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:94:5)\n\n  2) tests\\e2e\\login.e2e.spec.js:98:3 › Login Functionality › should show error with invalid credentials \n\n    TypeError: page.locator(...).click is not a function\n\n      127 |       });\n      128 |       \n    > 129 |       await page.locator(':text(\"Log in with email\")').click();\n          |                                                        ^\n      130 |       \n      131 |       // Track the operation\n      132 |       performanceTracker.trackOperation({\n        at C:\\Users\\<USER>\\QAWolfeesojc\\tests\\e2e\\login.e2e.spec.js:129:56\n\n    TypeError: feedbackCollector.exportFeedback is not a function\n\n       at test-framework\\src\\self-healing\\index.js:90\n\n      88 |       // Learn from feedback data if auto-learning is enabled\n      89 |       if (mergedConfig.learning.enabled && mergedConfig.learning.autoOptimize) {\n    > 90 |         await learningEngine.learn(await feedbackCollector.exportFeedback());\n         |                                                            ^\n      91 |       }\n      92 |     },\n      93 |     \n        at Object.endTest (C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\self-healing\\index.js:90:60)\n        at Object.page (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:94:5)\n\n  3) tests\\e2e\\login.e2e.spec.js:173:3 › Login Functionality › should show error with empty credentials \n\n    TypeError: page.locator(...).click is not a function\n\n      191 |       \n      192 |       // Click the login button without filling in credentials\n    > 193 |       await page.locator(':text(\"Log in with email\")').click();\n          |                                                        ^\n      194 |       \n      195 |       // Track the operation\n      196 |       performanceTracker.trackOperation({\n        at C:\\Users\\<USER>\\QAWolfeesojc\\tests\\e2e\\login.e2e.spec.js:193:56\n\n    TypeError: feedbackCollector.exportFeedback is not a function\n\n       at test-framework\\src\\self-healing\\index.js:90\n\n      88 |       // Learn from feedback data if auto-learning is enabled\n      89 |       if (mergedConfig.learning.enabled && mergedConfig.learning.autoOptimize) {\n    > 90 |         await learningEngine.learn(await feedbackCollector.exportFeedback());\n         |                                                            ^\n      91 |       }\n      92 |     },\n      93 |     \n        at Object.endTest (C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\self-healing\\index.js:90:60)\n        at Object.page (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:94:5)\n\n  4) tests\\e2e\\login.e2e.spec.js:236:3 › Login Functionality › should login and logout successfully \n\n    TypeError: page.locator(...).click is not a function\n\n      258 |       await page.fill('[data-test-id=\"SignInEmail\"]', email);\n      259 |       await page.fill('[data-test-id=\"SignInPassword\"]', password);\n    > 260 |       await page.locator(':text(\"Log in with email\")').click();\n          |                                                        ^\n      261 |       \n      262 |       // Wait for navigation to complete\n      263 |       await page.waitForNavigation();\n        at C:\\Users\\<USER>\\QAWolfeesojc\\tests\\e2e\\login.e2e.spec.js:260:56\n\n    TypeError: feedbackCollector.exportFeedback is not a function\n\n       at test-framework\\src\\self-healing\\index.js:90\n\n      88 |       // Learn from feedback data if auto-learning is enabled\n      89 |       if (mergedConfig.learning.enabled && mergedConfig.learning.autoOptimize) {\n    > 90 |         await learningEngine.learn(await feedbackCollector.exportFeedback());\n         |                                                            ^\n      91 |       }\n      92 |     },\n      93 |     \n        at Object.endTest (C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\self-healing\\index.js:90:60)\n        at Object.page (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:94:5)\n\n  4 failed\n    tests\\e2e\\login.e2e.spec.js:23:3 › Login Functionality › should login with valid credentials ───\n    tests\\e2e\\login.e2e.spec.js:98:3 › Login Functionality › should show error with invalid credentials \n    tests\\e2e\\login.e2e.spec.js:173:3 › Login Functionality › should show error with empty credentials \n    tests\\e2e\\login.e2e.spec.js:236:3 › Login Functionality › should login and logout successfully ─\n"}, {"name": "tests/e2e/file-operations.e2e.spec.js", "passed": false, "output": "\nRunning 3 tests using 1 worker\n\n  x  1 tests\\e2e\\file-operations.e2e.spec.js:27:3 › File Operations › should create a new file (1.6s)\n  x  2 tests\\e2e\\file-operations.e2e.spec.js:106:3 › File Operations › should delete a file (1.6s)\n  x  3 tests\\e2e\\file-operations.e2e.spec.js:198:3 › File Operations › should create and delete multiple files (2.0s)\n\n\n  1) tests\\e2e\\file-operations.e2e.spec.js:27:3 › File Operations › should create a new file ───────\n\n    TypeError: page.locator(...).click is not a function\n\n       at tests\\utils\\test-helpers.js:127\n\n      125 |   await page.fill('[data-test-id=\"SignInEmail\"]', email);\n      126 |   await page.fill('[data-test-id=\"SignInPassword\"]', password);\n    > 127 |   await page.locator(':text(\"Log in with email\")').click();\n          |                                                    ^\n      128 |\n      129 |   // Wait for navigation to complete\n      130 |   await page.waitForNavigation();\n        at login (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:127:52)\n        at C:\\Users\\<USER>\\QAWolfeesojc\\tests\\e2e\\file-operations.e2e.spec.js:18:5\n\n    TypeError: feedbackCollector.exportFeedback is not a function\n\n       at test-framework\\src\\self-healing\\index.js:90\n\n      88 |       // Learn from feedback data if auto-learning is enabled\n      89 |       if (mergedConfig.learning.enabled && mergedConfig.learning.autoOptimize) {\n    > 90 |         await learningEngine.learn(await feedbackCollector.exportFeedback());\n         |                                                            ^\n      91 |       }\n      92 |     },\n      93 |     \n        at Object.endTest (C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\self-healing\\index.js:90:60)\n        at Object.page (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:94:5)\n\n  2) tests\\e2e\\file-operations.e2e.spec.js:106:3 › File Operations › should delete a file ──────────\n\n    TypeError: page.locator(...).click is not a function\n\n       at tests\\utils\\test-helpers.js:127\n\n      125 |   await page.fill('[data-test-id=\"SignInEmail\"]', email);\n      126 |   await page.fill('[data-test-id=\"SignInPassword\"]', password);\n    > 127 |   await page.locator(':text(\"Log in with email\")').click();\n          |                                                    ^\n      128 |\n      129 |   // Wait for navigation to complete\n      130 |   await page.waitForNavigation();\n        at login (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:127:52)\n        at C:\\Users\\<USER>\\QAWolfeesojc\\tests\\e2e\\file-operations.e2e.spec.js:18:5\n\n    TypeError: feedbackCollector.exportFeedback is not a function\n\n       at test-framework\\src\\self-healing\\index.js:90\n\n      88 |       // Learn from feedback data if auto-learning is enabled\n      89 |       if (mergedConfig.learning.enabled && mergedConfig.learning.autoOptimize) {\n    > 90 |         await learningEngine.learn(await feedbackCollector.exportFeedback());\n         |                                                            ^\n      91 |       }\n      92 |     },\n      93 |     \n        at Object.endTest (C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\self-healing\\index.js:90:60)\n        at Object.page (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:94:5)\n\n  3) tests\\e2e\\file-operations.e2e.spec.js:198:3 › File Operations › should create and delete multiple files \n\n    TypeError: page.locator(...).click is not a function\n\n       at tests\\utils\\test-helpers.js:127\n\n      125 |   await page.fill('[data-test-id=\"SignInEmail\"]', email);\n      126 |   await page.fill('[data-test-id=\"SignInPassword\"]', password);\n    > 127 |   await page.locator(':text(\"Log in with email\")').click();\n          |                                                    ^\n      128 |\n      129 |   // Wait for navigation to complete\n      130 |   await page.waitForNavigation();\n        at login (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:127:52)\n        at C:\\Users\\<USER>\\QAWolfeesojc\\tests\\e2e\\file-operations.e2e.spec.js:18:5\n\n    TypeError: feedbackCollector.exportFeedback is not a function\n\n       at test-framework\\src\\self-healing\\index.js:90\n\n      88 |       // Learn from feedback data if auto-learning is enabled\n      89 |       if (mergedConfig.learning.enabled && mergedConfig.learning.autoOptimize) {\n    > 90 |         await learningEngine.learn(await feedbackCollector.exportFeedback());\n         |                                                            ^\n      91 |       }\n      92 |     },\n      93 |     \n        at Object.endTest (C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\self-healing\\index.js:90:60)\n        at Object.page (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:94:5)\n\n  3 failed\n    tests\\e2e\\file-operations.e2e.spec.js:27:3 › File Operations › should create a new file ────────\n    tests\\e2e\\file-operations.e2e.spec.js:106:3 › File Operations › should delete a file ───────────\n    tests\\e2e\\file-operations.e2e.spec.js:198:3 › File Operations › should create and delete multiple files \n"}, {"name": "tests/self-healing/selector-healing.spec.js", "passed": false, "output": "\nRunning 3 tests using 1 worker\n\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\run-local-1747635755912\\should heal CSS selectors\\012237_before-heal-css-selectors_Before attempting to heal CSS selectors.png\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\run-local-1747635755912\\should heal CSS selectors\\012238_error_TypeError-Method_Promise_proto.png\nPerformance metrics: {\n  \"executionTime\": {\n    \"start\": 1747635756822,\n    \"end\": 1747635758299,\n    \"duration\": 1477,\n    \"score\": 0.9015333333333333\n  },\n  \"tokenUsage\": {\n    \"total\": 0,\n    \"byOperation\": {},\n    \"score\": 1\n  },\n  \"resourceUsage\": {\n    \"cpu\": {\n      \"values\": [\n        30.763197979850588\n      ],\n      \"average\": 30.763197979850588,\n      \"score\": 0.6154600252518676\n    },\n    \"memory\": {\n      \"values\": [\n        469.86742355913515\n      ],\n      \"average\": 469.86742355913515,\n      \"score\": 0.06026515288172973\n    }\n  },\n  \"operations\": [],\n  \"overallScore\": 0.6443146278667327\n}\n  x  1 tests\\self-healing\\selector-healing.spec.js:23:3 › Selector Healing Capabilities › should heal CSS selectors (1.6s)\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\run-local-1747635759693\\should heal XPath selectors\\012241_before-heal-xpath-selectors_Before attempting to heal XPath selectors.png\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\run-local-1747635759693\\should heal XPath selectors\\012241_error_TypeError-Method_Promise_proto.png\nPerformance metrics: {\n  \"executionTime\": {\n    \"start\": 1747635760619,\n    \"end\": 1747635762102,\n    \"duration\": 1483,\n    \"score\": 0.9011333333333333\n  },\n  \"tokenUsage\": {\n    \"total\": 0,\n    \"byOperation\": {},\n    \"score\": 1\n  },\n  \"resourceUsage\": {\n    \"cpu\": {\n      \"values\": [\n        97.26506142015283\n      ],\n      \"average\": 97.26506142015283,\n      \"score\": 0\n    },\n    \"memory\": {\n      \"values\": [\n        894.1058910132551\n      ],\n      \"average\": 894.1058910132551,\n      \"score\": 0\n    }\n  },\n  \"operations\": [],\n  \"overallScore\": 0.47528333333333334\n}\n  x  2 tests\\self-healing\\selector-healing.spec.js:128:3 › Selector Healing Capabilities › should heal XPath selectors (1.6s)\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\run-local-1747635763582\\should heal text-based selectors\\012245_before-heal-text-selectors_Before attempting to heal text-based selectors.png\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\run-local-1747635763582\\should heal text-based selectors\\012246_error_TypeError-Method_Promise_proto.png\nPerformance metrics: {\n  \"executionTime\": {\n    \"start\": 1747635764724,\n    \"end\": 1747635766273,\n    \"duration\": 1549,\n    \"score\": 0.8967333333333334\n  },\n  \"tokenUsage\": {\n    \"total\": 0,\n    \"byOperation\": {},\n    \"score\": 1\n  },\n  \"resourceUsage\": {\n    \"cpu\": {\n      \"values\": [\n        54.37687628496373\n      ],\n      \"average\": 54.37687628496373,\n      \"score\": 0.3202890464379534\n    },\n    \"memory\": {\n      \"values\": [\n        811.7816251066316\n      ],\n      \"average\": 811.7816251066316,\n      \"score\": 0\n    }\n  },\n  \"operations\": [],\n  \"overallScore\": 0.5542555949428217\n}\n  x  3 tests\\self-healing\\selector-healing.spec.js:233:3 › Selector Healing Capabilities › should heal text-based selectors (1.7s)\n\n\n  1) tests\\self-healing\\selector-healing.spec.js:23:3 › Selector Healing Capabilities › should heal CSS selectors \n\n    TypeError: Method Promise.prototype.then called on incompatible receiver #<Promise>\n\n    TypeError: feedbackCollector.exportFeedback is not a function\n\n       at test-framework\\src\\self-healing\\index.js:90\n\n      88 |       // Learn from feedback data if auto-learning is enabled\n      89 |       if (mergedConfig.learning.enabled && mergedConfig.learning.autoOptimize) {\n    > 90 |         await learningEngine.learn(await feedbackCollector.exportFeedback());\n         |                                                            ^\n      91 |       }\n      92 |     },\n      93 |     \n        at Object.endTest (C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\self-healing\\index.js:90:60)\n        at Object.page (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:94:5)\n\n  2) tests\\self-healing\\selector-healing.spec.js:128:3 › Selector Healing Capabilities › should heal XPath selectors \n\n    TypeError: Method Promise.prototype.then called on incompatible receiver #<Promise>\n\n    TypeError: feedbackCollector.exportFeedback is not a function\n\n       at test-framework\\src\\self-healing\\index.js:90\n\n      88 |       // Learn from feedback data if auto-learning is enabled\n      89 |       if (mergedConfig.learning.enabled && mergedConfig.learning.autoOptimize) {\n    > 90 |         await learningEngine.learn(await feedbackCollector.exportFeedback());\n         |                                                            ^\n      91 |       }\n      92 |     },\n      93 |     \n        at Object.endTest (C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\self-healing\\index.js:90:60)\n        at Object.page (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:94:5)\n\n  3) tests\\self-healing\\selector-healing.spec.js:233:3 › Selector Healing Capabilities › should heal text-based selectors \n\n    TypeError: Method Promise.prototype.then called on incompatible receiver #<Promise>\n\n    TypeError: feedbackCollector.exportFeedback is not a function\n\n       at test-framework\\src\\self-healing\\index.js:90\n\n      88 |       // Learn from feedback data if auto-learning is enabled\n      89 |       if (mergedConfig.learning.enabled && mergedConfig.learning.autoOptimize) {\n    > 90 |         await learningEngine.learn(await feedbackCollector.exportFeedback());\n         |                                                            ^\n      91 |       }\n      92 |     },\n      93 |     \n        at Object.endTest (C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\self-healing\\index.js:90:60)\n        at Object.page (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:94:5)\n\n  3 failed\n    tests\\self-healing\\selector-healing.spec.js:23:3 › Selector Healing Capabilities › should heal CSS selectors \n    tests\\self-healing\\selector-healing.spec.js:128:3 › Selector Healing Capabilities › should heal XPath selectors \n    tests\\self-healing\\selector-healing.spec.js:233:3 › Selector Healing Capabilities › should heal text-based selectors \n"}, {"name": "tests/self-healing/recovery-strategies.spec.js", "passed": false, "output": "\nRunning 3 tests using 1 worker\n\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\run-local-1747635771635\\should recover using retry strategy\\012253_before-retry-strategy_Before attempting retry strategy.png\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\run-local-1747635771635\\should recover using retry strategy\\012256_after-retry-strategy_After attempting retry strategy.png\nPerformance metrics: {\n  \"executionTime\": {\n    \"start\": 1747635772534,\n    \"end\": 1747635776414,\n    \"duration\": 3880,\n    \"score\": 0.7413333333333334\n  },\n  \"tokenUsage\": {\n    \"total\": 0,\n    \"byOperation\": {},\n    \"score\": 1\n  },\n  \"resourceUsage\": {\n    \"cpu\": {\n      \"values\": [\n        97.26552752118734,\n        64.08258255991414,\n        22.44127835665004\n      ],\n      \"average\": 61.2631294792505,\n      \"score\": 0.23421088150936875\n    },\n    \"memory\": {\n      \"values\": [\n        788.3557700455242,\n        566.8833528958199,\n        690.3331048113224\n      ],\n      \"average\": 681.8574092508888,\n      \"score\": 0\n    }\n  },\n  \"operations\": [\n    {\n      \"name\": \"Retry Strategy\",\n      \"type\": \"self-healing\",\n      \"duration\": 2500,\n      \"timestamp\": 1747635776319\n    }\n  ],\n  \"overallScore\": 0.4938860537106755\n}\n  x  1 tests\\self-healing\\recovery-strategies.spec.js:23:3 › Recovery Strategies › should recover using retry strategy (4.0s)\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\run-local-1747635777792\\should recover using wait strategy\\012259_before-wait-strategy_Before attempting wait strategy.png\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\run-local-1747635777792\\should recover using wait strategy\\012302_after-wait-strategy_After attempting wait strategy.png\nPerformance metrics: {\n  \"executionTime\": {\n    \"start\": 1747635778747,\n    \"end\": 1747635782710,\n    \"duration\": 3963,\n    \"score\": 0.7358\n  },\n  \"tokenUsage\": {\n    \"total\": 0,\n    \"byOperation\": {},\n    \"score\": 1\n  },\n  \"resourceUsage\": {\n    \"cpu\": {\n      \"values\": [\n        89.82173776230657,\n        99.19747705164632,\n        43.53411202737006\n      ],\n      \"average\": 77.5177756137743,\n      \"score\": 0.031027804827821193\n    },\n    \"memory\": {\n      \"values\": [\n        938.1710461106359,\n        576.5117731448886,\n        275.8736286689012\n      ],\n      \"average\": 596.8521493081419,\n      \"score\": 0\n    }\n  },\n  \"operations\": [\n    {\n      \"name\": \"Wait Strategy\",\n      \"type\": \"self-healing\",\n      \"duration\": 2500,\n      \"timestamp\": 1747635782618\n    }\n  ],\n  \"overallScore\": 0.4417069512069553\n}\n  x  2 tests\\self-healing\\recovery-strategies.spec.js:140:3 › Recovery Strategies › should recover using wait strategy (4.1s)\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\run-local-1747635784019\\should recover using selector strategy\\012306_before-selector-strategy_Before attempting selector strategy.png\nTrying recovery strategy: retry\nRetrying action \"click\" after error: page.apply: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('#non-existent-login-button')\u001b[22m\n\n  x  3 tests\\self-healing\\recovery-strategies.spec.js:258:3 › Recovery Strategies › should recover using selector strategy (30.0s)\n\n\n  1) tests\\self-healing\\recovery-strategies.spec.js:23:3 › Recovery Strategies › should recover using retry strategy \n\n    TypeError: Method Promise.prototype.then called on incompatible receiver #<Promise>\n\n    TypeError: feedbackCollector.exportFeedback is not a function\n\n       at test-framework\\src\\self-healing\\index.js:90\n\n      88 |       // Learn from feedback data if auto-learning is enabled\n      89 |       if (mergedConfig.learning.enabled && mergedConfig.learning.autoOptimize) {\n    > 90 |         await learningEngine.learn(await feedbackCollector.exportFeedback());\n         |                                                            ^\n      91 |       }\n      92 |     },\n      93 |     \n        at Object.endTest (C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\self-healing\\index.js:90:60)\n        at Object.page (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:94:5)\n\n    Error: proxy.screenshot: Test ended.\n    Call log:\n    \u001b[2m  - taking page screenshot\u001b[22m\n    \u001b[2m  - waiting for fonts to load...\u001b[22m\n    \u001b[2m  - fonts loaded\u001b[22m\n\n\n       at shared-utils\\src\\screenshot\\screenshot-utils.js:116\n\n      114 |\n      115 |   // Take the screenshot with Playwright\n    > 116 |   await page.screenshot({\n          |              ^\n      117 |     path: screenshotPath,\n      118 |     fullPage,\n      119 |     ...screenshotOptions\n        at takeScreenshot (C:\\Users\\<USER>\\QAWolfeesojc\\shared-utils\\src\\screenshot\\screenshot-utils.js:116:14)\n        at Object.takeErrorScreenshot (C:\\Users\\<USER>\\QAWolfeesojc\\shared-utils\\src\\screenshot\\screenshot-utils.js:147:10)\n        at Proxy.takeErrorScreenshot (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:84:31)\n        at C:\\Users\\<USER>\\QAWolfeesojc\\tests\\self-healing\\recovery-strategies.spec.js:122:18\n\n    Error Context: test-results\\tests-self-healing-recover-ecd3f-ecover-using-retry-strategy\\error-context.md\n\n  2) tests\\self-healing\\recovery-strategies.spec.js:140:3 › Recovery Strategies › should recover using wait strategy \n\n    TypeError: Method Promise.prototype.then called on incompatible receiver #<Promise>\n\n    TypeError: feedbackCollector.exportFeedback is not a function\n\n       at test-framework\\src\\self-healing\\index.js:90\n\n      88 |       // Learn from feedback data if auto-learning is enabled\n      89 |       if (mergedConfig.learning.enabled && mergedConfig.learning.autoOptimize) {\n    > 90 |         await learningEngine.learn(await feedbackCollector.exportFeedback());\n         |                                                            ^\n      91 |       }\n      92 |     },\n      93 |     \n        at Object.endTest (C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\self-healing\\index.js:90:60)\n        at Object.page (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:94:5)\n\n    Error: proxy.screenshot: Test ended.\n    Call log:\n    \u001b[2m  - taking page screenshot\u001b[22m\n    \u001b[2m  - waiting for fonts to load...\u001b[22m\n    \u001b[2m  - fonts loaded\u001b[22m\n\n\n       at shared-utils\\src\\screenshot\\screenshot-utils.js:116\n\n      114 |\n      115 |   // Take the screenshot with Playwright\n    > 116 |   await page.screenshot({\n          |              ^\n      117 |     path: screenshotPath,\n      118 |     fullPage,\n      119 |     ...screenshotOptions\n        at takeScreenshot (C:\\Users\\<USER>\\QAWolfeesojc\\shared-utils\\src\\screenshot\\screenshot-utils.js:116:14)\n        at Object.takeErrorScreenshot (C:\\Users\\<USER>\\QAWolfeesojc\\shared-utils\\src\\screenshot\\screenshot-utils.js:147:10)\n        at Proxy.takeErrorScreenshot (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:84:31)\n        at C:\\Users\\<USER>\\QAWolfeesojc\\tests\\self-healing\\recovery-strategies.spec.js:240:18\n\n    Error Context: test-results\\tests-self-healing-recover-e261f-recover-using-wait-strategy\\error-context.md\n\n  3) tests\\self-healing\\recovery-strategies.spec.js:258:3 › Recovery Strategies › should recover using selector strategy \n\n    \u001b[31mTest timeout of 30000ms exceeded.\u001b[39m\n\n    TypeError: feedbackCollector.exportFeedback is not a function\n\n       at test-framework\\src\\self-healing\\index.js:90\n\n      88 |       // Learn from feedback data if auto-learning is enabled\n      89 |       if (mergedConfig.learning.enabled && mergedConfig.learning.autoOptimize) {\n    > 90 |         await learningEngine.learn(await feedbackCollector.exportFeedback());\n         |                                                            ^\n      91 |       }\n      92 |     },\n      93 |     \n        at Object.endTest (C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\self-healing\\index.js:90:60)\n        at Object.page (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:94:5)\n\n  3 failed\n    tests\\self-healing\\recovery-strategies.spec.js:23:3 › Recovery Strategies › should recover using retry strategy \n    tests\\self-healing\\recovery-strategies.spec.js:140:3 › Recovery Strategies › should recover using wait strategy \n    tests\\self-healing\\recovery-strategies.spec.js:258:3 › Recovery Strategies › should recover using selector strategy \n"}, {"name": "tests/performance/execution-time.spec.js", "passed": false, "output": "\nRunning 3 tests using 1 worker\n\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\run-local-1747635819856\\should measure login performance\\012340_before-login-performance_Before login performance test.png\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\run-local-1747635819856\\should measure login performance\\012342_error_TypeError-page_locator______cl.png\nPerformance metrics: {\n  \"executionTime\": {\n    \"start\": 1747635820754,\n    \"end\": 1747635822299,\n    \"duration\": 1545,\n    \"score\": 0.897\n  },\n  \"tokenUsage\": {\n    \"total\": 0,\n    \"byOperation\": {},\n    \"score\": 1\n  },\n  \"resourceUsage\": {\n    \"cpu\": {\n      \"values\": [\n        59.18180837972387\n      ],\n      \"average\": 59.18180837972387,\n      \"score\": 0.2602273952534516\n    },\n    \"memory\": {\n      \"values\": [\n        59.14853525462904\n      ],\n      \"average\": 59.14853525462904,\n      \"score\": 0.8817029294907419\n    }\n  },\n  \"operations\": [],\n  \"overallScore\": 0.7597325811860484\n}\n  x  1 tests\\performance\\execution-time.spec.js:23:3 › Execution Time Performance › should measure login performance (1.7s)\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\run-local-1747635823721\\should measure file creation performance with and without MCP optimization\\012346_error_TypeError-page_locator______cl.png\nPerformance metrics: {\n  \"executionTime\": {\n    \"start\": 1747635824682,\n    \"end\": 1747635826203,\n    \"duration\": 1521,\n    \"score\": 0.8986\n  },\n  \"tokenUsage\": {\n    \"total\": 0,\n    \"byOperation\": {},\n    \"score\": 1\n  },\n  \"resourceUsage\": {\n    \"cpu\": {\n      \"values\": [\n        62.206563237657434\n      ],\n      \"average\": 62.206563237657434,\n      \"score\": 0.22241795952928212\n    },\n    \"memory\": {\n      \"values\": [\n        839.7483650609603\n      ],\n      \"average\": 839.7483650609603,\n      \"score\": 0\n    }\n  },\n  \"operations\": [],\n  \"overallScore\": 0.5302544898823205\n}\n  x  2 tests\\performance\\execution-time.spec.js:98:3 › Execution Time Performance › should measure file creation performance with and without MCP optimization (1.7s)\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\run-local-1747635827617\\should measure self-healing performance\\012348_before-self-healing-performance_Before self-healing performance test.png\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\run-local-1747635827617\\should measure self-healing performance\\012350_error_TypeError-page_locator______cl.png\nPerformance metrics: {\n  \"executionTime\": {\n    \"start\": 1747635828583,\n    \"end\": 1747635830218,\n    \"duration\": 1635,\n    \"score\": 0.891\n  },\n  \"tokenUsage\": {\n    \"total\": 0,\n    \"byOperation\": {},\n    \"score\": 1\n  },\n  \"resourceUsage\": {\n    \"cpu\": {\n      \"values\": [\n        85.91286195212709\n      ],\n      \"average\": 85.91286195212709,\n      \"score\": 0\n    },\n    \"memory\": {\n      \"values\": [\n        426.1349269060199\n      ],\n      \"average\": 426.1349269060199,\n      \"score\": 0.1477301461879601\n    }\n  },\n  \"operations\": [],\n  \"overallScore\": 0.50968253654699\n}\n  x  3 tests\\performance\\execution-time.spec.js:219:3 › Execution Time Performance › should measure self-healing performance (1.8s)\n\n\n  1) tests\\performance\\execution-time.spec.js:23:3 › Execution Time Performance › should measure login performance \n\n    TypeError: page.locator(...).click is not a function\n\n      48 |       await page.fill('[data-test-id=\"SignInEmail\"]', email);\n      49 |       await page.fill('[data-test-id=\"SignInPassword\"]', password);\n    > 50 |       await page.locator(':text(\"Log in with email\")').click();\n         |                                                        ^\n      51 |       \n      52 |       // Wait for navigation to complete\n      53 |       await page.waitForNavigation();\n        at C:\\Users\\<USER>\\QAWolfeesojc\\tests\\performance\\execution-time.spec.js:50:56\n\n    TypeError: feedbackCollector.exportFeedback is not a function\n\n       at test-framework\\src\\self-healing\\index.js:90\n\n      88 |       // Learn from feedback data if auto-learning is enabled\n      89 |       if (mergedConfig.learning.enabled && mergedConfig.learning.autoOptimize) {\n    > 90 |         await learningEngine.learn(await feedbackCollector.exportFeedback());\n         |                                                            ^\n      91 |       }\n      92 |     },\n      93 |     \n        at Object.endTest (C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\self-healing\\index.js:90:60)\n        at Object.page (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:94:5)\n\n  2) tests\\performance\\execution-time.spec.js:98:3 › Execution Time Performance › should measure file creation performance with and without MCP optimization \n\n    TypeError: page.locator(...).click is not a function\n\n       at tests\\utils\\test-helpers.js:127\n\n      125 |   await page.fill('[data-test-id=\"SignInEmail\"]', email);\n      126 |   await page.fill('[data-test-id=\"SignInPassword\"]', password);\n    > 127 |   await page.locator(':text(\"Log in with email\")').click();\n          |                                                    ^\n      128 |\n      129 |   // Wait for navigation to complete\n      130 |   await page.waitForNavigation();\n        at login (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:127:52)\n        at C:\\Users\\<USER>\\QAWolfeesojc\\tests\\performance\\execution-time.spec.js:107:7\n\n    TypeError: feedbackCollector.exportFeedback is not a function\n\n       at test-framework\\src\\self-healing\\index.js:90\n\n      88 |       // Learn from feedback data if auto-learning is enabled\n      89 |       if (mergedConfig.learning.enabled && mergedConfig.learning.autoOptimize) {\n    > 90 |         await learningEngine.learn(await feedbackCollector.exportFeedback());\n         |                                                            ^\n      91 |       }\n      92 |     },\n      93 |     \n        at Object.endTest (C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\self-healing\\index.js:90:60)\n        at Object.page (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:94:5)\n\n  3) tests\\performance\\execution-time.spec.js:219:3 › Execution Time Performance › should measure self-healing performance \n\n    TypeError: page.locator(...).click is not a function\n\n      242 |       await page.fill('[data-test-id=\"SignInEmail\"]', config.auth.email);\n      243 |       await page.fill('[data-test-id=\"SignInPassword\"]', config.auth.password);\n    > 244 |       await page.locator(':text(\"Log in with email\")').click();\n          |                                                        ^\n      245 |       \n      246 |       // Wait for navigation to complete\n      247 |       await page.waitForNavigation();\n        at C:\\Users\\<USER>\\QAWolfeesojc\\tests\\performance\\execution-time.spec.js:244:56\n\n    TypeError: feedbackCollector.exportFeedback is not a function\n\n       at test-framework\\src\\self-healing\\index.js:90\n\n      88 |       // Learn from feedback data if auto-learning is enabled\n      89 |       if (mergedConfig.learning.enabled && mergedConfig.learning.autoOptimize) {\n    > 90 |         await learningEngine.learn(await feedbackCollector.exportFeedback());\n         |                                                            ^\n      91 |       }\n      92 |     },\n      93 |     \n        at Object.endTest (C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\self-healing\\index.js:90:60)\n        at Object.page (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:94:5)\n\n  3 failed\n    tests\\performance\\execution-time.spec.js:23:3 › Execution Time Performance › should measure login performance \n    tests\\performance\\execution-time.spec.js:98:3 › Execution Time Performance › should measure file creation performance with and without MCP optimization \n    tests\\performance\\execution-time.spec.js:219:3 › Execution Time Performance › should measure self-healing performance \n"}, {"name": "tests/performance/token-usage.spec.js", "passed": false, "output": "\nRunning 3 tests using 1 worker\n\nStarting Playwright MCP...\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\run-local-1747635835294\\should measure selector optimization token usage\\012356_error_Error-Config_file_not_foun.png\nPerformance metrics: {\n  \"executionTime\": {\n    \"start\": 1747635836182,\n    \"end\": 1747635836298,\n    \"duration\": 116,\n    \"score\": 0.9922666666666666\n  },\n  \"tokenUsage\": {\n    \"total\": 0,\n    \"byOperation\": {},\n    \"score\": 1\n  },\n  \"resourceUsage\": {\n    \"cpu\": {\n      \"values\": [],\n      \"average\": 0,\n      \"score\": 1\n    },\n    \"memory\": {\n      \"values\": [],\n      \"average\": 0,\n      \"score\": 1\n    }\n  },\n  \"operations\": [],\n  \"overallScore\": 0.9980666666666667\n}\n  x  1 tests\\performance\\token-usage.spec.js:23:3 › Token Usage Performance › should measure selector optimization token usage (268ms)\nStarting Playwright MCP...\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\run-local-1747635837618\\should measure screenshot analysis token usage\\012359_error_Error-Config_file_not_foun.png\nPerformance metrics: {\n  \"executionTime\": {\n    \"start\": 1747635838506,\n    \"end\": 1747635839933,\n    \"duration\": 1427,\n    \"score\": 0.9048666666666667\n  },\n  \"tokenUsage\": {\n    \"total\": 0,\n    \"byOperation\": {},\n    \"score\": 1\n  },\n  \"resourceUsage\": {\n    \"cpu\": {\n      \"values\": [\n        86.09178257335618\n      ],\n      \"average\": 86.09178257335618,\n      \"score\": 0\n    },\n    \"memory\": {\n      \"values\": [\n        29.72925719389452\n      ],\n      \"average\": 29.72925719389452,\n      \"score\": 0.940541485612211\n    }\n  },\n  \"operations\": [],\n  \"overallScore\": 0.7113520380697194\n}\n  x  2 tests\\performance\\token-usage.spec.js:120:3 › Token Usage Performance › should measure screenshot analysis token usage (1.6s)\nStarting Playwright MCP...\nScreenshot saved: C:\\Users\\<USER>\\QAWolfeesojc\\screenshots\\2025-05-19\\run-local-1747635841270\\should measure tool selection token usage\\012402_error_Error-Config_file_not_foun.png\nPerformance metrics: {\n  \"executionTime\": {\n    \"start\": 1747635842188,\n    \"end\": 1747635842313,\n    \"duration\": 125,\n    \"score\": 0.9916666666666667\n  },\n  \"tokenUsage\": {\n    \"total\": 0,\n    \"byOperation\": {},\n    \"score\": 1\n  },\n  \"resourceUsage\": {\n    \"cpu\": {\n      \"values\": [],\n      \"average\": 0,\n      \"score\": 1\n    },\n    \"memory\": {\n      \"values\": [],\n      \"average\": 0,\n      \"score\": 1\n    }\n  },\n  \"operations\": [],\n  \"overallScore\": 0.9979166666666667\n}\n  x  3 tests\\performance\\token-usage.spec.js:209:3 › Token Usage Performance › should measure tool selection token usage (285ms)\n\n\n  1) tests\\performance\\token-usage.spec.js:23:3 › Token Usage Performance › should measure selector optimization token usage \n\n    Error: Config file not found: null\n\n       at mcp-optimizer\\src\\tools\\playwright-mcp.js:32\n\n      30 |   // Check if config file exists\n      31 |   if (!fs.existsSync(serverOptions.configPath)) {\n    > 32 |     throw new Error(`Config file not found: ${serverOptions.configPath}`);\n         |           ^\n      33 |   }\n      34 |\n      35 |   // Build the command arguments\n        at Object.startPlaywrightMCP (C:\\Users\\<USER>\\QAWolfeesojc\\mcp-optimizer\\src\\tools\\playwright-mcp.js:32:11)\n        at C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\mcp\\controller.js:50:79\n        at C:\\Users\\<USER>\\QAWolfeesojc\\tests\\performance\\token-usage.spec.js:39:7\n\n    TypeError: feedbackCollector.exportFeedback is not a function\n\n       at test-framework\\src\\self-healing\\index.js:90\n\n      88 |       // Learn from feedback data if auto-learning is enabled\n      89 |       if (mergedConfig.learning.enabled && mergedConfig.learning.autoOptimize) {\n    > 90 |         await learningEngine.learn(await feedbackCollector.exportFeedback());\n         |                                                            ^\n      91 |       }\n      92 |     },\n      93 |     \n        at Object.endTest (C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\self-healing\\index.js:90:60)\n        at Object.page (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:94:5)\n\n  2) tests\\performance\\token-usage.spec.js:120:3 › Token Usage Performance › should measure screenshot analysis token usage \n\n    Error: Config file not found: null\n\n       at mcp-optimizer\\src\\tools\\playwright-mcp.js:32\n\n      30 |   // Check if config file exists\n      31 |   if (!fs.existsSync(serverOptions.configPath)) {\n    > 32 |     throw new Error(`Config file not found: ${serverOptions.configPath}`);\n         |           ^\n      33 |   }\n      34 |\n      35 |   // Build the command arguments\n        at Object.startPlaywrightMCP (C:\\Users\\<USER>\\QAWolfeesojc\\mcp-optimizer\\src\\tools\\playwright-mcp.js:32:11)\n        at C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\mcp\\controller.js:50:79\n        at C:\\Users\\<USER>\\QAWolfeesojc\\tests\\performance\\token-usage.spec.js:139:7\n\n    TypeError: feedbackCollector.exportFeedback is not a function\n\n       at test-framework\\src\\self-healing\\index.js:90\n\n      88 |       // Learn from feedback data if auto-learning is enabled\n      89 |       if (mergedConfig.learning.enabled && mergedConfig.learning.autoOptimize) {\n    > 90 |         await learningEngine.learn(await feedbackCollector.exportFeedback());\n         |                                                            ^\n      91 |       }\n      92 |     },\n      93 |     \n        at Object.endTest (C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\self-healing\\index.js:90:60)\n        at Object.page (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:94:5)\n\n  3) tests\\performance\\token-usage.spec.js:209:3 › Token Usage Performance › should measure tool selection token usage \n\n    Error: Config file not found: null\n\n       at mcp-optimizer\\src\\tools\\playwright-mcp.js:32\n\n      30 |   // Check if config file exists\n      31 |   if (!fs.existsSync(serverOptions.configPath)) {\n    > 32 |     throw new Error(`Config file not found: ${serverOptions.configPath}`);\n         |           ^\n      33 |   }\n      34 |\n      35 |   // Build the command arguments\n        at Object.startPlaywrightMCP (C:\\Users\\<USER>\\QAWolfeesojc\\mcp-optimizer\\src\\tools\\playwright-mcp.js:32:11)\n        at C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\mcp\\controller.js:50:79\n        at C:\\Users\\<USER>\\QAWolfeesojc\\tests\\performance\\token-usage.spec.js:226:7\n\n    TypeError: feedbackCollector.exportFeedback is not a function\n\n       at test-framework\\src\\self-healing\\index.js:90\n\n      88 |       // Learn from feedback data if auto-learning is enabled\n      89 |       if (mergedConfig.learning.enabled && mergedConfig.learning.autoOptimize) {\n    > 90 |         await learningEngine.learn(await feedbackCollector.exportFeedback());\n         |                                                            ^\n      91 |       }\n      92 |     },\n      93 |     \n        at Object.endTest (C:\\Users\\<USER>\\QAWolfeesojc\\test-framework\\src\\self-healing\\index.js:90:60)\n        at Object.page (C:\\Users\\<USER>\\QAWolfeesojc\\tests\\utils\\test-helpers.js:94:5)\n\n  3 failed\n    tests\\performance\\token-usage.spec.js:23:3 › Token Usage Performance › should measure selector optimization token usage \n    tests\\performance\\token-usage.spec.js:120:3 › Token Usage Performance › should measure screenshot analysis token usage \n    tests\\performance\\token-usage.spec.js:209:3 › Token Usage Performance › should measure tool selection token usage \n"}]}, "summary": {"simplified": {"passed": 3, "failed": 3, "skipped": 0, "total": 6, "passRate": 50}, "full": {"passed": 0, "failed": 10, "skipped": 0, "total": 10, "passRate": 0}, "overall": {"passed": 3, "failed": 13, "skipped": 0, "total": 16, "passRate": 18.75}}}