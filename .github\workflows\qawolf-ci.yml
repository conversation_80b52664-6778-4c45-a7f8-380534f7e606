name: QA Wolf CI Integration

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

jobs:
  qawolf-ci:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Install QA Wolf CI SDK
        run: npm install @qawolf/ci-sdk
      
      - name: Notify QA Wolf of deployment
        run: node scripts/qawolf-notify-deployment.js
        env:
          QA_WOLF_API_KEY: ${{ secrets.QA_WOLF_API_KEY }}
          QA_WOLF_TEAM_ID: ${{ secrets.QA_WOLF_TEAM_ID }}
      
      - name: Run tests and wait for CI greenlight
        run: node scripts/qawolf-ci-greenlight.js
        env:
          QA_WOLF_API_KEY: ${{ secrets.QA_WOLF_API_KEY }}
          QA_WOLF_TEAM_ID: ${{ secrets.QA_WOLF_TEAM_ID }}
      
      - name: Generate QA Wolf metrics report
        run: node scripts/qawolf-generate-report.js
        env:
          QA_WOLF_API_KEY: ${{ secrets.QA_WOLF_API_KEY }}
          QA_WOLF_TEAM_ID: ${{ secrets.QA_WOLF_TEAM_ID }}
      
      - name: Upload QA Wolf report
        uses: actions/upload-artifact@v3
        with:
          name: qawolf-report
          path: qawolf-reports/
          retention-days: 30
