{"testRuns": [{"testId": "test-1747635661692", "testName": "Unknown Test", "startTime": "2025-05-19T06:21:01.692Z", "endTime": "2025-05-19T06:21:01.795Z", "duration": 103, "events": [{"type": "test:start", "timestamp": "2025-05-19T06:21:01.692Z", "data": {"testId": "test-1747635661692", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:01.692Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:21:01.795Z", "data": {"testId": "test-1747635661692", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:01.795Z", "duration": 103, "error": null}}], "selectors": {}, "recoveries": [], "performance": {}}, {"testId": "test-1747635664300", "testName": "Unknown Test", "startTime": "2025-05-19T06:21:04.300Z", "endTime": "2025-05-19T06:21:04.400Z", "duration": 100, "events": [{"type": "test:start", "timestamp": "2025-05-19T06:21:04.300Z", "data": {"testId": "test-1747635664300", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:04.300Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:21:04.400Z", "data": {"testId": "test-1747635664300", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:04.400Z", "duration": 100, "error": null}}], "selectors": {}, "recoveries": [], "performance": {}}, {"testId": "test-1747635666765", "testName": "Unknown Test", "startTime": "2025-05-19T06:21:06.765Z", "endTime": "2025-05-19T06:21:06.772Z", "duration": 7, "events": [{"type": "test:start", "timestamp": "2025-05-19T06:21:06.765Z", "data": {"testId": "test-1747635666765", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:06.765Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:21:06.772Z", "data": {"testId": "test-1747635666765", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:06.772Z", "duration": 7, "error": null}}], "selectors": {}, "recoveries": [], "performance": {}}, {"testId": "test-1747635669282", "testName": "Unknown Test", "startTime": "2025-05-19T06:21:09.282Z", "endTime": "2025-05-19T06:21:09.324Z", "duration": 42, "events": [{"type": "test:start", "timestamp": "2025-05-19T06:21:09.282Z", "data": {"testId": "test-1747635669282", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:09.282Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:21:09.324Z", "data": {"testId": "test-1747635669282", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:09.324Z", "duration": 42, "error": null}}], "selectors": {}, "recoveries": [], "performance": {}}, {"testId": "test-1747635677167", "testName": "Unknown Test", "startTime": "2025-05-19T06:21:17.167Z", "endTime": "2025-05-19T06:21:17.188Z", "duration": 21, "events": [{"type": "test:start", "timestamp": "2025-05-19T06:21:17.167Z", "data": {"testId": "test-1747635677167", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:17.167Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:21:17.188Z", "data": {"testId": "test-1747635677167", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:17.188Z", "duration": 21, "error": null}}], "selectors": {}, "recoveries": [], "performance": {}}, {"testId": "test-1747635679799", "testName": "Unknown Test", "startTime": "2025-05-19T06:21:19.799Z", "endTime": "2025-05-19T06:21:19.822Z", "duration": 23, "events": [{"type": "test:start", "timestamp": "2025-05-19T06:21:19.799Z", "data": {"testId": "test-1747635679799", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:19.799Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:21:19.822Z", "data": {"testId": "test-1747635679799", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:19.822Z", "duration": 23, "error": null}}], "selectors": {}, "recoveries": [], "performance": {}}, {"testId": "test-1747635682433", "testName": "Unknown Test", "startTime": "2025-05-19T06:21:22.433Z", "endTime": "2025-05-19T06:21:22.451Z", "duration": 18, "events": [{"type": "test:start", "timestamp": "2025-05-19T06:21:22.433Z", "data": {"testId": "test-1747635682433", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:22.433Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:21:22.451Z", "data": {"testId": "test-1747635682433", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:22.451Z", "duration": 18, "error": null}}], "selectors": {}, "recoveries": [], "performance": {}}, {"testId": "test-1747635685030", "testName": "Unknown Test", "startTime": "2025-05-19T06:21:25.030Z", "endTime": "2025-05-19T06:21:25.050Z", "duration": 20, "events": [{"type": "test:start", "timestamp": "2025-05-19T06:21:25.030Z", "data": {"testId": "test-1747635685030", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:25.030Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:21:25.050Z", "data": {"testId": "test-1747635685030", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:25.050Z", "duration": 20, "error": null}}], "selectors": {}, "recoveries": [], "performance": {}}, {"testId": "test-1747635687722", "testName": "Unknown Test", "startTime": "2025-05-19T06:21:27.722Z", "endTime": "2025-05-19T06:21:27.741Z", "duration": 19, "events": [{"type": "test:start", "timestamp": "2025-05-19T06:21:27.722Z", "data": {"testId": "test-1747635687722", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:27.722Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:21:27.741Z", "data": {"testId": "test-1747635687722", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:27.741Z", "duration": 19, "error": null}}], "selectors": {}, "recoveries": [], "performance": {}}, {"testId": "test-1747635694032", "testName": "Unknown Test", "startTime": "2025-05-19T06:21:34.032Z", "endTime": "2025-05-19T06:21:34.046Z", "duration": 14, "events": [{"type": "test:start", "timestamp": "2025-05-19T06:21:34.032Z", "data": {"testId": "test-1747635694032", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:34.032Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:21:34.046Z", "data": {"testId": "test-1747635694032", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:34.046Z", "duration": 14, "error": null}}], "selectors": {}, "recoveries": [], "performance": {}}, {"testId": "test-1747635696558", "testName": "Unknown Test", "startTime": "2025-05-19T06:21:36.558Z", "endTime": "2025-05-19T06:21:36.569Z", "duration": 11, "events": [{"type": "test:start", "timestamp": "2025-05-19T06:21:36.558Z", "data": {"testId": "test-1747635696558", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:36.558Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:21:36.569Z", "data": {"testId": "test-1747635696558", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:36.569Z", "duration": 11, "error": null}}], "selectors": {}, "recoveries": [], "performance": {}}, {"testId": "test-1747635699101", "testName": "Unknown Test", "startTime": "2025-05-19T06:21:39.101Z", "endTime": "2025-05-19T06:21:39.112Z", "duration": 11, "events": [{"type": "test:start", "timestamp": "2025-05-19T06:21:39.101Z", "data": {"testId": "test-1747635699101", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:39.101Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:21:39.112Z", "data": {"testId": "test-1747635699101", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:39.112Z", "duration": 11, "error": null}}], "selectors": {}, "recoveries": [], "performance": {}}, {"testId": "test-1747635701764", "testName": "Unknown Test", "startTime": "2025-05-19T06:21:41.764Z", "endTime": "2025-05-19T06:21:41.775Z", "duration": 11, "events": [{"type": "test:start", "timestamp": "2025-05-19T06:21:41.764Z", "data": {"testId": "test-1747635701764", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:41.764Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:21:41.775Z", "data": {"testId": "test-1747635701764", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:41.775Z", "duration": 11, "error": null}}], "selectors": {}, "recoveries": [], "performance": {}}, {"testId": "test-1747635704340", "testName": "Unknown Test", "startTime": "2025-05-19T06:21:44.340Z", "endTime": "2025-05-19T06:21:44.350Z", "duration": 10, "events": [{"type": "test:start", "timestamp": "2025-05-19T06:21:44.340Z", "data": {"testId": "test-1747635704340", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:44.340Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:21:44.350Z", "data": {"testId": "test-1747635704340", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:44.350Z", "duration": 10, "error": null}}], "selectors": {}, "recoveries": [], "performance": {}}, {"testId": "test-1747635710678", "testName": "Unknown Test", "startTime": "2025-05-19T06:21:50.678Z", "endTime": "2025-05-19T06:21:50.698Z", "duration": 20, "events": [{"type": "test:start", "timestamp": "2025-05-19T06:21:50.678Z", "data": {"testId": "test-1747635710678", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:50.678Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:21:50.698Z", "data": {"testId": "test-1747635710678", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:50.698Z", "duration": 20, "error": null}}], "selectors": {}, "recoveries": [], "performance": {}}, {"testId": "test-1747635713078", "testName": "Unknown Test", "startTime": "2025-05-19T06:21:53.078Z", "endTime": "2025-05-19T06:21:53.097Z", "duration": 19, "events": [{"type": "test:start", "timestamp": "2025-05-19T06:21:53.078Z", "data": {"testId": "test-1747635713078", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:53.078Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:21:53.097Z", "data": {"testId": "test-1747635713078", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:53.097Z", "duration": 19, "error": null}}], "selectors": {}, "recoveries": [], "performance": {}}, {"testId": "test-1747635719390", "testName": "Unknown Test", "startTime": "2025-05-19T06:21:59.390Z", "endTime": "2025-05-19T06:22:01.020Z", "duration": 1630, "events": [{"type": "test:start", "timestamp": "2025-05-19T06:21:59.390Z", "data": {"testId": "test-1747635719390", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:59.390Z"}}, {"type": "selector:success", "timestamp": "2025-05-19T06:22:00.860Z", "data": {"testId": "test-1747635719390", "testName": "Unknown Test", "selector": "[data-test-id=\"SignInEmail\"]", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInEmail\"]", "<EMAIL>"]}}}, {"type": "selector:success", "timestamp": "2025-05-19T06:22:00.870Z", "data": {"testId": "test-1747635719390", "testName": "Unknown Test", "selector": "[data-test-id=\"SignInPassword\"]", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInPassword\"]", "vhc!tGK289IS&"]}}}, {"type": "test:end", "timestamp": "2025-05-19T06:22:01.020Z", "data": {"testId": "test-1747635719390", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:01.020Z", "duration": 1630, "error": null}}], "selectors": {"[data-test-id=\"SignInEmail\"]": {"selector": "[data-test-id=\"SignInEmail\"]", "attempts": 1, "successes": 1, "failures": 0, "successRate": 1, "events": [{"timestamp": "2025-05-19T06:22:00.860Z", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInEmail\"]", "<EMAIL>"]}}]}, "[data-test-id=\"SignInPassword\"]": {"selector": "[data-test-id=\"SignInPassword\"]", "attempts": 1, "successes": 1, "failures": 0, "successRate": 1, "events": [{"timestamp": "2025-05-19T06:22:00.870Z", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInPassword\"]", "vhc!tGK289IS&"]}}]}}, "recoveries": [], "performance": {}}, {"testId": "test-1747635723343", "testName": "Unknown Test", "startTime": "2025-05-19T06:22:03.343Z", "endTime": "2025-05-19T06:22:05.321Z", "duration": 1978, "events": [{"type": "test:start", "timestamp": "2025-05-19T06:22:03.343Z", "data": {"testId": "test-1747635723343", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:03.343Z"}}, {"type": "selector:success", "timestamp": "2025-05-19T06:22:05.156Z", "data": {"testId": "test-1747635723343", "testName": "Unknown Test", "selector": "[data-test-id=\"SignInEmail\"]", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInEmail\"]", "<EMAIL>"]}}}, {"type": "selector:success", "timestamp": "2025-05-19T06:22:05.165Z", "data": {"testId": "test-1747635723343", "testName": "Unknown Test", "selector": "[data-test-id=\"SignInPassword\"]", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInPassword\"]", "invalidpassword"]}}}, {"type": "test:end", "timestamp": "2025-05-19T06:22:05.321Z", "data": {"testId": "test-1747635723343", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:05.321Z", "duration": 1978, "error": null}}], "selectors": {"[data-test-id=\"SignInEmail\"]": {"selector": "[data-test-id=\"SignInEmail\"]", "attempts": 1, "successes": 1, "failures": 0, "successRate": 1, "events": [{"timestamp": "2025-05-19T06:22:05.156Z", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInEmail\"]", "<EMAIL>"]}}]}, "[data-test-id=\"SignInPassword\"]": {"selector": "[data-test-id=\"SignInPassword\"]", "attempts": 1, "successes": 1, "failures": 0, "successRate": 1, "events": [{"timestamp": "2025-05-19T06:22:05.165Z", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInPassword\"]", "invalidpassword"]}}]}}, "recoveries": [], "performance": {}}, {"testId": "test-1747635727625", "testName": "Unknown Test", "startTime": "2025-05-19T06:22:07.625Z", "endTime": "2025-05-19T06:22:09.074Z", "duration": 1449, "events": [{"type": "test:start", "timestamp": "2025-05-19T06:22:07.625Z", "data": {"testId": "test-1747635727625", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:07.625Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:22:09.074Z", "data": {"testId": "test-1747635727625", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:09.074Z", "duration": 1449, "error": null}}], "selectors": {}, "recoveries": [], "performance": {}}, {"testId": "test-1747635731445", "testName": "Unknown Test", "startTime": "2025-05-19T06:22:11.445Z", "endTime": "2025-05-19T06:22:13.073Z", "duration": 1628, "events": [{"type": "test:start", "timestamp": "2025-05-19T06:22:11.445Z", "data": {"testId": "test-1747635731445", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:11.445Z"}}, {"type": "selector:success", "timestamp": "2025-05-19T06:22:12.970Z", "data": {"testId": "test-1747635731445", "testName": "Unknown Test", "selector": "[data-test-id=\"SignInEmail\"]", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInEmail\"]", "<EMAIL>"]}}}, {"type": "selector:success", "timestamp": "2025-05-19T06:22:12.992Z", "data": {"testId": "test-1747635731445", "testName": "Unknown Test", "selector": "[data-test-id=\"SignInPassword\"]", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInPassword\"]", "vhc!tGK289IS&"]}}}, {"type": "test:end", "timestamp": "2025-05-19T06:22:13.073Z", "data": {"testId": "test-1747635731445", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:13.073Z", "duration": 1628, "error": null}}], "selectors": {"[data-test-id=\"SignInEmail\"]": {"selector": "[data-test-id=\"SignInEmail\"]", "attempts": 1, "successes": 1, "failures": 0, "successRate": 1, "events": [{"timestamp": "2025-05-19T06:22:12.970Z", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInEmail\"]", "<EMAIL>"]}}]}, "[data-test-id=\"SignInPassword\"]": {"selector": "[data-test-id=\"SignInPassword\"]", "attempts": 1, "successes": 1, "failures": 0, "successRate": 1, "events": [{"timestamp": "2025-05-19T06:22:12.992Z", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInPassword\"]", "vhc!tGK289IS&"]}}]}}, "recoveries": [], "performance": {}}, {"testId": "test-1747635741126", "testName": "Unknown Test", "startTime": "2025-05-19T06:22:21.126Z", "endTime": "2025-05-19T06:22:22.519Z", "duration": 1393, "events": [{"type": "test:start", "timestamp": "2025-05-19T06:22:21.126Z", "data": {"testId": "test-1747635741126", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:21.126Z"}}, {"type": "selector:success", "timestamp": "2025-05-19T06:22:22.506Z", "data": {"testId": "test-1747635741126", "testName": "Unknown Test", "selector": "[data-test-id=\"SignInEmail\"]", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInEmail\"]", "<EMAIL>"]}}}, {"type": "selector:success", "timestamp": "2025-05-19T06:22:22.516Z", "data": {"testId": "test-1747635741126", "testName": "Unknown Test", "selector": "[data-test-id=\"SignInPassword\"]", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInPassword\"]", "vhc!tGK289IS&"]}}}, {"type": "test:end", "timestamp": "2025-05-19T06:22:22.519Z", "data": {"testId": "test-1747635741126", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:22.519Z", "duration": 1393, "error": null}}], "selectors": {"[data-test-id=\"SignInEmail\"]": {"selector": "[data-test-id=\"SignInEmail\"]", "attempts": 1, "successes": 1, "failures": 0, "successRate": 1, "events": [{"timestamp": "2025-05-19T06:22:22.506Z", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInEmail\"]", "<EMAIL>"]}}]}, "[data-test-id=\"SignInPassword\"]": {"selector": "[data-test-id=\"SignInPassword\"]", "attempts": 1, "successes": 1, "failures": 0, "successRate": 1, "events": [{"timestamp": "2025-05-19T06:22:22.516Z", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInPassword\"]", "vhc!tGK289IS&"]}}]}}, "recoveries": [], "performance": {}}, {"testId": "test-1747635745116", "testName": "Unknown Test", "startTime": "2025-05-19T06:22:25.116Z", "endTime": "2025-05-19T06:22:26.509Z", "duration": 1393, "events": [{"type": "test:start", "timestamp": "2025-05-19T06:22:25.116Z", "data": {"testId": "test-1747635745116", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:25.116Z"}}, {"type": "selector:success", "timestamp": "2025-05-19T06:22:26.496Z", "data": {"testId": "test-1747635745116", "testName": "Unknown Test", "selector": "[data-test-id=\"SignInEmail\"]", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInEmail\"]", "<EMAIL>"]}}}, {"type": "selector:success", "timestamp": "2025-05-19T06:22:26.506Z", "data": {"testId": "test-1747635745116", "testName": "Unknown Test", "selector": "[data-test-id=\"SignInPassword\"]", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInPassword\"]", "vhc!tGK289IS&"]}}}, {"type": "test:end", "timestamp": "2025-05-19T06:22:26.509Z", "data": {"testId": "test-1747635745116", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:26.509Z", "duration": 1393, "error": null}}], "selectors": {"[data-test-id=\"SignInEmail\"]": {"selector": "[data-test-id=\"SignInEmail\"]", "attempts": 1, "successes": 1, "failures": 0, "successRate": 1, "events": [{"timestamp": "2025-05-19T06:22:26.496Z", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInEmail\"]", "<EMAIL>"]}}]}, "[data-test-id=\"SignInPassword\"]": {"selector": "[data-test-id=\"SignInPassword\"]", "attempts": 1, "successes": 1, "failures": 0, "successRate": 1, "events": [{"timestamp": "2025-05-19T06:22:26.506Z", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInPassword\"]", "vhc!tGK289IS&"]}}]}}, "recoveries": [], "performance": {}}, {"testId": "test-1747635748846", "testName": "Unknown Test", "startTime": "2025-05-19T06:22:28.846Z", "endTime": "2025-05-19T06:22:30.634Z", "duration": 1788, "events": [{"type": "test:start", "timestamp": "2025-05-19T06:22:28.846Z", "data": {"testId": "test-1747635748846", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:28.846Z"}}, {"type": "selector:success", "timestamp": "2025-05-19T06:22:30.620Z", "data": {"testId": "test-1747635748846", "testName": "Unknown Test", "selector": "[data-test-id=\"SignInEmail\"]", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInEmail\"]", "<EMAIL>"]}}}, {"type": "selector:success", "timestamp": "2025-05-19T06:22:30.630Z", "data": {"testId": "test-1747635748846", "testName": "Unknown Test", "selector": "[data-test-id=\"SignInPassword\"]", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInPassword\"]", "vhc!tGK289IS&"]}}}, {"type": "test:end", "timestamp": "2025-05-19T06:22:30.634Z", "data": {"testId": "test-1747635748846", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:30.634Z", "duration": 1788, "error": null}}], "selectors": {"[data-test-id=\"SignInEmail\"]": {"selector": "[data-test-id=\"SignInEmail\"]", "attempts": 1, "successes": 1, "failures": 0, "successRate": 1, "events": [{"timestamp": "2025-05-19T06:22:30.620Z", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInEmail\"]", "<EMAIL>"]}}]}, "[data-test-id=\"SignInPassword\"]": {"selector": "[data-test-id=\"SignInPassword\"]", "attempts": 1, "successes": 1, "failures": 0, "successRate": 1, "events": [{"timestamp": "2025-05-19T06:22:30.630Z", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInPassword\"]", "vhc!tGK289IS&"]}}]}}, "recoveries": [], "performance": {}}, {"testId": "test-1747635756821", "testName": "Unknown Test", "startTime": "2025-05-19T06:22:36.821Z", "endTime": "2025-05-19T06:22:38.301Z", "duration": 1480, "events": [{"type": "test:start", "timestamp": "2025-05-19T06:22:36.821Z", "data": {"testId": "test-1747635756821", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:36.821Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:22:38.301Z", "data": {"testId": "test-1747635756821", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:38.301Z", "duration": 1480, "error": null}}], "selectors": {}, "recoveries": [], "performance": {}}, {"testId": "test-1747635760619", "testName": "Unknown Test", "startTime": "2025-05-19T06:22:40.619Z", "endTime": "2025-05-19T06:22:42.105Z", "duration": 1486, "events": [{"type": "test:start", "timestamp": "2025-05-19T06:22:40.619Z", "data": {"testId": "test-1747635760619", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:40.619Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:22:42.105Z", "data": {"testId": "test-1747635760619", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:42.105Z", "duration": 1486, "error": null}}], "selectors": {}, "recoveries": [], "performance": {}}, {"testId": "test-1747635764723", "testName": "Unknown Test", "startTime": "2025-05-19T06:22:44.723Z", "endTime": "2025-05-19T06:22:46.275Z", "duration": 1552, "events": [{"type": "test:start", "timestamp": "2025-05-19T06:22:44.723Z", "data": {"testId": "test-1747635764723", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:44.723Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:22:46.275Z", "data": {"testId": "test-1747635764723", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:46.275Z", "duration": 1552, "error": null}}], "selectors": {}, "recoveries": [], "performance": {}}, {"testId": "test-1747635772533", "testName": "Unknown Test", "startTime": "2025-05-19T06:22:52.533Z", "endTime": "2025-05-19T06:22:56.391Z", "duration": 3858, "events": [{"type": "test:start", "timestamp": "2025-05-19T06:22:52.533Z", "data": {"testId": "test-1747635772533", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:52.533Z"}}, {"type": "selector:success", "timestamp": "2025-05-19T06:22:56.319Z", "data": {"testId": "test-1747635772533", "testName": "Unknown Test", "selector": "#delayed-button", "success": true, "context": {"action": "click", "args": ["#delayed-button"]}}}, {"type": "test:end", "timestamp": "2025-05-19T06:22:56.391Z", "data": {"testId": "test-1747635772533", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:56.391Z", "duration": 3858, "error": null}}], "selectors": {"#delayed-button": {"selector": "#delayed-button", "attempts": 1, "successes": 1, "failures": 0, "successRate": 1, "events": [{"timestamp": "2025-05-19T06:22:56.319Z", "success": true, "context": {"action": "click", "args": ["#delayed-button"]}}]}}, "recoveries": [], "performance": {}}, {"testId": "test-1747635778747", "testName": "Unknown Test", "startTime": "2025-05-19T06:22:58.747Z", "endTime": "2025-05-19T06:23:02.688Z", "duration": 3941, "events": [{"type": "test:start", "timestamp": "2025-05-19T06:22:58.747Z", "data": {"testId": "test-1747635778747", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:58.747Z"}}, {"type": "selector:success", "timestamp": "2025-05-19T06:23:02.617Z", "data": {"testId": "test-1747635778747", "testName": "Unknown Test", "selector": "#delayed-button-wait", "success": true, "context": {"action": "click", "args": ["#delayed-button-wait"]}}}, {"type": "test:end", "timestamp": "2025-05-19T06:23:02.688Z", "data": {"testId": "test-1747635778747", "testName": "Unknown Test", "timestamp": "2025-05-19T06:23:02.688Z", "duration": 3941, "error": null}}], "selectors": {"#delayed-button-wait": {"selector": "#delayed-button-wait", "attempts": 1, "successes": 1, "failures": 0, "successRate": 1, "events": [{"timestamp": "2025-05-19T06:23:02.617Z", "success": true, "context": {"action": "click", "args": ["#delayed-button-wait"]}}]}}, "recoveries": [], "performance": {}}, {"testId": "test-1747635784905", "testName": "Unknown Test", "startTime": "2025-05-19T06:23:04.905Z", "endTime": "2025-05-19T06:23:34.789Z", "duration": 29884, "events": [{"type": "test:start", "timestamp": "2025-05-19T06:23:04.905Z", "data": {"testId": "test-1747635784905", "testName": "Unknown Test", "timestamp": "2025-05-19T06:23:04.905Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:23:34.789Z", "data": {"testId": "test-1747635784905", "testName": "Unknown Test", "timestamp": "2025-05-19T06:23:34.789Z", "duration": 29884, "error": null}}], "selectors": {}, "recoveries": [], "performance": {}}, {"testId": "test-1747635820753", "testName": "Unknown Test", "startTime": "2025-05-19T06:23:40.753Z", "endTime": "2025-05-19T06:23:42.302Z", "duration": 1549, "events": [{"type": "test:start", "timestamp": "2025-05-19T06:23:40.753Z", "data": {"testId": "test-1747635820753", "testName": "Unknown Test", "timestamp": "2025-05-19T06:23:40.753Z"}}, {"type": "selector:success", "timestamp": "2025-05-19T06:23:42.220Z", "data": {"testId": "test-1747635820753", "testName": "Unknown Test", "selector": "[data-test-id=\"SignInEmail\"]", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInEmail\"]", "<EMAIL>"]}}}, {"type": "selector:success", "timestamp": "2025-05-19T06:23:42.229Z", "data": {"testId": "test-1747635820753", "testName": "Unknown Test", "selector": "[data-test-id=\"SignInPassword\"]", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInPassword\"]", "vhc!tGK289IS&"]}}}, {"type": "test:end", "timestamp": "2025-05-19T06:23:42.302Z", "data": {"testId": "test-1747635820753", "testName": "Unknown Test", "timestamp": "2025-05-19T06:23:42.302Z", "duration": 1549, "error": null}}], "selectors": {"[data-test-id=\"SignInEmail\"]": {"selector": "[data-test-id=\"SignInEmail\"]", "attempts": 1, "successes": 1, "failures": 0, "successRate": 1, "events": [{"timestamp": "2025-05-19T06:23:42.220Z", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInEmail\"]", "<EMAIL>"]}}]}, "[data-test-id=\"SignInPassword\"]": {"selector": "[data-test-id=\"SignInPassword\"]", "attempts": 1, "successes": 1, "failures": 0, "successRate": 1, "events": [{"timestamp": "2025-05-19T06:23:42.229Z", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInPassword\"]", "vhc!tGK289IS&"]}}]}}, "recoveries": [], "performance": {}}, {"testId": "test-1747635824682", "testName": "Unknown Test", "startTime": "2025-05-19T06:23:44.682Z", "endTime": "2025-05-19T06:23:46.206Z", "duration": 1524, "events": [{"type": "test:start", "timestamp": "2025-05-19T06:23:44.682Z", "data": {"testId": "test-1747635824682", "testName": "Unknown Test", "timestamp": "2025-05-19T06:23:44.682Z"}}, {"type": "selector:success", "timestamp": "2025-05-19T06:23:46.107Z", "data": {"testId": "test-1747635824682", "testName": "Unknown Test", "selector": "[data-test-id=\"SignInEmail\"]", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInEmail\"]", "<EMAIL>"]}}}, {"type": "selector:success", "timestamp": "2025-05-19T06:23:46.116Z", "data": {"testId": "test-1747635824682", "testName": "Unknown Test", "selector": "[data-test-id=\"SignInPassword\"]", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInPassword\"]", "vhc!tGK289IS&"]}}}, {"type": "test:end", "timestamp": "2025-05-19T06:23:46.206Z", "data": {"testId": "test-1747635824682", "testName": "Unknown Test", "timestamp": "2025-05-19T06:23:46.206Z", "duration": 1524, "error": null}}], "selectors": {"[data-test-id=\"SignInEmail\"]": {"selector": "[data-test-id=\"SignInEmail\"]", "attempts": 1, "successes": 1, "failures": 0, "successRate": 1, "events": [{"timestamp": "2025-05-19T06:23:46.107Z", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInEmail\"]", "<EMAIL>"]}}]}, "[data-test-id=\"SignInPassword\"]": {"selector": "[data-test-id=\"SignInPassword\"]", "attempts": 1, "successes": 1, "failures": 0, "successRate": 1, "events": [{"timestamp": "2025-05-19T06:23:46.116Z", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInPassword\"]", "vhc!tGK289IS&"]}}]}}, "recoveries": [], "performance": {}}, {"testId": "test-1747635828582", "testName": "Unknown Test", "startTime": "2025-05-19T06:23:48.582Z", "endTime": "2025-05-19T06:23:50.220Z", "duration": 1638, "events": [{"type": "test:start", "timestamp": "2025-05-19T06:23:48.582Z", "data": {"testId": "test-1747635828582", "testName": "Unknown Test", "timestamp": "2025-05-19T06:23:48.582Z"}}, {"type": "selector:success", "timestamp": "2025-05-19T06:23:50.121Z", "data": {"testId": "test-1747635828582", "testName": "Unknown Test", "selector": "[data-test-id=\"SignInEmail\"]", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInEmail\"]", "<EMAIL>"]}}}, {"type": "selector:success", "timestamp": "2025-05-19T06:23:50.131Z", "data": {"testId": "test-1747635828582", "testName": "Unknown Test", "selector": "[data-test-id=\"SignInPassword\"]", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInPassword\"]", "vhc!tGK289IS&"]}}}, {"type": "test:end", "timestamp": "2025-05-19T06:23:50.220Z", "data": {"testId": "test-1747635828582", "testName": "Unknown Test", "timestamp": "2025-05-19T06:23:50.220Z", "duration": 1638, "error": null}}], "selectors": {"[data-test-id=\"SignInEmail\"]": {"selector": "[data-test-id=\"SignInEmail\"]", "attempts": 1, "successes": 1, "failures": 0, "successRate": 1, "events": [{"timestamp": "2025-05-19T06:23:50.121Z", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInEmail\"]", "<EMAIL>"]}}]}, "[data-test-id=\"SignInPassword\"]": {"selector": "[data-test-id=\"SignInPassword\"]", "attempts": 1, "successes": 1, "failures": 0, "successRate": 1, "events": [{"timestamp": "2025-05-19T06:23:50.131Z", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInPassword\"]", "vhc!tGK289IS&"]}}]}}, "recoveries": [], "performance": {}}, {"testId": "test-1747635836182", "testName": "Unknown Test", "startTime": "2025-05-19T06:23:56.182Z", "endTime": "2025-05-19T06:23:56.300Z", "duration": 118, "events": [{"type": "test:start", "timestamp": "2025-05-19T06:23:56.182Z", "data": {"testId": "test-1747635836182", "testName": "Unknown Test", "timestamp": "2025-05-19T06:23:56.182Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:23:56.300Z", "data": {"testId": "test-1747635836182", "testName": "Unknown Test", "timestamp": "2025-05-19T06:23:56.300Z", "duration": 118, "error": null}}], "selectors": {}, "recoveries": [], "performance": {}}, {"testId": "test-1747635838505", "testName": "Unknown Test", "startTime": "2025-05-19T06:23:58.505Z", "endTime": "2025-05-19T06:23:59.935Z", "duration": 1430, "events": [{"type": "test:start", "timestamp": "2025-05-19T06:23:58.505Z", "data": {"testId": "test-1747635838505", "testName": "Unknown Test", "timestamp": "2025-05-19T06:23:58.505Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:23:59.935Z", "data": {"testId": "test-1747635838505", "testName": "Unknown Test", "timestamp": "2025-05-19T06:23:59.935Z", "duration": 1430, "error": null}}], "selectors": {}, "recoveries": [], "performance": {}}, {"testId": "test-1747635842187", "testName": "Unknown Test", "startTime": "2025-05-19T06:24:02.187Z", "endTime": "2025-05-19T06:24:02.315Z", "duration": 128, "events": [{"type": "test:start", "timestamp": "2025-05-19T06:24:02.187Z", "data": {"testId": "test-1747635842187", "testName": "Unknown Test", "timestamp": "2025-05-19T06:24:02.187Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:24:02.315Z", "data": {"testId": "test-1747635842187", "testName": "Unknown Test", "timestamp": "2025-05-19T06:24:02.315Z", "duration": 128, "error": null}}], "selectors": {}, "recoveries": [], "performance": {}}], "events": [{"type": "test:start", "timestamp": "2025-05-19T06:21:01.692Z", "data": {"testId": "test-1747635661692", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:01.692Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:21:01.795Z", "data": {"testId": "test-1747635661692", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:01.795Z", "duration": 103, "error": null}}, {"type": "test:start", "timestamp": "2025-05-19T06:21:04.300Z", "data": {"testId": "test-1747635664300", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:04.300Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:21:04.400Z", "data": {"testId": "test-1747635664300", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:04.400Z", "duration": 100, "error": null}}, {"type": "test:start", "timestamp": "2025-05-19T06:21:06.765Z", "data": {"testId": "test-1747635666765", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:06.765Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:21:06.772Z", "data": {"testId": "test-1747635666765", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:06.772Z", "duration": 7, "error": null}}, {"type": "test:start", "timestamp": "2025-05-19T06:21:09.282Z", "data": {"testId": "test-1747635669282", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:09.282Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:21:09.324Z", "data": {"testId": "test-1747635669282", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:09.324Z", "duration": 42, "error": null}}, {"type": "test:start", "timestamp": "2025-05-19T06:21:17.167Z", "data": {"testId": "test-1747635677167", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:17.167Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:21:17.188Z", "data": {"testId": "test-1747635677167", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:17.188Z", "duration": 21, "error": null}}, {"type": "test:start", "timestamp": "2025-05-19T06:21:19.799Z", "data": {"testId": "test-1747635679799", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:19.799Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:21:19.822Z", "data": {"testId": "test-1747635679799", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:19.822Z", "duration": 23, "error": null}}, {"type": "test:start", "timestamp": "2025-05-19T06:21:22.433Z", "data": {"testId": "test-1747635682433", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:22.433Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:21:22.451Z", "data": {"testId": "test-1747635682433", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:22.451Z", "duration": 18, "error": null}}, {"type": "test:start", "timestamp": "2025-05-19T06:21:25.030Z", "data": {"testId": "test-1747635685030", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:25.030Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:21:25.050Z", "data": {"testId": "test-1747635685030", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:25.050Z", "duration": 20, "error": null}}, {"type": "test:start", "timestamp": "2025-05-19T06:21:27.722Z", "data": {"testId": "test-1747635687722", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:27.722Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:21:27.741Z", "data": {"testId": "test-1747635687722", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:27.741Z", "duration": 19, "error": null}}, {"type": "test:start", "timestamp": "2025-05-19T06:21:34.032Z", "data": {"testId": "test-1747635694032", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:34.032Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:21:34.046Z", "data": {"testId": "test-1747635694032", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:34.046Z", "duration": 14, "error": null}}, {"type": "test:start", "timestamp": "2025-05-19T06:21:36.558Z", "data": {"testId": "test-1747635696558", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:36.558Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:21:36.569Z", "data": {"testId": "test-1747635696558", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:36.569Z", "duration": 11, "error": null}}, {"type": "test:start", "timestamp": "2025-05-19T06:21:39.101Z", "data": {"testId": "test-1747635699101", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:39.101Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:21:39.112Z", "data": {"testId": "test-1747635699101", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:39.112Z", "duration": 11, "error": null}}, {"type": "test:start", "timestamp": "2025-05-19T06:21:41.764Z", "data": {"testId": "test-1747635701764", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:41.764Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:21:41.775Z", "data": {"testId": "test-1747635701764", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:41.775Z", "duration": 11, "error": null}}, {"type": "test:start", "timestamp": "2025-05-19T06:21:44.340Z", "data": {"testId": "test-1747635704340", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:44.340Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:21:44.350Z", "data": {"testId": "test-1747635704340", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:44.350Z", "duration": 10, "error": null}}, {"type": "test:start", "timestamp": "2025-05-19T06:21:50.678Z", "data": {"testId": "test-1747635710678", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:50.678Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:21:50.698Z", "data": {"testId": "test-1747635710678", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:50.698Z", "duration": 20, "error": null}}, {"type": "test:start", "timestamp": "2025-05-19T06:21:53.078Z", "data": {"testId": "test-1747635713078", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:53.078Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:21:53.097Z", "data": {"testId": "test-1747635713078", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:53.097Z", "duration": 19, "error": null}}, {"type": "test:start", "timestamp": "2025-05-19T06:21:59.390Z", "data": {"testId": "test-1747635719390", "testName": "Unknown Test", "timestamp": "2025-05-19T06:21:59.390Z"}}, {"type": "selector:success", "timestamp": "2025-05-19T06:22:00.860Z", "data": {"testId": "test-1747635719390", "testName": "Unknown Test", "selector": "[data-test-id=\"SignInEmail\"]", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInEmail\"]", "<EMAIL>"]}}}, {"type": "selector:success", "timestamp": "2025-05-19T06:22:00.870Z", "data": {"testId": "test-1747635719390", "testName": "Unknown Test", "selector": "[data-test-id=\"SignInPassword\"]", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInPassword\"]", "vhc!tGK289IS&"]}}}, {"type": "test:end", "timestamp": "2025-05-19T06:22:01.020Z", "data": {"testId": "test-1747635719390", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:01.020Z", "duration": 1630, "error": null}}, {"type": "test:start", "timestamp": "2025-05-19T06:22:03.343Z", "data": {"testId": "test-1747635723343", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:03.343Z"}}, {"type": "selector:success", "timestamp": "2025-05-19T06:22:05.156Z", "data": {"testId": "test-1747635723343", "testName": "Unknown Test", "selector": "[data-test-id=\"SignInEmail\"]", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInEmail\"]", "<EMAIL>"]}}}, {"type": "selector:success", "timestamp": "2025-05-19T06:22:05.165Z", "data": {"testId": "test-1747635723343", "testName": "Unknown Test", "selector": "[data-test-id=\"SignInPassword\"]", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInPassword\"]", "invalidpassword"]}}}, {"type": "test:end", "timestamp": "2025-05-19T06:22:05.321Z", "data": {"testId": "test-1747635723343", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:05.321Z", "duration": 1978, "error": null}}, {"type": "test:start", "timestamp": "2025-05-19T06:22:07.625Z", "data": {"testId": "test-1747635727625", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:07.625Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:22:09.074Z", "data": {"testId": "test-1747635727625", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:09.074Z", "duration": 1449, "error": null}}, {"type": "test:start", "timestamp": "2025-05-19T06:22:11.445Z", "data": {"testId": "test-1747635731445", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:11.445Z"}}, {"type": "selector:success", "timestamp": "2025-05-19T06:22:12.970Z", "data": {"testId": "test-1747635731445", "testName": "Unknown Test", "selector": "[data-test-id=\"SignInEmail\"]", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInEmail\"]", "<EMAIL>"]}}}, {"type": "selector:success", "timestamp": "2025-05-19T06:22:12.992Z", "data": {"testId": "test-1747635731445", "testName": "Unknown Test", "selector": "[data-test-id=\"SignInPassword\"]", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInPassword\"]", "vhc!tGK289IS&"]}}}, {"type": "test:end", "timestamp": "2025-05-19T06:22:13.073Z", "data": {"testId": "test-1747635731445", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:13.073Z", "duration": 1628, "error": null}}, {"type": "test:start", "timestamp": "2025-05-19T06:22:21.126Z", "data": {"testId": "test-1747635741126", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:21.126Z"}}, {"type": "selector:success", "timestamp": "2025-05-19T06:22:22.506Z", "data": {"testId": "test-1747635741126", "testName": "Unknown Test", "selector": "[data-test-id=\"SignInEmail\"]", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInEmail\"]", "<EMAIL>"]}}}, {"type": "selector:success", "timestamp": "2025-05-19T06:22:22.516Z", "data": {"testId": "test-1747635741126", "testName": "Unknown Test", "selector": "[data-test-id=\"SignInPassword\"]", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInPassword\"]", "vhc!tGK289IS&"]}}}, {"type": "test:end", "timestamp": "2025-05-19T06:22:22.519Z", "data": {"testId": "test-1747635741126", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:22.519Z", "duration": 1393, "error": null}}, {"type": "test:start", "timestamp": "2025-05-19T06:22:25.116Z", "data": {"testId": "test-1747635745116", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:25.116Z"}}, {"type": "selector:success", "timestamp": "2025-05-19T06:22:26.496Z", "data": {"testId": "test-1747635745116", "testName": "Unknown Test", "selector": "[data-test-id=\"SignInEmail\"]", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInEmail\"]", "<EMAIL>"]}}}, {"type": "selector:success", "timestamp": "2025-05-19T06:22:26.506Z", "data": {"testId": "test-1747635745116", "testName": "Unknown Test", "selector": "[data-test-id=\"SignInPassword\"]", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInPassword\"]", "vhc!tGK289IS&"]}}}, {"type": "test:end", "timestamp": "2025-05-19T06:22:26.509Z", "data": {"testId": "test-1747635745116", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:26.509Z", "duration": 1393, "error": null}}, {"type": "test:start", "timestamp": "2025-05-19T06:22:28.846Z", "data": {"testId": "test-1747635748846", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:28.846Z"}}, {"type": "selector:success", "timestamp": "2025-05-19T06:22:30.620Z", "data": {"testId": "test-1747635748846", "testName": "Unknown Test", "selector": "[data-test-id=\"SignInEmail\"]", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInEmail\"]", "<EMAIL>"]}}}, {"type": "selector:success", "timestamp": "2025-05-19T06:22:30.630Z", "data": {"testId": "test-1747635748846", "testName": "Unknown Test", "selector": "[data-test-id=\"SignInPassword\"]", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInPassword\"]", "vhc!tGK289IS&"]}}}, {"type": "test:end", "timestamp": "2025-05-19T06:22:30.634Z", "data": {"testId": "test-1747635748846", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:30.634Z", "duration": 1788, "error": null}}, {"type": "test:start", "timestamp": "2025-05-19T06:22:36.821Z", "data": {"testId": "test-1747635756821", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:36.821Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:22:38.301Z", "data": {"testId": "test-1747635756821", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:38.301Z", "duration": 1480, "error": null}}, {"type": "test:start", "timestamp": "2025-05-19T06:22:40.619Z", "data": {"testId": "test-1747635760619", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:40.619Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:22:42.105Z", "data": {"testId": "test-1747635760619", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:42.105Z", "duration": 1486, "error": null}}, {"type": "test:start", "timestamp": "2025-05-19T06:22:44.723Z", "data": {"testId": "test-1747635764723", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:44.723Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:22:46.275Z", "data": {"testId": "test-1747635764723", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:46.275Z", "duration": 1552, "error": null}}, {"type": "test:start", "timestamp": "2025-05-19T06:22:52.533Z", "data": {"testId": "test-1747635772533", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:52.533Z"}}, {"type": "selector:success", "timestamp": "2025-05-19T06:22:56.319Z", "data": {"testId": "test-1747635772533", "testName": "Unknown Test", "selector": "#delayed-button", "success": true, "context": {"action": "click", "args": ["#delayed-button"]}}}, {"type": "test:end", "timestamp": "2025-05-19T06:22:56.391Z", "data": {"testId": "test-1747635772533", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:56.391Z", "duration": 3858, "error": null}}, {"type": "test:start", "timestamp": "2025-05-19T06:22:58.747Z", "data": {"testId": "test-1747635778747", "testName": "Unknown Test", "timestamp": "2025-05-19T06:22:58.747Z"}}, {"type": "selector:success", "timestamp": "2025-05-19T06:23:02.617Z", "data": {"testId": "test-1747635778747", "testName": "Unknown Test", "selector": "#delayed-button-wait", "success": true, "context": {"action": "click", "args": ["#delayed-button-wait"]}}}, {"type": "test:end", "timestamp": "2025-05-19T06:23:02.688Z", "data": {"testId": "test-1747635778747", "testName": "Unknown Test", "timestamp": "2025-05-19T06:23:02.688Z", "duration": 3941, "error": null}}, {"type": "test:start", "timestamp": "2025-05-19T06:23:04.905Z", "data": {"testId": "test-1747635784905", "testName": "Unknown Test", "timestamp": "2025-05-19T06:23:04.905Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:23:34.789Z", "data": {"testId": "test-1747635784905", "testName": "Unknown Test", "timestamp": "2025-05-19T06:23:34.789Z", "duration": 29884, "error": null}}, {"type": "test:start", "timestamp": "2025-05-19T06:23:40.753Z", "data": {"testId": "test-1747635820753", "testName": "Unknown Test", "timestamp": "2025-05-19T06:23:40.753Z"}}, {"type": "selector:success", "timestamp": "2025-05-19T06:23:42.220Z", "data": {"testId": "test-1747635820753", "testName": "Unknown Test", "selector": "[data-test-id=\"SignInEmail\"]", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInEmail\"]", "<EMAIL>"]}}}, {"type": "selector:success", "timestamp": "2025-05-19T06:23:42.229Z", "data": {"testId": "test-1747635820753", "testName": "Unknown Test", "selector": "[data-test-id=\"SignInPassword\"]", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInPassword\"]", "vhc!tGK289IS&"]}}}, {"type": "test:end", "timestamp": "2025-05-19T06:23:42.302Z", "data": {"testId": "test-1747635820753", "testName": "Unknown Test", "timestamp": "2025-05-19T06:23:42.302Z", "duration": 1549, "error": null}}, {"type": "test:start", "timestamp": "2025-05-19T06:23:44.682Z", "data": {"testId": "test-1747635824682", "testName": "Unknown Test", "timestamp": "2025-05-19T06:23:44.682Z"}}, {"type": "selector:success", "timestamp": "2025-05-19T06:23:46.107Z", "data": {"testId": "test-1747635824682", "testName": "Unknown Test", "selector": "[data-test-id=\"SignInEmail\"]", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInEmail\"]", "<EMAIL>"]}}}, {"type": "selector:success", "timestamp": "2025-05-19T06:23:46.116Z", "data": {"testId": "test-1747635824682", "testName": "Unknown Test", "selector": "[data-test-id=\"SignInPassword\"]", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInPassword\"]", "vhc!tGK289IS&"]}}}, {"type": "test:end", "timestamp": "2025-05-19T06:23:46.206Z", "data": {"testId": "test-1747635824682", "testName": "Unknown Test", "timestamp": "2025-05-19T06:23:46.206Z", "duration": 1524, "error": null}}, {"type": "test:start", "timestamp": "2025-05-19T06:23:48.582Z", "data": {"testId": "test-1747635828582", "testName": "Unknown Test", "timestamp": "2025-05-19T06:23:48.582Z"}}, {"type": "selector:success", "timestamp": "2025-05-19T06:23:50.121Z", "data": {"testId": "test-1747635828582", "testName": "Unknown Test", "selector": "[data-test-id=\"SignInEmail\"]", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInEmail\"]", "<EMAIL>"]}}}, {"type": "selector:success", "timestamp": "2025-05-19T06:23:50.131Z", "data": {"testId": "test-1747635828582", "testName": "Unknown Test", "selector": "[data-test-id=\"SignInPassword\"]", "success": true, "context": {"action": "fill", "args": ["[data-test-id=\"SignInPassword\"]", "vhc!tGK289IS&"]}}}, {"type": "test:end", "timestamp": "2025-05-19T06:23:50.220Z", "data": {"testId": "test-1747635828582", "testName": "Unknown Test", "timestamp": "2025-05-19T06:23:50.220Z", "duration": 1638, "error": null}}, {"type": "test:start", "timestamp": "2025-05-19T06:23:56.182Z", "data": {"testId": "test-1747635836182", "testName": "Unknown Test", "timestamp": "2025-05-19T06:23:56.182Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:23:56.300Z", "data": {"testId": "test-1747635836182", "testName": "Unknown Test", "timestamp": "2025-05-19T06:23:56.300Z", "duration": 118, "error": null}}, {"type": "test:start", "timestamp": "2025-05-19T06:23:58.505Z", "data": {"testId": "test-1747635838505", "testName": "Unknown Test", "timestamp": "2025-05-19T06:23:58.505Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:23:59.935Z", "data": {"testId": "test-1747635838505", "testName": "Unknown Test", "timestamp": "2025-05-19T06:23:59.935Z", "duration": 1430, "error": null}}, {"type": "test:start", "timestamp": "2025-05-19T06:24:02.187Z", "data": {"testId": "test-1747635842187", "testName": "Unknown Test", "timestamp": "2025-05-19T06:24:02.187Z"}}, {"type": "test:end", "timestamp": "2025-05-19T06:24:02.315Z", "data": {"testId": "test-1747635842187", "testName": "Unknown Test", "timestamp": "2025-05-19T06:24:02.315Z", "duration": 128, "error": null}}], "selectors": {"[data-test-id=\"SignInEmail\"]": {"selector": "[data-test-id=\"SignInEmail\"]", "attempts": 9, "successes": 9, "failures": 0, "successRate": 1, "testRuns": [{"testId": "test-1747635719390", "timestamp": "2025-05-19T06:22:01.020Z"}, {"testId": "test-1747635723343", "timestamp": "2025-05-19T06:22:05.321Z"}, {"testId": "test-1747635731445", "timestamp": "2025-05-19T06:22:13.073Z"}, {"testId": "test-1747635741126", "timestamp": "2025-05-19T06:22:22.519Z"}, {"testId": "test-1747635745116", "timestamp": "2025-05-19T06:22:26.509Z"}, {"testId": "test-1747635748846", "timestamp": "2025-05-19T06:22:30.634Z"}, {"testId": "test-1747635820753", "timestamp": "2025-05-19T06:23:42.302Z"}, {"testId": "test-1747635824682", "timestamp": "2025-05-19T06:23:46.206Z"}, {"testId": "test-1747635828582", "timestamp": "2025-05-19T06:23:50.220Z"}]}, "[data-test-id=\"SignInPassword\"]": {"selector": "[data-test-id=\"SignInPassword\"]", "attempts": 9, "successes": 9, "failures": 0, "successRate": 1, "testRuns": [{"testId": "test-1747635719390", "timestamp": "2025-05-19T06:22:01.020Z"}, {"testId": "test-1747635723343", "timestamp": "2025-05-19T06:22:05.321Z"}, {"testId": "test-1747635731445", "timestamp": "2025-05-19T06:22:13.073Z"}, {"testId": "test-1747635741126", "timestamp": "2025-05-19T06:22:22.519Z"}, {"testId": "test-1747635745116", "timestamp": "2025-05-19T06:22:26.509Z"}, {"testId": "test-1747635748846", "timestamp": "2025-05-19T06:22:30.634Z"}, {"testId": "test-1747635820753", "timestamp": "2025-05-19T06:23:42.302Z"}, {"testId": "test-1747635824682", "timestamp": "2025-05-19T06:23:46.206Z"}, {"testId": "test-1747635828582", "timestamp": "2025-05-19T06:23:50.220Z"}]}, "#delayed-button": {"selector": "#delayed-button", "attempts": 1, "successes": 1, "failures": 0, "successRate": 1, "testRuns": [{"testId": "test-1747635772533", "timestamp": "2025-05-19T06:22:56.391Z"}]}, "#delayed-button-wait": {"selector": "#delayed-button-wait", "attempts": 1, "successes": 1, "failures": 0, "successRate": 1, "testRuns": [{"testId": "test-1747635778747", "timestamp": "2025-05-19T06:23:02.688Z"}]}}, "recoveries": {}, "performance": {}}