/**
 * AAA Validator Script
 *
 * This script analyzes test files and validates their AAA compliance.
 */

const fs = require('fs');
const path = require('path');
const { validateAAACompliance } = require('./tests/utils/aaa-validator');

// Configuration
const config = {
  testDir: './tests',
  reportDir: './aaa-reports',
  threshold: 0.3, // 30% compliance threshold (adjusted for initial implementation)
  excludeDirs: ['node_modules', 'dist', 'build', 'coverage'],
  testFilePattern: /\.spec\.js$/
};

// Create report directory if it doesn't exist
if (!fs.existsSync(config.reportDir)) {
  fs.mkdirSync(config.reportDir, { recursive: true });
}

// Find all test files
function findTestFiles(dir) {
  const files = [];

  function traverse(currentDir) {
    const entries = fs.readdirSync(currentDir, { withFileTypes: true });

    for (const entry of entries) {
      const fullPath = path.join(currentDir, entry.name);

      if (entry.isDirectory()) {
        if (!config.excludeDirs.includes(entry.name)) {
          traverse(fullPath);
        }
      } else if (config.testFilePattern.test(entry.name)) {
        files.push(fullPath);
      }
    }
  }

  traverse(dir);
  return files;
}

// Validate AAA compliance for a test file
function validateFile(filePath) {
  console.log(`Validating ${filePath}...`);

  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const results = validateAAACompliance(content);

    return {
      filePath,
      results
    };
  } catch (error) {
    console.error(`Error validating ${filePath}:`, error);

    return {
      filePath,
      error: error.message
    };
  }
}

// Generate report
function generateReport(validationResults) {
  const report = {
    timestamp: new Date().toISOString(),
    summary: {
      totalFiles: validationResults.length,
      passedFiles: validationResults.filter(result => result.results && result.results.passesThreshold).length,
      failedFiles: validationResults.filter(result => result.results && !result.results.passesThreshold).length,
      errorFiles: validationResults.filter(result => result.error).length,
      overallScore: validationResults.reduce((sum, result) => {
        if (result.results) {
          return sum + result.results.overallScore;
        }
        return sum;
      }, 0) / validationResults.length
    },
    results: validationResults
  };

  // Write the report to a file
  const reportPath = path.join(config.reportDir, `aaa-report-${new Date().toISOString().replace(/[:.]/g, '-')}.json`);
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));

  console.log(`Report generated: ${reportPath}`);

  return report;
}

// Generate HTML report
function generateHtmlReport(report) {
  const html = `
<!DOCTYPE html>
<html>
<head>
  <title>AAA Compliance Report</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
    }

    h1, h2, h3 {
      margin-top: 0;
    }

    .summary {
      background-color: #f5f5f5;
      border-radius: 5px;
      padding: 15px;
      margin-bottom: 20px;
    }

    .overall-score {
      font-size: 24px;
      font-weight: bold;
    }

    .test-results {
      margin-top: 20px;
    }

    table {
      width: 100%;
      border-collapse: collapse;
    }

    th, td {
      padding: 8px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }

    th {
      background-color: #f2f2f2;
    }

    .passed {
      color: green;
    }

    .failed {
      color: red;
    }

    .error {
      color: orange;
    }

    .recommendations {
      margin-top: 10px;
      padding: 10px;
      background-color: #f9f9f9;
      border-left: 3px solid #ccc;
    }

    .file-details {
      margin-top: 20px;
      padding: 10px;
      background-color: #f9f9f9;
      border-radius: 5px;
    }

    .file-details h3 {
      margin-top: 0;
    }

    .aspect-score {
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
    }

    .aspect-score .score-bar {
      flex-grow: 1;
      margin: 0 10px;
      height: 10px;
      background-color: #eee;
      border-radius: 5px;
      overflow: hidden;
    }

    .aspect-score .score-bar .score-fill {
      height: 100%;
      background-color: #4CAF50;
    }
  </style>
</head>
<body>
  <h1>AAA Compliance Report</h1>
  <p>Generated: ${new Date(report.timestamp).toLocaleString()}</p>

  <div class="summary">
    <h2>Summary</h2>
    <p class="overall-score">Overall Score: ${(report.summary.overallScore * 100).toFixed(2)}%</p>
    <p>Total Files: ${report.summary.totalFiles}</p>
    <p>Passed Files: ${report.summary.passedFiles}</p>
    <p>Failed Files: ${report.summary.failedFiles}</p>
    <p>Error Files: ${report.summary.errorFiles}</p>
  </div>

  <div class="test-results">
    <h2>Test Results</h2>
    <table>
      <tr>
        <th>File</th>
        <th>Score</th>
        <th>Status</th>
      </tr>
      ${report.results.map(result => {
        let status = 'error';
        let score = 'N/A';

        if (result.results) {
          status = result.results.passesThreshold ? 'passed' : 'failed';
          score = (result.results.overallScore * 100).toFixed(2) + '%';
        }

        return `
          <tr>
            <td>${result.filePath}</td>
            <td>${score}</td>
            <td class="${status}">${status.toUpperCase()}</td>
          </tr>
        `;
      }).join('')}
    </table>
  </div>

  <div class="file-details">
    <h2>File Details</h2>
    ${report.results.map(result => {
      if (result.error) {
        return `
          <div class="file-details">
            <h3>${result.filePath}</h3>
            <p class="error">Error: ${result.error}</p>
          </div>
        `;
      }

      if (!result.results) {
        return '';
      }

      return `
        <div class="file-details">
          <h3>${result.filePath}</h3>
          <p>Overall Score: <strong>${(result.results.overallScore * 100).toFixed(2)}%</strong></p>

          <h4>Aspect Scores</h4>
          ${Object.entries(result.results.testResults[0].aspects).map(([aspect, score]) => `
            <div class="aspect-score">
              <span>${aspect}: ${(score * 100).toFixed(2)}%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: ${score * 100}%"></div>
              </div>
            </div>
          `).join('')}

          ${result.results.recommendations.length > 0 ? `
            <h4>Recommendations</h4>
            <div class="recommendations">
              <ul>
                ${result.results.recommendations.map(rec => `
                  <li>
                    <strong>${rec.testName}</strong> (Score: ${(rec.score * 100).toFixed(2)}%)
                    <ul>
                      ${rec.recommendations.map(r => `<li>${r}</li>`).join('')}
                    </ul>
                  </li>
                `).join('')}
              </ul>
            </div>
          ` : ''}
        </div>
      `;
    }).join('')}
  </div>
</body>
</html>
  `;

  // Write the HTML report to a file
  const reportPath = path.join(config.reportDir, `aaa-report-${new Date().toISOString().replace(/[:.]/g, '-')}.html`);
  fs.writeFileSync(reportPath, html);

  console.log(`HTML report generated: ${reportPath}`);
}

// Main function
async function main() {
  // Find all test files
  const testFiles = findTestFiles(config.testDir);
  console.log(`Found ${testFiles.length} test files.`);

  // Validate each file
  const validationResults = testFiles.map(validateFile);

  // Generate report
  const report = generateReport(validationResults);

  // Generate HTML report
  generateHtmlReport(report);

  // Print summary
  console.log('\nAAA Compliance Summary:');
  console.log(`Total Files: ${report.summary.totalFiles}`);
  console.log(`Passed Files: ${report.summary.passedFiles}`);
  console.log(`Failed Files: ${report.summary.failedFiles}`);
  console.log(`Error Files: ${report.summary.errorFiles}`);
  console.log(`Overall Score: ${(report.summary.overallScore * 100).toFixed(2)}%`);

  // Return success if overall score is above threshold
  return report.summary.overallScore >= config.threshold;
}

// Run the main function
main().then(success => {
  if (success) {
    console.log('\nAAA compliance validation passed!');
    process.exit(0);
  } else {
    console.error('\nAAA compliance validation failed!');
    process.exit(1);
  }
}).catch(error => {
  console.error('Error running AAA compliance validation:', error);
  process.exit(1);
});
