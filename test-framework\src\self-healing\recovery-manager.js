/**
 * Recovery Manager
 * 
 * This module provides functionality for recovering from test failures.
 */

const { createDefaultConfig, mergeConfigs } = require('./config');

/**
 * Class for managing recovery strategies
 */
class RecoveryManager {
  /**
   * Create a new RecoveryManager
   * @param {Object} [config] - Configuration options
   */
  constructor(config = {}) {
    this.config = mergeConfigs(createDefaultConfig().recovery, config);
    this.strategies = new Map();
    this.strategyStats = new Map();
    this.initialized = false;
    this.initializationPromise = null;
    
    // Register built-in strategies
    this.registerBuiltInStrategies();
  }
  
  /**
   * Initialize the recovery manager
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.initialized) {
      return;
    }
    
    if (this.initializationPromise) {
      return this.initializationPromise;
    }
    
    this.initializationPromise = (async () => {
      // Nothing to initialize yet
      this.initialized = true;
    })();
    
    return this.initializationPromise;
  }
  
  /**
   * Register built-in recovery strategies
   */
  registerBuiltInStrategies() {
    // Retry strategy
    this.registerStrategy('retry', async (context) => {
      const { error, action, args, page } = context;
      console.log(`Retrying action "${action}" after error: ${error.message}`);
      
      // Wait a bit before retrying
      await new Promise(resolve => setTimeout(resolve, this.config.waitTime));
      
      // Retry the action
      return await page[action](...args);
    }, 100);
    
    // Wait strategy
    this.registerStrategy('wait', async (context) => {
      const { error, selector, page } = context;
      console.log(`Waiting for selector "${selector}" after error: ${error.message}`);
      
      // Wait longer for the element
      try {
        await page.waitForSelector(selector, { timeout: this.config.waitTime * 2 });
        return true;
      } catch (waitError) {
        console.log(`Wait strategy failed: ${waitError.message}`);
        return false;
      }
    }, 90);
    
    // Selector strategy
    this.registerStrategy('selector', async (context) => {
      const { error, selector, action, args, page, selectorHealer } = context;
      
      if (!selectorHealer) {
        console.log('Selector strategy failed: No selector healer provided');
        return false;
      }
      
      console.log(`Trying alternative selectors for "${selector}" after error: ${error.message}`);
      
      // Get alternative selectors
      const healResult = await selectorHealer.healSelector(selector, context);
      
      if (!healResult.healed) {
        console.log('Selector strategy failed: No alternative selectors found');
        return false;
      }
      
      console.log(`Using alternative selector: ${healResult.selector}`);
      
      // Replace the selector in the args
      const newArgs = [...args];
      newArgs[0] = healResult.selector;
      
      // Try the action with the new selector
      try {
        await page[action](...newArgs);
        
        // Register success with the selector healer
        await selectorHealer.trackSelectorResult(healResult.selector, true, {
          ...context,
          original: selector
        });
        
        return true;
      } catch (actionError) {
        console.log(`Action with alternative selector failed: ${actionError.message}`);
        
        // Register failure with the selector healer
        await selectorHealer.trackSelectorResult(healResult.selector, false, {
          ...context,
          original: selector
        });
        
        return false;
      }
    }, 80);
    
    // Refresh strategy
    this.registerStrategy('refresh', async (context) => {
      const { error, page } = context;
      console.log(`Refreshing page after error: ${error.message}`);
      
      // Refresh the page
      try {
        await page.reload();
        return true;
      } catch (refreshError) {
        console.log(`Refresh strategy failed: ${refreshError.message}`);
        return false;
      }
    }, 70);
    
    // Screenshot strategy
    this.registerStrategy('screenshot', async (context) => {
      const { error, page, testName } = context;
      
      if (!this.config.screenshotOnFailure) {
        return false;
      }
      
      console.log(`Taking screenshot after error: ${error.message}`);
      
      // Take a screenshot
      try {
        const screenshotPath = `./screenshots/recovery/${testName || 'unknown'}_${Date.now()}.png`;
        await page.screenshot({ path: screenshotPath, fullPage: true });
        console.log(`Screenshot saved to ${screenshotPath}`);
        
        // This strategy doesn't actually recover, just provides debugging info
        return false;
      } catch (screenshotError) {
        console.log(`Screenshot strategy failed: ${screenshotError.message}`);
        return false;
      }
    }, 60);
  }
  
  /**
   * Register a recovery strategy
   * @param {string} name - Strategy name
   * @param {Function} execute - Function to execute the strategy
   * @param {number} [priority=0] - Priority (higher = tried first)
   * @returns {void}
   */
  registerStrategy(name, execute, priority = 0) {
    if (!name || typeof name !== 'string') {
      throw new Error('Strategy name is required and must be a string');
    }
    
    if (!execute || typeof execute !== 'function') {
      throw new Error('Strategy execute function is required and must be a function');
    }
    
    this.strategies.set(name, {
      name,
      execute,
      priority: priority || 0
    });
    
    this.strategyStats.set(name, {
      attempts: 0,
      successes: 0,
      failures: 0,
      successRate: 0
    });
  }
  
  /**
   * Recover from a failure
   * @param {Error} error - The error that occurred
   * @param {Object} context - Context information
   * @returns {Promise<{success: boolean, strategy: string|null, attempts: number, duration: number, error: Error|null}>} - Recovery result
   */
  async recoverFromFailure(error, context = {}) {
    await this.initialize();
    
    if (!this.config.enabled) {
      return {
        success: false,
        strategy: null,
        attempts: 0,
        duration: 0,
        error
      };
    }
    
    const startTime = Date.now();
    let attempts = 0;
    let lastError = error;
    
    // Get enabled strategies
    const enabledStrategies = this.getEnabledStrategies();
    
    // Try each strategy
    for (const strategy of enabledStrategies) {
      if (attempts >= this.config.maxAttempts) {
        break;
      }
      
      attempts++;
      
      try {
        console.log(`Trying recovery strategy: ${strategy.name}`);
        
        // Update strategy stats
        const stats = this.strategyStats.get(strategy.name);
        stats.attempts++;
        
        // Execute the strategy
        const success = await strategy.execute({
          ...context,
          error: lastError,
          recoveryManager: this
        });
        
        // Update strategy stats
        if (success) {
          stats.successes++;
          stats.successRate = stats.successes / stats.attempts;
          
          return {
            success: true,
            strategy: strategy.name,
            attempts,
            duration: Date.now() - startTime,
            error: null
          };
        } else {
          stats.failures++;
          stats.successRate = stats.successes / stats.attempts;
        }
      } catch (strategyError) {
        console.error(`Recovery strategy "${strategy.name}" failed:`, strategyError);
        lastError = strategyError;
        
        // Update strategy stats
        const stats = this.strategyStats.get(strategy.name);
        stats.failures++;
        stats.successRate = stats.successes / stats.attempts;
      }
    }
    
    return {
      success: false,
      strategy: null,
      attempts,
      duration: Date.now() - startTime,
      error: lastError
    };
  }
  
  /**
   * Get enabled strategies sorted by priority
   * @returns {Array<Object>} - Enabled strategies
   */
  getEnabledStrategies() {
    // Filter enabled strategies
    const enabledStrategies = [];
    
    for (const strategyName of this.config.strategies) {
      const strategy = this.strategies.get(strategyName);
      if (strategy) {
        enabledStrategies.push(strategy);
      }
    }
    
    // Sort by priority (higher first)
    return enabledStrategies.sort((a, b) => b.priority - a.priority);
  }
  
  /**
   * Execute a specific strategy
   * @param {string} name - Strategy name
   * @param {Object} context - Context information
   * @returns {Promise<boolean>} - Whether the strategy was successful
   */
  async executeStrategy(name, context = {}) {
    await this.initialize();
    
    const strategy = this.strategies.get(name);
    if (!strategy) {
      throw new Error(`Strategy "${name}" not found`);
    }
    
    try {
      // Update strategy stats
      const stats = this.strategyStats.get(name);
      stats.attempts++;
      
      // Execute the strategy
      const success = await strategy.execute({
        ...context,
        recoveryManager: this
      });
      
      // Update strategy stats
      if (success) {
        stats.successes++;
      } else {
        stats.failures++;
      }
      stats.successRate = stats.successes / stats.attempts;
      
      return success;
    } catch (error) {
      console.error(`Strategy "${name}" failed:`, error);
      
      // Update strategy stats
      const stats = this.strategyStats.get(name);
      stats.failures++;
      stats.successRate = stats.successes / stats.attempts;
      
      return false;
    }
  }
  
  /**
   * Get statistics for all strategies
   * @returns {Object} - Strategy statistics
   */
  getStrategyStats() {
    const stats = {};
    
    for (const [name, strategyStats] of this.strategyStats.entries()) {
      stats[name] = { ...strategyStats };
    }
    
    return stats;
  }
  
  /**
   * Clean up resources
   * @returns {Promise<void>}
   */
  async cleanup() {
    // Nothing to clean up yet
  }
}

module.exports = RecoveryManager;