/**
 * Performance reporter for performance tracking
 */

import * as fs from 'fs';
import * as path from 'path';
import { PerformanceMetrics, PerformanceReporterOptions } from './types';
import { writeJsonToFile, writeStringToFile, ensureDirectoryExists } from '@qawolf/shared-utils';
import { EventBus, EventType } from '@qawolf/core';

/**
 * Performance reporter
 */
export class PerformanceReporter {
  /**
   * Output directory
   */
  private outputDir: string;
  
  /**
   * Report formats
   */
  private formats: ('json' | 'html' | 'csv')[];
  
  /**
   * Whether to include charts in HTML report
   */
  private includeCharts: boolean;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options Performance reporter options
   */
  constructor(options: PerformanceReporterOptions = {}) {
    this.outputDir = options.outputDir || 'performance-reports';
    this.formats = options.formats || ['json', 'html'];
    this.includeCharts = options.includeCharts !== false;
    this.eventBus = EventBus.getInstance();
  }
  
  /**
   * Generate report
   * @param metrics Performance metrics
   * @param formats Report formats
   * @returns Report file paths
   */
  generateReport(metrics: PerformanceMetrics, formats?: ('json' | 'html' | 'csv')[]): string[] {
    const reportFormats = formats || this.formats;
    const reportFilePaths: string[] = [];
    
    // Create output directory if it doesn't exist
    ensureDirectoryExists(this.outputDir);
    
    // Generate reports
    for (const format of reportFormats) {
      const reportFilePath = this.generateReportForFormat(metrics, format);
      reportFilePaths.push(reportFilePath);
    }
    
    // Emit event
    this.eventBus.emit(EventType.PERFORMANCE_REPORT_GENERATED, {
      metrics,
      reportFilePaths
    });
    
    return reportFilePaths;
  }
  
  /**
   * Generate report for format
   * @param metrics Performance metrics
   * @param format Report format
   * @returns Report file path
   */
  private generateReportForFormat(metrics: PerformanceMetrics, format: 'json' | 'html' | 'csv'): string {
    const reportFilePath = path.join(this.outputDir, `${metrics.testId}.${format}`);
    
    switch (format) {
      case 'json':
        this.generateJsonReport(metrics, reportFilePath);
        break;
      case 'html':
        this.generateHtmlReport(metrics, reportFilePath);
        break;
      case 'csv':
        this.generateCsvReport(metrics, reportFilePath);
        break;
    }
    
    return reportFilePath;
  }
  
  /**
   * Generate JSON report
   * @param metrics Performance metrics
   * @param reportFilePath Report file path
   */
  private generateJsonReport(metrics: PerformanceMetrics, reportFilePath: string): void {
    writeJsonToFile(reportFilePath, metrics);
  }
  
  /**
   * Generate HTML report
   * @param metrics Performance metrics
   * @param reportFilePath Report file path
   */
  private generateHtmlReport(metrics: PerformanceMetrics, reportFilePath: string): void {
    const html = this.generateHtmlReportContent(metrics);
    writeStringToFile(reportFilePath, html);
  }
  
  /**
   * Generate CSV report
   * @param metrics Performance metrics
   * @param reportFilePath Report file path
   */
  private generateCsvReport(metrics: PerformanceMetrics, reportFilePath: string): void {
    const csv = this.generateCsvReportContent(metrics);
    writeStringToFile(reportFilePath, csv);
  }
  
  /**
   * Generate HTML report content
   * @param metrics Performance metrics
   * @returns HTML report content
   */
  private generateHtmlReportContent(metrics: PerformanceMetrics): string {
    const operationRows = metrics.operations.map(operation => `
      <tr>
        <td>${operation.name}</td>
        <td>${operation.type}</td>
        <td>${operation.duration}ms</td>
        <td>${new Date(operation.startTime).toISOString()}</td>
        <td>${new Date(operation.endTime).toISOString()}</td>
        <td>${JSON.stringify(operation.metadata || {})}</td>
      </tr>
    `).join('');
    
    const operationTypeRows = Object.entries(metrics.operationStats.byType).map(([type, stats]) => `
      <tr>
        <td>${type}</td>
        <td>${stats.count}</td>
        <td>${stats.totalDuration}ms</td>
        <td>${stats.averageDuration.toFixed(2)}ms</td>
        <td>${stats.minDuration}ms</td>
        <td>${stats.maxDuration}ms</td>
      </tr>
    `).join('');
    
    const resourceUsageRows = metrics.resourceUsage.map(usage => `
      <tr>
        <td>${new Date(usage.timestamp).toISOString()}</td>
        <td>${(usage.memory.rss / 1024 / 1024).toFixed(2)} MB</td>
        <td>${(usage.memory.heapTotal / 1024 / 1024).toFixed(2)} MB</td>
        <td>${(usage.memory.heapUsed / 1024 / 1024).toFixed(2)} MB</td>
        <td>${usage.cpu.user}</td>
        <td>${usage.cpu.system}</td>
      </tr>
    `).join('');
    
    const chartScripts = this.includeCharts ? this.generateChartScripts(metrics) : '';
    
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Performance Report - ${metrics.testName}</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
          }
          
          h1, h2, h3 {
            color: #2c3e50;
          }
          
          .container {
            max-width: 1200px;
            margin: 0 auto;
          }
          
          .card {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin-bottom: 20px;
          }
          
          table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
          }
          
          th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
          }
          
          th {
            background-color: #f2f2f2;
          }
          
          tr:hover {
            background-color: #f5f5f5;
          }
          
          .chart-container {
            width: 100%;
            height: 400px;
            margin-bottom: 20px;
          }
        </style>
        ${this.includeCharts ? '<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>' : ''}
      </head>
      <body>
        <div class="container">
          <h1>Performance Report - ${metrics.testName}</h1>
          
          <div class="card">
            <h2>Test Information</h2>
            <table>
              <tr>
                <th>Test Name</th>
                <td>${metrics.testName}</td>
              </tr>
              <tr>
                <th>Test ID</th>
                <td>${metrics.testId}</td>
              </tr>
              <tr>
                <th>Start Time</th>
                <td>${new Date(metrics.startTime).toISOString()}</td>
              </tr>
              <tr>
                <th>End Time</th>
                <td>${new Date(metrics.endTime).toISOString()}</td>
              </tr>
              <tr>
                <th>Duration</th>
                <td>${metrics.duration}ms (${(metrics.duration / 1000).toFixed(2)}s)</td>
              </tr>
            </table>
          </div>
          
          <div class="card">
            <h2>Operation Statistics</h2>
            <table>
              <tr>
                <th>Total Operations</th>
                <td>${metrics.operationStats.count}</td>
              </tr>
              <tr>
                <th>Total Duration</th>
                <td>${metrics.operationStats.totalDuration}ms (${(metrics.operationStats.totalDuration / 1000).toFixed(2)}s)</td>
              </tr>
              <tr>
                <th>Average Duration</th>
                <td>${metrics.operationStats.averageDuration.toFixed(2)}ms</td>
              </tr>
              <tr>
                <th>Minimum Duration</th>
                <td>${metrics.operationStats.minDuration}ms</td>
              </tr>
              <tr>
                <th>Maximum Duration</th>
                <td>${metrics.operationStats.maxDuration}ms</td>
              </tr>
            </table>
          </div>
          
          ${this.includeCharts ? `
            <div class="card">
              <h2>Operation Duration Chart</h2>
              <div class="chart-container">
                <canvas id="operationDurationChart"></canvas>
              </div>
            </div>
            
            <div class="card">
              <h2>Operation Type Chart</h2>
              <div class="chart-container">
                <canvas id="operationTypeChart"></canvas>
              </div>
            </div>
            
            <div class="card">
              <h2>Memory Usage Chart</h2>
              <div class="chart-container">
                <canvas id="memoryUsageChart"></canvas>
              </div>
            </div>
            
            <div class="card">
              <h2>CPU Usage Chart</h2>
              <div class="chart-container">
                <canvas id="cpuUsageChart"></canvas>
              </div>
            </div>
          ` : ''}
          
          <div class="card">
            <h2>Operation Type Statistics</h2>
            <table>
              <thead>
                <tr>
                  <th>Type</th>
                  <th>Count</th>
                  <th>Total Duration</th>
                  <th>Average Duration</th>
                  <th>Minimum Duration</th>
                  <th>Maximum Duration</th>
                </tr>
              </thead>
              <tbody>
                ${operationTypeRows}
              </tbody>
            </table>
          </div>
          
          <div class="card">
            <h2>Operations</h2>
            <table>
              <thead>
                <tr>
                  <th>Name</th>
                  <th>Type</th>
                  <th>Duration</th>
                  <th>Start Time</th>
                  <th>End Time</th>
                  <th>Metadata</th>
                </tr>
              </thead>
              <tbody>
                ${operationRows}
              </tbody>
            </table>
          </div>
          
          <div class="card">
            <h2>Resource Statistics</h2>
            <table>
              <tr>
                <th>Maximum RSS</th>
                <td>${(metrics.resourceStats.memory.maxRss / 1024 / 1024).toFixed(2)} MB</td>
              </tr>
              <tr>
                <th>Maximum Heap Total</th>
                <td>${(metrics.resourceStats.memory.maxHeapTotal / 1024 / 1024).toFixed(2)} MB</td>
              </tr>
              <tr>
                <th>Maximum Heap Used</th>
                <td>${(metrics.resourceStats.memory.maxHeapUsed / 1024 / 1024).toFixed(2)} MB</td>
              </tr>
              <tr>
                <th>Maximum External</th>
                <td>${(metrics.resourceStats.memory.maxExternal / 1024 / 1024).toFixed(2)} MB</td>
              </tr>
              <tr>
                <th>Maximum Array Buffers</th>
                <td>${(metrics.resourceStats.memory.maxArrayBuffers / 1024 / 1024).toFixed(2)} MB</td>
              </tr>
              <tr>
                <th>Total User CPU Time</th>
                <td>${metrics.resourceStats.cpu.totalUser}</td>
              </tr>
              <tr>
                <th>Total System CPU Time</th>
                <td>${metrics.resourceStats.cpu.totalSystem}</td>
              </tr>
            </table>
          </div>
          
          <div class="card">
            <h2>Resource Usage</h2>
            <table>
              <thead>
                <tr>
                  <th>Timestamp</th>
                  <th>RSS</th>
                  <th>Heap Total</th>
                  <th>Heap Used</th>
                  <th>CPU User</th>
                  <th>CPU System</th>
                </tr>
              </thead>
              <tbody>
                ${resourceUsageRows}
              </tbody>
            </table>
          </div>
        </div>
        
        ${chartScripts}
      </body>
      </html>
    `;
  }
  
  /**
   * Generate chart scripts
   * @param metrics Performance metrics
   * @returns Chart scripts
   */
  private generateChartScripts(metrics: PerformanceMetrics): string {
    return `
      <script>
        // Operation Duration Chart
        const operationDurationCtx = document.getElementById('operationDurationChart').getContext('2d');
        new Chart(operationDurationCtx, {
          type: 'bar',
          data: {
            labels: ${JSON.stringify(metrics.operations.map(op => op.name))},
            datasets: [{
              label: 'Duration (ms)',
              data: ${JSON.stringify(metrics.operations.map(op => op.duration))},
              backgroundColor: 'rgba(54, 162, 235, 0.5)',
              borderColor: 'rgba(54, 162, 235, 1)',
              borderWidth: 1
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
              y: {
                beginAtZero: true,
                title: {
                  display: true,
                  text: 'Duration (ms)'
                }
              },
              x: {
                title: {
                  display: true,
                  text: 'Operation'
                }
              }
            }
          }
        });
        
        // Operation Type Chart
        const operationTypeCtx = document.getElementById('operationTypeChart').getContext('2d');
        new Chart(operationTypeCtx, {
          type: 'pie',
          data: {
            labels: ${JSON.stringify(Object.keys(metrics.operationStats.byType))},
            datasets: [{
              label: 'Operation Types',
              data: ${JSON.stringify(Object.values(metrics.operationStats.byType).map(stats => stats.count))},
              backgroundColor: [
                'rgba(255, 99, 132, 0.5)',
                'rgba(54, 162, 235, 0.5)',
                'rgba(255, 206, 86, 0.5)',
                'rgba(75, 192, 192, 0.5)',
                'rgba(153, 102, 255, 0.5)',
                'rgba(255, 159, 64, 0.5)',
                'rgba(199, 199, 199, 0.5)',
                'rgba(83, 102, 255, 0.5)',
                'rgba(40, 159, 64, 0.5)',
                'rgba(210, 199, 199, 0.5)'
              ],
              borderColor: [
                'rgba(255, 99, 132, 1)',
                'rgba(54, 162, 235, 1)',
                'rgba(255, 206, 86, 1)',
                'rgba(75, 192, 192, 1)',
                'rgba(153, 102, 255, 1)',
                'rgba(255, 159, 64, 1)',
                'rgba(199, 199, 199, 1)',
                'rgba(83, 102, 255, 1)',
                'rgba(40, 159, 64, 1)',
                'rgba(210, 199, 199, 1)'
              ],
              borderWidth: 1
            }]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false
          }
        });
        
        // Memory Usage Chart
        const memoryUsageCtx = document.getElementById('memoryUsageChart').getContext('2d');
        new Chart(memoryUsageCtx, {
          type: 'line',
          data: {
            labels: ${JSON.stringify(metrics.resourceUsage.map(usage => new Date(usage.timestamp).toISOString()))},
            datasets: [
              {
                label: 'RSS (MB)',
                data: ${JSON.stringify(metrics.resourceUsage.map(usage => (usage.memory.rss / 1024 / 1024).toFixed(2)))},
                borderColor: 'rgba(255, 99, 132, 1)',
                backgroundColor: 'rgba(255, 99, 132, 0.1)',
                borderWidth: 2,
                fill: true
              },
              {
                label: 'Heap Total (MB)',
                data: ${JSON.stringify(metrics.resourceUsage.map(usage => (usage.memory.heapTotal / 1024 / 1024).toFixed(2)))},
                borderColor: 'rgba(54, 162, 235, 1)',
                backgroundColor: 'rgba(54, 162, 235, 0.1)',
                borderWidth: 2,
                fill: true
              },
              {
                label: 'Heap Used (MB)',
                data: ${JSON.stringify(metrics.resourceUsage.map(usage => (usage.memory.heapUsed / 1024 / 1024).toFixed(2)))},
                borderColor: 'rgba(75, 192, 192, 1)',
                backgroundColor: 'rgba(75, 192, 192, 0.1)',
                borderWidth: 2,
                fill: true
              }
            ]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
              y: {
                beginAtZero: true,
                title: {
                  display: true,
                  text: 'Memory (MB)'
                }
              },
              x: {
                title: {
                  display: true,
                  text: 'Time'
                }
              }
            }
          }
        });
        
        // CPU Usage Chart
        const cpuUsageCtx = document.getElementById('cpuUsageChart').getContext('2d');
        new Chart(cpuUsageCtx, {
          type: 'line',
          data: {
            labels: ${JSON.stringify(metrics.resourceUsage.map(usage => new Date(usage.timestamp).toISOString()))},
            datasets: [
              {
                label: 'User CPU',
                data: ${JSON.stringify(metrics.resourceUsage.map(usage => usage.cpu.user))},
                borderColor: 'rgba(255, 99, 132, 1)',
                backgroundColor: 'rgba(255, 99, 132, 0.1)',
                borderWidth: 2,
                fill: true
              },
              {
                label: 'System CPU',
                data: ${JSON.stringify(metrics.resourceUsage.map(usage => usage.cpu.system))},
                borderColor: 'rgba(54, 162, 235, 1)',
                backgroundColor: 'rgba(54, 162, 235, 0.1)',
                borderWidth: 2,
                fill: true
              }
            ]
          },
          options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
              y: {
                beginAtZero: true,
                title: {
                  display: true,
                  text: 'CPU Usage'
                }
              },
              x: {
                title: {
                  display: true,
                  text: 'Time'
                }
              }
            }
          }
        });
      </script>
    `;
  }
  
  /**
   * Generate CSV report content
   * @param metrics Performance metrics
   * @returns CSV report content
   */
  private generateCsvReportContent(metrics: PerformanceMetrics): string {
    // Test information
    let csv = 'Test Information\n';
    csv += 'Test Name,Test ID,Start Time,End Time,Duration (ms)\n';
    csv += `${metrics.testName},${metrics.testId},${new Date(metrics.startTime).toISOString()},${new Date(metrics.endTime).toISOString()},${metrics.duration}\n\n`;
    
    // Operation statistics
    csv += 'Operation Statistics\n';
    csv += 'Total Operations,Total Duration (ms),Average Duration (ms),Minimum Duration (ms),Maximum Duration (ms)\n';
    csv += `${metrics.operationStats.count},${metrics.operationStats.totalDuration},${metrics.operationStats.averageDuration.toFixed(2)},${metrics.operationStats.minDuration},${metrics.operationStats.maxDuration}\n\n`;
    
    // Operation type statistics
    csv += 'Operation Type Statistics\n';
    csv += 'Type,Count,Total Duration (ms),Average Duration (ms),Minimum Duration (ms),Maximum Duration (ms)\n';
    
    for (const [type, stats] of Object.entries(metrics.operationStats.byType)) {
      csv += `${type},${stats.count},${stats.totalDuration},${stats.averageDuration.toFixed(2)},${stats.minDuration},${stats.maxDuration}\n`;
    }
    
    csv += '\n';
    
    // Operations
    csv += 'Operations\n';
    csv += 'Name,Type,Duration (ms),Start Time,End Time,Metadata\n';
    
    for (const operation of metrics.operations) {
      csv += `"${operation.name}",${operation.type},${operation.duration},${new Date(operation.startTime).toISOString()},${new Date(operation.endTime).toISOString()},"${JSON.stringify(operation.metadata || {})}"\n`;
    }
    
    csv += '\n';
    
    // Resource statistics
    csv += 'Resource Statistics\n';
    csv += 'Maximum RSS (MB),Maximum Heap Total (MB),Maximum Heap Used (MB),Maximum External (MB),Maximum Array Buffers (MB),Total User CPU Time,Total System CPU Time\n';
    csv += `${(metrics.resourceStats.memory.maxRss / 1024 / 1024).toFixed(2)},${(metrics.resourceStats.memory.maxHeapTotal / 1024 / 1024).toFixed(2)},${(metrics.resourceStats.memory.maxHeapUsed / 1024 / 1024).toFixed(2)},${(metrics.resourceStats.memory.maxExternal / 1024 / 1024).toFixed(2)},${(metrics.resourceStats.memory.maxArrayBuffers / 1024 / 1024).toFixed(2)},${metrics.resourceStats.cpu.totalUser},${metrics.resourceStats.cpu.totalSystem}\n\n`;
    
    // Resource usage
    csv += 'Resource Usage\n';
    csv += 'Timestamp,RSS (MB),Heap Total (MB),Heap Used (MB),CPU User,CPU System\n';
    
    for (const usage of metrics.resourceUsage) {
      csv += `${new Date(usage.timestamp).toISOString()},${(usage.memory.rss / 1024 / 1024).toFixed(2)},${(usage.memory.heapTotal / 1024 / 1024).toFixed(2)},${(usage.memory.heapUsed / 1024 / 1024).toFixed(2)},${usage.cpu.user},${usage.cpu.system}\n`;
    }
    
    return csv;
  }
}

/**
 * Create performance reporter
 * @param options Performance reporter options
 * @returns Performance reporter
 */
export function createPerformanceReporter(options: PerformanceReporterOptions = {}): PerformanceReporter {
  return new PerformanceReporter(options);
}
