/**
 * Self-healing page for self-healing tests
 */

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>er<PERSON><PERSON><PERSON><PERSON>, <PERSON>ement<PERSON><PERSON>le, <PERSON>ame, JS<PERSON><PERSON>le, Response, FileChooser, Dialog, Download, Worker, CDPSession } from 'playwright';
import { HealingStrategy, HealingOptions, HealingResult, SelfHealingPageOptions } from './types';
import { defaultHealingStrategies } from './healing-strategies';
import { defaultSelectorHistoryManager } from './selector-history';
import { SelfHealingLocator } from './self-healing-locator';
import { EventBus, EventType } from '@qawolf/core';

/**
 * Self-healing page
 */
export class SelfHealingPage implements Page {
  /**
   * Playwright page
   */
  private page: Page;
  
  /**
   * Healing strategies
   */
  private strategies: HealingStrategy[];
  
  /**
   * Default healing options
   */
  private healingOptions: HealingOptions;
  
  /**
   * Whether self-healing is enabled
   */
  private enabled: boolean;
  
  /**
   * Whether to log healing attempts
   */
  private logging: boolean;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param page Playwright page
   * @param options Self-healing page options
   */
  constructor(page: Page, options: SelfHealingPageOptions = {}) {
    this.page = page;
    this.strategies = options.strategies || defaultHealingStrategies;
    this.healingOptions = options.healingOptions || { similarityThreshold: 0.7, maxCandidates: 5, timeout: 30000, logging: true };
    this.enabled = options.enabled !== false;
    this.logging = options.logging !== false;
    this.eventBus = EventBus.getInstance();
  }
  
  /**
   * Heal selector
   * @param selector Original selector
   * @param options Healing options
   * @returns Healing result
   */
  async healSelector(selector: string, options?: HealingOptions): Promise<HealingResult> {
    const startTime = Date.now();
    
    // Check if selector is already valid
    try {
      const count = await this.page.locator(selector).count();
      
      if (count > 0) {
        return {
          originalSelector: selector,
          healedSelector: selector,
          strategy: null,
          healingTime: Date.now() - startTime,
          success: true
        };
      }
    } catch (error) {
      // Ignore error
    }
    
    // Check if we have a known alternative for this selector
    const alternativeSelector = defaultSelectorHistoryManager.getAlternative(selector);
    
    if (alternativeSelector) {
      try {
        const count = await this.page.locator(alternativeSelector).count();
        
        if (count > 0) {
          if (this.logging) {
            console.log(`Using known alternative selector: ${alternativeSelector}`);
          }
          
          return {
            originalSelector: selector,
            healedSelector: alternativeSelector,
            strategy: 'SelectorHistory',
            healingTime: Date.now() - startTime,
            success: true
          };
        }
      } catch (error) {
        // Ignore error
      }
    }
    
    // Try each healing strategy
    for (const strategy of this.strategies) {
      try {
        const healedSelector = await strategy.heal(this.page, selector, {
          ...this.healingOptions,
          ...options
        });
        
        if (healedSelector) {
          // Save the healed selector for future use
          defaultSelectorHistoryManager.addAlternative(selector, healedSelector, strategy.name);
          
          // Emit event
          this.eventBus.emit(EventType.SELECTOR_HEALED, {
            originalSelector: selector,
            healedSelector,
            strategy: strategy.name,
            healingTime: Date.now() - startTime
          });
          
          if (this.logging) {
            console.log(`Healed selector using ${strategy.name}: ${healedSelector}`);
          }
          
          return {
            originalSelector: selector,
            healedSelector,
            strategy: strategy.name,
            healingTime: Date.now() - startTime,
            success: true
          };
        }
      } catch (error) {
        if (this.logging) {
          console.error(`Error healing selector with ${strategy.name}:`, error);
        }
      }
    }
    
    // If all healing strategies failed, return the original selector
    return {
      originalSelector: selector,
      healedSelector: null,
      strategy: null,
      healingTime: Date.now() - startTime,
      success: false,
      error: 'Failed to heal selector'
    };
  }
  
  /**
   * Create self-healing locator
   * @param selector Selector
   * @returns Self-healing locator
   */
  locator(selector: string): Locator {
    if (!this.enabled) {
      return this.page.locator(selector);
    }
    
    return new SelfHealingLocator(this, this.page, selector, {
      strategies: this.strategies,
      healingOptions: this.healingOptions,
      logging: this.logging
    });
  }
  
  /**
   * Enable self-healing
   */
  enable(): void {
    this.enabled = true;
  }
  
  /**
   * Disable self-healing
   */
  disable(): void {
    this.enabled = false;
  }
  
  /**
   * Check if self-healing is enabled
   * @returns True if self-healing is enabled, false otherwise
   */
  isEnabled(): boolean {
    return this.enabled;
  }
  
  /**
   * Get healing strategies
   * @returns Healing strategies
   */
  getStrategies(): HealingStrategy[] {
    return [...this.strategies];
  }
  
  /**
   * Set healing strategies
   * @param strategies Healing strategies
   */
  setStrategies(strategies: HealingStrategy[]): void {
    this.strategies = strategies;
  }
  
  /**
   * Add healing strategy
   * @param strategy Healing strategy
   */
  addStrategy(strategy: HealingStrategy): void {
    this.strategies.push(strategy);
  }
  
  /**
   * Remove healing strategy
   * @param strategyName Strategy name
   */
  removeStrategy(strategyName: string): void {
    this.strategies = this.strategies.filter(strategy => strategy.name !== strategyName);
  }
  
  /**
   * Get default healing options
   * @returns Default healing options
   */
  getHealingOptions(): HealingOptions {
    return { ...this.healingOptions };
  }
  
  /**
   * Set default healing options
   * @param options Default healing options
   */
  setHealingOptions(options: HealingOptions): void {
    this.healingOptions = { ...options };
  }
  
  /**
   * Get original page
   * @returns Original page
   */
  getOriginalPage(): Page {
    return this.page;
  }
  
  // Implement Page interface
  
  // Properties
  
  get url(): string {
    return this.page.url();
  }
  
  get context(): BrowserContext {
    return this.page.context();
  }
  
  // Methods
  
  $(selector: string): Promise<ElementHandle<SVGElement | HTMLElement> | null> {
    return this.page.$(selector);
  }
  
  $$(selector: string): Promise<ElementHandle<SVGElement | HTMLElement>[]> {
    return this.page.$$(selector);
  }
  
  $eval<R, E extends SVGElement | HTMLElement = SVGElement | HTMLElement>(selector: string, pageFunction: (element: E, ...args: any[]) => R | Promise<R>, ...args: any[]): Promise<R> {
    return this.page.$eval(selector, pageFunction, ...args);
  }
  
  $$eval<R, E extends SVGElement | HTMLElement = SVGElement | HTMLElement>(selector: string, pageFunction: (elements: E[], ...args: any[]) => R | Promise<R>, ...args: any[]): Promise<R> {
    return this.page.$$eval(selector, pageFunction, ...args);
  }
  
  addInitScript(script: Function | string | { path?: string | undefined; content?: string | undefined; }): Promise<void> {
    return this.page.addInitScript(script);
  }
  
  addScriptTag(options: { url?: string | undefined; path?: string | undefined; content?: string | undefined; type?: string | undefined; }): Promise<ElementHandle<HTMLElement>> {
    return this.page.addScriptTag(options);
  }
  
  addStyleTag(options: { url?: string | undefined; path?: string | undefined; content?: string | undefined; }): Promise<ElementHandle<HTMLElement>> {
    return this.page.addStyleTag(options);
  }
  
  bringToFront(): Promise<void> {
    return this.page.bringToFront();
  }
  
  check(selector: string, options?: { position?: { x: number; y: number; } | undefined; force?: boolean | undefined; noWaitAfter?: boolean | undefined; timeout?: number | undefined; trial?: boolean | undefined; } | undefined): Promise<void> {
    return this.locator(selector).check(options);
  }
  
  click(selector: string, options?: { button?: "left" | "right" | "middle" | undefined; clickCount?: number | undefined; delay?: number | undefined; position?: { x: number; y: number; } | undefined; force?: boolean | undefined; noWaitAfter?: boolean | undefined; timeout?: number | undefined; trial?: boolean | undefined; } | undefined): Promise<void> {
    return this.locator(selector).click(options);
  }
  
  close(options?: { runBeforeUnload?: boolean | undefined; } | undefined): Promise<void> {
    return this.page.close(options);
  }
  
  content(): Promise<string> {
    return this.page.content();
  }
  
  dblclick(selector: string, options?: { button?: "left" | "right" | "middle" | undefined; delay?: number | undefined; position?: { x: number; y: number; } | undefined; force?: boolean | undefined; noWaitAfter?: boolean | undefined; timeout?: number | undefined; trial?: boolean | undefined; } | undefined): Promise<void> {
    return this.locator(selector).dblclick(options);
  }
  
  dispatchEvent(selector: string, type: string, eventInit?: any, options?: { timeout?: number | undefined; } | undefined): Promise<void> {
    return this.locator(selector).dispatchEvent(type, eventInit, options);
  }
  
  emulateMedia(options?: { media?: "screen" | "print" | null | undefined; colorScheme?: "dark" | "light" | "no-preference" | null | undefined; reducedMotion?: "reduce" | "no-preference" | null | undefined; forcedColors?: "active" | "none" | null | undefined; } | undefined): Promise<void> {
    return this.page.emulateMedia(options);
  }
  
  evaluate<R, Arg>(pageFunction: (arg: Arg) => R | Promise<R>, arg: Arg): Promise<R>;
  evaluate<R>(pageFunction: () => R | Promise<R>, arg?: any): Promise<R>;
  evaluate<R, Arg>(pageFunction: (arg: Arg) => R | Promise<R>, arg?: Arg): Promise<R> {
    return this.page.evaluate(pageFunction, arg);
  }
  
  evaluateHandle<R, Arg>(pageFunction: (arg: Arg) => R | Promise<R>, arg: Arg): Promise<JSHandle<R>>;
  evaluateHandle<R>(pageFunction: () => R | Promise<R>, arg?: any): Promise<JSHandle<R>>;
  evaluateHandle<R, Arg>(pageFunction: (arg: Arg) => R | Promise<R>, arg?: Arg): Promise<JSHandle<R>> {
    return this.page.evaluateHandle(pageFunction, arg);
  }
  
  fill(selector: string, value: string, options?: { noWaitAfter?: boolean | undefined; timeout?: number | undefined; force?: boolean | undefined; } | undefined): Promise<void> {
    return this.locator(selector).fill(value, options);
  }
  
  focus(selector: string, options?: { timeout?: number | undefined; } | undefined): Promise<void> {
    return this.locator(selector).focus(options);
  }
  
  frame(nameOrUrl: string): Frame | null {
    return this.page.frame(nameOrUrl);
  }
  
  frameLocator(selector: string): Locator {
    return this.page.frameLocator(selector);
  }
  
  frames(): Frame[] {
    return this.page.frames();
  }
  
  getAttribute(selector: string, name: string, options?: { timeout?: number | undefined; } | undefined): Promise<string | null> {
    return this.locator(selector).getAttribute(name, options);
  }
  
  goto(url: string, options?: { referer?: string | undefined; timeout?: number | undefined; waitUntil?: "load" | "domcontentloaded" | "networkidle" | "commit" | undefined; } | undefined): Promise<Response | null> {
    return this.page.goto(url, options);
  }
  
  hover(selector: string, options?: { position?: { x: number; y: number; } | undefined; force?: boolean | undefined; noWaitAfter?: boolean | undefined; timeout?: number | undefined; trial?: boolean | undefined; } | undefined): Promise<void> {
    return this.locator(selector).hover(options);
  }
  
  innerHTML(selector: string, options?: { timeout?: number | undefined; } | undefined): Promise<string> {
    return this.locator(selector).innerHTML(options);
  }
  
  innerText(selector: string, options?: { timeout?: number | undefined; } | undefined): Promise<string> {
    return this.locator(selector).innerText(options);
  }
  
  inputValue(selector: string, options?: { timeout?: number | undefined; } | undefined): Promise<string> {
    return this.locator(selector).inputValue(options);
  }
  
  isChecked(selector: string, options?: { timeout?: number | undefined; } | undefined): Promise<boolean> {
    return this.locator(selector).isChecked(options);
  }
  
  isClosed(): boolean {
    return this.page.isClosed();
  }
  
  isDisabled(selector: string, options?: { timeout?: number | undefined; } | undefined): Promise<boolean> {
    return this.locator(selector).isDisabled(options);
  }
  
  isEditable(selector: string, options?: { timeout?: number | undefined; } | undefined): Promise<boolean> {
    return this.locator(selector).isEditable(options);
  }
  
  isEnabled(selector: string, options?: { timeout?: number | undefined; } | undefined): Promise<boolean> {
    return this.locator(selector).isEnabled(options);
  }
  
  isHidden(selector: string, options?: { timeout?: number | undefined; } | undefined): Promise<boolean> {
    return this.locator(selector).isHidden(options);
  }
  
  isVisible(selector: string, options?: { timeout?: number | undefined; } | undefined): Promise<boolean> {
    return this.locator(selector).isVisible(options);
  }
  
  keyboard: any;
  
  mainFrame(): Frame {
    return this.page.mainFrame();
  }
  
  mouse: any;
  
  on(event: string, listener: (...args: any[]) => void): this {
    this.page.on(event, listener);
    return this;
  }
  
  once(event: string, listener: (...args: any[]) => void): this {
    this.page.once(event, listener);
    return this;
  }
  
  off(event: string, listener: (...args: any[]) => void): this {
    this.page.off(event, listener);
    return this;
  }
  
  pause(): Promise<void> {
    return this.page.pause();
  }
  
  pdf(options?: { scale?: number | undefined; displayHeaderFooter?: boolean | undefined; headerTemplate?: string | undefined; footerTemplate?: string | undefined; printBackground?: boolean | undefined; landscape?: boolean | undefined; pageRanges?: string | undefined; format?: string | undefined; width?: string | number | undefined; height?: string | number | undefined; preferCSSPageSize?: boolean | undefined; margin?: { top?: string | number | undefined; right?: string | number | undefined; bottom?: string | number | undefined; left?: string | number | undefined; } | undefined; path?: string | undefined; } | undefined): Promise<Buffer> {
    return this.page.pdf(options);
  }
  
  press(selector: string, key: string, options?: { delay?: number | undefined; noWaitAfter?: boolean | undefined; timeout?: number | undefined; } | undefined): Promise<void> {
    return this.locator(selector).press(key, options);
  }
  
  reload(options?: { timeout?: number | undefined; waitUntil?: "load" | "domcontentloaded" | "networkidle" | "commit" | undefined; } | undefined): Promise<Response | null> {
    return this.page.reload(options);
  }
  
  route(url: string | RegExp | ((url: URL) => boolean), handler: (route: any, request: any) => void): Promise<void> {
    return this.page.route(url, handler);
  }
  
  screenshot(options?: { timeout?: number | undefined; type?: "png" | "jpeg" | undefined; path?: string | undefined; quality?: number | undefined; omitBackground?: boolean | undefined; fullPage?: boolean | undefined; clip?: { x: number; y: number; width: number; height: number; } | undefined; animations?: "disabled" | "allow" | undefined; caret?: "hide" | "initial" | undefined; scale?: "css" | "device" | undefined; mask?: Locator[] | undefined; } | undefined): Promise<Buffer> {
    return this.page.screenshot(options);
  }
  
  selectOption(selector: string, values: string | string[] | null | ElementHandle<SVGElement | HTMLElement> | ElementHandle<SVGElement | HTMLElement>[] | { value?: string | undefined; label?: string | undefined; index?: number | undefined; } | { value?: string | undefined; label?: string | undefined; index?: number | undefined; }[], options?: { force?: boolean | undefined; noWaitAfter?: boolean | undefined; timeout?: number | undefined; } | undefined): Promise<string[]> {
    return this.locator(selector).selectOption(values, options);
  }
  
  setContent(html: string, options?: { timeout?: number | undefined; waitUntil?: "load" | "domcontentloaded" | "networkidle" | "commit" | undefined; } | undefined): Promise<void> {
    return this.page.setContent(html, options);
  }
  
  setDefaultNavigationTimeout(timeout: number): void {
    return this.page.setDefaultNavigationTimeout(timeout);
  }
  
  setDefaultTimeout(timeout: number): void {
    return this.page.setDefaultTimeout(timeout);
  }
  
  setExtraHTTPHeaders(headers: Record<string, string>): Promise<void> {
    return this.page.setExtraHTTPHeaders(headers);
  }
  
  setInputFiles(selector: string, files: string | string[] | { name: string; mimeType: string; buffer: Buffer; } | { name: string; mimeType: string; buffer: Buffer; }[], options?: { noWaitAfter?: boolean | undefined; timeout?: number | undefined; } | undefined): Promise<void> {
    return this.locator(selector).setInputFiles(files, options);
  }
  
  setViewportSize(viewportSize: { width: number; height: number; }): Promise<void> {
    return this.page.setViewportSize(viewportSize);
  }
  
  tap(selector: string, options?: { position?: { x: number; y: number; } | undefined; force?: boolean | undefined; noWaitAfter?: boolean | undefined; timeout?: number | undefined; trial?: boolean | undefined; } | undefined): Promise<void> {
    return this.locator(selector).tap(options);
  }
  
  textContent(selector: string, options?: { timeout?: number | undefined; } | undefined): Promise<string | null> {
    return this.locator(selector).textContent(options);
  }
  
  title(): Promise<string> {
    return this.page.title();
  }
  
  type(selector: string, text: string, options?: { delay?: number | undefined; noWaitAfter?: boolean | undefined; timeout?: number | undefined; } | undefined): Promise<void> {
    return this.locator(selector).type(text, options);
  }
  
  uncheck(selector: string, options?: { position?: { x: number; y: number; } | undefined; force?: boolean | undefined; noWaitAfter?: boolean | undefined; timeout?: number | undefined; trial?: boolean | undefined; } | undefined): Promise<void> {
    return this.locator(selector).uncheck(options);
  }
  
  unroute(url: string | RegExp | ((url: URL) => boolean), handler?: ((route: any, request: any) => void) | undefined): Promise<void> {
    return this.page.unroute(url, handler);
  }
  
  viewportSize(): { width: number; height: number; } | null {
    return this.page.viewportSize();
  }
  
  waitForEvent(event: string, optionsOrPredicate?: ((arg: any) => boolean | Promise<boolean>) | { timeout?: number | undefined; predicate?: ((arg: any) => boolean | Promise<boolean>) | undefined; } | undefined): Promise<any> {
    return this.page.waitForEvent(event, optionsOrPredicate);
  }
  
  waitForFunction<R>(pageFunction: () => R | Promise<R>, options?: { polling?: "raf" | "mutation" | number | undefined; timeout?: number | undefined; } | undefined, ...args: any[]): Promise<JSHandle<R>> {
    return this.page.waitForFunction(pageFunction, options, ...args);
  }
  
  waitForLoadState(state?: "load" | "domcontentloaded" | "networkidle" | undefined, options?: { timeout?: number | undefined; } | undefined): Promise<void> {
    return this.page.waitForLoadState(state, options);
  }
  
  waitForNavigation(options?: { url?: string | RegExp | ((url: URL) => boolean) | undefined; waitUntil?: "load" | "domcontentloaded" | "networkidle" | "commit" | undefined; timeout?: number | undefined; } | undefined): Promise<Response | null> {
    return this.page.waitForNavigation(options);
  }
  
  waitForRequest(urlOrPredicate: string | RegExp | ((request: any) => boolean | Promise<boolean>), options?: { timeout?: number | undefined; } | undefined): Promise<any> {
    return this.page.waitForRequest(urlOrPredicate, options);
  }
  
  waitForResponse(urlOrPredicate: string | RegExp | ((response: any) => boolean | Promise<boolean>), options?: { timeout?: number | undefined; } | undefined): Promise<any> {
    return this.page.waitForResponse(urlOrPredicate, options);
  }
  
  waitForSelector(selector: string, options?: { state?: "attached" | "detached" | "visible" | "hidden" | undefined; timeout?: number | undefined; } | undefined): Promise<ElementHandle<SVGElement | HTMLElement> | null> {
    return this.page.waitForSelector(selector, options);
  }
  
  waitForTimeout(timeout: number): Promise<void> {
    return this.page.waitForTimeout(timeout);
  }
  
  waitForURL(url: string | RegExp | ((url: URL) => boolean), options?: { waitUntil?: "load" | "domcontentloaded" | "networkidle" | "commit" | undefined; timeout?: number | undefined; } | undefined): Promise<void> {
    return this.page.waitForURL(url, options);
  }
}

/**
 * Create self-healing page
 * @param page Playwright page
 * @param options Self-healing page options
 * @returns Self-healing page
 */
export function createSelfHealingPage(page: Page, options: SelfHealingPageOptions = {}): SelfHealingPage {
  return new SelfHealingPage(page, options);
}
