/**
 * MCP Optimizer CLI Commands
 * 
 * This module defines the CLI commands for the MCP optimizer.
 */

const { optimizeSelectors } = require('../core/optimizer');
const { selectMcpTool } = require('../core/selector');
const { generateReport } = require('../reporting/reporter');
const playwrightMcp = require('../tools/playwright-mcp');

/**
 * Run the optimizer on a test or set of tests
 * @param {Object} options - Command options
 * @returns {Promise<void>}
 */
async function runCommand(options) {
  console.log('Running MCP optimizer...');
  
  // Validate options
  if (!options.test && !options.dir) {
    throw new Error('Either --test or --dir must be specified');
  }
  
  // Start Playwright MCP if needed
  if (options.startMcp) {
    console.log('Starting Playwright MCP...');
    const mcpServer = await playwrightMcp.startPlaywrightMCP({
      port: options.port || 8932,
      headless: options.headless || false
    });
    console.log(`Playwright MCP started on port ${mcpServer.port}`);
  }
  
  // Run optimization
  console.log('Optimizing selectors...');
  const result = await optimizeSelectors({
    selectors: options.selectors || [],
    options: {
      generateFallbacks: options.fallbacks !== false,
      prioritizeTestIds: options.testIds !== false
    }
  });
  
  // Generate report
  if (options.report) {
    console.log('Generating report...');
    const report = await generateReport(result, {
      format: options.format || 'markdown',
      includeTimestamps: options.timestamps !== false,
      includeTokenUsage: options.tokens !== false
    });
    
    console.log(`Report generated: ${report.report.substring(0, 100)}...`);
  }
  
  console.log('Optimization complete!');
}

/**
 * Explain the optimization decisions made
 * @param {Object} options - Command options
 * @returns {Promise<void>}
 */
async function explainCommand(options) {
  console.log('Explaining MCP optimization decisions...');
  
  // Validate options
  if (!options.task) {
    throw new Error('--task must be specified');
  }
  
  // Select MCP tool
  const toolSelection = await selectMcpTool({
    type: options.task,
    subtype: options.subtype
  }, {
    tokenOptimized: options.tokenOptimized !== false,
    performanceOptimized: options.performanceOptimized || false
  });
  
  console.log(`Selected tool: ${toolSelection.tool}`);
  console.log(`Category: ${toolSelection.category}`);
  console.log(`Estimated token usage: ${toolSelection.estimatedTokenUsage}`);
  
  // Generate explanation
  console.log('\nExplanation:');
  console.log(`The ${toolSelection.tool} tool was selected for this task because it is the most appropriate for ${toolSelection.category} tasks.`);
  console.log(`Using this tool will save approximately ${3000 - toolSelection.estimatedTokenUsage} tokens compared to using the LLM directly.`);
  
  console.log('\nExplanation complete!');
}

module.exports = {
  runCommand,
  explainCommand
};