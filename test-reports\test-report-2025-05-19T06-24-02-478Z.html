
<!DOCTYPE html>
<html>
<head>
  <title>Test Report</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
    }
    
    h1, h2, h3 {
      margin-top: 0;
    }
    
    .summary {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }
    
    .summary-card {
      background-color: #f5f5f5;
      border-radius: 5px;
      padding: 15px;
      width: 30%;
    }
    
    .summary-card h3 {
      margin-top: 0;
    }
    
    .pass-rate {
      font-size: 24px;
      font-weight: bold;
    }
    
    .test-results {
      margin-top: 20px;
    }
    
    table {
      width: 100%;
      border-collapse: collapse;
    }
    
    th, td {
      padding: 8px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    
    th {
      background-color: #f2f2f2;
    }
    
    .passed {
      color: green;
    }
    
    .failed {
      color: red;
    }
  </style>
</head>
<body>
  <h1>Test Report</h1>
  <p>Generated: 5/19/2025, 1:24:02 AM</p>
  
  <div class="summary">
    <div class="summary-card">
      <h3>Simplified Tests</h3>
      <p class="pass-rate">50.00%</p>
      <p>Passed: 3</p>
      <p>Failed: 3</p>
      <p>Skipped: 0</p>
      <p>Total: 6</p>
    </div>
    
    <div class="summary-card">
      <h3>Full Tests</h3>
      <p class="pass-rate">0.00%</p>
      <p>Passed: 0</p>
      <p>Failed: 10</p>
      <p>Skipped: 0</p>
      <p>Total: 10</p>
    </div>
    
    <div class="summary-card">
      <h3>Overall</h3>
      <p class="pass-rate">18.75%</p>
      <p>Passed: 3</p>
      <p>Failed: 13</p>
      <p>Skipped: 0</p>
      <p>Total: 16</p>
    </div>
  </div>
  
  <div class="test-results">
    <h2>Simplified Tests</h2>
    <table>
      <tr>
        <th>Test</th>
        <th>Result</th>
      </tr>
      
        <tr>
          <td>tests/simple-test.spec.js</td>
          <td class="passed">Passed</td>
        </tr>
      
        <tr>
          <td>tests/simplified-integration.spec.js</td>
          <td class="failed">Failed</td>
        </tr>
      
        <tr>
          <td>tests/simplified-login.spec.js</td>
          <td class="failed">Failed</td>
        </tr>
      
        <tr>
          <td>tests/simplified-file-operations.spec.js</td>
          <td class="passed">Passed</td>
        </tr>
      
        <tr>
          <td>tests/simplified-self-healing.spec.js</td>
          <td class="failed">Failed</td>
        </tr>
      
        <tr>
          <td>tests/simplified-performance.spec.js</td>
          <td class="passed">Passed</td>
        </tr>
      
    </table>
  </div>
  
  <div class="test-results">
    <h2>Full Tests</h2>
    <table>
      <tr>
        <th>Test</th>
        <th>Result</th>
      </tr>
      
        <tr>
          <td>tests/integration/shared-utils.integration.spec.js</td>
          <td class="failed">Failed</td>
        </tr>
      
        <tr>
          <td>tests/integration/mcp-optimizer.integration.spec.js</td>
          <td class="failed">Failed</td>
        </tr>
      
        <tr>
          <td>tests/integration/self-healing.integration.spec.js</td>
          <td class="failed">Failed</td>
        </tr>
      
        <tr>
          <td>tests/integration/cross-component.integration.spec.js</td>
          <td class="failed">Failed</td>
        </tr>
      
        <tr>
          <td>tests/e2e/login.e2e.spec.js</td>
          <td class="failed">Failed</td>
        </tr>
      
        <tr>
          <td>tests/e2e/file-operations.e2e.spec.js</td>
          <td class="failed">Failed</td>
        </tr>
      
        <tr>
          <td>tests/self-healing/selector-healing.spec.js</td>
          <td class="failed">Failed</td>
        </tr>
      
        <tr>
          <td>tests/self-healing/recovery-strategies.spec.js</td>
          <td class="failed">Failed</td>
        </tr>
      
        <tr>
          <td>tests/performance/execution-time.spec.js</td>
          <td class="failed">Failed</td>
        </tr>
      
        <tr>
          <td>tests/performance/token-usage.spec.js</td>
          <td class="failed">Failed</td>
        </tr>
      
    </table>
  </div>
</body>
</html>
  