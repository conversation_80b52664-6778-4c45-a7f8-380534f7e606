/**
 * Run Testmo Integration
 * 
 * This script runs the entire Testmo integration process:
 * 1. Run Playwright tests
 * 2. Convert Playwright test results to QA Wolf format
 * 3. Submit test results to Testmo
 */

const { execSync } = require('child_process');
const path = require('path');
const { findLatestPlaywrightReport, convertAndSave } = require('./convert-playwright-results');
const { processTestResults } = require('./testmo-integration');

// Configuration
const config = {
  testPattern: process.env.TEST_PATTERN || 'tests/simplified-*.spec.js',
  testmoRunName: process.env.TESTMO_RUN_NAME || `QA Wolf Test Run - ${new Date().toISOString()}`,
  testmoRunDescription: process.env.TESTMO_RUN_DESCRIPTION || 'Automated test run from QA Wolf',
  testmoSource: process.env.TESTMO_SOURCE || 'qawolf',
  testmoBranch: process.env.TESTMO_BRANCH || process.env.GITHUB_REF_NAME || 'main',
  testmoCommit: process.env.TESTMO_COMMIT || process.env.GITHUB_SHA || 'latest',
  testmoEnvironment: process.env.TESTMO_ENVIRONMENT || 'staging'
};

/**
 * Run Playwright tests
 * @param {string} testPattern - Test pattern to run
 * @returns {string} - Path to Playwright report file
 */
function runPlaywrightTests(testPattern) {
  try {
    console.log(`Running Playwright tests with pattern: ${testPattern}...`);
    
    // Run Playwright tests
    execSync(`npx playwright test ${testPattern} --reporter=json`, {
      stdio: 'inherit'
    });
    
    // Find the latest Playwright report file
    const reportFile = findLatestPlaywrightReport();
    
    console.log(`Playwright tests completed successfully!`);
    console.log(`Report file: ${reportFile}`);
    
    return reportFile;
  } catch (error) {
    console.error('Error running Playwright tests:', error.message);
    
    // Find the latest Playwright report file even if tests failed
    try {
      const reportFile = findLatestPlaywrightReport();
      console.log(`Report file: ${reportFile}`);
      return reportFile;
    } catch (findError) {
      console.error('Error finding Playwright report:', findError.message);
      throw error;
    }
  }
}

/**
 * Run the entire Testmo integration process
 * @param {Object} options - Options
 * @returns {Promise<Object>} - Test run data
 */
async function runTestmoIntegration(options = {}) {
  try {
    console.log('Starting Testmo integration process...');
    
    // Run Playwright tests
    const reportFile = runPlaywrightTests(options.testPattern || config.testPattern);
    
    // Convert Playwright test results to QA Wolf format
    const outputFile = `qawolf-results-${Date.now()}.json`;
    const qaWolfResultsFile = convertAndSave(reportFile, outputFile);
    
    // Submit test results to Testmo
    const testRun = await processTestResults(qaWolfResultsFile, {
      name: options.testmoRunName || config.testmoRunName,
      description: options.testmoRunDescription || config.testmoRunDescription,
      source: options.testmoSource || config.testmoSource,
      branch: options.testmoBranch || config.testmoBranch,
      commit: options.testmoCommit || config.testmoCommit,
      environment: options.testmoEnvironment || config.testmoEnvironment
    });
    
    console.log('Testmo integration process completed successfully!');
    
    return testRun;
  } catch (error) {
    console.error('Error running Testmo integration:', error.message);
    throw error;
  }
}

// Export functions for use in other scripts
module.exports = {
  runPlaywrightTests,
  runTestmoIntegration
};

// Run the script if called directly
if (require.main === module) {
  // Get options from command line arguments
  const options = {
    testPattern: process.argv[2] || config.testPattern,
    testmoRunName: process.argv[3] || config.testmoRunName,
    testmoRunDescription: process.argv[4] || config.testmoRunDescription,
    testmoSource: process.argv[5] || config.testmoSource,
    testmoBranch: process.argv[6] || config.testmoBranch,
    testmoCommit: process.argv[7] || config.testmoCommit,
    testmoEnvironment: process.argv[8] || config.testmoEnvironment
  };
  
  runTestmoIntegration(options)
    .then(() => {
      console.log('Done!');
      process.exit(0);
    })
    .catch(error => {
      console.error('Error:', error.message);
      process.exit(1);
    });
}
