name: Documentation

on:
  push:
    branches: [ main ]
    paths:
      - 'docs/**'
      - '**/*.md'
      - '**/*.js'
      - '**/*.ts'

jobs:
  build:
    name: Build Documentation
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: 20
        cache: 'npm'
    
    - name: Install dependencies
      run: |
        npm ci
        npm run bootstrap
    
    - name: Generate API documentation
      run: npm run docs:api
    
    - name: Build documentation website
      run: npm run docs:build
    
    - name: Deploy to GitHub Pages
      uses: JamesIves/github-pages-deploy-action@v4
      with:
        folder: docs/build
        branch: gh-pages