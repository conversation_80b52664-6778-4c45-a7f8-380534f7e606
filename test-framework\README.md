# @qawolf/test-framework

Core QA Wolf testing framework with MCP integration and self-healing automation.

## Installation

```bash
npm install @qawolf/test-framework
```

## Usage

### MCP Integration

```javascript
const { test } = require('@playwright/test');
const { createMcpController, test: testUtils } = require('@qawolf/test-framework');

test('Login with MCP integration', async ({ page }) => {
  // Create an MCP controller
  const mcpController = createMcpController({
    autoStartPlaywrightMcp: true,
    generateFallbacks: true,
    prioritizeTestIds: true
  });
  
  try {
    // Start performance tracking
    const performanceTracker = new testUtils.PerformanceTracker();
    
    // Navigate to the app
    performanceTracker.startOperation('navigation_to_app');
    await page.goto('https://app.lidostaging.com');
    performanceTracker.endOperation();
    
    // Optimize selectors for login form
    const selectors = await mcpController.optimizeSelectors([
      '[data-test-id="SignInEmail"]',
      '[data-test-id="SignInPassword"]',
      ':text("Log in with email")'
    ]);
    
    // Fill in login form
    performanceTracker.startOperation('login');
    await page.fill(selectors.selectors[0].optimized, '<EMAIL>');
    await page.fill(selectors.selectors[1].optimized, 'vhc!tGK289IS&');
    
    // Click the login button
    await page.click(selectors.selectors[2].optimized);
    
    // Wait for login to complete
    await page.waitForSelector('div[class*="FilesTable"]', { timeout: 15000 });
    performanceTracker.endOperation();
    
    // Print test summary
    testUtils.printTestSummary(
      {
        success: true,
        performanceMetrics: performanceTracker.getMetrics(),
        aaaComplianceScore: testUtils.calculateAAAComplianceScore({
          success: true,
          performanceMetrics: performanceTracker.getMetrics(),
          allPerformanceMetricsWithinThresholds: performanceTracker.areAllMetricsWithinThresholds()
        })
      },
      'login-with-mcp',
      'Login with MCP integration',
      performanceTracker.startTime
    );
  } finally {
    // Clean up MCP resources
    await mcpController.cleanup();
  }
});
```

### Self-Healing Automation

```javascript
const { test } = require('@playwright/test');
const { createSelfHealingController, test: testUtils } = require('@qawolf/test-framework');

test('Login with self-healing automation', async ({ page }) => {
  // Create a self-healing controller
  const selfHealingController = createSelfHealingController({
    selectorHealing: {
      enabled: true,
      maxAttempts: 3,
      strategies: ['css-relaxation', 'attribute-based', 'text-based', 'xpath']
    },
    recovery: {
      enabled: true,
      maxAttempts: 3,
      strategies: ['retry', 'wait', 'selector', 'refresh', 'screenshot']
    }
  });
  
  try {
    // Start the test run
    await selfHealingController.startTest({
      testId: 'login-self-healing',
      testName: 'Login with self-healing automation'
    });
    
    // Create a self-healing page
    const selfHealingPage = selfHealingController.createPage(page);
    
    // Start performance tracking
    const performanceTracker = new testUtils.PerformanceTracker();
    
    // Navigate to the app
    performanceTracker.startOperation('navigation_to_app');
    await selfHealingPage.goto('https://app.lidostaging.com');
    performanceTracker.endOperation();
    
    // Fill in login form
    performanceTracker.startOperation('login');
    
    // These selectors will be automatically healed if they fail
    await selfHealingPage.fill('[data-test-id="SignInEmail"]', '<EMAIL>');
    await selfHealingPage.fill('[data-test-id="SignInPassword"]', 'vhc!tGK289IS&');
    
    // Use a locator with self-healing
    const loginButton = selfHealingPage.locator(':text("Log in with email")');
    await loginButton.click();
    
    // Wait for login to complete
    await selfHealingPage.waitForSelector('div[class*="FilesTable"]', { timeout: 15000 });
    performanceTracker.endOperation();
    
    // End the test run
    await selfHealingController.endTest({
      success: true
    });
    
    // Print test summary
    testUtils.printTestSummary(
      {
        success: true,
        performanceMetrics: performanceTracker.getMetrics(),
        aaaComplianceScore: testUtils.calculateAAAComplianceScore({
          success: true,
          performanceMetrics: performanceTracker.getMetrics(),
          allPerformanceMetricsWithinThresholds: performanceTracker.areAllMetricsWithinThresholds()
        })
      },
      'login-self-healing',
      'Login with self-healing automation',
      performanceTracker.startTime
    );
  } catch (error) {
    // End the test run with failure
    await selfHealingController.endTest({
      success: false,
      error
    });
    
    throw error;
  } finally {
    // Clean up resources
    await selfHealingController.cleanup();
  }
});
```

## Features

### MCP Integration

- **Selector Optimization**: Automatically optimize selectors for better reliability
- **Tool Selection**: Automatically select the appropriate MCP tool for each task
- **Screenshot Analysis**: Analyze screenshots to identify UI elements
- **Performance Tracking**: Track the performance of test operations

### Self-Healing Automation

- **Self-Healing Selectors**: Automatically try alternative selectors when the primary selector fails
- **Automatic Recovery**: Attempt recovery strategies when a test step fails
- **Feedback Collection**: Collect data on failures and successful recoveries
- **Learning Engine**: Use collected data to improve future test runs

### Test Utilities

- **Performance Tracking**: Track the performance of test operations
- **Screenshot Utilities**: Take and manage screenshots
- **Test Helpers**: Common utilities for testing

## Documentation

- [MCP Migration Guide](./docs/mcp-migration-guide.md): Guide for migrating existing tests to use the MCP integration
- [Self-Healing Guide](./docs/self-healing-guide.md): Guide for using self-healing automation

## API Reference

### MCP Integration

- `createMcpController(config)`: Create a new MCP controller
- `McpController`: Controller for MCP operations
  - `optimizeSelectors(selectors, options)`: Optimize selectors for better reliability
  - `selectMcpTool(task, options)`: Select the appropriate MCP tool for a task
  - `analyzeScreenshot(screenshot, options)`: Analyze a screenshot to identify UI elements
  - `generateReport(results, options)`: Generate a report on MCP usage
  - `initialize()`: Initialize MCP tools
  - `cleanup()`: Clean up MCP resources

### Test Utilities

- `PerformanceTracker`: Track the performance of test operations
- `calculateAAAComplianceScore(testSummary)`: Calculate AAA compliance score
- `printTestSummary(result, testId, testName, startTime)`: Print test summary
- `launchBrowser(options)`: Launch a browser for testing
- `login(options)`: Log in to the application
- `createFile(page, options)`: Create a new file in the application

### Screenshot Utilities

- `takeScreenshot(page, options)`: Take a screenshot
- `takeErrorScreenshot(page, options)`: Take an error screenshot
- `getTestScreenshots(testName, ticketId, date, runId)`: Get all screenshots for a test
- `cleanupScreenshots(olderThanDays)`: Clean up old screenshots
- `verifyScreenshotStructure()`: Verify screenshot structure
- `moveStrayScreenshots(testName)`: Move stray screenshots

### Configuration Utilities

- `loadConfigFromFile(configPath)`: Load configuration from a file
- `getConfig(key, defaultValue)`: Get configuration value
- `getAllConfig()`: Get all configuration values
- `setConfig(key, value)`: Set configuration value

## Documentation

- [MCP Migration Guide](./docs/mcp-migration-guide.md): Guide for migrating existing tests to use the MCP integration

## License

MIT