/**
 * Utilities for reporting
 */

import * as fs from 'fs';
import * as path from 'path';
import { ReportSection, ReportChart, ReportTable, ReportImage, ReportLink, ReportAttachment, ReportChartType } from './types';

/**
 * Create section
 * @param id Section ID
 * @param title Section title
 * @param description Section description
 * @param content Section content
 * @param data Section data
 * @param charts Section charts
 * @param tables Section tables
 * @param images Section images
 * @param links Section links
 * @param attachments Section attachments
 * @param order Section order
 * @param visible Section visible
 * @param collapsible Section collapsible
 * @param collapsed Section collapsed
 * @returns Section
 */
export function createSection(
  id: string,
  title: string,
  description?: string,
  content?: string,
  data?: Record<string, any>,
  charts?: ReportChart[],
  tables?: ReportTable[],
  images?: ReportImage[],
  links?: ReportLink[],
  attachments?: ReportAttachment[],
  order?: number,
  visible?: boolean,
  collapsible?: boolean,
  collapsed?: boolean
): ReportSection {
  return {
    id,
    title,
    description,
    content,
    data,
    charts,
    tables,
    images,
    links,
    attachments,
    order,
    visible,
    collapsible,
    collapsed
  };
}

/**
 * Create chart
 * @param id Chart ID
 * @param title Chart title
 * @param description Chart description
 * @param type Chart type
 * @param data Chart data
 * @param options Chart options
 * @param width Chart width
 * @param height Chart height
 * @param order Chart order
 * @param visible Chart visible
 * @returns Chart
 */
export function createChart(
  id: string,
  title: string,
  description: string,
  type: ReportChartType,
  data: any,
  options?: any,
  width?: number,
  height?: number,
  order?: number,
  visible?: boolean
): ReportChart {
  return {
    id,
    title,
    description,
    type,
    data,
    options,
    width,
    height,
    order,
    visible
  };
}

/**
 * Create bar chart
 * @param id Chart ID
 * @param title Chart title
 * @param description Chart description
 * @param labels Chart labels
 * @param datasets Chart datasets
 * @param options Chart options
 * @param width Chart width
 * @param height Chart height
 * @param order Chart order
 * @param visible Chart visible
 * @returns Chart
 */
export function createBarChart(
  id: string,
  title: string,
  description: string,
  labels: string[],
  datasets: any[],
  options?: any,
  width?: number,
  height?: number,
  order?: number,
  visible?: boolean
): ReportChart {
  return createChart(
    id,
    title,
    description,
    ReportChartType.BAR,
    {
      labels,
      datasets
    },
    options,
    width,
    height,
    order,
    visible
  );
}

/**
 * Create line chart
 * @param id Chart ID
 * @param title Chart title
 * @param description Chart description
 * @param labels Chart labels
 * @param datasets Chart datasets
 * @param options Chart options
 * @param width Chart width
 * @param height Chart height
 * @param order Chart order
 * @param visible Chart visible
 * @returns Chart
 */
export function createLineChart(
  id: string,
  title: string,
  description: string,
  labels: string[],
  datasets: any[],
  options?: any,
  width?: number,
  height?: number,
  order?: number,
  visible?: boolean
): ReportChart {
  return createChart(
    id,
    title,
    description,
    ReportChartType.LINE,
    {
      labels,
      datasets
    },
    options,
    width,
    height,
    order,
    visible
  );
}

/**
 * Create pie chart
 * @param id Chart ID
 * @param title Chart title
 * @param description Chart description
 * @param labels Chart labels
 * @param datasets Chart datasets
 * @param options Chart options
 * @param width Chart width
 * @param height Chart height
 * @param order Chart order
 * @param visible Chart visible
 * @returns Chart
 */
export function createPieChart(
  id: string,
  title: string,
  description: string,
  labels: string[],
  datasets: any[],
  options?: any,
  width?: number,
  height?: number,
  order?: number,
  visible?: boolean
): ReportChart {
  return createChart(
    id,
    title,
    description,
    ReportChartType.PIE,
    {
      labels,
      datasets
    },
    options,
    width,
    height,
    order,
    visible
  );
}

/**
 * Create table
 * @param id Table ID
 * @param title Table title
 * @param description Table description
 * @param headers Table headers
 * @param rows Table rows
 * @param footer Table footer
 * @param width Table width
 * @param height Table height
 * @param order Table order
 * @param visible Table visible
 * @param sortable Table sortable
 * @param filterable Table filterable
 * @param pageable Table pageable
 * @param pageSize Table page size
 * @returns Table
 */
export function createTable(
  id: string,
  title: string,
  description: string,
  headers: string[],
  rows: any[][],
  footer?: string[],
  width?: number,
  height?: number,
  order?: number,
  visible?: boolean,
  sortable?: boolean,
  filterable?: boolean,
  pageable?: boolean,
  pageSize?: number
): ReportTable {
  return {
    id,
    title,
    description,
    headers,
    rows,
    footer,
    width,
    height,
    order,
    visible,
    sortable,
    filterable,
    pageable,
    pageSize
  };
}

/**
 * Create image
 * @param id Image ID
 * @param title Image title
 * @param description Image description
 * @param url Image URL
 * @param alt Image alt
 * @param width Image width
 * @param height Image height
 * @param order Image order
 * @param visible Image visible
 * @returns Image
 */
export function createImage(
  id: string,
  title: string,
  description: string,
  url: string,
  alt?: string,
  width?: number,
  height?: number,
  order?: number,
  visible?: boolean
): ReportImage {
  return {
    id,
    title,
    description,
    url,
    alt,
    width,
    height,
    order,
    visible
  };
}

/**
 * Create link
 * @param id Link ID
 * @param title Link title
 * @param description Link description
 * @param url Link URL
 * @param target Link target
 * @param order Link order
 * @param visible Link visible
 * @returns Link
 */
export function createLink(
  id: string,
  title: string,
  description: string,
  url: string,
  target?: '_blank' | '_self' | '_parent' | '_top',
  order?: number,
  visible?: boolean
): ReportLink {
  return {
    id,
    title,
    description,
    url,
    target,
    order,
    visible
  };
}

/**
 * Create attachment
 * @param id Attachment ID
 * @param title Attachment title
 * @param description Attachment description
 * @param url Attachment URL
 * @param type Attachment type
 * @param size Attachment size
 * @param order Attachment order
 * @param visible Attachment visible
 * @returns Attachment
 */
export function createAttachment(
  id: string,
  title: string,
  description: string,
  url: string,
  type: string,
  size?: number,
  order?: number,
  visible?: boolean
): ReportAttachment {
  return {
    id,
    title,
    description,
    url,
    type,
    size,
    order,
    visible
  };
}

/**
 * Format date
 * @param date Date
 * @param format Format
 * @returns Formatted date
 */
export function formatDate(date: Date, format: string = 'YYYY-MM-DD'): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  
  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
}

/**
 * Format number
 * @param number Number
 * @param decimals Decimals
 * @param decimalSeparator Decimal separator
 * @param thousandsSeparator Thousands separator
 * @returns Formatted number
 */
export function formatNumber(
  number: number,
  decimals: number = 2,
  decimalSeparator: string = '.',
  thousandsSeparator: string = ','
): string {
  const fixed = number.toFixed(decimals);
  const [whole, decimal] = fixed.split('.');
  
  const parts = [];
  let count = 0;
  
  for (let i = whole.length - 1; i >= 0; i--) {
    if (count === 3) {
      parts.unshift(thousandsSeparator);
      count = 0;
    }
    
    parts.unshift(whole[i]);
    count++;
  }
  
  return `${parts.join('')}${decimal ? `${decimalSeparator}${decimal}` : ''}`;
}

/**
 * Format percentage
 * @param number Number
 * @param decimals Decimals
 * @returns Formatted percentage
 */
export function formatPercentage(number: number, decimals: number = 2): string {
  return `${formatNumber(number * 100, decimals)}%`;
}

/**
 * Format duration
 * @param milliseconds Milliseconds
 * @returns Formatted duration
 */
export function formatDuration(milliseconds: number): string {
  const seconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  const days = Math.floor(hours / 24);
  
  if (days > 0) {
    return `${days}d ${hours % 24}h ${minutes % 60}m ${seconds % 60}s`;
  } else if (hours > 0) {
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`;
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`;
  } else {
    return `${seconds}s`;
  }
}

/**
 * Format file size
 * @param bytes Bytes
 * @param decimals Decimals
 * @returns Formatted file size
 */
export function formatFileSize(bytes: number, decimals: number = 2): string {
  if (bytes === 0) {
    return '0 Bytes';
  }
  
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`;
}

/**
 * Get file extension
 * @param filePath File path
 * @returns File extension
 */
export function getFileExtension(filePath: string): string {
  return path.extname(filePath).slice(1);
}

/**
 * Get file type
 * @param filePath File path
 * @returns File type
 */
export function getFileType(filePath: string): string {
  const extension = getFileExtension(filePath).toLowerCase();
  
  switch (extension) {
    case 'jpg':
    case 'jpeg':
    case 'png':
    case 'gif':
    case 'bmp':
    case 'svg':
    case 'webp':
      return 'image';
    case 'mp4':
    case 'avi':
    case 'mov':
    case 'wmv':
    case 'flv':
    case 'webm':
      return 'video';
    case 'mp3':
    case 'wav':
    case 'ogg':
    case 'flac':
    case 'aac':
      return 'audio';
    case 'pdf':
      return 'pdf';
    case 'doc':
    case 'docx':
      return 'word';
    case 'xls':
    case 'xlsx':
      return 'excel';
    case 'ppt':
    case 'pptx':
      return 'powerpoint';
    case 'txt':
    case 'md':
    case 'markdown':
      return 'text';
    case 'html':
    case 'htm':
      return 'html';
    case 'css':
      return 'css';
    case 'js':
      return 'javascript';
    case 'json':
      return 'json';
    case 'xml':
      return 'xml';
    case 'csv':
      return 'csv';
    case 'zip':
    case 'rar':
    case '7z':
    case 'tar':
    case 'gz':
      return 'archive';
    default:
      return 'unknown';
  }
}

/**
 * Get file size
 * @param filePath File path
 * @returns File size
 */
export function getFileSize(filePath: string): number {
  try {
    const stats = fs.statSync(filePath);
    return stats.size;
  } catch (error) {
    return 0;
  }
}

/**
 * Get file name
 * @param filePath File path
 * @returns File name
 */
export function getFileName(filePath: string): string {
  return path.basename(filePath);
}

/**
 * Get file directory
 * @param filePath File path
 * @returns File directory
 */
export function getFileDirectory(filePath: string): string {
  return path.dirname(filePath);
}

/**
 * Get file creation date
 * @param filePath File path
 * @returns File creation date
 */
export function getFileCreationDate(filePath: string): Date {
  try {
    const stats = fs.statSync(filePath);
    return stats.birthtime;
  } catch (error) {
    return new Date();
  }
}

/**
 * Get file modification date
 * @param filePath File path
 * @returns File modification date
 */
export function getFileModificationDate(filePath: string): Date {
  try {
    const stats = fs.statSync(filePath);
    return stats.mtime;
  } catch (error) {
    return new Date();
  }
}
