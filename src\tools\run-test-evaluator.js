/**
 * Run Test Evaluator
 * 
 * This script runs the test evaluator on all test files in the specified directory.
 * It generates a report for each test file and a summary for the directory.
 * 
 * Usage:
 * node run-test-evaluator.js [directory] [pattern]
 * 
 * Example:
 * node run-test-evaluator.js ../../tests/qawolf ".*_node20.*\.js$"
 * 
 * @module run-test-evaluator
 * <AUTHOR> Wolf Team
 * @date 2025-01-01
 */

const path = require('path');
const { evaluateTestDirectory } = require('./test-evaluator');

// Get command line arguments
const args = process.argv.slice(2);
const dirPath = args[0] || '../../tests/qawolf';
const pattern = args[1] ? new RegExp(args[1]) : /.*\.js$/;

// Resolve the directory path
const resolvedDirPath = path.resolve(__dirname, dirPath);

console.log(`Evaluating test files in ${resolvedDirPath} matching pattern ${pattern}`);

// Run the evaluator
const results = evaluateTestDirectory(resolvedDirPath, pattern);

// Generate a detailed report
console.log('\n========== DETAILED EVALUATION REPORT ==========');

// Group files by compliance level
const excellentFiles = results.filter(result => result.percentage >= 90);
const goodFiles = results.filter(result => result.percentage >= 80 && result.percentage < 90);
const averageFiles = results.filter(result => result.percentage >= 70 && result.percentage < 80);
const poorFiles = results.filter(result => result.percentage < 70);

// Print files by compliance level
console.log(`\nExcellent AAA Compliance (90%+): ${excellentFiles.length} files`);
excellentFiles.forEach(result => {
  console.log(`- ${result.fileName}: ${result.percentage}%`);
});

console.log(`\nGood AAA Compliance (80-89%): ${goodFiles.length} files`);
goodFiles.forEach(result => {
  console.log(`- ${result.fileName}: ${result.percentage}%`);
});

console.log(`\nAverage AAA Compliance (70-79%): ${averageFiles.length} files`);
averageFiles.forEach(result => {
  console.log(`- ${result.fileName}: ${result.percentage}%`);
});

console.log(`\nPoor AAA Compliance (<70%): ${poorFiles.length} files`);
poorFiles.forEach(result => {
  console.log(`- ${result.fileName}: ${result.percentage}%`);
});

// Print category averages
console.log('\nCategory Averages:');
const categories = Object.keys(results[0].criteria);

for (const category of categories) {
  const categoryScores = results.map(result => {
    const criteria = result.criteria[category];
    const categoryScore = Object.values(criteria).reduce((sum, { weight, passed }) => sum + (passed ? weight : 0), 0);
    const categoryMaxScore = Object.values(criteria).reduce((sum, { weight }) => sum + weight, 0);
    return { score: categoryScore, maxScore: categoryMaxScore };
  });
  
  const totalScore = categoryScores.reduce((sum, { score }) => sum + score, 0);
  const totalMaxScore = categoryScores.reduce((sum, { maxScore }) => sum + maxScore, 0);
  const averagePercentage = Math.round((totalScore / totalMaxScore) * 100);
  
  console.log(`- ${category}: ${averagePercentage}%`);
}

// Print most common suggestions
console.log('\nMost Common Improvement Suggestions:');
const allSuggestions = results.flatMap(result => result.suggestions);
const suggestionCounts = {};

for (const suggestion of allSuggestions) {
  suggestionCounts[suggestion] = (suggestionCounts[suggestion] || 0) + 1;
}

const sortedSuggestions = Object.entries(suggestionCounts)
  .sort((a, b) => b[1] - a[1])
  .slice(0, 10);

sortedSuggestions.forEach(([suggestion, count], index) => {
  console.log(`${index + 1}. ${suggestion} (${count} files)`);
});

console.log('\n===================================================');

// Print conclusion
const averagePercentage = results.reduce((sum, result) => sum + result.percentage, 0) / results.length;
console.log(`\nConclusion:`);
console.log(`Average AAA Compliance: ${Math.round(averagePercentage)}%`);

if (averagePercentage >= 90) {
  console.log(`The test suite meets the high AAA compliance standard (${Math.round(averagePercentage)}%). Great job! 🎉`);
} else if (averagePercentage >= 80) {
  console.log(`The test suite is close to meeting the high AAA compliance standard (${Math.round(averagePercentage)}%). A few improvements would push it over 90%. 👍`);
} else if (averagePercentage >= 70) {
  console.log(`The test suite needs improvement to meet the high AAA compliance standard (${Math.round(averagePercentage)}%). Follow the suggestions to improve it. ⚠️`);
} else {
  console.log(`The test suite needs significant improvement to meet the high AAA compliance standard (${Math.round(averagePercentage)}%). Follow the suggestions to improve it. ❌`);
}

// Print next steps
console.log('\nNext Steps:');
if (poorFiles.length > 0) {
  console.log(`1. Focus on improving the ${poorFiles.length} files with poor AAA compliance (<70%)`);
} else if (averageFiles.length > 0) {
  console.log(`1. Focus on improving the ${averageFiles.length} files with average AAA compliance (70-79%)`);
} else if (goodFiles.length > 0) {
  console.log(`1. Focus on improving the ${goodFiles.length} files with good AAA compliance (80-89%)`);
} else {
  console.log(`1. Maintain the high AAA compliance standard in all files`);
}

console.log(`2. Address the most common improvement suggestions`);
console.log(`3. Standardize all test files to use the shared utilities module`);
console.log(`4. Run this evaluator regularly to track progress`);

console.log('\n===================================================');
