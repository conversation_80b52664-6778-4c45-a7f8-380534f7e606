/**
 * Utilities for MCP optimizer
 */

import { MC<PERSON>peration, MCPOperationPattern, MCPOptimizationSuggestion, MCPToolType, MCPOperationType } from './types';

/**
 * Format cost
 * @param cost Cost
 * @param decimals Decimal places
 * @returns Formatted cost
 */
export function formatCost(cost: number, decimals: number = 6): string {
  return `$${cost.toFixed(decimals)}`;
}

/**
 * Format token count
 * @param tokenCount Token count
 * @returns Formatted token count
 */
export function formatTokenCount(tokenCount: number): string {
  if (tokenCount < 1000) {
    return `${tokenCount} tokens`;
  } else {
    return `${(tokenCount / 1000).toFixed(1)}k tokens`;
  }
}

/**
 * Format duration
 * @param duration Duration in milliseconds
 * @returns Formatted duration
 */
export function formatDuration(duration: number): string {
  if (duration < 1000) {
    return `${duration}ms`;
  } else if (duration < 60000) {
    return `${(duration / 1000).toFixed(2)}s`;
  } else {
    const minutes = Math.floor(duration / 60000);
    const seconds = ((duration % 60000) / 1000).toFixed(2);
    return `${minutes}m ${seconds}s`;
  }
}

/**
 * Format timestamp
 * @param timestamp Timestamp
 * @returns Formatted timestamp
 */
export function formatTimestamp(timestamp: number): string {
  return new Date(timestamp).toISOString();
}

/**
 * Group operations by type
 * @param operations Operations
 * @returns Operations grouped by type
 */
export function groupOperationsByType(operations: MCPOperation[]): Record<string, MCPOperation[]> {
  const grouped: Record<string, MCPOperation[]> = {};
  
  for (const operation of operations) {
    if (!grouped[operation.type]) {
      grouped[operation.type] = [];
    }
    
    grouped[operation.type].push(operation);
  }
  
  return grouped;
}

/**
 * Group operations by tool
 * @param operations Operations
 * @returns Operations grouped by tool
 */
export function groupOperationsByTool(operations: MCPOperation[]): Record<string, MCPOperation[]> {
  const grouped: Record<string, MCPOperation[]> = {};
  
  for (const operation of operations) {
    if (!grouped[operation.toolType]) {
      grouped[operation.toolType] = [];
    }
    
    grouped[operation.toolType].push(operation);
  }
  
  return grouped;
}

/**
 * Find similar operations
 * @param operations Operations
 * @returns Similar operations
 */
export function findSimilarOperations(operations: MCPOperation[]): Record<string, MCPOperation[]> {
  const similarOperations: Record<string, MCPOperation[]> = {};
  
  for (const operation of operations) {
    const key = `${operation.type}:${operation.toolType}:${operation.toolName}`;
    
    if (!similarOperations[key]) {
      similarOperations[key] = [];
    }
    
    similarOperations[key].push(operation);
  }
  
  // Filter out keys with only one operation
  return Object.fromEntries(
    Object.entries(similarOperations).filter(([_, ops]) => ops.length > 1)
  );
}

/**
 * Find operation sequences
 * @param operations Operations
 * @param sequenceLength Sequence length
 * @returns Operation sequences
 */
export function findOperationSequences(operations: MCPOperation[], sequenceLength: number = 3): MCPOperation[][] {
  const sequences: MCPOperation[][] = [];
  
  for (let i = 0; i <= operations.length - sequenceLength; i++) {
    const sequence = operations.slice(i, i + sequenceLength);
    sequences.push(sequence);
  }
  
  return sequences;
}

/**
 * Find duplicate sequences
 * @param operations Operations
 * @param sequenceLength Sequence length
 * @returns Duplicate sequences
 */
export function findDuplicateSequences(operations: MCPOperation[], sequenceLength: number = 3): Record<string, MCPOperation[][]> {
  const sequences = findOperationSequences(operations, sequenceLength);
  const sequenceMap: Record<string, MCPOperation[][]> = {};
  
  for (const sequence of sequences) {
    const key = sequence.map(op => `${op.type}:${op.toolType}:${op.toolName}`).join('|');
    
    if (!sequenceMap[key]) {
      sequenceMap[key] = [];
    }
    
    sequenceMap[key].push(sequence);
  }
  
  // Filter out keys with only one sequence
  return Object.fromEntries(
    Object.entries(sequenceMap).filter(([_, seqs]) => seqs.length > 1)
  );
}

/**
 * Calculate operation statistics
 * @param operations Operations
 * @returns Operation statistics
 */
export function calculateOperationStatistics(operations: MCPOperation[]): {
  totalOperations: number;
  totalCost: number;
  totalTokens: number;
  averageCost: number;
  averageTokens: number;
  operationsByType: Record<string, number>;
  operationsByTool: Record<string, number>;
  costByType: Record<string, number>;
  costByTool: Record<string, number>;
  tokensByType: Record<string, number>;
  tokensByTool: Record<string, number>;
} {
  // Calculate total operations
  const totalOperations = operations.length;
  
  // Calculate total cost
  const totalCost = operations.reduce((sum, operation) => sum + (operation.cost || 0), 0);
  
  // Calculate total tokens
  const totalTokens = operations.reduce((sum, operation) => sum + (operation.tokenCount || 0), 0);
  
  // Calculate average cost
  const averageCost = totalOperations > 0 ? totalCost / totalOperations : 0;
  
  // Calculate average tokens
  const averageTokens = totalOperations > 0 ? totalTokens / totalOperations : 0;
  
  // Calculate operations by type
  const operationsByType: Record<string, number> = {};
  
  for (const operation of operations) {
    if (!operationsByType[operation.type]) {
      operationsByType[operation.type] = 0;
    }
    
    operationsByType[operation.type]++;
  }
  
  // Calculate operations by tool
  const operationsByTool: Record<string, number> = {};
  
  for (const operation of operations) {
    if (!operationsByTool[operation.toolType]) {
      operationsByTool[operation.toolType] = 0;
    }
    
    operationsByTool[operation.toolType]++;
  }
  
  // Calculate cost by type
  const costByType: Record<string, number> = {};
  
  for (const operation of operations) {
    if (!costByType[operation.type]) {
      costByType[operation.type] = 0;
    }
    
    costByType[operation.type] += operation.cost || 0;
  }
  
  // Calculate cost by tool
  const costByTool: Record<string, number> = {};
  
  for (const operation of operations) {
    if (!costByTool[operation.toolType]) {
      costByTool[operation.toolType] = 0;
    }
    
    costByTool[operation.toolType] += operation.cost || 0;
  }
  
  // Calculate tokens by type
  const tokensByType: Record<string, number> = {};
  
  for (const operation of operations) {
    if (!tokensByType[operation.type]) {
      tokensByType[operation.type] = 0;
    }
    
    tokensByType[operation.type] += operation.tokenCount || 0;
  }
  
  // Calculate tokens by tool
  const tokensByTool: Record<string, number> = {};
  
  for (const operation of operations) {
    if (!tokensByTool[operation.toolType]) {
      tokensByTool[operation.toolType] = 0;
    }
    
    tokensByTool[operation.toolType] += operation.tokenCount || 0;
  }
  
  return {
    totalOperations,
    totalCost,
    totalTokens,
    averageCost,
    averageTokens,
    operationsByType,
    operationsByTool,
    costByType,
    costByTool,
    tokensByType,
    tokensByTool
  };
}

/**
 * Calculate pattern statistics
 * @param patterns Patterns
 * @returns Pattern statistics
 */
export function calculatePatternStatistics(patterns: MCPOperationPattern[]): {
  totalPatterns: number;
  totalFrequency: number;
  totalCost: number;
  totalTokens: number;
  averageFrequency: number;
  averageCost: number;
  averageTokens: number;
} {
  // Calculate total patterns
  const totalPatterns = patterns.length;
  
  // Calculate total frequency
  const totalFrequency = patterns.reduce((sum, pattern) => sum + pattern.frequency, 0);
  
  // Calculate total cost
  const totalCost = patterns.reduce((sum, pattern) => sum + pattern.cost, 0);
  
  // Calculate total tokens
  const totalTokens = patterns.reduce((sum, pattern) => sum + pattern.tokenCount, 0);
  
  // Calculate average frequency
  const averageFrequency = totalPatterns > 0 ? totalFrequency / totalPatterns : 0;
  
  // Calculate average cost
  const averageCost = totalPatterns > 0 ? totalCost / totalPatterns : 0;
  
  // Calculate average tokens
  const averageTokens = totalPatterns > 0 ? totalTokens / totalPatterns : 0;
  
  return {
    totalPatterns,
    totalFrequency,
    totalCost,
    totalTokens,
    averageFrequency,
    averageCost,
    averageTokens
  };
}

/**
 * Calculate suggestion statistics
 * @param suggestions Suggestions
 * @returns Suggestion statistics
 */
export function calculateSuggestionStatistics(suggestions: MCPOptimizationSuggestion[]): {
  totalSuggestions: number;
  totalCostSavings: number;
  totalTokenSavings: number;
  averageCostSavings: number;
  averageTokenSavings: number;
  suggestionsByType: Record<string, number>;
  suggestionsByPriority: Record<string, number>;
  costSavingsByType: Record<string, number>;
  costSavingsByPriority: Record<string, number>;
  tokenSavingsByType: Record<string, number>;
  tokenSavingsByPriority: Record<string, number>;
} {
  // Calculate total suggestions
  const totalSuggestions = suggestions.length;
  
  // Calculate total cost savings
  const totalCostSavings = suggestions.reduce((sum, suggestion) => sum + suggestion.costSavings, 0);
  
  // Calculate total token savings
  const totalTokenSavings = suggestions.reduce((sum, suggestion) => sum + suggestion.tokenSavings, 0);
  
  // Calculate average cost savings
  const averageCostSavings = totalSuggestions > 0 ? totalCostSavings / totalSuggestions : 0;
  
  // Calculate average token savings
  const averageTokenSavings = totalSuggestions > 0 ? totalTokenSavings / totalSuggestions : 0;
  
  // Calculate suggestions by type
  const suggestionsByType: Record<string, number> = {};
  
  for (const suggestion of suggestions) {
    if (!suggestionsByType[suggestion.type]) {
      suggestionsByType[suggestion.type] = 0;
    }
    
    suggestionsByType[suggestion.type]++;
  }
  
  // Calculate suggestions by priority
  const suggestionsByPriority: Record<string, number> = {};
  
  for (const suggestion of suggestions) {
    if (!suggestionsByPriority[suggestion.priority]) {
      suggestionsByPriority[suggestion.priority] = 0;
    }
    
    suggestionsByPriority[suggestion.priority]++;
  }
  
  // Calculate cost savings by type
  const costSavingsByType: Record<string, number> = {};
  
  for (const suggestion of suggestions) {
    if (!costSavingsByType[suggestion.type]) {
      costSavingsByType[suggestion.type] = 0;
    }
    
    costSavingsByType[suggestion.type] += suggestion.costSavings;
  }
  
  // Calculate cost savings by priority
  const costSavingsByPriority: Record<string, number> = {};
  
  for (const suggestion of suggestions) {
    if (!costSavingsByPriority[suggestion.priority]) {
      costSavingsByPriority[suggestion.priority] = 0;
    }
    
    costSavingsByPriority[suggestion.priority] += suggestion.costSavings;
  }
  
  // Calculate token savings by type
  const tokenSavingsByType: Record<string, number> = {};
  
  for (const suggestion of suggestions) {
    if (!tokenSavingsByType[suggestion.type]) {
      tokenSavingsByType[suggestion.type] = 0;
    }
    
    tokenSavingsByType[suggestion.type] += suggestion.tokenSavings;
  }
  
  // Calculate token savings by priority
  const tokenSavingsByPriority: Record<string, number> = {};
  
  for (const suggestion of suggestions) {
    if (!tokenSavingsByPriority[suggestion.priority]) {
      tokenSavingsByPriority[suggestion.priority] = 0;
    }
    
    tokenSavingsByPriority[suggestion.priority] += suggestion.tokenSavings;
  }
  
  return {
    totalSuggestions,
    totalCostSavings,
    totalTokenSavings,
    averageCostSavings,
    averageTokenSavings,
    suggestionsByType,
    suggestionsByPriority,
    costSavingsByType,
    costSavingsByPriority,
    tokenSavingsByType,
    tokenSavingsByPriority
  };
}
