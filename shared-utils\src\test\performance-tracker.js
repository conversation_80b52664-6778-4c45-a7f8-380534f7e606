/**
 * Performance Tracking Utility
 * 
 * This module provides utilities for tracking the performance of tests.
 */

/**
 * Performance tracking utility
 * Tracks execution time of operations and provides performance metrics
 */
class PerformanceTracker {
  /**
   * Create a new PerformanceTracker
   * @param {Object} customThresholds - Custom thresholds for operations
   */
  constructor(customThresholds = {}) {
    this.metrics = {};
    this.startTime = Date.now();
    this.currentOperation = null;
    this.operationStartTime = null;
    
    // Default thresholds
    this.thresholds = {
      'browser_launch': 5000,     // 5 seconds
      'navigation_to_app': 5000,  // 5 seconds
      'login': 5000,              // 5 seconds
      'file_creation': 3000,      // 3 seconds
      'file_deletion': 3000,      // 3 seconds
      'navigation': 2000,         // 2 seconds
      'sort_by_name': 3000,       // 3 seconds
      'sort_by_date': 3000,       // 3 seconds
      'login_attempt': 3000,      // 3 seconds
      'error_verification': 2000, // 2 seconds
      'total_execution': 30000    // 30 seconds
    };
    
    // Override defaults with custom thresholds
    Object.assign(this.thresholds, customThresholds);
  }

  /**
   * Start tracking an operation
   * @param {string} operation - Name of the operation
   */
  startOperation(operation) {
    this.currentOperation = operation;
    this.operationStartTime = Date.now();
    console.log(`PERFORMANCE: Starting operation "${operation}"`);
  }

  /**
   * End tracking the current operation
   * @returns {number} - Duration of the operation in milliseconds
   */
  endOperation() {
    if (!this.currentOperation || !this.operationStartTime) {
      console.log('PERFORMANCE: No operation in progress');
      return 0;
    }

    const endTime = Date.now();
    const duration = endTime - this.operationStartTime;
    
    this.metrics[this.currentOperation] = duration;
    
    const threshold = this.thresholds[this.currentOperation] || 5000;
    const isWithinThreshold = duration <= threshold;
    
    console.log(`PERFORMANCE: Operation "${this.currentOperation}" completed in ${duration}ms (threshold: ${threshold}ms) - ${isWithinThreshold ? 'WITHIN THRESHOLD ✅' : 'EXCEEDED THRESHOLD ❌'}`);
    
    this.currentOperation = null;
    this.operationStartTime = null;
    
    return duration;
  }

  /**
   * Get the total execution time
   * @returns {number} - Total execution time in milliseconds
   */
  getTotalExecutionTime() {
    return Date.now() - this.startTime;
  }

  /**
   * Get all performance metrics
   * @returns {Object} - Performance metrics
   */
  getMetrics() {
    const totalTime = this.getTotalExecutionTime();
    return {
      ...this.metrics,
      total_execution: totalTime
    };
  }

  /**
   * Check if all metrics are within thresholds
   * @returns {boolean} - Whether all metrics are within thresholds
   */
  areAllMetricsWithinThresholds() {
    const metrics = this.getMetrics();
    
    for (const [operation, duration] of Object.entries(metrics)) {
      const threshold = this.thresholds[operation] || 5000;
      if (duration > threshold) {
        return false;
      }
    }
    
    return true;
  }

  /**
   * Generate a performance report
   * @returns {string} - Performance report
   */
  generateReport() {
    const metrics = this.getMetrics();
    let report = '\n=== PERFORMANCE REPORT ===\n';
    
    for (const [operation, duration] of Object.entries(metrics)) {
      const threshold = this.thresholds[operation] || 5000;
      const isWithinThreshold = duration <= threshold;
      report += `${operation}: ${duration}ms (threshold: ${threshold}ms) - ${isWithinThreshold ? 'PASS ✅' : 'FAIL ❌'}\n`;
    }
    
    const allWithinThresholds = this.areAllMetricsWithinThresholds();
    report += `\nOverall Performance: ${allWithinThresholds ? 'PASS ✅' : 'FAIL ❌'}\n`;
    report += '=========================\n';
    
    return report;
  }
}

module.exports = PerformanceTracker;