{"name": "@qawolf/mcp-optimizer", "version": "1.0.0", "description": "MCP optimization and orchestration for QA Wolf testing", "main": "src/index.js", "bin": {"mcp-optimize": "./bin/mcp-optimize.js"}, "scripts": {"test": "playwright test", "lint": "eslint .", "build": "mkdir -p dist && cp -r src/* dist/ && cp -r bin dist/"}, "publishConfig": {"registry": "https://npm.pkg.github.com/"}, "repository": {"type": "git", "url": "git+https://github.com/sneezyxl/QAWolfeesojc.git", "directory": "mcp-optimizer"}, "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "playwright", "mcp", "optimization", "ai", "llm"], "author": "", "license": "MIT", "dependencies": {"@qawolf/shared-utils": "1.0.0", "@playwright/mcp": "^0.0.25"}, "devDependencies": {"eslint": "^9.27.0"}}