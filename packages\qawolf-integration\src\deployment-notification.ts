/**
 * QA Wolf Deployment Notification
 * 
 * This module provides functionality to notify QA Wolf of a new deployment.
 */

import * as fs from 'fs';
import * as path from 'path';
import axios from 'axios';
import { EventBus, EventType } from '@qawolf/core';

/**
 * Deployment notification options
 */
export interface DeploymentNotificationOptions {
  /**
   * API key
   */
  apiKey?: string;
  
  /**
   * Team ID
   */
  teamId?: string;
  
  /**
   * API URL
   */
  apiUrl?: string;
  
  /**
   * Reports directory
   */
  reportsDir?: string;
  
  /**
   * Environment
   */
  environment?: string;
  
  /**
   * Branch
   */
  branch?: string;
  
  /**
   * Commit
   */
  commit?: string;
  
  /**
   * Build URL
   */
  buildUrl?: string;
}

/**
 * Deployment info
 */
export interface DeploymentInfo {
  /**
   * Team ID
   */
  teamId: string;
  
  /**
   * Environment
   */
  environment: string;
  
  /**
   * Branch
   */
  branch: string;
  
  /**
   * Commit
   */
  commit: string;
  
  /**
   * Build URL
   */
  buildUrl?: string;
  
  /**
   * Timestamp
   */
  timestamp: string;
}

/**
 * Deployment notification
 */
export class DeploymentNotification {
  /**
   * API key
   */
  private apiKey: string;
  
  /**
   * Team ID
   */
  private teamId: string;
  
  /**
   * API URL
   */
  private apiUrl: string;
  
  /**
   * Reports directory
   */
  private reportsDir: string;
  
  /**
   * Environment
   */
  private environment: string;
  
  /**
   * Branch
   */
  private branch: string;
  
  /**
   * Commit
   */
  private commit: string;
  
  /**
   * Build URL
   */
  private buildUrl?: string;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options Deployment notification options
   */
  constructor(options: DeploymentNotificationOptions = {}) {
    this.apiKey = options.apiKey || process.env.QA_WOLF_API_KEY || '';
    this.teamId = options.teamId || process.env.QA_WOLF_TEAM_ID || 'clux0gjs50sb3ak01fnh7wvja';
    this.apiUrl = options.apiUrl || 'https://app.qawolf.com/api/ci';
    this.reportsDir = options.reportsDir || './qawolf-reports';
    this.environment = options.environment || process.env.ENVIRONMENT || 'staging';
    this.branch = options.branch || process.env.GITHUB_REF_NAME || 'main';
    this.commit = options.commit || process.env.GITHUB_SHA || 'latest';
    this.buildUrl = options.buildUrl || (process.env.GITHUB_SERVER_URL ? `${process.env.GITHUB_SERVER_URL}/${process.env.GITHUB_REPOSITORY}/actions/runs/${process.env.GITHUB_RUN_ID}` : undefined);
    
    this.eventBus = EventBus.getInstance();
    
    // Create reports directory if it doesn't exist
    if (!fs.existsSync(this.reportsDir)) {
      fs.mkdirSync(this.reportsDir, { recursive: true });
    }
  }
  
  /**
   * Get deployment info
   * @returns Deployment info
   */
  getDeploymentInfo(): DeploymentInfo {
    return {
      teamId: this.teamId,
      environment: this.environment,
      branch: this.branch,
      commit: this.commit,
      buildUrl: this.buildUrl,
      timestamp: new Date().toISOString()
    };
  }
  
  /**
   * Save deployment info
   * @returns Deployment info
   */
  saveDeploymentInfo(): DeploymentInfo {
    const deploymentInfo = this.getDeploymentInfo();
    
    fs.writeFileSync(
      path.join(this.reportsDir, 'deployment-info.json'),
      JSON.stringify(deploymentInfo, null, 2)
    );
    
    return deploymentInfo;
  }
  
  /**
   * Notify deployment
   * @returns Promise<any> - Deployment response
   */
  async notifyDeployment(): Promise<any> {
    console.log('Notifying QA Wolf of deployment...');
    
    if (!this.apiKey) {
      throw new Error('QA Wolf API key is required');
    }
    
    if (!this.teamId) {
      throw new Error('QA Wolf team ID is required');
    }
    
    // Save deployment info
    const deploymentInfo = this.saveDeploymentInfo();
    
    console.log('Deployment info:', deploymentInfo);
    
    try {
      const response = await axios.post(
        `${this.apiUrl}/deployments`,
        deploymentInfo,
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.apiKey}`
          }
        }
      );
      
      console.log('QA Wolf deployment notification successful!');
      console.log('Response:', response.data);
      
      // Save deployment response to file
      fs.writeFileSync(
        path.join(this.reportsDir, 'deployment-response.json'),
        JSON.stringify(response.data, null, 2)
      );
      
      // Emit event
      this.eventBus.emit(EventType.QA_WOLF_DEPLOYMENT_NOTIFICATION_SUCCESS, {
        deploymentInfo,
        response: response.data
      });
      
      return response.data;
    } catch (error) {
      console.error('Error notifying QA Wolf of deployment:', error.message);
      
      if (error.response) {
        console.error('Response data:', error.response.data);
        console.error('Response status:', error.response.status);
      }
      
      // Save error to file
      fs.writeFileSync(
        path.join(this.reportsDir, 'deployment-error.json'),
        JSON.stringify({
          error: error.message,
          response: error.response ? {
            data: error.response.data,
            status: error.response.status
          } : null,
          timestamp: new Date().toISOString()
        }, null, 2)
      );
      
      // Emit event
      this.eventBus.emit(EventType.QA_WOLF_DEPLOYMENT_NOTIFICATION_ERROR, {
        deploymentInfo,
        error
      });
      
      throw error;
    }
  }
  
  /**
   * Get API key
   * @returns API key
   */
  getApiKey(): string {
    return this.apiKey;
  }
  
  /**
   * Get team ID
   * @returns Team ID
   */
  getTeamId(): string {
    return this.teamId;
  }
  
  /**
   * Get API URL
   * @returns API URL
   */
  getApiUrl(): string {
    return this.apiUrl;
  }
  
  /**
   * Get reports directory
   * @returns Reports directory
   */
  getReportsDir(): string {
    return this.reportsDir;
  }
  
  /**
   * Get environment
   * @returns Environment
   */
  getEnvironment(): string {
    return this.environment;
  }
  
  /**
   * Get branch
   * @returns Branch
   */
  getBranch(): string {
    return this.branch;
  }
  
  /**
   * Get commit
   * @returns Commit
   */
  getCommit(): string {
    return this.commit;
  }
  
  /**
   * Get build URL
   * @returns Build URL
   */
  getBuildUrl(): string | undefined {
    return this.buildUrl;
  }
  
  /**
   * Set API key
   * @param apiKey API key
   * @returns This instance for chaining
   */
  setApiKey(apiKey: string): DeploymentNotification {
    this.apiKey = apiKey;
    return this;
  }
  
  /**
   * Set team ID
   * @param teamId Team ID
   * @returns This instance for chaining
   */
  setTeamId(teamId: string): DeploymentNotification {
    this.teamId = teamId;
    return this;
  }
  
  /**
   * Set API URL
   * @param apiUrl API URL
   * @returns This instance for chaining
   */
  setApiUrl(apiUrl: string): DeploymentNotification {
    this.apiUrl = apiUrl;
    return this;
  }
  
  /**
   * Set reports directory
   * @param reportsDir Reports directory
   * @returns This instance for chaining
   */
  setReportsDir(reportsDir: string): DeploymentNotification {
    this.reportsDir = reportsDir;
    return this;
  }
  
  /**
   * Set environment
   * @param environment Environment
   * @returns This instance for chaining
   */
  setEnvironment(environment: string): DeploymentNotification {
    this.environment = environment;
    return this;
  }
  
  /**
   * Set branch
   * @param branch Branch
   * @returns This instance for chaining
   */
  setBranch(branch: string): DeploymentNotification {
    this.branch = branch;
    return this;
  }
  
  /**
   * Set commit
   * @param commit Commit
   * @returns This instance for chaining
   */
  setCommit(commit: string): DeploymentNotification {
    this.commit = commit;
    return this;
  }
  
  /**
   * Set build URL
   * @param buildUrl Build URL
   * @returns This instance for chaining
   */
  setBuildUrl(buildUrl: string): DeploymentNotification {
    this.buildUrl = buildUrl;
    return this;
  }
}

/**
 * Create deployment notification
 * @param options Deployment notification options
 * @returns Deployment notification
 */
export function createDeploymentNotification(options: DeploymentNotificationOptions = {}): DeploymentNotification {
  return new DeploymentNotification(options);
}
