/**
 * QA Wolf reporter for QA Wolf integration
 */

import axios, { AxiosInstance } from 'axios';
import { QAWolfReporterOptions, QAWolfAPIResponse, QAWolfReport } from './types';
import { EventBus, EventType } from '@qawolf/core';

/**
 * QA Wolf reporter
 */
export class QAWolfReporter {
  /**
   * API key
   */
  private apiKey: string;
  
  /**
   * Team ID
   */
  private teamId: string;
  
  /**
   * API URL
   */
  private apiUrl: string;
  
  /**
   * Report name
   */
  private reportName: string;
  
  /**
   * Report type
   */
  private reportType: string;
  
  /**
   * Report format
   */
  private reportFormat: string;
  
  /**
   * Timeout in milliseconds
   */
  private timeout: number;
  
  /**
   * Axios instance
   */
  private axios: AxiosInstance;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options QA Wolf reporter options
   */
  constructor(options: QAWolfReporterOptions = {}) {
    this.apiKey = options.apiKey || '';
    this.teamId = options.teamId || '';
    this.apiUrl = options.apiUrl || 'https://app.qawolf.com/api';
    this.reportName = options.reportName || 'QA Wolf Report';
    this.reportType = options.reportType || 'test';
    this.reportFormat = options.reportFormat || 'html';
    this.timeout = options.timeout || 30000;
    
    this.axios = axios.create({
      baseURL: this.apiUrl,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        'X-Team-ID': this.teamId
      }
    });
    
    this.eventBus = EventBus.getInstance();
  }
  
  /**
   * Get reports
   * @returns Reports
   */
  async getReports(): Promise<QAWolfReport[]> {
    try {
      const response = await this.axios.get<QAWolfAPIResponse<QAWolfReport[]>>('/reports');
      
      // Emit event
      this.eventBus.emit(EventType.QAWOLF_REPORTER_GET_REPORTS, {
        reports: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, 'Failed to get reports');
      return [];
    }
  }
  
  /**
   * Get report by ID
   * @param reportId Report ID
   * @returns Report
   */
  async getReportById(reportId: string): Promise<QAWolfReport | null> {
    try {
      const response = await this.axios.get<QAWolfAPIResponse<QAWolfReport>>(`/reports/${reportId}`);
      
      // Emit event
      this.eventBus.emit(EventType.QAWOLF_REPORTER_GET_REPORT_BY_ID, {
        report: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, `Failed to get report with ID ${reportId}`);
      return null;
    }
  }
  
  /**
   * Create test report
   * @param testId Test ID
   * @param name Report name
   * @param format Report format
   * @param metadata Report metadata
   * @returns Report
   */
  async createTestReport(testId: string, name?: string, format?: string, metadata?: Record<string, any>): Promise<QAWolfReport | null> {
    try {
      const response = await this.axios.post<QAWolfAPIResponse<QAWolfReport>>('/reports', {
        type: 'test',
        testId,
        name: name || this.reportName,
        format: format || this.reportFormat,
        metadata
      });
      
      // Emit event
      this.eventBus.emit(EventType.QAWOLF_REPORTER_CREATE_TEST_REPORT, {
        report: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, `Failed to create test report for test with ID ${testId}`);
      return null;
    }
  }
  
  /**
   * Create run report
   * @param runId Run ID
   * @param name Report name
   * @param format Report format
   * @param metadata Report metadata
   * @returns Report
   */
  async createRunReport(runId: string, name?: string, format?: string, metadata?: Record<string, any>): Promise<QAWolfReport | null> {
    try {
      const response = await this.axios.post<QAWolfAPIResponse<QAWolfReport>>('/reports', {
        type: 'run',
        runId,
        name: name || this.reportName,
        format: format || this.reportFormat,
        metadata
      });
      
      // Emit event
      this.eventBus.emit(EventType.QAWOLF_REPORTER_CREATE_RUN_REPORT, {
        report: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, `Failed to create run report for run with ID ${runId}`);
      return null;
    }
  }
  
  /**
   * Create CI report
   * @param ciRunId CI run ID
   * @param name Report name
   * @param format Report format
   * @param metadata Report metadata
   * @returns Report
   */
  async createCIReport(ciRunId: string, name?: string, format?: string, metadata?: Record<string, any>): Promise<QAWolfReport | null> {
    try {
      const response = await this.axios.post<QAWolfAPIResponse<QAWolfReport>>('/reports', {
        type: 'ci',
        ciRunId,
        name: name || this.reportName,
        format: format || this.reportFormat,
        metadata
      });
      
      // Emit event
      this.eventBus.emit(EventType.QAWOLF_REPORTER_CREATE_CI_REPORT, {
        report: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, `Failed to create CI report for CI run with ID ${ciRunId}`);
      return null;
    }
  }
  
  /**
   * Create metrics report
   * @param testId Test ID
   * @param name Report name
   * @param format Report format
   * @param metadata Report metadata
   * @returns Report
   */
  async createMetricsReport(testId: string, name?: string, format?: string, metadata?: Record<string, any>): Promise<QAWolfReport | null> {
    try {
      const response = await this.axios.post<QAWolfAPIResponse<QAWolfReport>>('/reports', {
        type: 'metrics',
        testId,
        name: name || this.reportName,
        format: format || this.reportFormat,
        metadata
      });
      
      // Emit event
      this.eventBus.emit(EventType.QAWOLF_REPORTER_CREATE_METRICS_REPORT, {
        report: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, `Failed to create metrics report for test with ID ${testId}`);
      return null;
    }
  }
  
  /**
   * Create custom report
   * @param name Report name
   * @param format Report format
   * @param content Report content
   * @param metadata Report metadata
   * @returns Report
   */
  async createCustomReport(name?: string, format?: string, content?: string, metadata?: Record<string, any>): Promise<QAWolfReport | null> {
    try {
      const response = await this.axios.post<QAWolfAPIResponse<QAWolfReport>>('/reports', {
        type: 'custom',
        name: name || this.reportName,
        format: format || this.reportFormat,
        content,
        metadata
      });
      
      // Emit event
      this.eventBus.emit(EventType.QAWOLF_REPORTER_CREATE_CUSTOM_REPORT, {
        report: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, 'Failed to create custom report');
      return null;
    }
  }
  
  /**
   * Delete report
   * @param reportId Report ID
   * @returns True if deleted, false otherwise
   */
  async deleteReport(reportId: string): Promise<boolean> {
    try {
      await this.axios.delete<QAWolfAPIResponse<void>>(`/reports/${reportId}`);
      
      // Emit event
      this.eventBus.emit(EventType.QAWOLF_REPORTER_DELETE_REPORT, {
        reportId
      });
      
      return true;
    } catch (error) {
      this.handleError(error, `Failed to delete report with ID ${reportId}`);
      return false;
    }
  }
  
  /**
   * Handle error
   * @param error Error
   * @param message Error message
   */
  private handleError(error: any, message: string): void {
    console.error(message, error);
    
    // Emit event
    this.eventBus.emit(EventType.QAWOLF_REPORTER_ERROR, {
      error,
      message
    });
  }
  
  /**
   * Get API key
   * @returns API key
   */
  getAPIKey(): string {
    return this.apiKey;
  }
  
  /**
   * Get team ID
   * @returns Team ID
   */
  getTeamId(): string {
    return this.teamId;
  }
  
  /**
   * Get API URL
   * @returns API URL
   */
  getAPIUrl(): string {
    return this.apiUrl;
  }
  
  /**
   * Get report name
   * @returns Report name
   */
  getReportName(): string {
    return this.reportName;
  }
  
  /**
   * Get report type
   * @returns Report type
   */
  getReportType(): string {
    return this.reportType;
  }
  
  /**
   * Get report format
   * @returns Report format
   */
  getReportFormat(): string {
    return this.reportFormat;
  }
  
  /**
   * Get timeout
   * @returns Timeout in milliseconds
   */
  getTimeout(): number {
    return this.timeout;
  }
  
  /**
   * Set API key
   * @param apiKey API key
   * @returns This instance for chaining
   */
  setAPIKey(apiKey: string): QAWolfReporter {
    this.apiKey = apiKey;
    
    this.axios = axios.create({
      baseURL: this.apiUrl,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        'X-Team-ID': this.teamId
      }
    });
    
    return this;
  }
  
  /**
   * Set team ID
   * @param teamId Team ID
   * @returns This instance for chaining
   */
  setTeamId(teamId: string): QAWolfReporter {
    this.teamId = teamId;
    
    this.axios = axios.create({
      baseURL: this.apiUrl,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        'X-Team-ID': this.teamId
      }
    });
    
    return this;
  }
  
  /**
   * Set API URL
   * @param apiUrl API URL
   * @returns This instance for chaining
   */
  setAPIUrl(apiUrl: string): QAWolfReporter {
    this.apiUrl = apiUrl;
    
    this.axios = axios.create({
      baseURL: this.apiUrl,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        'X-Team-ID': this.teamId
      }
    });
    
    return this;
  }
  
  /**
   * Set report name
   * @param reportName Report name
   * @returns This instance for chaining
   */
  setReportName(reportName: string): QAWolfReporter {
    this.reportName = reportName;
    return this;
  }
  
  /**
   * Set report type
   * @param reportType Report type
   * @returns This instance for chaining
   */
  setReportType(reportType: string): QAWolfReporter {
    this.reportType = reportType;
    return this;
  }
  
  /**
   * Set report format
   * @param reportFormat Report format
   * @returns This instance for chaining
   */
  setReportFormat(reportFormat: string): QAWolfReporter {
    this.reportFormat = reportFormat;
    return this;
  }
  
  /**
   * Set timeout
   * @param timeout Timeout in milliseconds
   * @returns This instance for chaining
   */
  setTimeout(timeout: number): QAWolfReporter {
    this.timeout = timeout;
    
    this.axios = axios.create({
      baseURL: this.apiUrl,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        'X-Team-ID': this.teamId
      }
    });
    
    return this;
  }
}

/**
 * Create QA Wolf reporter
 * @param options QA Wolf reporter options
 * @returns QA Wolf reporter
 */
export function createQAWolfReporter(options: QAWolfReporterOptions = {}): QAWolfReporter {
  return new QAWolfReporter(options);
}
