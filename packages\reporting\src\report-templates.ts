/**
 * Report templates for reporting
 */

import * as fs from 'fs';
import * as path from 'path';
import * as handlebars from 'handlebars';
import * as ejs from 'ejs';
import * as pug from 'pug';
import { ReportTemplateOptions, ReportTemplateEngine } from './types';
import { EventBus, EventType } from '@qawolf/core';

/**
 * Report templates
 */
export class ReportTemplates {
  /**
   * Templates
   */
  private templates: Map<string, ReportTemplateOptions>;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   */
  constructor() {
    this.templates = new Map<string, ReportTemplateOptions>();
    this.eventBus = EventBus.getInstance();
    
    // Register default templates
    this.registerDefaultTemplates();
  }
  
  /**
   * Register template
   * @param options Template options
   * @returns This instance for chaining
   */
  registerTemplate(options: ReportTemplateOptions): ReportTemplates {
    this.templates.set(options.name, options);
    
    // Emit event
    this.eventBus.emit(EventType.REPORT_TEMPLATES_REGISTER_TEMPLATE, {
      template: options
    });
    
    return this;
  }
  
  /**
   * Get template
   * @param name Template name
   * @returns Template options
   */
  getTemplate(name: string): ReportTemplateOptions | undefined {
    return this.templates.get(name);
  }
  
  /**
   * Get templates
   * @returns Templates
   */
  getTemplates(): Map<string, ReportTemplateOptions> {
    return this.templates;
  }
  
  /**
   * Render template
   * @param name Template name
   * @param data Template data
   * @returns Rendered template
   */
  async render(name: string, data: any): Promise<string> {
    try {
      const template = this.templates.get(name);
      
      if (!template) {
        throw new Error(`Template ${name} not found`);
      }
      
      let rendered = '';
      
      switch (template.engine) {
        case ReportTemplateEngine.HANDLEBARS:
          rendered = this.renderHandlebars(template, data);
          break;
        case ReportTemplateEngine.EJS:
          rendered = this.renderEJS(template, data);
          break;
        case ReportTemplateEngine.PUG:
          rendered = this.renderPug(template, data);
          break;
        case ReportTemplateEngine.CUSTOM:
          rendered = this.renderCustom(template, data);
          break;
        default:
          throw new Error(`Template engine ${template.engine} not supported`);
      }
      
      // Emit event
      this.eventBus.emit(EventType.REPORT_TEMPLATES_RENDER, {
        template,
        data,
        rendered
      });
      
      return rendered;
    } catch (error) {
      this.handleError(error, `Failed to render template ${name}`);
      return '';
    }
  }
  
  /**
   * Render Handlebars template
   * @param template Template options
   * @param data Template data
   * @returns Rendered template
   */
  private renderHandlebars(template: ReportTemplateOptions, data: any): string {
    // Register partials
    if (template.partials) {
      for (const [name, content] of Object.entries(template.partials)) {
        handlebars.registerPartial(name, content);
      }
    }
    
    // Register helpers
    if (template.helpers) {
      for (const [name, helper] of Object.entries(template.helpers)) {
        handlebars.registerHelper(name, helper);
      }
    }
    
    // Compile template
    const compiled = handlebars.compile(template.content);
    
    // Render template
    return compiled(data);
  }
  
  /**
   * Render EJS template
   * @param template Template options
   * @param data Template data
   * @returns Rendered template
   */
  private renderEJS(template: ReportTemplateOptions, data: any): string {
    // Render template
    return ejs.render(template.content, data, {
      filename: template.name
    });
  }
  
  /**
   * Render Pug template
   * @param template Template options
   * @param data Template data
   * @returns Rendered template
   */
  private renderPug(template: ReportTemplateOptions, data: any): string {
    // Compile template
    const compiled = pug.compile(template.content, {
      filename: template.name
    });
    
    // Render template
    return compiled(data);
  }
  
  /**
   * Render custom template
   * @param template Template options
   * @param data Template data
   * @returns Rendered template
   */
  private renderCustom(template: ReportTemplateOptions, data: any): string {
    // Custom rendering logic
    return template.content;
  }
  
  /**
   * Register default templates
   */
  private registerDefaultTemplates(): void {
    // Register default HTML template
    this.registerTemplate({
      name: 'default',
      description: 'Default HTML template',
      engine: ReportTemplateEngine.HANDLEBARS,
      content: `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>{{title}}</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      margin: 0;
      padding: 0;
      color: #333;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    header {
      background-color: #f4f4f4;
      padding: 20px;
      margin-bottom: 20px;
    }
    h1, h2, h3 {
      color: #444;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      margin-bottom: 20px;
    }
    table, th, td {
      border: 1px solid #ddd;
    }
    th, td {
      padding: 12px;
      text-align: left;
    }
    th {
      background-color: #f4f4f4;
    }
    .section {
      margin-bottom: 30px;
    }
    .chart {
      margin-bottom: 20px;
    }
    .footer {
      background-color: #f4f4f4;
      padding: 20px;
      margin-top: 20px;
      text-align: center;
    }
    {{#if stylesheet}}
    {{{stylesheet}}}
    {{/if}}
  </style>
  {{#each scripts}}
  <script src="{{this}}"></script>
  {{/each}}
</head>
<body>
  <div class="container">
    <header>
      {{#if logo}}
      <img src="{{logo}}" alt="Logo">
      {{/if}}
      <h1>{{title}}</h1>
      <p>{{description}}</p>
      <p>Date: {{date}}</p>
      <p>Author: {{author}}</p>
      <p>Version: {{version}}</p>
    </header>
    
    {{#each sections}}
    <div class="section" id="{{id}}">
      <h2>{{title}}</h2>
      {{#if description}}
      <p>{{description}}</p>
      {{/if}}
      {{#if content}}
      <div>{{{content}}}</div>
      {{/if}}
      
      {{#each charts}}
      <div class="chart" id="{{id}}">
        <h3>{{title}}</h3>
        {{#if description}}
        <p>{{description}}</p>
        {{/if}}
        <div class="chart-container" style="width: {{width}}px; height: {{height}}px;">
          <!-- Chart will be rendered here -->
        </div>
      </div>
      {{/each}}
      
      {{#each tables}}
      <div class="table" id="{{id}}">
        <h3>{{title}}</h3>
        {{#if description}}
        <p>{{description}}</p>
        {{/if}}
        <table>
          <thead>
            <tr>
              {{#each headers}}
              <th>{{this}}</th>
              {{/each}}
            </tr>
          </thead>
          <tbody>
            {{#each rows}}
            <tr>
              {{#each this}}
              <td>{{this}}</td>
              {{/each}}
            </tr>
            {{/each}}
          </tbody>
          {{#if footer}}
          <tfoot>
            <tr>
              {{#each footer}}
              <td>{{this}}</td>
              {{/each}}
            </tr>
          </tfoot>
          {{/if}}
        </table>
      </div>
      {{/each}}
      
      {{#each images}}
      <div class="image" id="{{id}}">
        <h3>{{title}}</h3>
        {{#if description}}
        <p>{{description}}</p>
        {{/if}}
        <img src="{{url}}" alt="{{alt}}" {{#if width}}width="{{width}}"{{/if}} {{#if height}}height="{{height}}"{{/if}}>
      </div>
      {{/each}}
      
      {{#each links}}
      <div class="link" id="{{id}}">
        <h3>{{title}}</h3>
        {{#if description}}
        <p>{{description}}</p>
        {{/if}}
        <a href="{{url}}" {{#if target}}target="{{target}}"{{/if}}>{{title}}</a>
      </div>
      {{/each}}
      
      {{#each attachments}}
      <div class="attachment" id="{{id}}">
        <h3>{{title}}</h3>
        {{#if description}}
        <p>{{description}}</p>
        {{/if}}
        <a href="{{url}}" download>Download {{title}}</a>
      </div>
      {{/each}}
    </div>
    {{/each}}
    
    {{#each charts}}
    <div class="chart" id="{{id}}">
      <h2>{{title}}</h2>
      {{#if description}}
      <p>{{description}}</p>
      {{/if}}
      <div class="chart-container" style="width: {{width}}px; height: {{height}}px;">
        <!-- Chart will be rendered here -->
      </div>
    </div>
    {{/each}}
    
    {{#each tables}}
    <div class="table" id="{{id}}">
      <h2>{{title}}</h2>
      {{#if description}}
      <p>{{description}}</p>
      {{/if}}
      <table>
        <thead>
          <tr>
            {{#each headers}}
            <th>{{this}}</th>
            {{/each}}
          </tr>
        </thead>
        <tbody>
          {{#each rows}}
          <tr>
            {{#each this}}
            <td>{{this}}</td>
            {{/each}}
          </tr>
          {{/each}}
        </tbody>
        {{#if footer}}
        <tfoot>
          <tr>
            {{#each footer}}
            <td>{{this}}</td>
            {{/each}}
          </tr>
        </tfoot>
        {{/if}}
      </table>
    </div>
    {{/each}}
    
    {{#each images}}
    <div class="image" id="{{id}}">
      <h2>{{title}}</h2>
      {{#if description}}
      <p>{{description}}</p>
      {{/if}}
      <img src="{{url}}" alt="{{alt}}" {{#if width}}width="{{width}}"{{/if}} {{#if height}}height="{{height}}"{{/if}}>
    </div>
    {{/each}}
    
    {{#each links}}
    <div class="link" id="{{id}}">
      <h2>{{title}}</h2>
      {{#if description}}
      <p>{{description}}</p>
      {{/if}}
      <a href="{{url}}" {{#if target}}target="{{target}}"{{/if}}>{{title}}</a>
    </div>
    {{/each}}
    
    {{#each attachments}}
    <div class="attachment" id="{{id}}">
      <h2>{{title}}</h2>
      {{#if description}}
      <p>{{description}}</p>
      {{/if}}
      <a href="{{url}}" download>Download {{title}}</a>
    </div>
    {{/each}}
    
    <div class="footer">
      <p>Generated by QA Wolf Metrics Framework</p>
    </div>
  </div>
  
  <script>
    // Initialize charts
    document.addEventListener('DOMContentLoaded', function() {
      // Chart initialization code
    });
  </script>
</body>
</html>
      `,
      partials: {},
      helpers: {
        formatDate: (date: Date) => {
          return date.toLocaleDateString();
        },
        formatTime: (date: Date) => {
          return date.toLocaleTimeString();
        },
        formatDateTime: (date: Date) => {
          return date.toLocaleString();
        }
      }
    });
    
    // Register default Markdown template
    this.registerTemplate({
      name: 'markdown',
      description: 'Default Markdown template',
      engine: ReportTemplateEngine.HANDLEBARS,
      content: `
# {{title}}

{{description}}

Date: {{date}}
Author: {{author}}
Version: {{version}}

{{#each sections}}
## {{title}}

{{#if description}}
{{description}}
{{/if}}

{{#if content}}
{{{content}}}
{{/if}}

{{#each tables}}
### {{title}}

{{#if description}}
{{description}}
{{/if}}

| {{#each headers}}{{this}} | {{/each}}
| {{#each headers}}--- | {{/each}}
{{#each rows}}
| {{#each this}}{{this}} | {{/each}}
{{/each}}

{{/each}}

{{#each images}}
### {{title}}

{{#if description}}
{{description}}
{{/if}}

![{{alt}}]({{url}})

{{/each}}

{{#each links}}
### {{title}}

{{#if description}}
{{description}}
{{/if}}

[{{title}}]({{url}})

{{/each}}

{{#each attachments}}
### {{title}}

{{#if description}}
{{description}}
{{/if}}

[Download {{title}}]({{url}})

{{/each}}

{{/each}}

{{#each tables}}
## {{title}}

{{#if description}}
{{description}}
{{/if}}

| {{#each headers}}{{this}} | {{/each}}
| {{#each headers}}--- | {{/each}}
{{#each rows}}
| {{#each this}}{{this}} | {{/each}}
{{/each}}

{{/each}}

{{#each images}}
## {{title}}

{{#if description}}
{{description}}
{{/if}}

![{{alt}}]({{url}})

{{/each}}

{{#each links}}
## {{title}}

{{#if description}}
{{description}}
{{/if}}

[{{title}}]({{url}})

{{/each}}

{{#each attachments}}
## {{title}}

{{#if description}}
{{description}}
{{/if}}

[Download {{title}}]({{url}})

{{/each}}

---

Generated by QA Wolf Metrics Framework
      `,
      partials: {},
      helpers: {
        formatDate: (date: Date) => {
          return date.toLocaleDateString();
        },
        formatTime: (date: Date) => {
          return date.toLocaleTimeString();
        },
        formatDateTime: (date: Date) => {
          return date.toLocaleString();
        }
      }
    });
  }
  
  /**
   * Handle error
   * @param error Error
   * @param message Error message
   */
  private handleError(error: any, message: string): void {
    console.error(message, error);
    
    // Emit event
    this.eventBus.emit(EventType.REPORT_TEMPLATES_ERROR, {
      error,
      message
    });
  }
}

/**
 * Create report templates
 * @returns Report templates
 */
export function createReportTemplates(): ReportTemplates {
  return new ReportTemplates();
}
