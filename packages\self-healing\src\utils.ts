/**
 * Utilities for self-healing tests
 */

import { SelectorAttributes } from './types';

/**
 * Parse CSS selector
 * @param selector CSS selector
 * @returns Selector attributes
 */
export function parseCssSelector(selector: string): SelectorAttributes {
  const attributes: SelectorAttributes = {};
  
  // Parse ID
  const idMatch = selector.match(/#([a-zA-Z0-9_-]+)/);
  
  if (idMatch) {
    attributes.id = idMatch[1];
  }
  
  // Parse classes
  const classMatches = selector.match(/\.([a-zA-Z0-9_-]+)/g);
  
  if (classMatches) {
    attributes.class = classMatches.map(match => match.substring(1)).join(' ');
  }
  
  // Parse tag
  const tagMatch = selector.match(/^([a-zA-Z0-9_-]+)/);
  
  if (tagMatch) {
    attributes.type = tagMatch[1];
  }
  
  // Parse attributes
  const attributeMatches = selector.match(/\[([a-zA-Z0-9_-]+)(=|~=|\|=|\^=|\$=|\*=)["']?([^"'\]]+)["']?\]/g);
  
  if (attributeMatches) {
    for (const match of attributeMatches) {
      const attributeMatch = match.match(/\[([a-zA-Z0-9_-]+)(=|~=|\|=|\^=|\$=|\*=)["']?([^"'\]]+)["']?\]/);
      
      if (attributeMatch) {
        const [, name, operator, value] = attributeMatch;
        
        attributes[name] = value;
      }
    }
  }
  
  // Parse :text() pseudo-class
  const textMatch = selector.match(/:text\(["']([^"']+)["']\)/);
  
  if (textMatch) {
    attributes.text = textMatch[1];
  }
  
  return attributes;
}

/**
 * Parse XPath selector
 * @param selector XPath selector
 * @returns Selector attributes
 */
export function parseXPathSelector(selector: string): SelectorAttributes {
  const attributes: SelectorAttributes = {};
  
  // Parse tag
  const tagMatch = selector.match(/\/\/([a-zA-Z0-9_-]+)/);
  
  if (tagMatch) {
    attributes.type = tagMatch[1];
  }
  
  // Parse ID
  const idMatch = selector.match(/@id=['"]([a-zA-Z0-9_-]+)['"]/);
  
  if (idMatch) {
    attributes.id = idMatch[1];
  }
  
  // Parse class
  const classMatch = selector.match(/@class=['"]([a-zA-Z0-9_\- ]+)['"]/);
  
  if (classMatch) {
    attributes.class = classMatch[1];
  }
  
  // Parse other attributes
  const attributeMatches = selector.match(/@([a-zA-Z0-9_-]+)=['"]([^'"]+)['"]/g);
  
  if (attributeMatches) {
    for (const match of attributeMatches) {
      const attributeMatch = match.match(/@([a-zA-Z0-9_-]+)=['"]([^'"]+)['"]/);
      
      if (attributeMatch) {
        const [, name, value] = attributeMatch;
        
        if (name !== 'id' && name !== 'class') {
          attributes[name] = value;
        }
      }
    }
  }
  
  // Parse text
  const textMatch = selector.match(/text\(\)=['"]([^'"]+)['"]/);
  
  if (textMatch) {
    attributes.text = textMatch[1];
  }
  
  return attributes;
}

/**
 * Build CSS selector
 * @param attributes Selector attributes
 * @returns CSS selector
 */
export function buildCssSelector(attributes: SelectorAttributes): string {
  const parts: string[] = [];
  
  // Add tag
  if (attributes.type) {
    parts.push(attributes.type);
  } else {
    parts.push('*');
  }
  
  // Add ID
  if (attributes.id) {
    parts.push(`#${attributes.id}`);
  }
  
  // Add classes
  if (attributes.class) {
    const classes = attributes.class.split(' ').filter(c => c.trim() !== '');
    
    for (const className of classes) {
      parts.push(`.${className}`);
    }
  }
  
  // Add other attributes
  for (const [name, value] of Object.entries(attributes)) {
    if (name !== 'type' && name !== 'id' && name !== 'class' && name !== 'text' && value !== undefined) {
      parts.push(`[${name}="${value}"]`);
    }
  }
  
  // Add text
  if (attributes.text) {
    parts.push(`:text("${attributes.text}")`);
  }
  
  return parts.join('');
}

/**
 * Build XPath selector
 * @param attributes Selector attributes
 * @returns XPath selector
 */
export function buildXPathSelector(attributes: SelectorAttributes): string {
  const parts: string[] = [];
  
  // Add tag
  if (attributes.type) {
    parts.push(`//${attributes.type}`);
  } else {
    parts.push('//*');
  }
  
  // Add predicates
  const predicates: string[] = [];
  
  // Add ID
  if (attributes.id) {
    predicates.push(`@id="${attributes.id}"`);
  }
  
  // Add class
  if (attributes.class) {
    predicates.push(`@class="${attributes.class}"`);
  }
  
  // Add other attributes
  for (const [name, value] of Object.entries(attributes)) {
    if (name !== 'type' && name !== 'id' && name !== 'class' && name !== 'text' && value !== undefined) {
      predicates.push(`@${name}="${value}"`);
    }
  }
  
  // Add text
  if (attributes.text) {
    predicates.push(`text()="${attributes.text}"`);
  }
  
  // Add predicates to selector
  if (predicates.length > 0) {
    parts.push(`[${predicates.join(' and ')}]`);
  }
  
  return parts.join('');
}

/**
 * Get similar selectors
 * @param selector Original selector
 * @returns Similar selectors
 */
export function getSimilarSelectors(selector: string): string[] {
  const selectors: string[] = [];
  
  // Parse selector
  let attributes: SelectorAttributes;
  
  if (selector.startsWith('//')) {
    attributes = parseXPathSelector(selector);
  } else {
    attributes = parseCssSelector(selector);
  }
  
  // Generate selectors with different combinations of attributes
  const attributeEntries = Object.entries(attributes);
  
  for (let i = attributeEntries.length; i > 0; i--) {
    const combinations = getCombinations(attributeEntries, i);
    
    for (const combination of combinations) {
      const combinationAttributes: SelectorAttributes = {};
      
      for (const [key, value] of combination) {
        combinationAttributes[key] = value;
      }
      
      const cssSelector = buildCssSelector(combinationAttributes);
      
      if (cssSelector) {
        selectors.push(cssSelector);
      }
    }
  }
  
  return selectors;
}

/**
 * Get combinations
 * @param array Array to get combinations from
 * @param size Combination size
 * @returns Combinations
 */
function getCombinations<T>(array: T[], size: number): T[][] {
  const result: T[][] = [];
  
  // Get all combinations of size `size` from `array`
  const combine = (start: number, current: T[]) => {
    if (current.length === size) {
      result.push([...current]);
      return;
    }
    
    for (let i = start; i < array.length; i++) {
      current.push(array[i]);
      combine(i + 1, current);
      current.pop();
    }
  };
  
  combine(0, []);
  
  return result;
}

/**
 * Get fallback selectors
 * @param selector Original selector
 * @returns Fallback selectors
 */
export function getFallbackSelectors(selector: string): string[] {
  const fallbackSelectors: string[] = [];
  
  // Add original selector
  fallbackSelectors.push(selector);
  
  // Add similar selectors
  fallbackSelectors.push(...getSimilarSelectors(selector));
  
  // Add text-based selectors
  const attributes = parseCssSelector(selector);
  
  if (attributes.text) {
    fallbackSelectors.push(`:text("${attributes.text}")`);
    fallbackSelectors.push(`:text-is("${attributes.text}")`);
    fallbackSelectors.push(`:text-matches("${attributes.text}")`);
  }
  
  return fallbackSelectors;
}

/**
 * Try selectors
 * @param page Page
 * @param selectors Selectors to try
 * @returns First valid selector or null if none are valid
 */
export async function trySelectors(page: any, selectors: string[]): Promise<string | null> {
  for (const selector of selectors) {
    try {
      const count = await page.locator(selector).count();
      
      if (count > 0) {
        return selector;
      }
    } catch (error) {
      // Ignore error
    }
  }
  
  return null;
}

/**
 * Create self-healing selector
 * @param selector Original selector
 * @returns Self-healing selector
 */
export function createSelfHealingSelector(selector: string): string {
  const fallbackSelectors = getFallbackSelectors(selector);
  
  return fallbackSelectors.join(' >> visible=true, ') + ' >> visible=true';
}
