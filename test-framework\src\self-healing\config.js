/**
 * Self-Healing Configuration
 * 
 * This module provides configuration options for the self-healing automation system.
 */

/**
 * Create default self-healing configuration
 * @returns {Object} - Default configuration
 */
function createDefaultConfig() {
  return {
    // General
    enabled: true,
    
    // Selector Healing
    selectorHealing: {
      enabled: true,
      maxAttempts: 3,
      strategies: ['css-relaxation', 'attribute-based', 'text-based', 'xpath'],
      persistHistory: true,
      historyPath: './selector-history.json',
      optimizationThreshold: 5, // Minimum number of attempts before optimization
      confidenceThreshold: 0.7 // Minimum confidence for a selector to be used
    },
    
    // Recovery
    recovery: {
      enabled: true,
      maxAttempts: 3,
      strategies: ['retry', 'wait', 'selector', 'refresh', 'screenshot'],
      timeout: 30000, // Maximum time to spend on recovery attempts
      waitTime: 1000, // Time to wait between recovery attempts
      screenshotOnFailure: true
    },
    
    // Feedback Collection
    feedbackCollection: {
      enabled: true,
      collectScreenshots: true,
      screenshotDir: './screenshots/feedback',
      persistFeedback: true,
      feedbackPath: './feedback-data.json',
      anonymizeData: false
    },
    
    // Learning
    learning: {
      enabled: true,
      autoOptimize: true,
      optimizationInterval: 10, // Optimize after every 10 test runs
      persistLearning: true,
      learningPath: './learning-data.json',
      suggestImprovements: true
    },
    
    // Logging
    logging: {
      level: 'info', // 'debug', 'info', 'warn', 'error'
      logToConsole: true,
      logToFile: false,
      logFilePath: './self-healing.log'
    }
  };
}

/**
 * Validate self-healing configuration
 * @param {Object} config - Configuration to validate
 * @throws {Error} - If configuration is invalid
 */
function validateConfig(config) {
  if (!config) {
    throw new Error('Configuration is required');
  }
  
  if (typeof config !== 'object') {
    throw new Error('Configuration must be an object');
  }
  
  // Validate required fields
  if (config.enabled !== undefined && typeof config.enabled !== 'boolean') {
    throw new Error('config.enabled must be a boolean');
  }
  
  // Validate selector healing config
  if (config.selectorHealing) {
    if (typeof config.selectorHealing !== 'object') {
      throw new Error('config.selectorHealing must be an object');
    }
    
    if (config.selectorHealing.enabled !== undefined && typeof config.selectorHealing.enabled !== 'boolean') {
      throw new Error('config.selectorHealing.enabled must be a boolean');
    }
    
    if (config.selectorHealing.maxAttempts !== undefined && typeof config.selectorHealing.maxAttempts !== 'number') {
      throw new Error('config.selectorHealing.maxAttempts must be a number');
    }
    
    if (config.selectorHealing.strategies !== undefined && !Array.isArray(config.selectorHealing.strategies)) {
      throw new Error('config.selectorHealing.strategies must be an array');
    }
  }
  
  // Validate recovery config
  if (config.recovery) {
    if (typeof config.recovery !== 'object') {
      throw new Error('config.recovery must be an object');
    }
    
    if (config.recovery.enabled !== undefined && typeof config.recovery.enabled !== 'boolean') {
      throw new Error('config.recovery.enabled must be a boolean');
    }
    
    if (config.recovery.maxAttempts !== undefined && typeof config.recovery.maxAttempts !== 'number') {
      throw new Error('config.recovery.maxAttempts must be a number');
    }
    
    if (config.recovery.strategies !== undefined && !Array.isArray(config.recovery.strategies)) {
      throw new Error('config.recovery.strategies must be an array');
    }
  }
  
  // Validate feedback collection config
  if (config.feedbackCollection) {
    if (typeof config.feedbackCollection !== 'object') {
      throw new Error('config.feedbackCollection must be an object');
    }
    
    if (config.feedbackCollection.enabled !== undefined && typeof config.feedbackCollection.enabled !== 'boolean') {
      throw new Error('config.feedbackCollection.enabled must be a boolean');
    }
  }
  
  // Validate learning config
  if (config.learning) {
    if (typeof config.learning !== 'object') {
      throw new Error('config.learning must be an object');
    }
    
    if (config.learning.enabled !== undefined && typeof config.learning.enabled !== 'boolean') {
      throw new Error('config.learning.enabled must be a boolean');
    }
    
    if (config.learning.autoOptimize !== undefined && typeof config.learning.autoOptimize !== 'boolean') {
      throw new Error('config.learning.autoOptimize must be a boolean');
    }
  }
}

/**
 * Merge configuration objects
 * @param {Object} baseConfig - Base configuration
 * @param {Object} overrides - Configuration overrides
 * @returns {Object} - Merged configuration
 */
function mergeConfigs(baseConfig, overrides) {
  if (!baseConfig) {
    throw new Error('Base configuration is required');
  }
  
  if (!overrides) {
    return { ...baseConfig };
  }
  
  // Deep merge for nested objects
  const merged = { ...baseConfig };
  
  for (const key in overrides) {
    if (typeof overrides[key] === 'object' && overrides[key] !== null && !Array.isArray(overrides[key]) &&
        typeof baseConfig[key] === 'object' && baseConfig[key] !== null && !Array.isArray(baseConfig[key])) {
      merged[key] = mergeConfigs(baseConfig[key], overrides[key]);
    } else {
      merged[key] = overrides[key];
    }
  }
  
  return merged;
}

module.exports = {
  createDefaultConfig,
  validateConfig,
  mergeConfigs
};