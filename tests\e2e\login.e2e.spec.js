/**
 * Login End-to-End Tests
 * 
 * This file contains end-to-end tests for the login functionality.
 * It validates that users can log in with valid credentials and that
 * appropriate error messages are shown for invalid credentials.
 */

const { test, expect, login } = require('../utils/test-helpers');
const { PerformanceTracker } = require('../utils/performance-tracker');
const { getConfig } = require('../config/test.config');

// Test configuration
const config = getConfig();

test.describe('Login Functionality', () => {
  /**
   * Test: Login with valid credentials
   * Purpose: Verify that users can log in with valid credentials
   * Input: Valid email and password
   * Expected: User is logged in successfully
   */
  test('should login with valid credentials', async ({ page }) => {
    // Performance tracking
    const performanceTracker = new PerformanceTracker({
      thresholds: config.performance.thresholds
    }).start();
    
    try {
      // ARRANGE: Set up the test environment
      const email = config.auth.email;
      const password = config.auth.password;
      const url = config.baseUrl;
      
      // Take a screenshot before login
      await page.takeScreenshot({
        action: 'before-login',
        description: 'Before login'
      });
      
      // ACT: Log in with valid credentials
      await page.goto(url);
      
      // Fill in login credentials and submit
      await page.fill('[data-test-id="SignInEmail"]', email);
      await page.fill('[data-test-id="SignInPassword"]', password);
      
      // Take a screenshot before clicking login button
      await page.takeScreenshot({
        action: 'filled-login-form',
        description: 'After filling login form'
      });
      
      await page.locator(':text("Log in with email")').click();
      
      // Wait for navigation to complete
      await page.waitForNavigation();
      
      // Track the operation
      performanceTracker.trackOperation({
        name: 'Login with Valid Credentials',
        type: 'login',
        duration: 2000 // Placeholder value
      });
      
      // Take a screenshot after login
      await page.takeScreenshot({
        action: 'after-login',
        description: 'After successful login'
      });
      
      // ASSERT: Verify successful login
      // Check that we're redirected to the dashboard or home page
      await expect(page).not.toHaveURL(/login/);
      
      // Check for elements that should be visible after login
      await expect(page.locator('div[class*="FilesTable__Wrapper"]')).toBeVisible();
    } catch (error) {
      // Take a screenshot on error
      await page.takeErrorScreenshot({
        error
      });
      
      throw error;
    } finally {
      // Stop performance tracking
      const metrics = performanceTracker.stop();
      console.log('Performance metrics:', JSON.stringify(metrics, null, 2));
    }
  });
  
  /**
   * Test: Login with invalid credentials
   * Purpose: Verify that appropriate error messages are shown for invalid credentials
   * Input: Invalid email and password
   * Expected: Error message is shown
   */
  test('should show error with invalid credentials', async ({ page }) => {
    // Performance tracking
    const performanceTracker = new PerformanceTracker({
      thresholds: config.performance.thresholds
    }).start();
    
    try {
      // ARRANGE: Set up the test environment
      const email = '<EMAIL>';
      const password = 'invalidpassword';
      const url = config.baseUrl;
      
      // Take a screenshot before login
      await page.takeScreenshot({
        action: 'before-invalid-login',
        description: 'Before invalid login'
      });
      
      // ACT: Log in with invalid credentials
      await page.goto(url);
      
      // Fill in invalid login credentials and submit
      await page.fill('[data-test-id="SignInEmail"]', email);
      await page.fill('[data-test-id="SignInPassword"]', password);
      
      // Take a screenshot before clicking login button
      await page.takeScreenshot({
        action: 'filled-invalid-login-form',
        description: 'After filling invalid login form'
      });
      
      await page.locator(':text("Log in with email")').click();
      
      // Track the operation
      performanceTracker.trackOperation({
        name: 'Login with Invalid Credentials',
        type: 'login',
        duration: 1000 // Placeholder value
      });
      
      // Take a screenshot after login attempt
      await page.takeScreenshot({
        action: 'after-invalid-login',
        description: 'After invalid login attempt'
      });
      
      // ASSERT: Verify error message
      // Wait for the error message to appear
      await page.waitForSelector('text=Email or password incorrect', { timeout: 5000 });
      
      // Check that we're still on the login page
      await expect(page).toHaveURL(/login/);
      
      // Check that the error message is visible
      await expect(page.locator('text=Email or password incorrect')).toBeVisible();
    } catch (error) {
      // Take a screenshot on error
      await page.takeErrorScreenshot({
        error
      });
      
      throw error;
    } finally {
      // Stop performance tracking
      const metrics = performanceTracker.stop();
      console.log('Performance metrics:', JSON.stringify(metrics, null, 2));
    }
  });
  
  /**
   * Test: Login with empty credentials
   * Purpose: Verify that appropriate error messages are shown for empty credentials
   * Input: Empty email and password
   * Expected: Error message is shown
   */
  test('should show error with empty credentials', async ({ page }) => {
    // Performance tracking
    const performanceTracker = new PerformanceTracker({
      thresholds: config.performance.thresholds
    }).start();
    
    try {
      // ARRANGE: Set up the test environment
      const url = config.baseUrl;
      
      // Take a screenshot before login
      await page.takeScreenshot({
        action: 'before-empty-login',
        description: 'Before empty login'
      });
      
      // ACT: Log in with empty credentials
      await page.goto(url);
      
      // Click the login button without filling in credentials
      await page.locator(':text("Log in with email")').click();
      
      // Track the operation
      performanceTracker.trackOperation({
        name: 'Login with Empty Credentials',
        type: 'login',
        duration: 500 // Placeholder value
      });
      
      // Take a screenshot after login attempt
      await page.takeScreenshot({
        action: 'after-empty-login',
        description: 'After empty login attempt'
      });
      
      // ASSERT: Verify error message
      // Check that we're still on the login page
      await expect(page).toHaveURL(/login/);
      
      // Check for validation error messages
      // The exact error message might vary, so we'll check for common patterns
      const errorVisible = await page.locator('text=/required|cannot be empty|invalid/i').isVisible();
      expect(errorVisible).toBeTruthy();
    } catch (error) {
      // Take a screenshot on error
      await page.takeErrorScreenshot({
        error
      });
      
      throw error;
    } finally {
      // Stop performance tracking
      const metrics = performanceTracker.stop();
      console.log('Performance metrics:', JSON.stringify(metrics, null, 2));
    }
  });
  
  /**
   * Test: Login and logout
   * Purpose: Verify that users can log in and then log out
   * Input: Valid email and password
   * Expected: User is logged in and then logged out successfully
   */
  test('should login and logout successfully', async ({ page }) => {
    // Performance tracking
    const performanceTracker = new PerformanceTracker({
      thresholds: config.performance.thresholds
    }).start();
    
    try {
      // ARRANGE: Set up the test environment
      const email = config.auth.email;
      const password = config.auth.password;
      const url = config.baseUrl;
      
      // Take a screenshot before login
      await page.takeScreenshot({
        action: 'before-login-logout',
        description: 'Before login for logout test'
      });
      
      // ACT: Log in with valid credentials
      await page.goto(url);
      
      // Fill in login credentials and submit
      await page.fill('[data-test-id="SignInEmail"]', email);
      await page.fill('[data-test-id="SignInPassword"]', password);
      await page.locator(':text("Log in with email")').click();
      
      // Wait for navigation to complete
      await page.waitForNavigation();
      
      // Track the operation
      performanceTracker.trackOperation({
        name: 'Login for Logout Test',
        type: 'login',
        duration: 2000 // Placeholder value
      });
      
      // Take a screenshot after login
      await page.takeScreenshot({
        action: 'after-login-for-logout',
        description: 'After login for logout test'
      });
      
      // Verify successful login
      await expect(page).not.toHaveURL(/login/);
      await expect(page.locator('div[class*="FilesTable__Wrapper"]')).toBeVisible();
      
      // Find and click the user menu
      // Try multiple selectors to find the user menu
      try {
        await page.click('button[aria-label="User menu"]');
      } catch {
        try {
          await page.click('div[class*="UserMenu"]');
        } catch {
          // If we can't find a specific user menu, look for any button that might be a user menu
          const buttons = await page.locator('button').all();
          
          // Click the last button, which is often the user menu
          if (buttons.length > 0) {
            await buttons[buttons.length - 1].click();
          }
        }
      }
      
      // Wait for the menu to appear
      await page.waitForTimeout(1000);
      
      // Take a screenshot with the user menu open
      await page.takeScreenshot({
        action: 'user-menu-open',
        description: 'User menu open'
      });
      
      // Click the logout option
      // Try multiple selectors to find the logout option
      try {
        await page.click('text=Log out');
      } catch {
        try {
          await page.click('a:has-text("Log out")');
        } catch {
          // If we can't find a specific logout option, look for any element that might be a logout option
          await page.click('text=/logout|sign out/i');
        }
      }
      
      // Wait for navigation to complete
      await page.waitForNavigation();
      
      // Track the operation
      performanceTracker.trackOperation({
        name: 'Logout',
        type: 'logout',
        duration: 1000 // Placeholder value
      });
      
      // Take a screenshot after logout
      await page.takeScreenshot({
        action: 'after-logout',
        description: 'After logout'
      });
      
      // ASSERT: Verify successful logout
      // Check that we're redirected to the login page
      await expect(page).toHaveURL(/login/);
      
      // Check for elements that should be visible on the login page
      await expect(page.locator('[data-test-id="SignInEmail"]')).toBeVisible();
      await expect(page.locator('[data-test-id="SignInPassword"]')).toBeVisible();
    } catch (error) {
      // Take a screenshot on error
      await page.takeErrorScreenshot({
        error
      });
      
      throw error;
    } finally {
      // Stop performance tracking
      const metrics = performanceTracker.stop();
      console.log('Performance metrics:', JSON.stringify(metrics, null, 2));
    }
  });
});
