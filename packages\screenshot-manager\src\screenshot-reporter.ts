/**
 * Screenshot reporter for screenshot manager
 */

import * as fs from 'fs';
import * as path from 'path';
import { ScreenshotInfo, ScreenshotReporterOptions } from './types';
import { formatBytes } from './utils';
import { writeJsonToFile, writeStringToFile, ensureDirectoryExists } from '@qawolf/shared-utils';
import { EventBus, EventType } from '@qawolf/core';

/**
 * Screenshot reporter
 */
export class ScreenshotReporter {
  /**
   * Output directory
   */
  private outputDir: string;
  
  /**
   * Report formats
   */
  private formats: ('json' | 'html' | 'csv')[];
  
  /**
   * Whether to include thumbnails in HTML report
   */
  private includeThumbnails: boolean;
  
  /**
   * Whether to include metadata in report
   */
  private includeMetadata: boolean;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options Screenshot reporter options
   */
  constructor(options: ScreenshotReporterOptions = {}) {
    this.outputDir = options.outputDir || 'screenshot-reports';
    this.formats = options.formats || ['json', 'html'];
    this.includeThumbnails = options.includeThumbnails !== false;
    this.includeMetadata = options.includeMetadata !== false;
    this.eventBus = EventBus.getInstance();
  }
  
  /**
   * Generate report
   * @param screenshots Screenshot info list
   * @param outputDir Output directory
   * @param formats Report formats
   * @returns Report file paths
   */
  async generateReport(screenshots: ScreenshotInfo[], outputDir?: string, formats?: ('json' | 'html' | 'csv')[]): Promise<string[]> {
    const reportDir = outputDir || this.outputDir;
    const reportFormats = formats || this.formats;
    const reportFilePaths: string[] = [];
    
    // Create output directory if it doesn't exist
    ensureDirectoryExists(reportDir);
    
    // Generate reports
    for (const format of reportFormats) {
      const reportFilePath = await this.generateReportForFormat(screenshots, reportDir, format);
      reportFilePaths.push(reportFilePath);
    }
    
    // Emit event
    this.eventBus.emit(EventType.SCREENSHOT_REPORT_GENERATED, {
      screenshots,
      reportFilePaths
    });
    
    return reportFilePaths;
  }
  
  /**
   * Generate report for format
   * @param screenshots Screenshot info list
   * @param outputDir Output directory
   * @param format Report format
   * @returns Report file path
   */
  private async generateReportForFormat(screenshots: ScreenshotInfo[], outputDir: string, format: 'json' | 'html' | 'csv'): Promise<string> {
    const timestamp = Date.now();
    const reportFilePath = path.join(outputDir, `screenshot-report-${timestamp}.${format}`);
    
    switch (format) {
      case 'json':
        await this.generateJsonReport(screenshots, reportFilePath);
        break;
      case 'html':
        await this.generateHtmlReport(screenshots, reportFilePath, outputDir);
        break;
      case 'csv':
        await this.generateCsvReport(screenshots, reportFilePath);
        break;
    }
    
    return reportFilePath;
  }
  
  /**
   * Generate JSON report
   * @param screenshots Screenshot info list
   * @param reportFilePath Report file path
   */
  private async generateJsonReport(screenshots: ScreenshotInfo[], reportFilePath: string): Promise<void> {
    const report = {
      timestamp: Date.now(),
      count: screenshots.length,
      screenshots: this.includeMetadata ? screenshots : screenshots.map(screenshot => {
        const { metadata, ...rest } = screenshot;
        return rest;
      })
    };
    
    writeJsonToFile(reportFilePath, report);
  }
  
  /**
   * Generate HTML report
   * @param screenshots Screenshot info list
   * @param reportFilePath Report file path
   * @param outputDir Output directory
   */
  private async generateHtmlReport(screenshots: ScreenshotInfo[], reportFilePath: string, outputDir: string): Promise<void> {
    // Create thumbnails directory
    const thumbnailsDir = path.join(outputDir, 'thumbnails');
    
    if (this.includeThumbnails) {
      ensureDirectoryExists(thumbnailsDir);
    }
    
    // Generate thumbnails
    if (this.includeThumbnails) {
      for (const screenshot of screenshots) {
        try {
          const thumbnailPath = path.join(thumbnailsDir, `${screenshot.id}.jpg`);
          
          // Copy screenshot to thumbnail
          fs.copyFileSync(screenshot.path, thumbnailPath);
        } catch (error) {
          console.error(`Error generating thumbnail for ${screenshot.path}:`, error);
        }
      }
    }
    
    // Generate screenshot rows
    const screenshotRows = screenshots.map(screenshot => `
      <tr>
        <td>${screenshot.name}</td>
        <td>${screenshot.type}</td>
        <td>${screenshot.testName || ''}</td>
        <td>${screenshot.browserName || ''} ${screenshot.browserVersion || ''}</td>
        <td>${screenshot.width || 0} x ${screenshot.height || 0}</td>
        <td>${formatBytes(screenshot.size || 0)}</td>
        <td>${new Date(screenshot.timestamp).toISOString()}</td>
        <td>
          <a href="${screenshot.path}" target="_blank">View</a>
          ${this.includeThumbnails ? `
            <div class="thumbnail">
              <img src="thumbnails/${screenshot.id}.jpg" alt="${screenshot.name}" />
            </div>
          ` : ''}
        </td>
      </tr>
    `).join('');
    
    // Generate HTML
    const html = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Screenshot Report</title>
        <style>
          body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
          }
          
          h1, h2, h3 {
            color: #2c3e50;
          }
          
          table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
          }
          
          th, td {
            padding: 8px;
            text-align: left;
            border-bottom: 1px solid #ddd;
          }
          
          th {
            background-color: #f2f2f2;
          }
          
          tr:hover {
            background-color: #f5f5f5;
          }
          
          .thumbnail {
            margin-top: 5px;
          }
          
          .thumbnail img {
            max-width: 100px;
            max-height: 100px;
            border: 1px solid #ddd;
          }
        </style>
      </head>
      <body>
        <h1>Screenshot Report</h1>
        
        <h2>Summary</h2>
        <p>Total Screenshots: ${screenshots.length}</p>
        <p>Generated: ${new Date().toISOString()}</p>
        
        <h2>Screenshots</h2>
        <table>
          <thead>
            <tr>
              <th>Name</th>
              <th>Type</th>
              <th>Test</th>
              <th>Browser</th>
              <th>Dimensions</th>
              <th>Size</th>
              <th>Timestamp</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            ${screenshotRows}
          </tbody>
        </table>
      </body>
      </html>
    `;
    
    writeStringToFile(reportFilePath, html);
  }
  
  /**
   * Generate CSV report
   * @param screenshots Screenshot info list
   * @param reportFilePath Report file path
   */
  private async generateCsvReport(screenshots: ScreenshotInfo[], reportFilePath: string): Promise<void> {
    // Generate CSV header
    let csv = 'Name,Type,Test Name,Test ID,Browser,Browser Version,Width,Height,Size,Timestamp,Path\n';
    
    // Generate CSV rows
    for (const screenshot of screenshots) {
      csv += `"${screenshot.name}",`;
      csv += `"${screenshot.type}",`;
      csv += `"${screenshot.testName || ''}",`;
      csv += `"${screenshot.testId || ''}",`;
      csv += `"${screenshot.browserName || ''}",`;
      csv += `"${screenshot.browserVersion || ''}",`;
      csv += `${screenshot.width || 0},`;
      csv += `${screenshot.height || 0},`;
      csv += `${screenshot.size || 0},`;
      csv += `${screenshot.timestamp},`;
      csv += `"${screenshot.path}"\n`;
    }
    
    writeStringToFile(reportFilePath, csv);
  }
  
  /**
   * Get output directory
   * @returns Output directory
   */
  getOutputDir(): string {
    return this.outputDir;
  }
  
  /**
   * Set output directory
   * @param outputDir Output directory
   * @returns This instance for chaining
   */
  setOutputDir(outputDir: string): ScreenshotReporter {
    this.outputDir = outputDir;
    return this;
  }
  
  /**
   * Get report formats
   * @returns Report formats
   */
  getFormats(): ('json' | 'html' | 'csv')[] {
    return [...this.formats];
  }
  
  /**
   * Set report formats
   * @param formats Report formats
   * @returns This instance for chaining
   */
  setFormats(formats: ('json' | 'html' | 'csv')[]): ScreenshotReporter {
    this.formats = formats;
    return this;
  }
  
  /**
   * Enable thumbnails
   * @returns This instance for chaining
   */
  enableThumbnails(): ScreenshotReporter {
    this.includeThumbnails = true;
    return this;
  }
  
  /**
   * Disable thumbnails
   * @returns This instance for chaining
   */
  disableThumbnails(): ScreenshotReporter {
    this.includeThumbnails = false;
    return this;
  }
  
  /**
   * Enable metadata
   * @returns This instance for chaining
   */
  enableMetadata(): ScreenshotReporter {
    this.includeMetadata = true;
    return this;
  }
  
  /**
   * Disable metadata
   * @returns This instance for chaining
   */
  disableMetadata(): ScreenshotReporter {
    this.includeMetadata = false;
    return this;
  }
}

/**
 * Create screenshot reporter
 * @param options Screenshot reporter options
 * @returns Screenshot reporter
 */
export function createScreenshotReporter(options: ScreenshotReporterOptions = {}): ScreenshotReporter {
  return new ScreenshotReporter(options);
}
