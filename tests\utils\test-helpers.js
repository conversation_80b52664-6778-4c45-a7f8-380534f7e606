/**
 * Test Helpers
 *
 * This module provides utility functions for tests.
 */

const { test: baseTest, expect } = require('@playwright/test');
// Use local imports instead of package imports
const { createMcpController, createSelfHealingController } = require('../../test-framework/src');
const { screenshot } = require('../../shared-utils/src');
require('dotenv').config();

/**
 * Default test timeout in milliseconds
 */
const DEFAULT_TIMEOUT = 30000;

/**
 * Default test retry count
 */
const DEFAULT_RETRY_COUNT = 2;

/**
 * Default test retry delay in milliseconds
 */
const DEFAULT_RETRY_DELAY = 1000;

/**
 * Enhanced test fixture with MCP and self-healing capabilities
 */
const test = baseTest.extend({
  /**
   * Enhanced page fixture with MCP and self-healing capabilities
   */
  page: async ({ page }, use, testInfo) => {
    // Create MCP controller
    const mcpController = createMcpController({
      autoStartPlaywrightMcp: true,
      generateFallbacks: true,
      prioritizeTestIds: true
    });

    // Create self-healing controller
    const selfHealingController = createSelfHealingController({
      enabled: true,
      selectorHealing: {
        enabled: true,
        maxAttempts: 3
      },
      recovery: {
        enabled: true,
        maxAttempts: 3
      },
      feedbackCollection: {
        enabled: true,
        collectScreenshots: true
      }
    });

    // Start test run
    await selfHealingController.startTest({
      name: testInfo.title,
      file: testInfo.file,
      project: testInfo.project?.name
    });

    // Create self-healing page
    const enhancedPage = selfHealingController.createPage(page);

    // Add MCP and self-healing utilities to the page
    enhancedPage.mcpController = mcpController;
    enhancedPage.selfHealingController = selfHealingController;

    // Add screenshot utility to the page
    enhancedPage.takeScreenshot = async (options) => {
      return await screenshot.takeScreenshot(enhancedPage, {
        testName: testInfo.title,
        ...options
      });
    };

    // Add error screenshot utility to the page
    enhancedPage.takeErrorScreenshot = async (options) => {
      return await screenshot.takeErrorScreenshot(enhancedPage, {
        testName: testInfo.title,
        ...options
      });
    };

    // Use the enhanced page
    await use(enhancedPage);

    // End test run
    await selfHealingController.endTest({
      name: testInfo.title,
      status: testInfo.status,
      duration: testInfo.duration
    });

    // Clean up resources
    await mcpController.cleanup();
    await selfHealingController.cleanup();
  }
});

/**
 * Login to the application
 *
 * @param {Object} page - Playwright page
 * @param {Object} [options] - Login options
 * @param {string} [options.email] - Email to use for login
 * @param {string} [options.password] - Password to use for login
 * @param {string} [options.url] - URL to navigate to
 * @returns {Promise<void>}
 */
async function login(page, options = {}) {
  const email = options.email || process.env.EMAIL || '<EMAIL>';
  const password = options.password || process.env.PASSWORD || 'vhc!tGK289IS&';
  const url = options.url || process.env.URL || 'https://app.lidostaging.com';

  // Navigate to the login page
  await page.goto(url);

  // Fill in login credentials and submit
  await page.fill('[data-test-id="SignInEmail"]', email);
  await page.fill('[data-test-id="SignInPassword"]', password);
  await page.locator(':text("Log in with email")').click();

  // Wait for navigation to complete
  await page.waitForNavigation();

  // Verify successful login
  await expect(page).not.toHaveURL(/login/);

  // Check for elements that should be visible after login
  await expect(page.locator('div[class*="FilesTable__Wrapper"]')).toBeVisible();
}

/**
 * Navigate to the files page
 *
 * @param {Object} page - Playwright page
 * @returns {Promise<void>}
 */
async function navigateToFilesPage(page) {
  try {
    // Try to find and click a "Files" link or button if we're not already on the files page
    await page.click('a:has-text("Files")').catch(() => {
      // If "Files" link not found, try clicking a back button
      return page.click('button[aria-label="Back"]');
    });

    // Wait for the file list to load
    await page.waitForTimeout(1000);
  } catch {
    // Continue with the test even if navigation fails
    // We're likely already on the files list page
  }

  // Wait for the files table to appear
  await page.waitForSelector('div[class*="FilesTable"]', { timeout: 5000 });
}

/**
 * Create a new file
 *
 * @param {Object} page - Playwright page
 * @param {Object} [options] - File creation options
 * @param {string} [options.fileName] - Name of the file to create
 * @returns {Promise<string>} - Name of the created file
 */
async function createFile(page, options = {}) {
  // Default file name is "untitled"
  const fileName = options.fileName || "untitled";

  // Count existing files with the same name before creating a new one
  const existingCount1 = await page.locator(`span[class*="FileTitle"]:has-text("${fileName}")`).count();
  const existingCount2 = await page.locator(`div[class*="styled_FileName"]:has-text("${fileName}"), div.jHdmZQ:has-text("${fileName}")`).count();
  const existingCount = Math.max(existingCount1, existingCount2);

  // Click on the "New file" button
  await page.click('div[class*="pages__NewFileButton"]');

  // Wait for the file to be created
  await page.waitForTimeout(1000);

  // Check if we need to click on the file to open it
  // First check if we're already in the file editor
  const fileViewerCount = await page.locator('div[class*="FileViewer"]').count();
  const isInFileEditor = fileViewerCount > 0;

  if (!isInFileEditor) {
    // We're not in the file editor, so we need to click on the file to open it
    try {
      // Try multiple selectors to find the file
      await page.locator(`span[class*="FileTitle"]:has-text("${fileName}")`).first().click()
        .catch(() => {
          return page.locator(`div[class*="styled_FileName"]:has-text("${fileName}"), div.jHdmZQ:has-text("${fileName}")`).first().click();
        });

      // Wait for the file to open
      await page.waitForTimeout(1000);
    } catch {
      // Continue with the test even if we can't click on the file
    }
  }

  return fileName;
}

/**
 * Delete a file
 *
 * @param {Object} page - Playwright page
 * @param {Object} [options] - File deletion options
 * @param {string} [options.fileName] - Name of the file to delete
 * @returns {Promise<void>}
 */
async function deleteFile(page, options = {}) {
  // Default file name is "untitled"
  const fileName = options.fileName || "untitled";

  // Navigate back to the main menu if needed
  try {
    await page.click('button[aria-label="Back"], a[href="/"]');
    await page.waitForTimeout(1000);
  } catch {
    // If that fails, try direct navigation
    await page.goto(process.env.URL || 'https://app.lidostaging.com');
  }

  // Wait for the files table to appear
  await page.waitForSelector('div[class*="FilesTable"]', { timeout: 5000 });

  // Get all files with the specified name using multiple selectors
  const files1 = page.locator(`span[class*="FileTitle"]:has-text("${fileName}")`);
  const files2 = page.locator(`div[class*="styled_FileName"]:has-text("${fileName}"), div.jHdmZQ:has-text("${fileName}")`);

  // Try to click the more options menu using different approaches
  try {
    // First approach: Using the file title element and ancestor
    await files1.first().locator('xpath=./ancestor::tr').locator('button[aria-label="More"]').click({ timeout: 5000 });
  } catch {
    try {
      // Second approach: Using the exact class from the HTML
      await page.locator('button.HomePageFileMoreOptionsMenu__MenuButton-sc-13mscgm-2').first().click({ timeout: 5000 });
    } catch {
      try {
        // Third approach: Using a more generic selector
        await page.locator('button:has-text("...")').first().click({ timeout: 5000 });
      } catch {
        // Fourth approach: Find any button that might be a more options button
        const buttons = await page.locator('button').all();

        // Click the first button that contains "..." or is at the end of a row
        let buttonClicked = false;
        for (let i = 0; i < buttons.length; i++) {
          const buttonText = await buttons[i].textContent();
          if (buttonText.includes('...')) {
            await buttons[i].click();
            buttonClicked = true;
            break;
          }
        }

        if (!buttonClicked && buttons.length > 0) {
          // If we couldn't find a button with "...", just click the last button
          await buttons[buttons.length - 1].click();
        }
      }
    }
  }

  // Wait for the menu to appear
  await page.waitForTimeout(1000);

  // Click the delete option using multiple selectors
  try {
    // First try with text
    await page.click('text=Delete', { timeout: 5000 });
  } catch {
    try {
      // Second try with the exact class from the HTML
      await page.locator('div.HomePageFileMoreOptionsMenu__MenuItemWrapper-sc-13mscgm-1:has(svg[data-test-id="trash-icon"])').first().click({ timeout: 5000 });
    } catch {
      // Third try with a more generic approach
      await page.locator('div[role="menuitem"]:has-text("Delete")').click({ timeout: 5000 });
    }
  }

  // Wait for the confirmation dialog
  try {
    await page.waitForSelector('text=Are you sure you want to delete', { timeout: 5000 });
  } catch {
    // Continue anyway, the dialog might be there but with different text
  }

  // Confirm deletion by clicking "Yes, I'm sure"
  try {
    // First try with text
    await page.click('text=Yes, I\'m sure', { timeout: 5000 });
  } catch {
    try {
      // Second try with the class from the HTML
      await page.locator('div[class*="styled_ConfirmButtonBasic"]').click({ timeout: 5000 });
    } catch {
      // Third try with a more generic approach
      await page.locator('div[role="dialog"] button:nth-child(2)').click({ timeout: 5000 });
    }
  }

  // Wait for the deletion to complete
  await page.waitForTimeout(1000);
}

module.exports = {
  test,
  expect,
  login,
  navigateToFilesPage,
  createFile,
  deleteFile,
  DEFAULT_TIMEOUT,
  DEFAULT_RETRY_COUNT,
  DEFAULT_RETRY_DELAY
};
