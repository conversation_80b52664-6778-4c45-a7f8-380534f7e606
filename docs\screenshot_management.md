# Screenshot Management System

This document describes the screenshot management system for the QA Wolf Metrics Framework. The system provides a standardized way to capture, organize, and manage screenshots during test execution.

## Table of Contents

- [Directory Structure](#directory-structure)
- [Screenshot Utilities](#screenshot-utilities)
- [Management Scripts](#management-scripts)
- [Usage in Tests](#usage-in-tests)
- [Report Generation](#report-generation)
- [Visual Regression Testing](#visual-regression-testing)
- [Best Practices](#best-practices)
- [Troubleshooting](#troubleshooting)

## Directory Structure

Screenshots are stored in a structured directory hierarchy:

```
screenshots/
  YYYY-MM-DD/
    run-<buildNumber>/
      test_name/
        action-timestamp.png
```

- **YYYY-MM-DD**: Date when the screenshots were taken
- **run-\<buildNumber\>**: Run ID to prevent collisions when running tests multiple times on the same day
- **test_name**: Name of the test that generated the screenshots
- **action-timestamp.png**: Screenshot filename with action and timestamp

## Screenshot Utilities

The screenshot utilities are defined in `src/utils/screenshot-utils.js` and provide the following functions:

### `takeScreenshot(page, options)`

Takes a screenshot and saves it in the organized directory structure.

**Parameters:**
- `page`: Playwright page object
- `options`: Screenshot options
  - `testName`: Name of the test
  - `action`: Action being performed
  - `fullPage`: Whether to take a full page screenshot (default: false)

**Example:**
```javascript
await takeScreenshot(page, {
  testName: 'login_test',
  action: 'login-form',
  fullPage: false
});
```

### `takeErrorScreenshot(page, options)`

Takes a screenshot when an error occurs.

**Parameters:**
- `page`: Playwright page object
- `options`: Screenshot options
  - `testName`: Name of the test
  - `error`: Error object
  - `fullPage`: Whether to take a full page screenshot (default: true)

**Example:**
```javascript
try {
  // Test code
} catch (error) {
  await takeErrorScreenshot(page, {
    testName: 'login_test',
    error,
    fullPage: true
  });
  throw error;
}
```

### `getTestScreenshots(testName, ticketId, date, runId)`

Gets all screenshots for a specific test.

**Parameters:**
- `testName`: Name of the test
- `ticketId`: Optional ticket ID associated with the test
- `date`: Optional date in 'yyyy-MM-dd' format (defaults to today)
- `runId`: Optional run ID (defaults to current run ID)

**Example:**
```javascript
const screenshots = getTestScreenshots('login_test', 'QW-123', '2025-05-18', 'run-12345');
```

### `cleanupScreenshots(olderThanDays)`

Cleans up screenshots older than the specified number of days.

**Parameters:**
- `olderThanDays`: Number of days to keep screenshots (default: 7)

**Example:**
```javascript
await cleanupScreenshots(14); // Keep screenshots for 14 days
```

### `verifyScreenshotStructure()`

Verifies that all screenshots are in the correct directory structure.

**Example:**
```javascript
const isValid = verifyScreenshotStructure();
if (!isValid) {
  console.error('Screenshot verification failed');
}
```

### `moveStrayScreenshots(testName)`

Moves stray screenshots to the screenshots directory.

**Parameters:**
- `testName`: Name of the test to associate with the screenshots

**Example:**
```javascript
await moveStrayScreenshots('migration');
```

## Management Scripts

The following scripts are available for managing screenshots:

### `npm run screenshots:cleanup [days]`

Cleans up screenshots older than the specified number of days.

**Example:**
```bash
npm run screenshots:cleanup 14 # Keep screenshots for 14 days
```

### `npm run screenshots:verify`

Verifies that all screenshots are in the correct directory structure.

**Example:**
```bash
npm run screenshots:verify
```

### `npm run screenshots:verify:fix`

Verifies that all screenshots are in the correct directory structure and moves stray screenshots to the screenshots directory.

**Example:**
```bash
npm run screenshots:verify:fix
```

### `npm run screenshots:migrate`

Migrates existing screenshots to the new directory structure.

**Example:**
```bash
npm run screenshots:migrate
```

### `npm run screenshots:convert`

Converts direct calls to `page.screenshot()` to use the screenshot utilities.

**Example:**
```bash
npm run screenshots:convert
```

### `npm run screenshots:report`

Generates an HTML report of screenshots for a specific test.

**Example:**
```bash
npm run screenshots:report -- --test=login_test
npm run screenshots:report -- --test=login_test --ticket=QW-123
npm run screenshots:report -- --test=login_test --date=2025-05-18
npm run screenshots:report -- --test=login_test --run=run-12345
```

### `npm run screenshots:report:all`

Generates HTML reports for all tests that have screenshots.

**Example:**
```bash
npm run screenshots:report:all
npm run screenshots:report:all -- --date=2025-05-18
```

### `npm run screenshots:baseline`

Sets a baseline for visual regression testing by copying screenshots from a specific test run to a baseline directory.

**Example:**
```bash
npm run screenshots:baseline -- --test=login_test --run=run-12345
npm run screenshots:baseline -- --test=login_test --run=run-12345 --date=2025-05-18
```

### `npm run screenshots:compare`

Compares screenshots from two different test runs to detect visual changes.

**Example:**
```bash
npm run screenshots:compare -- --test=login_test --baseline=run-12345 --compare=run-67890
npm run screenshots:compare -- --test=login_test --baseline=run-12345 --compare=run-67890 --date=2025-05-18 --threshold=0.05
```

### `npm run screenshots:compare:baseline`

Compares screenshots from a test run with the baseline screenshots.

**Example:**
```bash
npm run screenshots:compare:baseline -- --test=login_test --run=run-12345
npm run screenshots:compare:baseline -- --test=login_test --run=run-12345 --date=2025-05-18 --threshold=0.05
```

## Usage in Tests

To use the screenshot utilities in your tests, follow these steps:

1. Import the screenshot utilities:
```javascript
const { takeScreenshot, takeErrorScreenshot } = require('../../src/utils/screenshot-utils');
```

2. Take screenshots at key points in your test:
```javascript
// Take a screenshot of the login form
await takeScreenshot(page, {
  testName: 'login_test',
  action: 'login-form',
  fullPage: false
});

// Take a screenshot after login
await takeScreenshot(page, {
  testName: 'login_test',
  action: 'after-login',
  fullPage: false
});
```

3. Handle errors with error screenshots:
```javascript
try {
  // Test code
} catch (error) {
  await takeErrorScreenshot(page, {
    testName: 'login_test',
    error,
    fullPage: true
  });
  throw error;
}
```

## Report Generation

The screenshot management system includes tools for generating HTML reports of screenshots:

### Single Test Report

To generate a report for a specific test:

```bash
npm run screenshots:report -- --test=login_test
```

This will generate an HTML report with all screenshots for the test, organized by action.

### All Tests Report

To generate reports for all tests:

```bash
npm run screenshots:report:all
```

This will generate HTML reports for all tests and an index.html file that links to all reports.

## Visual Regression Testing

The screenshot management system includes tools for visual regression testing. Visual regression testing compares screenshots from different test runs to detect visual changes in the UI.

### Setting a Baseline

To set a baseline for visual regression testing, use the `screenshots:baseline` script:

```bash
npm run screenshots:baseline -- --test=login_test --run=run-12345
```

This will copy screenshots from the specified test run to a baseline directory (`screenshots/baseline/login_test/`). The baseline serves as the reference point for future comparisons.

### Comparing with Baseline

To compare screenshots from a test run with the baseline, use the `screenshots:compare:baseline` script:

```bash
npm run screenshots:compare:baseline -- --test=login_test --run=run-67890
```

This will compare screenshots from the specified test run with the baseline screenshots and generate an HTML report showing the differences. The comparison results are saved in the `screenshots/comparison/login_test/` directory.

### Comparing Two Test Runs

To compare screenshots from two different test runs, use the `screenshots:compare` script:

```bash
npm run screenshots:compare -- --test=login_test --baseline=run-12345 --compare=run-67890
```

This will compare screenshots from the two specified test runs and generate an HTML report showing the differences.

### Threshold Configuration

You can specify a threshold for pixel matching using the `--threshold` parameter. The threshold is a number between 0 and 1, where 0 means exact matching and 1 means any difference is acceptable.

```bash
npm run screenshots:compare:baseline -- --test=login_test --run=run-67890 --threshold=0.05
```

The default threshold is 0.1 (10% difference), which allows for minor variations in rendering while still catching significant visual changes.

### HTML Reports

The visual regression testing tools generate comprehensive HTML reports showing the differences between screenshots. The reports include:

- Summary statistics (passed/failed comparisons, overall pass rate)
- Overall pass/fail status for each comparison
- Side-by-side comparison of each screenshot (baseline, current, diff)
- Pixel difference percentage for each screenshot
- Threshold used for comparison

The reports are saved in the `screenshots/comparison-report/` directory and include:
- An index page linking to all test reports
- Individual test reports showing all comparisons for that test

### GitHub Actions Integration

Visual regression testing is integrated with GitHub Actions through the `visual-regression.yml` workflow. This workflow:

1. Runs tests to generate screenshots
2. Compares the screenshots with the baseline
3. Generates a comparison report
4. Uploads the screenshots, baseline, and report as artifacts
5. Fails the workflow if significant visual differences are detected

You can manually trigger the workflow with specific parameters:
- Test name to run visual regression for (default: all)
- Pixel difference threshold (default: 0.1)

### Best Practices for Visual Regression Testing

1. **Set a stable baseline**: Choose a test run with known good screenshots as your baseline.
2. **Update baselines when UI changes are expected**: After intentional UI changes, update the baseline to reflect the new expected appearance.
3. **Use appropriate thresholds**: Adjust thresholds based on the nature of your UI and the level of precision required.
4. **Include visual regression in CI/CD**: Run visual regression tests as part of your CI/CD pipeline to catch unexpected visual changes.
5. **Review reports carefully**: When differences are detected, review the reports to determine if the changes are expected or represent bugs.

## Best Practices

1. **Always use the screenshot utilities**: Never use `page.screenshot()` directly. Always use `takeScreenshot()` or `takeErrorScreenshot()` from the screenshot utilities.

2. **Use descriptive action names**: The action name should describe what the screenshot is showing, e.g., 'login-form', 'after-login', 'error-message'.

3. **Take screenshots at key points**: Take screenshots at key points in your test, such as before and after important actions, and when errors occur.

4. **Clean up old screenshots**: Run the cleanup script regularly to prevent disk space issues.

5. **Verify screenshot structure**: Run the verification script regularly to ensure all screenshots are in the correct directory structure.

6. **Generate reports**: Generate reports to make it easier to analyze test results.

## Troubleshooting

### Screenshots are not being saved

- Check that the screenshot directory exists and is writable
- Check that the test name is valid
- Check that the page object is valid

### Screenshots are not appearing in reports

- Check that the screenshots exist in the correct directory
- Check that the test name, ticket ID, date, and run ID are correct
- Check that the report generation script has permission to read the screenshots

### Direct calls to `page.screenshot()` are still being used

- Run the conversion script to convert direct calls to use the screenshot utilities
- Use the ESLint rule to prevent direct calls to `page.screenshot()`
- Review test files manually to find and fix direct calls

## ESLint Rules

To enforce best practices for screenshot management, we've implemented an ESLint rule that prevents direct calls to `page.screenshot()`. The rule is defined in `eslint-rules/no-direct-screenshot.js`:

```javascript
module.exports = {
  meta: {
    type: 'suggestion',
    docs: {
      description: 'Disallow direct calls to page.screenshot()',
      category: 'Best Practices',
      recommended: true
    },
    fixable: null,
    schema: []
  },
  create: function(context) {
    return {
      CallExpression(node) {
        if (
          node.callee.type === 'MemberExpression' &&
          node.callee.property.name === 'screenshot' &&
          node.callee.object.name === 'page'
        ) {
          context.report({
            node,
            message: 'Direct calls to page.screenshot() are not allowed. Use the screenshot utility instead.'
          });
        }
      }
    };
  }
};
```

This rule is configured in `.eslintrc.js`:

```javascript
module.exports = {
  // ... other ESLint configuration
  rules: {
    // ... other rules
    'no-direct-screenshot': 'error'
  }
};
```

## GitHub Actions Integration

We've integrated screenshot management into our GitHub Actions workflow. The workflow file is located at `.github/workflows/playwright-tests.yml`:

```yaml
name: Playwright Tests

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
      # ... other steps

      - name: Run Playwright tests
        run: npx playwright test

      - name: Generate screenshot reports
        run: node scripts/generate_screenshot_report.js

      - name: Upload screenshots
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: screenshots
          path: screenshots/
          retention-days: 30

      - name: Upload screenshot reports
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: screenshot-reports
          path: reports/screenshots/
          retention-days: 30
```

This workflow:
1. Runs the Playwright tests
2. Generates screenshot reports
3. Uploads screenshots and reports as artifacts

The artifacts are available for download from the GitHub Actions workflow run page, making it easy to access and review screenshots from test runs.
