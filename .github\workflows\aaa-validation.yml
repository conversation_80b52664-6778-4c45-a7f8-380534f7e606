name: AAA Validation

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

jobs:
  validate-aaa:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run AAA validation
        run: node validate-aaa.js
      
      - name: Upload AAA validation report
        uses: actions/upload-artifact@v3
        with:
          name: aaa-validation-report
          path: aaa-reports/
          retention-days: 14
