# Performance Tracking System

This document describes our performance tracking system for QA Wolf tests, which helps monitor and optimize test execution performance.

## Overview

Our performance tracking system provides:

1. **Test Execution Timing**: Tracks the duration of test execution
2. **Operation Timing**: Tracks the duration of specific operations within tests
3. **Resource Usage**: Monitors CPU and memory usage during test execution
4. **Performance Reports**: Generates reports of performance metrics
5. **Performance Thresholds**: Defines thresholds for acceptable performance
6. **Performance Optimization**: Provides recommendations for improving performance

## Performance Tracker Class

The core of our performance tracking system is the `PerformanceTracker` class, which is implemented in `tests/utils/performance-tracker.js`:

```javascript
/**
 * Performance Tracker for QA Wolf tests
 * 
 * This class provides utilities for tracking test performance metrics.
 */
class PerformanceTracker {
  /**
   * Create a new PerformanceTracker instance
   * @param {Object} options - Performance tracker options
   * @param {string} options.testName - Name of the test being tracked
   * @param {string} options.testId - ID of the test being tracked
   */
  constructor(options = {}) {
    this.testName = options.testName || 'unknown';
    this.testId = options.testId || `test-${Date.now()}`;
    this.startTime = null;
    this.endTime = null;
    this.operations = [];
    this.resourceUsage = [];
    this.resourceMonitorInterval = null;
  }
  
  /**
   * Start tracking performance
   * @returns {PerformanceTracker} - This instance for chaining
   */
  start() {
    this.startTime = Date.now();
    this.startResourceMonitoring();
    return this;
  }
  
  /**
   * Stop tracking performance
   * @returns {Object} - Performance metrics
   */
  stop() {
    this.endTime = Date.now();
    this.stopResourceMonitoring();
    
    return this.getMetrics();
  }
  
  /**
   * Track a specific operation
   * @param {Object} operation - Operation details
   * @param {string} operation.name - Name of the operation
   * @param {string} operation.type - Type of operation (e.g., 'navigation', 'click', 'fill')
   * @param {number} operation.duration - Duration of the operation in milliseconds
   * @param {Object} operation.metadata - Additional metadata about the operation
   * @returns {PerformanceTracker} - This instance for chaining
   */
  trackOperation(operation) {
    if (!operation.name) {
      throw new Error('Operation name is required');
    }
    
    if (!operation.type) {
      throw new Error('Operation type is required');
    }
    
    if (!operation.duration) {
      throw new Error('Operation duration is required');
    }
    
    this.operations.push({
      ...operation,
      timestamp: Date.now()
    });
    
    return this;
  }
  
  /**
   * Start monitoring resource usage
   * @private
   */
  startResourceMonitoring() {
    // Record initial resource usage
    this.recordResourceUsage();
    
    // Set up interval to record resource usage
    this.resourceMonitorInterval = setInterval(() => {
      this.recordResourceUsage();
    }, 1000); // Record every second
  }
  
  /**
   * Stop monitoring resource usage
   * @private
   */
  stopResourceMonitoring() {
    if (this.resourceMonitorInterval) {
      clearInterval(this.resourceMonitorInterval);
      this.resourceMonitorInterval = null;
    }
    
    // Record final resource usage
    this.recordResourceUsage();
  }
  
  /**
   * Record current resource usage
   * @private
   */
  recordResourceUsage() {
    // Get current resource usage
    const usage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    this.resourceUsage.push({
      timestamp: Date.now(),
      memory: {
        rss: usage.rss, // Resident Set Size - total memory allocated for the process
        heapTotal: usage.heapTotal, // Total size of the allocated heap
        heapUsed: usage.heapUsed, // Actual memory used during execution
        external: usage.external, // Memory used by C++ objects bound to JavaScript objects
        arrayBuffers: usage.arrayBuffers // Memory allocated for ArrayBuffers and SharedArrayBuffers
      },
      cpu: {
        user: cpuUsage.user, // CPU time spent in user code
        system: cpuUsage.system // CPU time spent in system code
      }
    });
  }
  
  /**
   * Get performance metrics
   * @returns {Object} - Performance metrics
   */
  getMetrics() {
    if (!this.startTime) {
      throw new Error('Performance tracking has not been started');
    }
    
    const endTime = this.endTime || Date.now();
    const duration = endTime - this.startTime;
    
    // Calculate operation statistics
    const operationStats = this.calculateOperationStats();
    
    // Calculate resource usage statistics
    const resourceStats = this.calculateResourceStats();
    
    return {
      testName: this.testName,
      testId: this.testId,
      startTime: this.startTime,
      endTime,
      duration,
      operations: this.operations,
      operationStats,
      resourceUsage: this.resourceUsage,
      resourceStats
    };
  }
  
  /**
   * Calculate operation statistics
   * @private
   * @returns {Object} - Operation statistics
   */
  calculateOperationStats() {
    if (this.operations.length === 0) {
      return {
        count: 0,
        totalDuration: 0,
        averageDuration: 0,
        minDuration: 0,
        maxDuration: 0,
        byType: {}
      };
    }
    
    // Calculate overall statistics
    const totalDuration = this.operations.reduce((sum, op) => sum + op.duration, 0);
    const averageDuration = totalDuration / this.operations.length;
    const minDuration = Math.min(...this.operations.map(op => op.duration));
    const maxDuration = Math.max(...this.operations.map(op => op.duration));
    
    // Calculate statistics by operation type
    const byType = {};
    
    for (const operation of this.operations) {
      const { type, duration } = operation;
      
      if (!byType[type]) {
        byType[type] = {
          count: 0,
          totalDuration: 0,
          averageDuration: 0,
          minDuration: Infinity,
          maxDuration: 0
        };
      }
      
      byType[type].count++;
      byType[type].totalDuration += duration;
      byType[type].minDuration = Math.min(byType[type].minDuration, duration);
      byType[type].maxDuration = Math.max(byType[type].maxDuration, duration);
    }
    
    // Calculate average duration by type
    for (const type in byType) {
      byType[type].averageDuration = byType[type].totalDuration / byType[type].count;
    }
    
    return {
      count: this.operations.length,
      totalDuration,
      averageDuration,
      minDuration,
      maxDuration,
      byType
    };
  }
  
  /**
   * Calculate resource usage statistics
   * @private
   * @returns {Object} - Resource usage statistics
   */
  calculateResourceStats() {
    if (this.resourceUsage.length === 0) {
      return {
        memory: {
          maxRss: 0,
          maxHeapTotal: 0,
          maxHeapUsed: 0,
          maxExternal: 0,
          maxArrayBuffers: 0
        },
        cpu: {
          totalUser: 0,
          totalSystem: 0
        }
      };
    }
    
    // Calculate memory statistics
    const maxRss = Math.max(...this.resourceUsage.map(usage => usage.memory.rss));
    const maxHeapTotal = Math.max(...this.resourceUsage.map(usage => usage.memory.heapTotal));
    const maxHeapUsed = Math.max(...this.resourceUsage.map(usage => usage.memory.heapUsed));
    const maxExternal = Math.max(...this.resourceUsage.map(usage => usage.memory.external));
    const maxArrayBuffers = Math.max(...this.resourceUsage.map(usage => usage.memory.arrayBuffers || 0));
    
    // Calculate CPU statistics
    const totalUser = this.resourceUsage.length > 0 ? this.resourceUsage[this.resourceUsage.length - 1].cpu.user : 0;
    const totalSystem = this.resourceUsage.length > 0 ? this.resourceUsage[this.resourceUsage.length - 1].cpu.system : 0;
    
    return {
      memory: {
        maxRss,
        maxHeapTotal,
        maxHeapUsed,
        maxExternal,
        maxArrayBuffers
      },
      cpu: {
        totalUser,
        totalSystem
      }
    };
  }
}

module.exports = PerformanceTracker;
```

## Using the Performance Tracker

To use the performance tracker in your tests:

```javascript
const { test, expect } = require('@playwright/test');
const PerformanceTracker = require('../utils/performance-tracker');

test('should login with valid credentials', async ({ page }) => {
  // Performance tracking
  const performanceTracker = new PerformanceTracker({
    testName: 'login-valid-credentials',
    testId: 'login-001'
  }).start();
  
  try {
    // Navigate to the login page
    await page.goto('https://app.lidostaging.com');
    
    // Track navigation operation
    performanceTracker.trackOperation({
      name: 'Navigate to Login Page',
      type: 'navigation',
      duration: 1000 // Measure this in your actual code
    });
    
    // Fill in login credentials
    await page.fill('[data-test-id="SignInEmail"]', '<EMAIL>');
    await page.fill('[data-test-id="SignInPassword"]', 'password');
    
    // Track form fill operation
    performanceTracker.trackOperation({
      name: 'Fill Login Form',
      type: 'form-fill',
      duration: 500 // Measure this in your actual code
    });
    
    // Click login button
    await page.click(':text("Log in with email")');
    
    // Track click operation
    performanceTracker.trackOperation({
      name: 'Click Login Button',
      type: 'click',
      duration: 200 // Measure this in your actual code
    });
    
    // Wait for navigation to complete
    await page.waitForNavigation();
    
    // Track wait operation
    performanceTracker.trackOperation({
      name: 'Wait for Navigation',
      type: 'wait',
      duration: 1500 // Measure this in your actual code
    });
    
    // Verify login was successful
    await expect(page).not.toHaveURL(/login/);
  } catch (error) {
    throw error;
  } finally {
    // Stop performance tracking and get metrics
    const metrics = performanceTracker.stop();
    console.log('Performance metrics:', JSON.stringify(metrics, null, 2));
  }
});
```

## Performance Reports

The performance tracking system generates reports of performance metrics. These reports include:

- Overall test execution duration
- Operation statistics (count, total duration, average duration, min/max duration)
- Operation statistics by type (navigation, click, form-fill, wait, etc.)
- Resource usage statistics (memory, CPU)

The reports are saved in the `performance-reports` directory and include:
- JSON reports with raw performance data
- HTML reports with visualizations of performance metrics

## Performance Thresholds

The performance tracking system defines thresholds for acceptable performance. These thresholds are defined in `config/performance-thresholds.js`:

```javascript
module.exports = {
  // Maximum acceptable test execution duration in milliseconds
  maxTestDuration: 60000, // 1 minute
  
  // Maximum acceptable operation durations in milliseconds
  maxOperationDurations: {
    navigation: 5000, // 5 seconds
    click: 1000, // 1 second
    'form-fill': 2000, // 2 seconds
    wait: 10000, // 10 seconds
    default: 3000 // 3 seconds
  },
  
  // Maximum acceptable resource usage
  maxResourceUsage: {
    memory: {
      rss: 500 * 1024 * 1024, // 500 MB
      heapTotal: 200 * 1024 * 1024, // 200 MB
      heapUsed: 150 * 1024 * 1024 // 150 MB
    }
  }
};
```

## Performance Optimization

The performance tracking system provides recommendations for improving performance. These recommendations are based on the performance metrics and thresholds.

For example, if a test exceeds the maximum acceptable duration, the system might recommend:
- Reducing the number of screenshots
- Using more efficient selectors
- Reducing wait times
- Optimizing resource usage

## GitHub Actions Integration

We've integrated performance tracking into our GitHub Actions workflow. The workflow file is located at `.github/workflows/performance-tests.yml`:

```yaml
name: Performance Tests

on:
  schedule:
    - cron: '0 0 * * *' # Run daily at midnight
  workflow_dispatch:

jobs:
  performance:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run performance tests
        run: npm run test:performance
      
      - name: Generate performance report
        run: node scripts/generate-performance-report.js
      
      - name: Upload performance report
        uses: actions/upload-artifact@v3
        with:
          name: performance-report
          path: performance-reports/
          retention-days: 30
```

This workflow:
1. Runs performance tests daily
2. Generates a performance report
3. Uploads the report as an artifact

## Benefits

Our performance tracking system provides several benefits:

1. **Performance Monitoring**: Track test execution performance over time
2. **Performance Optimization**: Identify and fix performance bottlenecks
3. **Resource Usage Monitoring**: Monitor CPU and memory usage during test execution
4. **Performance Regression Detection**: Detect performance regressions early
5. **Performance Reports**: Generate reports of performance metrics for analysis
