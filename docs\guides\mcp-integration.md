# MCP Integration

This guide explains how to use MCP integration in the QA Wolf testing framework.

## What is MCP?

MCP (Multi-modal Conversational Processor) is a technology that enables AI models to interact with tools and perform tasks. In the context of the QA Wolf testing framework, MCP is used to optimize selectors, select appropriate tools for tasks, and analyze screenshots.

## MCP Tools

The QA Wolf testing framework integrates with the following MCP tools:

- **Playwright MCP**: A tool for interacting with Play<PERSON>.
- **Browser Tools MCP**: A tool for analyzing web pages and screenshots.

## Creating an MCP Controller

To use MCP integration, you need to create an MCP controller:

```javascript
const { createMcpController } = require('@qawolf/test-framework');

const mcpController = createMcpController({
  autoStartPlaywrightMcp: true,
  playwrightMcpPort: 8932,
  generateFallbacks: true,
  prioritizeTestIds: true
});
```

The `createMcpController` function accepts the following options:

- `autoStartPlaywrightMcp` (boolean, default: true): Whether to automatically start the Playwright MCP server.
- `playwrightMcpPort` (number, default: 8932): Port for the Playwright MCP server.
- `generateFallbacks` (boolean, default: true): Whether to generate fallback selectors.
- `prioritizeTestIds` (boolean, default: true): Whether to prioritize test IDs.

## Optimizing Selectors

One of the main benefits of MCP integration is selector optimization. The MCP controller can optimize selectors for better reliability:

```javascript
const selectors = await mcpController.optimizeSelectors([
  '[data-test-id="SignInEmail"]',
  '[data-test-id="SignInPassword"]',
  ':text("Log in with email")'
]);

console.log(selectors);
// {
//   selectors: [
//     {
//       original: '[data-test-id="SignInEmail"]',
//       optimized: '[data-test-id="SignInEmail"]',
//       fallbacks: ['input[type="email"]', 'input[placeholder="Email"]'],
//       reliability: 0.95
//     },
//     {
//       original: '[data-test-id="SignInPassword"]',
//       optimized: '[data-test-id="SignInPassword"]',
//       fallbacks: ['input[type="password"]', 'input[placeholder="Password"]'],
//       reliability: 0.95
//     },
//     {
//       original: ':text("Log in with email")',
//       optimized: ':text("Log in with email")',
//       fallbacks: ['button:has-text("Log in with email")', 'button[type="submit"]'],
//       reliability: 0.95
//     }
//   ]
// }
```

The `optimizeSelectors` function returns an object with an array of optimized selectors. Each optimized selector has the following properties:

- `original` (string): The original selector.
- `optimized` (string): The optimized selector.
- `fallbacks` (Array<string>): Fallback selectors to use if the optimized selector fails.
- `reliability` (number): A reliability score between 0 and 1.

## Selecting Tools

The MCP controller can also select the appropriate MCP tool for a task:

```javascript
const tool = await mcpController.selectTool({
  type: 'click',
  description: 'Click the login button',
  parameters: {
    selector: ':text("Log in with email")'
  }
});

console.log(tool);
// {
//   name: 'browser_click_Playwright',
//   command: 'browser_click_Playwright',
//   parameters: {
//     element: 'Login button',
//     ref: ':text("Log in with email")'
//   },
//   reliability: 0.95
// }
```

The `selectTool` function returns an object with the selected tool. The tool object has the following properties:

- `name` (string): The name of the tool.
- `command` (string): The command to execute.
- `parameters` (Object): The parameters for the command.
- `reliability` (number): A reliability score between 0 and 1.

## Analyzing Screenshots

The MCP controller can analyze screenshots to identify UI elements:

```javascript
const analysis = await mcpController.analyzeScreenshot('screenshot.png');

console.log(analysis);
// {
//   elements: [
//     {
//       type: 'input',
//       text: '',
//       selector: '[data-test-id="SignInEmail"]',
//       boundingBox: { x: 100, y: 100, width: 200, height: 30 },
//       attributes: { type: 'email', placeholder: 'Email' }
//     },
//     {
//       type: 'input',
//       text: '',
//       selector: '[data-test-id="SignInPassword"]',
//       boundingBox: { x: 100, y: 150, width: 200, height: 30 },
//       attributes: { type: 'password', placeholder: 'Password' }
//     },
//     {
//       type: 'button',
//       text: 'Log in with email',
//       selector: ':text("Log in with email")',
//       boundingBox: { x: 100, y: 200, width: 200, height: 40 },
//       attributes: { type: 'submit' }
//     }
//   ],
//   screenshot: 'screenshot.png'
// }
```

The `analyzeScreenshot` function returns an object with an array of identified elements. Each element has the following properties:

- `type` (string): The type of the element (e.g., 'input', 'button').
- `text` (string): The text of the element.
- `selector` (string): A selector that can be used to locate the element.
- `boundingBox` (Object): The bounding box of the element.
- `attributes` (Object): The attributes of the element.

## Cleaning Up

When you're done with the MCP controller, you should clean up its resources:

```javascript
await mcpController.cleanup();
```

This will stop the Playwright MCP server and release any other resources used by the MCP controller.

## Best Practices

Here are some best practices for using MCP integration:

1. **Create the MCP controller at the beginning of your test**: This ensures that the MCP controller is available throughout your test.

2. **Clean up resources at the end of your test**: This prevents resource leaks.

3. **Use optimized selectors**: Optimized selectors are more reliable than manual selectors.

4. **Use fallback selectors**: Fallback selectors provide alternatives if the primary selector fails.

5. **Prioritize test IDs**: Test IDs are more stable than other selectors.

## Conclusion

MCP integration provides powerful capabilities for optimizing selectors, selecting tools, and analyzing screenshots. By using these capabilities, you can create more reliable and maintainable tests.