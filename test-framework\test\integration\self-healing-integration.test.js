/**
 * Self-Healing Integration Tests
 * 
 * This file contains integration tests for the self-healing module.
 */

const { test, expect } = require('@playwright/test');
const { selfHealing } = require('../../src');
const { createTestApp } = require('../fixtures/test-app');

test.describe('Self-Healing Integration', () => {
  let testApp;
  
  test.beforeEach(async () => {
    testApp = await createTestApp();
  });
  
  test.afterEach(async () => {
    await testApp.cleanup();
  });
  
  test('should heal broken selectors', async () => {
    // Create a self-healing controller
    const selfHealingController = selfHealing.createSelfHealingController({
      selectorHealing: {
        enabled: true,
        maxAttempts: 3,
        strategies: ['css-relaxation', 'attribute-based', 'text-based', 'xpath']
      }
    });
    
    // Initialize the controller
    await selfHealingController.initialize();
    
    // Create a broken selector
    const brokenSelector = 'button.non-existent-class';
    
    // Create a valid alternative
    const validSelector = ':text("Log in with email")';
    
    // Register the valid alternative
    await selfHealingController.selectorHealer.trackSelectorResult(validSelector, true, {
      action: 'click',
      page: testApp.page
    });
    
    // Try to heal the broken selector
    const healResult = await selfHealingController.selectorHealer.healSelector(brokenSelector, {
      action: 'click',
      page: testApp.page
    });
    
    // Verify
    expect(healResult.healed).toBe(true);
    expect(healResult.selector).toBe(validSelector);
    
    // Clean up
    await selfHealingController.cleanup();
  });
  
  test('should recover from failures', async () => {
    // Create a self-healing controller
    const selfHealingController = selfHealing.createSelfHealingController({
      recovery: {
        enabled: true,
        maxAttempts: 3,
        strategies: ['retry', 'wait', 'selector', 'refresh', 'screenshot']
      }
    });
    
    // Initialize the controller
    await selfHealingController.initialize();
    
    // Create a test error
    const error = new Error('Element not found');
    
    // Try to recover from the error
    const recoveryResult = await selfHealingController.recoveryManager.recoverFromFailure(error, {
      action: 'click',
      selector: ':text("Log in with email")',
      page: testApp.page
    });
    
    // Verify
    expect(recoveryResult.attempts).toBeGreaterThan(0);
    
    // Clean up
    await selfHealingController.cleanup();
  });
  
  test('should collect feedback', async () => {
    // Create a self-healing controller
    const selfHealingController = selfHealing.createSelfHealingController({
      feedbackCollection: {
        enabled: true,
        persistFeedback: false
      }
    });
    
    // Initialize the controller
    await selfHealingController.initialize();
    
    // Start a test run
    await selfHealingController.feedbackCollector.startTestRun({
      testId: 'test-feedback',
      testName: 'Test Feedback Collection'
    });
    
    // Track a selector result
    await selfHealingController.feedbackCollector.trackSelectorResult(':text("Log in with email")', true, {
      action: 'click',
      page: testApp.page
    });
    
    // Track performance metrics
    await selfHealingController.feedbackCollector.trackPerformance({
      'navigation': 1000,
      'login': 2000
    });
    
    // End the test run
    await selfHealingController.feedbackCollector.endTestRun({
      success: true
    });
    
    // Generate a report
    const report = await selfHealingController.feedbackCollector.generateReport({ format: 'json' });
    
    // Verify
    expect(report).toContain('test-feedback');
    
    // Clean up
    await selfHealingController.cleanup();
  });
  
  test('should learn from feedback', async () => {
    // Create a self-healing controller
    const selfHealingController = selfHealing.createSelfHealingController({
      learning: {
        enabled: true,
        persistLearning: false
      }
    });
    
    // Initialize the controller
    await selfHealingController.initialize();
    
    // Create test feedback data
    const feedbackData = {
      testRuns: [
        {
          testId: 'test-learning',
          testName: 'Test Learning',
          success: true,
          selectors: {
            ':text("Log in with email")': {
              selector: ':text("Log in with email")',
              attempts: 5,
              successes: 5,
              failures: 0,
              successRate: 1
            }
          }
        }
      ],
      selectors: {
        ':text("Log in with email")': {
          selector: ':text("Log in with email")',
          attempts: 5,
          successes: 5,
          failures: 0,
          successRate: 1
        }
      }
    };
    
    // Learn from feedback data
    const learningResult = await selfHealingController.learningEngine.learn(feedbackData);
    
    // Verify
    expect(learningResult.learned).toBe(true);
    
    // Clean up
    await selfHealingController.cleanup();
  });
  
  test('should create a self-healing page', async () => {
    // Create a self-healing controller
    const selfHealingController = selfHealing.createSelfHealingController();
    
    // Initialize the controller
    await selfHealingController.initialize();
    
    // Create a self-healing page
    const selfHealingPage = selfHealingController.createPage(testApp.page);
    
    // Verify
    expect(selfHealingPage).toBeDefined();
    expect(typeof selfHealingPage.goto).toBe('function');
    expect(typeof selfHealingPage.click).toBe('function');
    expect(typeof selfHealingPage.fill).toBe('function');
    
    // Clean up
    await selfHealingController.cleanup();
  });
});