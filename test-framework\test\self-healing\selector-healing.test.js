/**
 * Selector Healing Tests
 * 
 * This file contains tests for the selector healing capabilities.
 */

const { test, expect } = require('@playwright/test');
const { selfHealing } = require('../../src');
const { createTestApp, createMetricsCollector, createReportGenerator } = require('../fixtures');

test.describe('Selector Healing Tests', () => {
  let testApp;
  let metricsCollector;
  let reportGenerator;
  
  test.beforeEach(async () => {
    testApp = await createTestApp();
    metricsCollector = createMetricsCollector();
    reportGenerator = createReportGenerator();
  });
  
  test.afterEach(async () => {
    await testApp.cleanup();
  });
  
  test('should heal broken CSS selectors', async () => {
    const testName = 'heal-css-selector';
    const startTime = Date.now();
    
    // Create a self-healing controller
    const selfHealingController = selfHealing.createSelfHealingController({
      selectorHealing: {
        enabled: true,
        maxAttempts: 3,
        strategies: ['css-relaxation', 'attribute-based', 'text-based', 'xpath'],
        persistHistory: false
      }
    });
    
    try {
      // Initialize the controller
      await selfHealingController.initialize();
      
      // Create a self-healing page
      const selfHealingPage = selfHealingController.createPage(testApp.page);
      
      // Navigate to the app
      await selfHealingPage.goto(testApp.baseUrl);
      
      // Register a working selector
      await selfHealingController.selectorHealer.trackSelectorResult('[data-test-id="SignInEmail"]', true, {
        action: 'fill',
        page: testApp.page
      });
      
      // Create a broken selector with the same base element
      const brokenSelector = '[data-test-id="SignInEmail"].non-existent-class';
      
      // Try to use the broken selector
      try {
        await selfHealingPage.fill(brokenSelector, '<EMAIL>');
        
        // If we get here, the selector was healed
        metricsCollector.recordSelfHealingResult(testName, true);
      } catch (error) {
        // If we get here, the selector was not healed
        metricsCollector.recordSelfHealingResult(testName, false);
        throw error;
      }
      
      // Record metrics
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      metricsCollector.recordExecutionTime(testName, duration);
      metricsCollector.recordTestResult(testName, true);
      
      // Generate report
      await reportGenerator.generateReport(metricsCollector.getMetrics(), 'selector-healing-tests');
    } finally {
      // Clean up resources
      await selfHealingController.cleanup();
    }
  });
  
  test('should heal broken XPath selectors', async () => {
    const testName = 'heal-xpath-selector';
    const startTime = Date.now();
    
    // Create a self-healing controller
    const selfHealingController = selfHealing.createSelfHealingController({
      selectorHealing: {
        enabled: true,
        maxAttempts: 3,
        strategies: ['css-relaxation', 'attribute-based', 'text-based', 'xpath'],
        persistHistory: false
      }
    });
    
    try {
      // Initialize the controller
      await selfHealingController.initialize();
      
      // Create a self-healing page
      const selfHealingPage = selfHealingController.createPage(testApp.page);
      
      // Navigate to the app
      await selfHealingPage.goto(testApp.baseUrl);
      
      // Register a working selector
      await selfHealingController.selectorHealer.trackSelectorResult('[data-test-id="SignInEmail"]', true, {
        action: 'fill',
        page: testApp.page
      });
      
      // Create a broken XPath selector
      const brokenSelector = '//input[@data-test-id="SignInEmail" and @non-existent-attr="value"]';
      
      // Try to use the broken selector
      try {
        await selfHealingPage.fill(brokenSelector, '<EMAIL>');
        
        // If we get here, the selector was healed
        metricsCollector.recordSelfHealingResult(testName, true);
      } catch (error) {
        // If we get here, the selector was not healed
        metricsCollector.recordSelfHealingResult(testName, false);
        throw error;
      }
      
      // Record metrics
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      metricsCollector.recordExecutionTime(testName, duration);
      metricsCollector.recordTestResult(testName, true);
      
      // Generate report
      await reportGenerator.generateReport(metricsCollector.getMetrics(), 'selector-healing-tests');
    } finally {
      // Clean up resources
      await selfHealingController.cleanup();
    }
  });
  
  test('should heal broken text selectors', async () => {
    const testName = 'heal-text-selector';
    const startTime = Date.now();
    
    // Create a self-healing controller
    const selfHealingController = selfHealing.createSelfHealingController({
      selectorHealing: {
        enabled: true,
        maxAttempts: 3,
        strategies: ['css-relaxation', 'attribute-based', 'text-based', 'xpath'],
        persistHistory: false
      }
    });
    
    try {
      // Initialize the controller
      await selfHealingController.initialize();
      
      // Create a self-healing page
      const selfHealingPage = selfHealingController.createPage(testApp.page);
      
      // Navigate to the app
      await selfHealingPage.goto(testApp.baseUrl);
      
      // Register a working selector
      await selfHealingController.selectorHealer.trackSelectorResult(':text("Log in with email")', true, {
        action: 'click',
        page: testApp.page
      });
      
      // Create a broken text selector
      const brokenSelector = ':text("Log in with email that does not exist")';
      
      // Try to use the broken selector
      try {
        await selfHealingPage.click(brokenSelector);
        
        // If we get here, the selector was healed
        metricsCollector.recordSelfHealingResult(testName, true);
      } catch (error) {
        // If we get here, the selector was not healed
        metricsCollector.recordSelfHealingResult(testName, false);
        throw error;
      }
      
      // Record metrics
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      metricsCollector.recordExecutionTime(testName, duration);
      metricsCollector.recordTestResult(testName, true);
      
      // Generate report
      await reportGenerator.generateReport(metricsCollector.getMetrics(), 'selector-healing-tests');
    } finally {
      // Clean up resources
      await selfHealingController.cleanup();
    }
  });
});