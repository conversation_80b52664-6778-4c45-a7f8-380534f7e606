/**
 * AAA (Arrange-Act-Assert) pattern for QA Wolf Test Framework
 */

import * as fs from 'fs';
import * as path from 'path';
import { AAAPattern, AAAValidationResult, AAAValidator, AAAValidatorOptions } from './types';
import { fileExists, readFileAsString } from '@qawolf/shared-utils';

/**
 * Default AAA validator options
 */
const defaultOptions: AAAValidatorOptions = {
  minScore: 90,
  strictMode: false,
  ignoreComments: true,
  ignoreImports: true
};

/**
 * AAA validator
 */
export class AAAValidator implements AAAValidator {
  /**
   * Validate code against AAA pattern
   * @param code Code to validate
   * @param options Validation options
   * @returns Validation result
   */
  validate(code: string, options: Partial<AAAValidatorOptions> = {}): AAAValidationResult {
    const opts = { ...defaultOptions, ...options };
    const pattern = this.extractAAAPattern(code, opts);
    
    const errors: string[] = [];
    const warnings: string[] = [];
    
    // Check if all sections are present
    if (pattern.arrange.length === 0) {
      errors.push('Missing Arrange section');
    }
    
    if (pattern.act.length === 0) {
      errors.push('Missing Act section');
    }
    
    if (pattern.assert.length === 0) {
      errors.push('Missing Assert section');
    }
    
    // Check if sections are in the correct order
    if (pattern.arrange.length > 0 && pattern.act.length > 0) {
      const lastArrangeLineNumber = this.getLastLineNumber(pattern.arrange);
      const firstActLineNumber = this.getFirstLineNumber(pattern.act);
      
      if (lastArrangeLineNumber > firstActLineNumber) {
        errors.push('Arrange section should come before Act section');
      }
    }
    
    if (pattern.act.length > 0 && pattern.assert.length > 0) {
      const lastActLineNumber = this.getLastLineNumber(pattern.act);
      const firstAssertLineNumber = this.getFirstLineNumber(pattern.assert);
      
      if (lastActLineNumber > firstAssertLineNumber) {
        errors.push('Act section should come before Assert section');
      }
    }
    
    // Check if there are any lines outside of AAA sections
    const allLines = code.split('\n');
    const aaaLines = [...pattern.arrange, ...pattern.act, ...pattern.assert];
    
    const nonAAALines = allLines.filter((line, index) => {
      // Skip empty lines
      if (line.trim() === '') {
        return false;
      }
      
      // Skip comments if ignoreComments is true
      if (opts.ignoreComments && (line.trim().startsWith('//') || line.trim().startsWith('/*') || line.trim().startsWith('*'))) {
        return false;
      }
      
      // Skip imports if ignoreImports is true
      if (opts.ignoreImports && (line.trim().startsWith('import ') || line.trim().startsWith('require('))) {
        return false;
      }
      
      // Skip lines that are part of AAA sections
      return !aaaLines.includes(line);
    });
    
    if (nonAAALines.length > 0) {
      warnings.push(`Found ${nonAAALines.length} lines outside of AAA sections`);
    }
    
    // Calculate score
    const totalLines = allLines.length - (opts.ignoreComments ? allLines.filter(line => line.trim().startsWith('//') || line.trim().startsWith('/*') || line.trim().startsWith('*')).length : 0) - (opts.ignoreImports ? allLines.filter(line => line.trim().startsWith('import ') || line.trim().startsWith('require(')).length : 0);
    const aaaLinesCount = aaaLines.length;
    const score = totalLines > 0 ? (aaaLinesCount / totalLines) * 100 : 0;
    
    // Check if score is above minimum
    const valid = errors.length === 0 && score >= opts.minScore;
    
    return {
      valid,
      score,
      errors,
      warnings,
      pattern
    };
  }
  
  /**
   * Extract AAA pattern from code
   * @param code Code to extract pattern from
   * @param options Validation options
   * @returns AAA pattern
   */
  private extractAAAPattern(code: string, options: AAAValidatorOptions): AAAPattern {
    const lines = code.split('\n');
    const pattern: AAAPattern = {
      arrange: [],
      act: [],
      assert: []
    };
    
    let currentSection: 'arrange' | 'act' | 'assert' | null = null;
    
    for (const line of lines) {
      // Skip empty lines
      if (line.trim() === '') {
        continue;
      }
      
      // Skip comments if ignoreComments is true
      if (options.ignoreComments && (line.trim().startsWith('//') || line.trim().startsWith('/*') || line.trim().startsWith('*'))) {
        continue;
      }
      
      // Skip imports if ignoreImports is true
      if (options.ignoreImports && (line.trim().startsWith('import ') || line.trim().startsWith('require('))) {
        continue;
      }
      
      // Check for section markers
      if (line.includes('// Arrange') || line.includes('/* Arrange') || line.includes('* Arrange') || line.includes('await testContext.arrange(')) {
        currentSection = 'arrange';
      } else if (line.includes('// Act') || line.includes('/* Act') || line.includes('* Act') || line.includes('await testContext.act(')) {
        currentSection = 'act';
      } else if (line.includes('// Assert') || line.includes('/* Assert') || line.includes('* Assert') || line.includes('await testContext.assert(')) {
        currentSection = 'assert';
      }
      
      // Add line to current section
      if (currentSection) {
        pattern[currentSection].push(line);
      }
    }
    
    return pattern;
  }
  
  /**
   * Get first line number of a section
   * @param section Section lines
   * @returns First line number
   */
  private getFirstLineNumber(section: string[]): number {
    return section.length > 0 ? 0 : -1;
  }
  
  /**
   * Get last line number of a section
   * @param section Section lines
   * @returns Last line number
   */
  private getLastLineNumber(section: string[]): number {
    return section.length > 0 ? section.length - 1 : -1;
  }
}

/**
 * Create AAA validator
 * @returns AAA validator
 */
export function createAAAValidator(): AAAValidator {
  return new AAAValidator();
}

/**
 * Validate file against AAA pattern
 * @param filePath File path
 * @param options Validation options
 * @returns Validation result
 */
export function validateFile(filePath: string, options: Partial<AAAValidatorOptions> = {}): AAAValidationResult {
  if (!fileExists(filePath)) {
    return {
      valid: false,
      score: 0,
      errors: [`File not found: ${filePath}`],
      warnings: [],
      pattern: {
        arrange: [],
        act: [],
        assert: []
      }
    };
  }
  
  const code = readFileAsString(filePath);
  const validator = createAAAValidator();
  
  return validator.validate(code, options);
}

/**
 * Validate directory against AAA pattern
 * @param dirPath Directory path
 * @param options Validation options
 * @returns Validation results
 */
export function validateDirectory(dirPath: string, options: Partial<AAAValidatorOptions> & { filePattern?: string } = {}): { [filePath: string]: AAAValidationResult } {
  const results: { [filePath: string]: AAAValidationResult } = {};
  
  if (!fs.existsSync(dirPath)) {
    return results;
  }
  
  const filePattern = options.filePattern || /\.(js|ts)$/;
  const files = fs.readdirSync(dirPath);
  
  for (const file of files) {
    const filePath = path.join(dirPath, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      const subResults = validateDirectory(filePath, options);
      Object.assign(results, subResults);
    } else if (stat.isFile() && filePattern.test(file)) {
      results[filePath] = validateFile(filePath, options);
    }
  }
  
  return results;
}

/**
 * Generate AAA validation report
 * @param results Validation results
 * @param outputPath Output path
 */
export function generateAAAValidationReport(results: { [filePath: string]: AAAValidationResult }, outputPath: string): void {
  const totalFiles = Object.keys(results).length;
  const validFiles = Object.values(results).filter(result => result.valid).length;
  const invalidFiles = totalFiles - validFiles;
  const averageScore = totalFiles > 0 ? Object.values(results).reduce((sum, result) => sum + result.score, 0) / totalFiles : 0;
  
  const report = {
    summary: {
      totalFiles,
      validFiles,
      invalidFiles,
      averageScore,
      timestamp: new Date().toISOString()
    },
    results
  };
  
  fs.writeFileSync(outputPath, JSON.stringify(report, null, 2));
}

/**
 * Generate AAA validation HTML report
 * @param results Validation results
 * @param outputPath Output path
 */
export function generateAAAValidationHtmlReport(results: { [filePath: string]: AAAValidationResult }, outputPath: string): void {
  const totalFiles = Object.keys(results).length;
  const validFiles = Object.values(results).filter(result => result.valid).length;
  const invalidFiles = totalFiles - validFiles;
  const averageScore = totalFiles > 0 ? Object.values(results).reduce((sum, result) => sum + result.score, 0) / totalFiles : 0;
  
  const html = `
<!DOCTYPE html>
<html>
<head>
  <title>AAA Validation Report</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      color: #333;
    }
    
    h1, h2, h3 {
      color: #2c3e50;
    }
    
    .summary {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }
    
    .summary-item {
      text-align: center;
      padding: 10px;
      border-radius: 5px;
      flex: 1;
      margin: 0 5px;
    }
    
    .summary-item.total {
      background-color: #f8f9fa;
    }
    
    .summary-item.valid {
      background-color: #d4edda;
      color: #155724;
    }
    
    .summary-item.invalid {
      background-color: #f8d7da;
      color: #721c24;
    }
    
    .summary-item h2 {
      margin: 0;
      font-size: 24px;
    }
    
    .summary-item p {
      margin: 5px 0 0;
    }
    
    .file {
      margin-bottom: 20px;
      padding: 15px;
      border-radius: 5px;
    }
    
    .file.valid {
      background-color: #d4edda;
      border-left: 5px solid #28a745;
    }
    
    .file.invalid {
      background-color: #f8d7da;
      border-left: 5px solid #dc3545;
    }
    
    .file h3 {
      margin-top: 0;
    }
    
    .file-info {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
      font-size: 14px;
      color: #6c757d;
    }
    
    .errors, .warnings {
      margin-top: 10px;
    }
    
    .errors h4, .warnings h4 {
      margin-bottom: 5px;
    }
    
    .errors ul, .warnings ul {
      margin-top: 0;
    }
    
    .pattern {
      margin-top: 10px;
    }
    
    .pattern h4 {
      margin-bottom: 5px;
    }
    
    .pattern-section {
      margin-bottom: 10px;
    }
    
    .pattern-section h5 {
      margin-bottom: 5px;
    }
    
    .pattern-section pre {
      margin: 0;
      padding: 10px;
      background-color: #f8f9fa;
      border-radius: 3px;
      overflow-x: auto;
    }
  </style>
</head>
<body>
  <h1>AAA Validation Report</h1>
  
  <div class="summary">
    <div class="summary-item total">
      <h2>${totalFiles}</h2>
      <p>Total Files</p>
    </div>
    <div class="summary-item valid">
      <h2>${validFiles}</h2>
      <p>Valid Files</p>
    </div>
    <div class="summary-item invalid">
      <h2>${invalidFiles}</h2>
      <p>Invalid Files</p>
    </div>
  </div>
  
  <p>Average Score: ${averageScore.toFixed(2)}% | Timestamp: ${new Date().toISOString()}</p>
  
  <h2>Files</h2>
  
  ${Object.entries(results).map(([filePath, result]) => `
    <div class="file ${result.valid ? 'valid' : 'invalid'}">
      <h3>${filePath}</h3>
      <div class="file-info">
        <span>Valid: ${result.valid ? 'Yes' : 'No'}</span>
        <span>Score: ${result.score.toFixed(2)}%</span>
      </div>
      
      ${result.errors.length > 0 ? `
        <div class="errors">
          <h4>Errors</h4>
          <ul>
            ${result.errors.map(error => `<li>${error}</li>`).join('')}
          </ul>
        </div>
      ` : ''}
      
      ${result.warnings.length > 0 ? `
        <div class="warnings">
          <h4>Warnings</h4>
          <ul>
            ${result.warnings.map(warning => `<li>${warning}</li>`).join('')}
          </ul>
        </div>
      ` : ''}
      
      <div class="pattern">
        <h4>AAA Pattern</h4>
        
        <div class="pattern-section">
          <h5>Arrange (${result.pattern.arrange.length} lines)</h5>
          <pre>${result.pattern.arrange.join('\n')}</pre>
        </div>
        
        <div class="pattern-section">
          <h5>Act (${result.pattern.act.length} lines)</h5>
          <pre>${result.pattern.act.join('\n')}</pre>
        </div>
        
        <div class="pattern-section">
          <h5>Assert (${result.pattern.assert.length} lines)</h5>
          <pre>${result.pattern.assert.join('\n')}</pre>
        </div>
      </div>
    </div>
  `).join('')}
</body>
</html>
  `;
  
  fs.writeFileSync(outputPath, html);
}
