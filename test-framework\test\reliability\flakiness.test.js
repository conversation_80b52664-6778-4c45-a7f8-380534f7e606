/**
 * Flakiness Tests
 * 
 * This file contains tests for measuring test flakiness.
 */

const { test, expect } = require('@playwright/test');
const { createSelfHealingController, test: testUtils } = require('../../src');
const { createTestApp, createMetricsCollector, createReportGenerator } = require('../fixtures');

test.describe('Flakiness Tests', () => {
  let testApp;
  let metricsCollector;
  let reportGenerator;
  
  test.beforeEach(async () => {
    testApp = await createTestApp();
    metricsCollector = createMetricsCollector();
    reportGenerator = createReportGenerator();
  });
  
  test.afterEach(async () => {
    await testApp.cleanup();
  });
  
  test('should measure flakiness with and without self-healing', async () => {
    // Create a self-healing controller
    const selfHealingController = createSelfHealingController({
      selectorHealing: {
        enabled: true,
        persistHistory: false
      },
      recovery: {
        enabled: true
      },
      feedbackCollection: {
        enabled: true,
        persistFeedback: false
      }
    });
    
    try {
      // Initialize the controller
      await selfHealingController.initialize();
      
      // Run flaky test without self-healing
      const numRuns = 5;
      let successfulRuns = 0;
      
      for (let i = 0; i < numRuns; i++) {
        try {
          // Navigate to the app
          await testApp.navigate();
          
          // Introduce flakiness by using a selector that sometimes works
          // In a real test, this would be a selector that is flaky due to timing issues or dynamic content
          const flakySelector = i % 2 === 0 ? '[data-test-id="SignInEmail"]' : '[data-test-id="SignInEmail-not-found"]';
          
          await testApp.page.fill(flakySelector, testApp.credentials.email);
          await testApp.page.fill('[data-test-id="SignInPassword"]', testApp.credentials.password);
          await testApp.page.click(':text("Log in with email")');
          
          // If we get here, the test was successful
          successfulRuns++;
          
          // Record test result
          metricsCollector.recordTestResult(`flaky-test-without-self-healing-${i}`, true);
        } catch (error) {
          // Record test result
          metricsCollector.recordTestResult(`flaky-test-without-self-healing-${i}`, false);
          
          // Navigate back to login page
          await testApp.page.goto(testApp.baseUrl);
        }
      }
      
      // Calculate flakiness rate
      const flakinessRateWithout = 1 - (successfulRuns / numRuns);
      console.log(`Flakiness rate without self-healing: ${flakinessRateWithout * 100}%`);
      
      // Run flaky test with self-healing
      successfulRuns = 0;
      
      // Create a self-healing page
      const selfHealingPage = selfHealingController.createPage(testApp.page);
      
      // Register a working selector
      await selfHealingController.selectorHealer.trackSelectorResult('[data-test-id="SignInEmail"]', true, {
        action: 'fill',
        page: testApp.page
      });
      
      for (let i = 0; i < numRuns; i++) {
        try {
          // Start the test run
          await selfHealingController.startTest({
            testId: `flaky-test-with-self-healing-${i}`,
            testName: `Flaky Test With Self-Healing ${i}`
          });
          
          // Navigate to the app
          await selfHealingPage.goto(testApp.baseUrl);
          
          // Introduce flakiness by using a selector that sometimes works
          const flakySelector = i % 2 === 0 ? '[data-test-id="SignInEmail"]' : '[data-test-id="SignInEmail-not-found"]';
          
          await selfHealingPage.fill(flakySelector, testApp.credentials.email);
          await selfHealingPage.fill('[data-test-id="SignInPassword"]', testApp.credentials.password);
          await selfHealingPage.click(':text("Log in with email")');
          
          // If we get here, the test was successful
          successfulRuns++;
          
          // End the test run
          await selfHealingController.endTest({
            success: true
          });
          
          // Record test result
          metricsCollector.recordTestResult(`flaky-test-with-self-healing-${i}`, true);
          metricsCollector.recordSelfHealingResult(`flaky-test-with-self-healing-${i}`, true);
        } catch (error) {
          // End the test run with failure
          await selfHealingController.endTest({
            success: false,
            error
          });
          
          // Record test result
          metricsCollector.recordTestResult(`flaky-test-with-self-healing-${i}`, false);
          metricsCollector.recordSelfHealingResult(`flaky-test-with-self-healing-${i}`, false);
          
          // Navigate back to login page
          await selfHealingPage.goto(testApp.baseUrl);
        }
      }
      
      // Calculate flakiness rate
      const flakinessRateWith = 1 - (successfulRuns / numRuns);
      console.log(`Flakiness rate with self-healing: ${flakinessRateWith * 100}%`);
      
      // Compare flakiness rates
      console.log(`Flakiness reduction: ${(flakinessRateWithout - flakinessRateWith) * 100}%`);
      
      // Generate report
      await reportGenerator.generateReport(metricsCollector.getMetrics(), 'flakiness-tests');
    } finally {
      // Clean up resources
      await selfHealingController.cleanup();
    }
  });
});