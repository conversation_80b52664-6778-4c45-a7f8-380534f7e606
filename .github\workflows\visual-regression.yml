name: Visual Regression Testing

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      test_name:
        description: 'Test name to run visual regression for'
        required: false
        default: 'all'
      threshold:
        description: 'Pixel difference threshold (0-1)'
        required: false
        default: '0.1'

jobs:
  visual-regression:
    runs-on: ubuntu-latest
    
    env:
      SCREENSHOT_DIR: screenshots
      CI_BUILD_NUMBER: ${{ github.run_id }}
      PIXEL_DIFF_THRESHOLD: ${{ github.event.inputs.threshold || '0.1' }}
    
    steps:
    - uses: actions/checkout@v3
      with:
        fetch-depth: 0  # Fetch all history for proper baseline comparison
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '20'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Download baseline screenshots
      uses: actions/download-artifact@v3
      with:
        name: baseline-screenshots
        path: screenshots/baseline
      continue-on-error: true
      
    - name: Run tests
      run: npm test
      
    - name: Verify screenshot structure
      run: npm run screenshots:verify
      
    - name: Compare with baseline
      id: compare
      run: |
        if [ "${{ github.event.inputs.test_name }}" = "all" ] || [ "${{ github.event.inputs.test_name }}" = "" ]; then
          echo "Running comparison for all tests"
          node scripts/compare_with_baseline.js --threshold=${{ env.PIXEL_DIFF_THRESHOLD }}
        else
          echo "Running comparison for test: ${{ github.event.inputs.test_name }}"
          node scripts/compare_with_baseline.js --test=${{ github.event.inputs.test_name }} --threshold=${{ env.PIXEL_DIFF_THRESHOLD }}
        fi
      continue-on-error: true
      
    - name: Generate comparison report
      run: node scripts/generate_comparison_report.js
      
    - name: Upload screenshots
      uses: actions/upload-artifact@v3
      with:
        name: screenshots
        path: screenshots/
        
    - name: Upload comparison report
      uses: actions/upload-artifact@v3
      with:
        name: visual-regression-report
        path: screenshots/comparison-report/
        
    - name: Upload baseline screenshots
      uses: actions/upload-artifact@v3
      with:
        name: baseline-screenshots
        path: screenshots/baseline/
        
    - name: Clean up screenshots
      run: npm run screenshots:cleanup
      
    - name: Check comparison results
      if: steps.compare.outcome == 'success'
      run: |
        echo "Visual regression tests passed!"
        exit 0
        
    - name: Fail if comparison failed
      if: steps.compare.outcome != 'success'
      run: |
        echo "Visual regression tests failed! Check the comparison report for details."
        exit 1
