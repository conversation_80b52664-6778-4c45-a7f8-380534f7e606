/**
 * Healing strategies for self-healing tests
 */

import { Page } from 'playwright';
import { HealingStrategy, HealingOptions, SelectorAttributes, ElementInfo } from './types';
import { parseCssSelector, parseXPathSelector, buildCssSelector } from './utils';
import * as stringSimilarity from 'string-similarity';

/**
 * Base healing strategy
 */
export abstract class BaseHealingStrategy implements HealingStrategy {
  /**
   * Strategy name
   */
  abstract name: string;
  
  /**
   * Heal selector
   * @param page Page
   * @param selector Original selector
   * @param options Healing options
   * @returns Healed selector or null if healing failed
   */
  abstract heal(page: Page, selector: string, options?: HealingOptions): Promise<string | null>;
  
  /**
   * Log healing attempt
   * @param message Message to log
   * @param options Healing options
   */
  protected log(message: string, options?: HealingOptions): void {
    if (options?.logging) {
      console.log(`[${this.name}] ${message}`);
    }
  }
  
  /**
   * Get element info
   * @param page Page
   * @param selector Selector
   * @returns Element info or null if element not found
   */
  protected async getElementInfo(page: Page, selector: string): Promise<ElementInfo | null> {
    try {
      return await page.evaluate((selector) => {
        const element = document.querySelector(selector);
        
        if (!element) {
          return null;
        }
        
        // Get attributes
        const attributes: Record<string, string> = {};
        
        for (const attr of element.attributes) {
          attributes[attr.name] = attr.value;
        }
        
        // Get position
        const rect = element.getBoundingClientRect();
        
        return {
          selector,
          attributes,
          position: {
            x: rect.left,
            y: rect.top,
            width: rect.width,
            height: rect.height
          },
          textContent: element.textContent || '',
          innerHTML: element.innerHTML,
          outerHTML: element.outerHTML
        };
      }, selector);
    } catch (error) {
      return null;
    }
  }
  
  /**
   * Get all elements
   * @param page Page
   * @returns All elements
   */
  protected async getAllElements(page: Page): Promise<ElementInfo[]> {
    return page.evaluate(() => {
      const elements = document.querySelectorAll('*');
      const result: ElementInfo[] = [];
      
      for (const element of elements) {
        // Skip script, style, and hidden elements
        if (element.tagName === 'SCRIPT' || element.tagName === 'STYLE' || element.tagName === 'HEAD' || element.tagName === 'META' || element.tagName === 'LINK' || element.tagName === 'TITLE') {
          continue;
        }
        
        // Get attributes
        const attributes: Record<string, string> = {};
        
        for (const attr of element.attributes) {
          attributes[attr.name] = attr.value;
        }
        
        // Get position
        const rect = element.getBoundingClientRect();
        
        // Skip elements with zero size
        if (rect.width === 0 || rect.height === 0) {
          continue;
        }
        
        // Create CSS selector
        const selectorParts: string[] = [];
        
        if (element.id) {
          selectorParts.push(`#${element.id}`);
        } else {
          selectorParts.push(element.tagName.toLowerCase());
          
          if (element.className) {
            const classes = element.className.split(' ').filter(c => c.trim() !== '');
            
            if (classes.length > 0) {
              selectorParts.push(`.${classes.join('.')}`);
            }
          }
        }
        
        result.push({
          selector: selectorParts.join(''),
          attributes,
          position: {
            x: rect.left,
            y: rect.top,
            width: rect.width,
            height: rect.height
          },
          textContent: element.textContent || '',
          innerHTML: element.innerHTML,
          outerHTML: element.outerHTML
        });
      }
      
      return result;
    });
  }
  
  /**
   * Check if selector is valid
   * @param page Page
   * @param selector Selector
   * @returns True if selector is valid, false otherwise
   */
  protected async isSelectorValid(page: Page, selector: string): Promise<boolean> {
    try {
      const count = await page.locator(selector).count();
      return count > 0;
    } catch (error) {
      return false;
    }
  }
}

/**
 * Attribute healing strategy
 */
export class AttributeHealingStrategy extends BaseHealingStrategy {
  /**
   * Strategy name
   */
  name = 'AttributeHealingStrategy';
  
  /**
   * Heal selector
   * @param page Page
   * @param selector Original selector
   * @param options Healing options
   * @returns Healed selector or null if healing failed
   */
  async heal(page: Page, selector: string, options?: HealingOptions): Promise<string | null> {
    this.log(`Healing selector: ${selector}`, options);
    
    // Check if selector is valid
    if (await this.isSelectorValid(page, selector)) {
      this.log(`Selector is already valid: ${selector}`, options);
      return selector;
    }
    
    // Parse selector
    let attributes: SelectorAttributes;
    
    if (selector.startsWith('//')) {
      attributes = parseXPathSelector(selector);
    } else {
      attributes = parseCssSelector(selector);
    }
    
    if (Object.keys(attributes).length === 0) {
      this.log(`Failed to parse selector: ${selector}`, options);
      return null;
    }
    
    this.log(`Parsed attributes: ${JSON.stringify(attributes)}`, options);
    
    // Try different combinations of attributes
    const attributeEntries = Object.entries(attributes);
    
    for (let i = attributeEntries.length; i > 0; i--) {
      const combinations = this.getCombinations(attributeEntries, i);
      
      for (const combination of combinations) {
        const combinationAttributes: SelectorAttributes = {};
        
        for (const [key, value] of combination) {
          combinationAttributes[key] = value;
        }
        
        const newSelector = buildCssSelector(combinationAttributes);
        
        if (newSelector && await this.isSelectorValid(page, newSelector)) {
          this.log(`Found valid selector: ${newSelector}`, options);
          return newSelector;
        }
      }
    }
    
    this.log(`Failed to heal selector: ${selector}`, options);
    return null;
  }
  
  /**
   * Get combinations
   * @param array Array to get combinations from
   * @param size Combination size
   * @returns Combinations
   */
  private getCombinations<T>(array: T[], size: number): T[][] {
    const result: T[][] = [];
    
    // Get all combinations of size `size` from `array`
    const combine = (start: number, current: T[]) => {
      if (current.length === size) {
        result.push([...current]);
        return;
      }
      
      for (let i = start; i < array.length; i++) {
        current.push(array[i]);
        combine(i + 1, current);
        current.pop();
      }
    };
    
    combine(0, []);
    
    return result;
  }
}

/**
 * Text content healing strategy
 */
export class TextContentHealingStrategy extends BaseHealingStrategy {
  /**
   * Strategy name
   */
  name = 'TextContentHealingStrategy';
  
  /**
   * Heal selector
   * @param page Page
   * @param selector Original selector
   * @param options Healing options
   * @returns Healed selector or null if healing failed
   */
  async heal(page: Page, selector: string, options?: HealingOptions): Promise<string | null> {
    this.log(`Healing selector: ${selector}`, options);
    
    // Check if selector is valid
    if (await this.isSelectorValid(page, selector)) {
      this.log(`Selector is already valid: ${selector}`, options);
      return selector;
    }
    
    // Get element info using a similar selector
    const similarSelector = this.getSimilarSelector(selector);
    
    if (!similarSelector) {
      this.log(`Failed to get similar selector: ${selector}`, options);
      return null;
    }
    
    const elementInfo = await this.getElementInfo(page, similarSelector);
    
    if (!elementInfo) {
      this.log(`Failed to get element info: ${similarSelector}`, options);
      return null;
    }
    
    // Get text content
    const textContent = elementInfo.textContent;
    
    if (!textContent || textContent.trim() === '') {
      this.log(`Element has no text content: ${similarSelector}`, options);
      return null;
    }
    
    // Try to find element with the same text content
    const newSelector = `:text("${textContent.trim()}")`;
    
    if (await this.isSelectorValid(page, newSelector)) {
      this.log(`Found valid selector: ${newSelector}`, options);
      return newSelector;
    }
    
    this.log(`Failed to heal selector: ${selector}`, options);
    return null;
  }
  
  /**
   * Get similar selector
   * @param selector Original selector
   * @returns Similar selector
   */
  private getSimilarSelector(selector: string): string | null {
    // For CSS selectors
    if (!selector.startsWith('//')) {
      // Remove attributes that might have changed
      return selector.replace(/\[.*?\]/g, '');
    }
    
    // For XPath selectors
    return null;
  }
}

/**
 * Position healing strategy
 */
export class PositionHealingStrategy extends BaseHealingStrategy {
  /**
   * Strategy name
   */
  name = 'PositionHealingStrategy';
  
  /**
   * Heal selector
   * @param page Page
   * @param selector Original selector
   * @param options Healing options
   * @returns Healed selector or null if healing failed
   */
  async heal(page: Page, selector: string, options?: HealingOptions): Promise<string | null> {
    this.log(`Healing selector: ${selector}`, options);
    
    // Check if selector is valid
    if (await this.isSelectorValid(page, selector)) {
      this.log(`Selector is already valid: ${selector}`, options);
      return selector;
    }
    
    // Get element info using a similar selector
    const similarSelector = this.getSimilarSelector(selector);
    
    if (!similarSelector) {
      this.log(`Failed to get similar selector: ${selector}`, options);
      return null;
    }
    
    const elementInfo = await this.getElementInfo(page, similarSelector);
    
    if (!elementInfo || !elementInfo.position) {
      this.log(`Failed to get element info: ${similarSelector}`, options);
      return null;
    }
    
    // Get position
    const position = elementInfo.position;
    
    // Get all elements
    const allElements = await this.getAllElements(page);
    
    // Find elements at similar position
    const similarElements = allElements.filter(element => {
      if (!element.position) {
        return false;
      }
      
      const elementPosition = element.position;
      
      // Check if element is at similar position
      return (
        Math.abs(elementPosition.x - position.x) < 10 &&
        Math.abs(elementPosition.y - position.y) < 10 &&
        Math.abs(elementPosition.width - position.width) < 10 &&
        Math.abs(elementPosition.height - position.height) < 10
      );
    });
    
    if (similarElements.length === 0) {
      this.log(`No elements found at similar position: ${JSON.stringify(position)}`, options);
      return null;
    }
    
    // Try each similar element
    for (const element of similarElements) {
      if (await this.isSelectorValid(page, element.selector)) {
        this.log(`Found valid selector: ${element.selector}`, options);
        return element.selector;
      }
    }
    
    this.log(`Failed to heal selector: ${selector}`, options);
    return null;
  }
  
  /**
   * Get similar selector
   * @param selector Original selector
   * @returns Similar selector
   */
  private getSimilarSelector(selector: string): string | null {
    // For CSS selectors
    if (!selector.startsWith('//')) {
      // Remove attributes that might have changed
      return selector.replace(/\[.*?\]/g, '');
    }
    
    // For XPath selectors
    return null;
  }
}

/**
 * Fuzzy match healing strategy
 */
export class FuzzyMatchHealingStrategy extends BaseHealingStrategy {
  /**
   * Strategy name
   */
  name = 'FuzzyMatchHealingStrategy';
  
  /**
   * Heal selector
   * @param page Page
   * @param selector Original selector
   * @param options Healing options
   * @returns Healed selector or null if healing failed
   */
  async heal(page: Page, selector: string, options?: HealingOptions): Promise<string | null> {
    this.log(`Healing selector: ${selector}`, options);
    
    // Check if selector is valid
    if (await this.isSelectorValid(page, selector)) {
      this.log(`Selector is already valid: ${selector}`, options);
      return selector;
    }
    
    // Get all elements
    const allElements = await this.getAllElements(page);
    
    // Calculate similarity scores
    const scores = allElements.map(element => {
      const score = this.calculateSimilarityScore(selector, element);
      
      return {
        element,
        score
      };
    });
    
    // Sort by score (descending)
    scores.sort((a, b) => b.score - a.score);
    
    // Get threshold
    const threshold = options?.similarityThreshold || 0.7;
    
    // Get max candidates
    const maxCandidates = options?.maxCandidates || 5;
    
    // Get candidates
    const candidates = scores
      .filter(item => item.score > threshold)
      .slice(0, maxCandidates);
    
    if (candidates.length === 0) {
      this.log(`No candidates found with similarity score above ${threshold}`, options);
      return null;
    }
    
    // Try each candidate
    for (const candidate of candidates) {
      if (await this.isSelectorValid(page, candidate.element.selector)) {
        this.log(`Found valid selector: ${candidate.element.selector} (score: ${candidate.score})`, options);
        return candidate.element.selector;
      }
    }
    
    this.log(`Failed to heal selector: ${selector}`, options);
    return null;
  }
  
  /**
   * Calculate similarity score
   * @param selector Original selector
   * @param element Element info
   * @returns Similarity score
   */
  private calculateSimilarityScore(selector: string, element: ElementInfo): number {
    // For CSS selectors
    if (!selector.startsWith('//')) {
      // Calculate similarity between selectors
      const selectorSimilarity = stringSimilarity.compareTwoStrings(selector, element.selector);
      
      // Calculate similarity between attributes
      const attributes = parseCssSelector(selector);
      const elementAttributes = element.attributes;
      
      let attributeSimilarity = 0;
      let attributeCount = 0;
      
      for (const [key, value] of Object.entries(attributes)) {
        if (elementAttributes[key]) {
          attributeSimilarity += stringSimilarity.compareTwoStrings(value, elementAttributes[key]);
          attributeCount++;
        }
      }
      
      // Calculate final score
      const finalScore = selectorSimilarity * 0.7 + (attributeCount > 0 ? attributeSimilarity / attributeCount * 0.3 : 0);
      
      return finalScore;
    }
    
    // For XPath selectors
    return 0;
  }
}

/**
 * Create healing strategies
 * @returns Healing strategies
 */
export function createHealingStrategies(): HealingStrategy[] {
  return [
    new AttributeHealingStrategy(),
    new TextContentHealingStrategy(),
    new PositionHealingStrategy(),
    new FuzzyMatchHealingStrategy()
  ];
}

/**
 * Default healing strategies
 */
export const defaultHealingStrategies = createHealingStrategies();
