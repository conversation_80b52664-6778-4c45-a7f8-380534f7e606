#!/usr/bin/env node

/**
 * Init Script
 * 
 * This script initializes a new project with the QA Wolf testing framework.
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');
const readline = require('readline');

// Create readline interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

/**
 * Ask a question
 * @param {string} question - Question to ask
 * @param {string} [defaultValue] - Default value
 * @returns {Promise<string>} - User's answer
 */
function ask(question, defaultValue) {
  const defaultText = defaultValue ? ` (${defaultValue})` : '';
  return new Promise((resolve) => {
    rl.question(`${question}${defaultText}: `, (answer) => {
      resolve(answer || defaultValue);
    });
  });
}

/**
 * Ask a yes/no question
 * @param {string} question - Question to ask
 * @param {boolean} [defaultValue=true] - Default value
 * @returns {Promise<boolean>} - User's answer
 */
async function askYesNo(question, defaultValue = true) {
  const defaultText = defaultValue ? 'Y/n' : 'y/N';
  const answer = await ask(`${question} [${defaultText}]`);
  
  if (!answer) {
    return defaultValue;
  }
  
  return answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes';
}

/**
 * Create a directory if it doesn't exist
 * @param {string} dir - Directory path
 * @returns {void}
 */
function createDirectory(dir) {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
}

/**
 * Create a project
 * @param {string} projectName - Project name
 * @returns {Promise<void>}
 */
async function createProject(projectName) {
  // Create project directory
  const projectDir = path.join(process.cwd(), projectName);
  createDirectory(projectDir);
  
  // Change to project directory
  process.chdir(projectDir);
  
  // Initialize npm project
  console.log('Initializing npm project...');
  execSync('npm init -y', { stdio: 'inherit' });
  
  // Update package.json
  const packageJsonPath = path.join(projectDir, 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  packageJson.name = projectName;
  packageJson.description = `QA Wolf tests for ${projectName}`;
  packageJson.scripts = {
    test: 'playwright test',
    'test:ui': 'playwright test --ui',
    'test:debug': 'playwright test --debug',
    'test:report': 'playwright show-report'
  };
  
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2), 'utf8');
  
  // Create directory structure
  console.log('Creating directory structure...');
  createDirectory(path.join(projectDir, 'tests'));
  createDirectory(path.join(projectDir, 'tests/helpers'));
  createDirectory(path.join(projectDir, 'tests/fixtures'));
  createDirectory(path.join(projectDir, 'screenshots'));
  createDirectory(path.join(projectDir, 'reports'));
  
  // Create configuration files
  console.log('Creating configuration files...');
  
  // Create playwright.config.js
  const playwrightConfigPath = path.join(projectDir, 'playwright.config.js');
  const playwrightConfigContent = `
const { defineConfig } = require('@playwright/test');

module.exports = defineConfig({
  testDir: './tests',
  timeout: 30000,
  expect: {
    timeout: 5000
  },
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: [
    ['html'],
    ['json', { outputFile: 'reports/test-results.json' }]
  ],
  use: {
    actionTimeout: 0,
    trace: 'on-first-retry',
    screenshot: 'only-on-failure'
  },
  projects: [
    {
      name: 'chromium',
      use: {
        browserName: 'chromium'
      }
    }
  ]
});
`;
  
  fs.writeFileSync(playwrightConfigPath, playwrightConfigContent, 'utf8');
  
  // Create .env file
  const envPath = path.join(projectDir, '.env');
  const envContent = `
QAWOLF_API_KEY=
QAWOLF_EMAIL=
QAWOLF_PASSWORD=
`;
  
  fs.writeFileSync(envPath, envContent, 'utf8');
  
  // Create .gitignore file
  const gitignorePath = path.join(projectDir, '.gitignore');
  const gitignoreContent = `
node_modules/
.env
/test-results/
/playwright-report/
/playwright/.cache/
/screenshots/
/reports/
`;
  
  fs.writeFileSync(gitignorePath, gitignoreContent, 'utf8');
  
  // Create example test
  console.log('Creating example test...');
  const exampleTestPath = path.join(projectDir, 'tests/example.spec.js');
  const exampleTestContent = `
const { test, expect } = require('@playwright/test');
const { createMcpController, createSelfHealingController } = require('@qawolf/test-framework');

test('example test', async ({ page }) => {
  // Create an MCP controller
  const mcpController = createMcpController();
  
  // Create a self-healing controller
  const selfHealingController = createSelfHealingController();
  
  try {
    // Start the test run
    await selfHealingController.startTest({
      testId: 'example-test',
      testName: 'Example Test'
    });
    
    // Create a self-healing page
    const selfHealingPage = selfHealingController.createPage(page);
    
    // Navigate to a website
    await selfHealingPage.goto('https://example.com');
    
    // Check the title
    const title = await selfHealingPage.title();
    expect(title).toBe('Example Domain');
    
    // End the test run
    await selfHealingController.endTest({
      success: true
    });
  } catch (error) {
    // End the test run with failure
    await selfHealingController.endTest({
      success: false,
      error
    });
    
    throw error;
  } finally {
    // Clean up resources
    await mcpController.cleanup();
    await selfHealingController.cleanup();
  }
});
`;
  
  fs.writeFileSync(exampleTestPath, exampleTestContent, 'utf8');
  
  // Create helper file
  console.log('Creating helper file...');
  const helperPath = path.join(projectDir, 'tests/helpers/auth.js');
  const helperContent = `
const { config } = require('@qawolf/shared-utils');

/**
 * Login to the application
 * @param {Object} page - Playwright page
 * @param {Object} [options] - Login options
 * @param {string} [options.email] - Email
 * @param {string} [options.password] - Password
 * @param {string} [options.baseUrl] - Base URL
 * @returns {Promise<void>}
 */
async function login(page, options = {}) {
  const email = options.email || config.getEnv('QAWOLF_EMAIL', '<EMAIL>');
  const password = options.password || config.getEnv('QAWOLF_PASSWORD', 'vhc!tGK289IS&');
  const baseUrl = options.baseUrl || config.getEnv('QAWOLF_BASE_URL', 'https://app.lidostaging.com');
  
  // Navigate to the app
  await page.goto(baseUrl);
  
  // Fill in login form
  await page.fill('[data-test-id="SignInEmail"]', email);
  await page.fill('[data-test-id="SignInPassword"]', password);
  
  // Click the login button
  await page.click(':text("Log in with email")');
  
  // Wait for login to complete
  await page.waitForSelector('div[class*="FilesTable"]', { timeout: 15000 });
}

module.exports = {
  login
};
`;
  
  fs.writeFileSync(helperPath, helperContent, 'utf8');
  
  // Install dependencies
  console.log('Installing dependencies...');
  execSync('npm install --save-dev @playwright/test eslint prettier', { stdio: 'inherit' });
  execSync('npm install @qawolf/shared-utils @qawolf/mcp-optimizer @qawolf/test-framework', { stdio: 'inherit' });
  
  // Install Playwright browsers
  console.log('Installing Playwright browsers...');
  execSync('npx playwright install --with-deps', { stdio: 'inherit' });
  
  console.log(`Project ${projectName} created successfully!`);
  console.log(`To get started, run the following commands:`);
  console.log(`  cd ${projectName}`);
  console.log(`  npm test`);
}

/**
 * Run the initialization
 * @returns {Promise<void>}
 */
async function runInit() {
  try {
    console.log('Initializing a new QA Wolf project...');
    
    // Ask for project name
    const projectName = await ask('Enter project name', 'qawolf-tests');
    
    // Create project
    await createProject(projectName);
    
    // Ask if the user wants to configure the project
    const configureProject = await askYesNo('Do you want to configure the project now?');
    
    if (configureProject) {
      // Run the configure script
      execSync('node ../configure.js', { stdio: 'inherit' });
    } else {
      console.log('You can configure the project later by running:');
      console.log('  npx @qawolf/test-framework configure');
    }
    
    rl.close();
  } catch (error) {
    console.error('Initialization failed:', error.message);
    process.exit(1);
  }
}

// Run the script
runInit();