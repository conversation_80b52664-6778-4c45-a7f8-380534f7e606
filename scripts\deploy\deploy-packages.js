#!/usr/bin/env node

/**
 * Deploy Packages Script
 * 
 * This script deploys packages to npm.
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Configuration
const config = {
  packages: [
    'shared-utils',
    'mcp-optimizer',
    'test-framework'
  ],
  scope: '@qawolf',
  registry: 'https://registry.npmjs.org/'
};

/**
 * Deploy a package to npm
 * @param {string} packageName - Package name
 * @returns {void}
 */
function deployPackage(packageName) {
  const packagePath = path.join(process.cwd(), packageName);
  const packageJsonPath = path.join(packagePath, 'package.json');
  
  // Check if package exists
  if (!fs.existsSync(packageJsonPath)) {
    console.error(`Package ${packageName} does not exist at ${packagePath}`);
    process.exit(1);
  }
  
  // Read package.json
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  // Check if package is private
  if (packageJson.private) {
    console.log(`Skipping private package ${packageName}`);
    return;
  }
  
  // Build package
  console.log(`Building package ${packageName}...`);
  execSync('npm run build', { cwd: packagePath, stdio: 'inherit' });
  
  // Publish package
  console.log(`Publishing package ${packageName}...`);
  execSync(`npm publish --access public`, { cwd: packagePath, stdio: 'inherit' });
  
  console.log(`Successfully published ${packageName}@${packageJson.version}`);
}

/**
 * Deploy all packages
 * @returns {void}
 */
function deployAllPackages() {
  // Check npm authentication
  try {
    execSync('npm whoami', { stdio: 'inherit' });
  } catch (error) {
    console.error('You are not logged in to npm. Please run `npm login` first.');
    process.exit(1);
  }
  
  // Deploy packages in order
  for (const packageName of config.packages) {
    deployPackage(packageName);
  }
  
  console.log('All packages deployed successfully!');
}

// Run the script
deployAllPackages();