/**
 * MCP transformer for MCP optimizer
 */

import { v4 as uuidv4 } from 'uuid';
import { MCPOperation, MCPOperationPattern, MCPOptimizationSuggestion, MCPTransformerOptions, MCPToolType, MCPOperationType } from './types';
import { EventBus, EventType } from '@qawolf/core';

/**
 * MCP transformer
 */
export class MCPTransformer {
  /**
   * Whether to generate suggestions
   */
  private generateSuggestions: boolean;
  
  /**
   * Minimum cost savings
   */
  private minCostSavings: number;
  
  /**
   * Minimum token savings
   */
  private minTokenSavings: number;
  
  /**
   * Whether to include low priority suggestions
   */
  private includeLowPrioritySuggestions: boolean;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options MCP transformer options
   */
  constructor(options: MCPTransformerOptions = {}) {
    this.generateSuggestions = options.generateSuggestions !== false;
    this.minCostSavings = options.minCostSavings || 0.01;
    this.minTokenSavings = options.minTokenSavings || 100;
    this.includeLowPrioritySuggestions = options.includeLowPrioritySuggestions || false;
    this.eventBus = EventBus.getInstance();
  }
  
  /**
   * Generate suggestions
   * @param operations Operations
   * @param patterns Patterns
   * @returns Optimization suggestions
   */
  generateSuggestions(operations: MCPOperation[], patterns: MCPOperationPattern[]): MCPOptimizationSuggestion[] {
    if (!this.generateSuggestions) {
      return [];
    }
    
    const suggestions: MCPOptimizationSuggestion[] = [];
    
    // Generate suggestions for replacing operations
    suggestions.push(...this.generateReplaceOperationsSuggestions(operations));
    
    // Generate suggestions for removing operations
    suggestions.push(...this.generateRemoveOperationsSuggestions(operations));
    
    // Generate suggestions for reordering operations
    suggestions.push(...this.generateReorderOperationsSuggestions(operations));
    
    // Generate suggestions for combining operations
    suggestions.push(...this.generateCombineOperationsSuggestions(operations));
    
    // Generate suggestions for splitting operations
    suggestions.push(...this.generateSplitOperationsSuggestions(operations));
    
    // Generate suggestions for patterns
    suggestions.push(...this.generatePatternSuggestions(operations, patterns));
    
    // Filter suggestions by cost savings and token savings
    const filteredSuggestions = suggestions.filter(suggestion => {
      if (suggestion.priority === 'low' && !this.includeLowPrioritySuggestions) {
        return false;
      }
      
      return suggestion.costSavings >= this.minCostSavings || suggestion.tokenSavings >= this.minTokenSavings;
    });
    
    // Sort suggestions by cost savings (descending)
    filteredSuggestions.sort((a, b) => b.costSavings - a.costSavings);
    
    // Emit event
    this.eventBus.emit(EventType.MCP_SUGGESTIONS_GENERATED, {
      suggestions: filteredSuggestions
    });
    
    return filteredSuggestions;
  }
  
  /**
   * Generate replace operations suggestions
   * @param operations Operations
   * @returns Optimization suggestions
   */
  private generateReplaceOperationsSuggestions(operations: MCPOperation[]): MCPOptimizationSuggestion[] {
    const suggestions: MCPOptimizationSuggestion[] = [];
    
    // Replace browser-tools operations with playwright operations
    for (let i = 0; i < operations.length; i++) {
      const operation = operations[i];
      
      if (operation.toolType === MCPToolType.BROWSER_TOOLS && operation.type === MCPOperationType.BROWSER_OPERATION) {
        // Create optimized operation
        const optimizedOperation: MCPOperation = {
          id: uuidv4(),
          name: operation.name,
          type: operation.type,
          toolType: MCPToolType.PLAYWRIGHT,
          toolName: 'playwright',
          parameters: operation.parameters,
          duration: operation.duration,
          timestamp: operation.timestamp,
          cost: (operation.cost || 0) * 0.8, // 20% cost reduction
          tokenCount: (operation.tokenCount || 0) * 0.8 // 20% token reduction
        };
        
        // Calculate savings
        const costSavings = (operation.cost || 0) - (optimizedOperation.cost || 0);
        const tokenSavings = (operation.tokenCount || 0) - (optimizedOperation.tokenCount || 0);
        
        // Create suggestion
        suggestions.push({
          id: uuidv4(),
          name: 'Replace Browser Tools with Playwright',
          description: `Replace Browser Tools operation "${operation.name}" with Playwright operation`,
          type: 'replace',
          originalOperations: [operation],
          optimizedOperations: [optimizedOperation],
          costSavings,
          tokenSavings,
          priority: 'medium',
          codeExample: `// Before\nbrowser_click_Playwright({ element: "Button", ref: "button-1" });\n\n// After\npage.locator("button-1").click();`
        });
      }
    }
    
    return suggestions;
  }
  
  /**
   * Generate remove operations suggestions
   * @param operations Operations
   * @returns Optimization suggestions
   */
  private generateRemoveOperationsSuggestions(operations: MCPOperation[]): MCPOptimizationSuggestion[] {
    const suggestions: MCPOptimizationSuggestion[] = [];
    
    // Find redundant operations
    for (let i = 0; i < operations.length - 1; i++) {
      const operation = operations[i];
      const nextOperation = operations[i + 1];
      
      // Check if operations are the same
      if (
        operation.type === nextOperation.type &&
        operation.toolType === nextOperation.toolType &&
        operation.toolName === nextOperation.toolName &&
        JSON.stringify(operation.parameters) === JSON.stringify(nextOperation.parameters)
      ) {
        // Calculate savings
        const costSavings = nextOperation.cost || 0;
        const tokenSavings = nextOperation.tokenCount || 0;
        
        // Create suggestion
        suggestions.push({
          id: uuidv4(),
          name: 'Remove Redundant Operation',
          description: `Remove redundant operation "${nextOperation.name}"`,
          type: 'remove',
          originalOperations: [operation, nextOperation],
          optimizedOperations: [operation],
          costSavings,
          tokenSavings,
          priority: 'high'
        });
      }
    }
    
    return suggestions;
  }
  
  /**
   * Generate reorder operations suggestions
   * @param operations Operations
   * @returns Optimization suggestions
   */
  private generateReorderOperationsSuggestions(operations: MCPOperation[]): MCPOptimizationSuggestion[] {
    const suggestions: MCPOptimizationSuggestion[] = [];
    
    // Group operations by type
    for (let i = 0; i < operations.length - 2; i++) {
      const operation1 = operations[i];
      const operation2 = operations[i + 1];
      const operation3 = operations[i + 2];
      
      // Check if operations 1 and 3 are the same type but operation 2 is different
      if (
        operation1.type === operation3.type &&
        operation1.toolType === operation3.toolType &&
        operation2.type !== operation1.type
      ) {
        // Create optimized operations
        const optimizedOperations = [operation1, operation3, operation2];
        
        // Calculate savings (minimal for reordering)
        const costSavings = 0.01;
        const tokenSavings = 10;
        
        // Create suggestion
        suggestions.push({
          id: uuidv4(),
          name: 'Reorder Operations',
          description: `Reorder operations to group similar operations together`,
          type: 'reorder',
          originalOperations: [operation1, operation2, operation3],
          optimizedOperations,
          costSavings,
          tokenSavings,
          priority: 'low'
        });
      }
    }
    
    return suggestions;
  }
  
  /**
   * Generate combine operations suggestions
   * @param operations Operations
   * @returns Optimization suggestions
   */
  private generateCombineOperationsSuggestions(operations: MCPOperation[]): MCPOptimizationSuggestion[] {
    const suggestions: MCPOptimizationSuggestion[] = [];
    
    // Combine file operations
    for (let i = 0; i < operations.length - 1; i++) {
      const operation1 = operations[i];
      const operation2 = operations[i + 1];
      
      // Check if both operations are file operations
      if (
        operation1.type === MCPOperationType.FILE_OPERATION &&
        operation2.type === MCPOperationType.FILE_OPERATION &&
        operation1.toolType === MCPToolType.DESKTOP_COMMANDER &&
        operation2.toolType === MCPToolType.DESKTOP_COMMANDER
      ) {
        // Create optimized operation
        const optimizedOperation: MCPOperation = {
          id: uuidv4(),
          name: `Combined ${operation1.name} and ${operation2.name}`,
          type: MCPOperationType.FILE_OPERATION,
          toolType: MCPToolType.DESKTOP_COMMANDER,
          toolName: 'desktop-commander',
          parameters: {
            ...operation1.parameters,
            ...operation2.parameters
          },
          duration: operation1.duration + operation2.duration,
          timestamp: operation1.timestamp,
          cost: (operation1.cost || 0) + (operation2.cost || 0) * 0.5, // 50% cost reduction for second operation
          tokenCount: (operation1.tokenCount || 0) + (operation2.tokenCount || 0) * 0.5 // 50% token reduction for second operation
        };
        
        // Calculate savings
        const costSavings = (operation1.cost || 0) + (operation2.cost || 0) - (optimizedOperation.cost || 0);
        const tokenSavings = (operation1.tokenCount || 0) + (operation2.tokenCount || 0) - (optimizedOperation.tokenCount || 0);
        
        // Create suggestion
        suggestions.push({
          id: uuidv4(),
          name: 'Combine File Operations',
          description: `Combine file operations "${operation1.name}" and "${operation2.name}"`,
          type: 'combine',
          originalOperations: [operation1, operation2],
          optimizedOperations: [optimizedOperation],
          costSavings,
          tokenSavings,
          priority: 'medium',
          codeExample: `// Before\nread_file_Desktop_Commander({ path: "file1.txt" });\nread_file_Desktop_Commander({ path: "file2.txt" });\n\n// After\nread_multiple_files_Desktop_Commander({ paths: ["file1.txt", "file2.txt"] });`
        });
      }
    }
    
    return suggestions;
  }
  
  /**
   * Generate split operations suggestions
   * @param operations Operations
   * @returns Optimization suggestions
   */
  private generateSplitOperationsSuggestions(operations: MCPOperation[]): MCPOptimizationSuggestion[] {
    const suggestions: MCPOptimizationSuggestion[] = [];
    
    // Split large thinking operations
    for (const operation of operations) {
      if (
        operation.type === MCPOperationType.THINKING_OPERATION &&
        operation.toolType === MCPToolType.SEQUENTIAL_THINKING &&
        (operation.tokenCount || 0) > 1000
      ) {
        // Create optimized operations
        const optimizedOperation1: MCPOperation = {
          id: uuidv4(),
          name: `${operation.name} (Part 1)`,
          type: operation.type,
          toolType: operation.toolType,
          toolName: operation.toolName,
          parameters: operation.parameters,
          duration: operation.duration / 2,
          timestamp: operation.timestamp,
          cost: (operation.cost || 0) / 2,
          tokenCount: (operation.tokenCount || 0) / 2
        };
        
        const optimizedOperation2: MCPOperation = {
          id: uuidv4(),
          name: `${operation.name} (Part 2)`,
          type: operation.type,
          toolType: operation.toolType,
          toolName: operation.toolName,
          parameters: operation.parameters,
          duration: operation.duration / 2,
          timestamp: operation.timestamp + operation.duration / 2,
          cost: (operation.cost || 0) / 2,
          tokenCount: (operation.tokenCount || 0) / 2
        };
        
        // Calculate savings (minimal for splitting)
        const costSavings = 0.01;
        const tokenSavings = 10;
        
        // Create suggestion
        suggestions.push({
          id: uuidv4(),
          name: 'Split Thinking Operation',
          description: `Split large thinking operation "${operation.name}" into smaller parts`,
          type: 'split',
          originalOperations: [operation],
          optimizedOperations: [optimizedOperation1, optimizedOperation2],
          costSavings,
          tokenSavings,
          priority: 'low'
        });
      }
    }
    
    return suggestions;
  }
  
  /**
   * Generate pattern suggestions
   * @param operations Operations
   * @param patterns Patterns
   * @returns Optimization suggestions
   */
  private generatePatternSuggestions(operations: MCPOperation[], patterns: MCPOperationPattern[]): MCPOptimizationSuggestion[] {
    const suggestions: MCPOptimizationSuggestion[] = [];
    
    // Generate suggestions for patterns
    for (const pattern of patterns) {
      if (pattern.frequency >= 3) {
        // Create optimized operation
        const optimizedOperation: MCPOperation = {
          id: uuidv4(),
          name: `Optimized ${pattern.name}`,
          type: pattern.operations[0].type,
          toolType: pattern.operations[0].toolType,
          toolName: pattern.operations[0].toolName,
          parameters: {
            pattern: pattern.id,
            operations: pattern.operations.map(op => op.id)
          },
          duration: pattern.operations.reduce((sum, op) => sum + op.duration, 0) * 0.7, // 30% duration reduction
          timestamp: pattern.operations[0].timestamp,
          cost: pattern.cost * 0.7, // 30% cost reduction
          tokenCount: pattern.tokenCount * 0.7 // 30% token reduction
        };
        
        // Calculate savings
        const costSavings = pattern.cost - (optimizedOperation.cost || 0);
        const tokenSavings = pattern.tokenCount - (optimizedOperation.tokenCount || 0);
        
        // Create suggestion
        suggestions.push({
          id: uuidv4(),
          name: `Optimize Pattern ${pattern.name}`,
          description: `Optimize pattern "${pattern.name}" that occurs ${pattern.frequency} times`,
          type: 'combine',
          originalOperations: pattern.operations,
          optimizedOperations: [optimizedOperation],
          costSavings,
          tokenSavings,
          priority: 'high',
          codeExample: `// Create a helper function for this pattern\nfunction ${pattern.name.replace(/\s+/g, '')}() {\n  // Implementation\n}`
        });
      }
    }
    
    return suggestions;
  }
  
  /**
   * Get minimum cost savings
   * @returns Minimum cost savings
   */
  getMinCostSavings(): number {
    return this.minCostSavings;
  }
  
  /**
   * Set minimum cost savings
   * @param savings Minimum cost savings
   * @returns This instance for chaining
   */
  setMinCostSavings(savings: number): MCPTransformer {
    this.minCostSavings = savings;
    return this;
  }
  
  /**
   * Get minimum token savings
   * @returns Minimum token savings
   */
  getMinTokenSavings(): number {
    return this.minTokenSavings;
  }
  
  /**
   * Set minimum token savings
   * @param savings Minimum token savings
   * @returns This instance for chaining
   */
  setMinTokenSavings(savings: number): MCPTransformer {
    this.minTokenSavings = savings;
    return this;
  }
  
  /**
   * Enable low priority suggestions
   * @returns This instance for chaining
   */
  enableLowPrioritySuggestions(): MCPTransformer {
    this.includeLowPrioritySuggestions = true;
    return this;
  }
  
  /**
   * Disable low priority suggestions
   * @returns This instance for chaining
   */
  disableLowPrioritySuggestions(): MCPTransformer {
    this.includeLowPrioritySuggestions = false;
    return this;
  }
}

/**
 * Create MCP transformer
 * @param options MCP transformer options
 * @returns MCP transformer
 */
export function createMCPTransformer(options: MCPTransformerOptions = {}): MCPTransformer {
  return new MCPTransformer(options);
}
