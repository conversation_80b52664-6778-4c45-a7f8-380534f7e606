{"name": "@qawolf/shared-utils", "version": "0.1.0", "description": "Shared utilities for QA Wolf Metrics Framework", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "test": "jest", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "testing", "utils"], "author": "", "license": "MIT", "dependencies": {"axios": "^1.6.8", "date-fns": "^4.1.0"}, "devDependencies": {"@types/jest": "^29.5.12", "@types/node": "^20.11.30", "jest": "^29.7.0", "ts-jest": "^29.1.2", "typescript": "^5.4.3"}}