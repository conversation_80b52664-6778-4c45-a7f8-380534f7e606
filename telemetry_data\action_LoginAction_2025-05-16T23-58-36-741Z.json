{"name": "LoginAction", "params": {"email": "<EMAIL>", "password": "***REDACTED***", "url": "https://app.lidostaging.com"}, "startTime": "2025-05-16T23:57:02.275Z", "retries": [{"timestamp": "2025-05-16T23:57:34.004Z", "attempt": 1, "error": "page.waitForNavigation: Timeout 30000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation until \"networkidle\"\n  navigated to \"https://app.lidostaging.com/\"\n  \"domcontentloaded\" event fired\n  \"load\" event fired\n============================================================", "selector": null}, {"timestamp": "2025-05-16T23:58:05.385Z", "attempt": 2, "error": "page.fill: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-test-id=\"SignInEmail\"]')\u001b[22m\n", "selector": null}, {"timestamp": "2025-05-16T23:58:36.740Z", "attempt": 3, "error": "page.fill: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-test-id=\"SignInEmail\"]')\u001b[22m\n", "selector": null}], "selectors": [{"timestamp": "2025-05-16T23:57:03.989Z", "selector": "[data-test-id=\"SignInEmail\"]", "success": true, "duration": 365, "attempt": 1}, {"timestamp": "2025-05-16T23:57:03.997Z", "selector": "[data-test-id=\"SignInPassword\"]", "success": true, "duration": 8, "attempt": 1}, {"timestamp": "2025-05-16T23:57:34.003Z", "selector": ":text(\"Log in with email\")", "success": false, "duration": 30006, "attempt": 1}, {"timestamp": "2025-05-16T23:58:05.385Z", "selector": "[data-test-id=\"SignInEmail\"]", "success": false, "duration": 30003, "attempt": 1}, {"timestamp": "2025-05-16T23:58:36.739Z", "selector": "[data-test-id=\"SignInEmail\"]", "success": false, "duration": 30000, "attempt": 1}], "events": [{"timestamp": "2025-05-16T23:57:03.624Z", "type": "navigation", "data": {"url": "https://app.lidostaging.com"}}, {"timestamp": "2025-05-16T23:57:34.003Z", "type": "error", "data": {"selector": ":text(\"Log in with email\")", "error": "page.waitForNavigation: Timeout 30000ms exceeded.\n=========================== logs ===========================\nwaiting for navigation until \"networkidle\"\n  navigated to \"https://app.lidostaging.com/\"\n  \"domcontentloaded\" event fired\n  \"load\" event fired\n============================================================"}}, {"timestamp": "2025-05-16T23:57:35.381Z", "type": "navigation", "data": {"url": "https://app.lidostaging.com"}}, {"timestamp": "2025-05-16T23:58:05.384Z", "type": "error", "data": {"selector": "[data-test-id=\"SignInEmail\"]", "error": "page.fill: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-test-id=\"SignInEmail\"]')\u001b[22m\n"}}, {"timestamp": "2025-05-16T23:58:06.739Z", "type": "navigation", "data": {"url": "https://app.lidostaging.com"}}, {"timestamp": "2025-05-16T23:58:36.739Z", "type": "error", "data": {"selector": "[data-test-id=\"SignInEmail\"]", "error": "page.fill: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-test-id=\"SignInEmail\"]')\u001b[22m\n"}}], "endTime": "2025-05-16T23:58:36.741Z", "duration": 94466, "success": false, "attempts": 1, "error": "page.fill: Timeout 30000ms exceeded.\nCall log:\n\u001b[2m  - waiting for locator('[data-test-id=\"SignInEmail\"]')\u001b[22m\n"}