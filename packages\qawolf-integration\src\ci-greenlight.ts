/**
 * QA Wolf CI Greenlight
 * 
 * This module provides functionality to poll the QA Wolf API for CI greenlight status.
 */

import * as fs from 'fs';
import * as path from 'path';
import axios from 'axios';
import { EventBus, EventType } from '@qawolf/core';

/**
 * CI greenlight options
 */
export interface CIGreenlightOptions {
  /**
   * API key
   */
  apiKey?: string;
  
  /**
   * Team ID
   */
  teamId?: string;
  
  /**
   * API URL
   */
  apiUrl?: string;
  
  /**
   * Reports directory
   */
  reportsDir?: string;
  
  /**
   * Maximum number of polling attempts
   */
  maxPolls?: number;
  
  /**
   * Polling interval in milliseconds
   */
  pollInterval?: number;
  
  /**
   * Environment
   */
  environment?: string;
  
  /**
   * Branch
   */
  branch?: string;
}

/**
 * CI greenlight result
 */
export interface CIGreenlightResult {
  /**
   * Run stage
   */
  runStage: string;
  
  /**
   * Greenlight status
   */
  greenlight: boolean;
  
  /**
   * Additional data
   */
  [key: string]: any;
}

/**
 * CI greenlight poll result
 */
export interface CIGreenlightPollResult {
  /**
   * Timestamp
   */
  timestamp: string;
  
  /**
   * Attempt number
   */
  attempt: number;
  
  /**
   * Poll result
   */
  result: CIGreenlightResult;
}

/**
 * CI greenlight
 */
export class CIGreenlight {
  /**
   * API key
   */
  private apiKey: string;
  
  /**
   * Team ID
   */
  private teamId: string;
  
  /**
   * API URL
   */
  private apiUrl: string;
  
  /**
   * Reports directory
   */
  private reportsDir: string;
  
  /**
   * Maximum number of polling attempts
   */
  private maxPolls: number;
  
  /**
   * Polling interval in milliseconds
   */
  private pollInterval: number;
  
  /**
   * Environment
   */
  private environment: string;
  
  /**
   * Branch
   */
  private branch: string;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options CI greenlight options
   */
  constructor(options: CIGreenlightOptions = {}) {
    this.apiKey = options.apiKey || process.env.QA_WOLF_API_KEY || '';
    this.teamId = options.teamId || process.env.QA_WOLF_TEAM_ID || 'clux0gjs50sb3ak01fnh7wvja';
    this.apiUrl = options.apiUrl || 'https://app.qawolf.com/api/ci';
    this.reportsDir = options.reportsDir || './qawolf-reports';
    this.maxPolls = options.maxPolls || 30;
    this.pollInterval = options.pollInterval || 60000;
    this.environment = options.environment || '';
    this.branch = options.branch || '';
    
    this.eventBus = EventBus.getInstance();
    
    // Create reports directory if it doesn't exist
    if (!fs.existsSync(this.reportsDir)) {
      fs.mkdirSync(this.reportsDir, { recursive: true });
    }
    
    // Load deployment info if environment and branch are not provided
    if (!this.environment || !this.branch) {
      try {
        const deploymentInfo = JSON.parse(
          fs.readFileSync(path.join(this.reportsDir, 'deployment-info.json'), 'utf8')
        );
        
        this.environment = this.environment || deploymentInfo.environment;
        this.branch = this.branch || deploymentInfo.branch;
      } catch (error) {
        console.error('Error loading deployment info:', error.message);
      }
    }
  }
  
  /**
   * Poll for CI greenlight
   * @returns Promise<boolean> - Greenlight status
   */
  async pollForGreenlight(): Promise<boolean> {
    console.log('Polling for QA Wolf CI greenlight...');
    
    if (!this.apiKey) {
      throw new Error('QA Wolf API key is required');
    }
    
    if (!this.teamId) {
      throw new Error('QA Wolf team ID is required');
    }
    
    if (!this.environment) {
      throw new Error('Environment is required');
    }
    
    if (!this.branch) {
      throw new Error('Branch is required');
    }
    
    let pollCount = 0;
    let greenlightStatus: boolean | null = null;
    
    const pollResults: CIGreenlightPollResult[] = [];
    
    while (pollCount < this.maxPolls) {
      pollCount++;
      
      try {
        console.log(`Polling attempt ${pollCount}/${this.maxPolls}...`);
        
        const response = await axios.get(
          `${this.apiUrl}/greenlight?teamId=${this.teamId}&environment=${this.environment}&branch=${this.branch}`,
          {
            headers: {
              'Authorization': `Bearer ${this.apiKey}`
            }
          }
        );
        
        const result = response.data;
        
        console.log('Poll result:', result);
        
        pollResults.push({
          timestamp: new Date().toISOString(),
          attempt: pollCount,
          result
        });
        
        // Save poll results to file
        fs.writeFileSync(
          path.join(this.reportsDir, 'greenlight-polls.json'),
          JSON.stringify(pollResults, null, 2)
        );
        
        // Emit event
        this.eventBus.emit(EventType.QA_WOLF_CI_GREENLIGHT_POLL, {
          attempt: pollCount,
          result
        });
        
        // Check if we have a greenlight
        if (result.runStage === 'complete') {
          greenlightStatus = result.greenlight;
          
          console.log(`QA Wolf tests completed with greenlight status: ${greenlightStatus}`);
          
          // Save final result to file
          fs.writeFileSync(
            path.join(this.reportsDir, 'greenlight-result.json'),
            JSON.stringify(result, null, 2)
          );
          
          // Emit event
          this.eventBus.emit(EventType.QA_WOLF_CI_GREENLIGHT_COMPLETE, {
            greenlight: greenlightStatus,
            result
          });
          
          break;
        }
        
        // If tests are still running, wait and poll again
        console.log(`Tests are still running (stage: ${result.runStage}). Waiting ${this.pollInterval / 1000} seconds before next poll...`);
        
        await new Promise(resolve => setTimeout(resolve, this.pollInterval));
      } catch (error) {
        console.error('Error polling for greenlight:', error.message);
        
        if (error.response) {
          console.error('Response data:', error.response.data);
          console.error('Response status:', error.response.status);
        }
        
        // Save error to file
        fs.writeFileSync(
          path.join(this.reportsDir, 'greenlight-error.json'),
          JSON.stringify({
            error: error.message,
            response: error.response ? {
              data: error.response.data,
              status: error.response.status
            } : null,
            timestamp: new Date().toISOString()
          }, null, 2)
        );
        
        // Emit event
        this.eventBus.emit(EventType.QA_WOLF_CI_GREENLIGHT_ERROR, {
          error,
          attempt: pollCount
        });
        
        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, this.pollInterval));
      }
    }
    
    // Check if we reached the maximum number of polls without a result
    if (pollCount >= this.maxPolls && greenlightStatus === null) {
      console.error('Maximum number of polling attempts reached without a result.');
      
      // Emit event
      this.eventBus.emit(EventType.QA_WOLF_CI_GREENLIGHT_TIMEOUT, {
        maxPolls: this.maxPolls
      });
      
      throw new Error('Maximum number of polling attempts reached without a result');
    }
    
    return greenlightStatus === true;
  }
  
  /**
   * Get API key
   * @returns API key
   */
  getApiKey(): string {
    return this.apiKey;
  }
  
  /**
   * Get team ID
   * @returns Team ID
   */
  getTeamId(): string {
    return this.teamId;
  }
  
  /**
   * Get API URL
   * @returns API URL
   */
  getApiUrl(): string {
    return this.apiUrl;
  }
  
  /**
   * Get reports directory
   * @returns Reports directory
   */
  getReportsDir(): string {
    return this.reportsDir;
  }
  
  /**
   * Get maximum number of polling attempts
   * @returns Maximum number of polling attempts
   */
  getMaxPolls(): number {
    return this.maxPolls;
  }
  
  /**
   * Get polling interval
   * @returns Polling interval
   */
  getPollInterval(): number {
    return this.pollInterval;
  }
  
  /**
   * Get environment
   * @returns Environment
   */
  getEnvironment(): string {
    return this.environment;
  }
  
  /**
   * Get branch
   * @returns Branch
   */
  getBranch(): string {
    return this.branch;
  }
  
  /**
   * Set API key
   * @param apiKey API key
   * @returns This instance for chaining
   */
  setApiKey(apiKey: string): CIGreenlight {
    this.apiKey = apiKey;
    return this;
  }
  
  /**
   * Set team ID
   * @param teamId Team ID
   * @returns This instance for chaining
   */
  setTeamId(teamId: string): CIGreenlight {
    this.teamId = teamId;
    return this;
  }
  
  /**
   * Set API URL
   * @param apiUrl API URL
   * @returns This instance for chaining
   */
  setApiUrl(apiUrl: string): CIGreenlight {
    this.apiUrl = apiUrl;
    return this;
  }
  
  /**
   * Set reports directory
   * @param reportsDir Reports directory
   * @returns This instance for chaining
   */
  setReportsDir(reportsDir: string): CIGreenlight {
    this.reportsDir = reportsDir;
    return this;
  }
  
  /**
   * Set maximum number of polling attempts
   * @param maxPolls Maximum number of polling attempts
   * @returns This instance for chaining
   */
  setMaxPolls(maxPolls: number): CIGreenlight {
    this.maxPolls = maxPolls;
    return this;
  }
  
  /**
   * Set polling interval
   * @param pollInterval Polling interval
   * @returns This instance for chaining
   */
  setPollInterval(pollInterval: number): CIGreenlight {
    this.pollInterval = pollInterval;
    return this;
  }
  
  /**
   * Set environment
   * @param environment Environment
   * @returns This instance for chaining
   */
  setEnvironment(environment: string): CIGreenlight {
    this.environment = environment;
    return this;
  }
  
  /**
   * Set branch
   * @param branch Branch
   * @returns This instance for chaining
   */
  setBranch(branch: string): CIGreenlight {
    this.branch = branch;
    return this;
  }
}

/**
 * Create CI greenlight
 * @param options CI greenlight options
 * @returns CI greenlight
 */
export function createCIGreenlight(options: CIGreenlightOptions = {}): CIGreenlight {
  return new CIGreenlight(options);
}
