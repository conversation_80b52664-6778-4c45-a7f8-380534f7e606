# Test Framework API

The `@qawolf/test-framework` package provides a comprehensive testing framework with MCP integration and self-healing automation.

## Installation

```bash
npm install @qawolf/test-framework
```

## Usage

```javascript
const { test } = require('@playwright/test');
const { createMcpController, createSelfHealingController } = require('@qawolf/test-framework');

test('example test', async ({ page }) => {
  // Create an MCP controller
  const mcpController = createMcpController();
  
  // Create a self-healing controller
  const selfHealingController = createSelfHealingController();
  
  try {
    // Start the test run
    await selfHealingController.startTest({
      testId: 'example-test',
      testName: 'Example Test'
    });
    
    // Create a self-healing page
    const selfHealingPage = selfHealingController.createPage(page);
    
    // Navigate to a website
    await selfHealingPage.goto('https://example.com');
    
    // End the test run
    await selfHealingController.endTest({
      success: true
    });
  } catch (error) {
    // End the test run with failure
    await selfHealingController.endTest({
      success: false,
      error
    });
    
    throw error;
  } finally {
    // Clean up resources
    await mcpController.cleanup();
    await selfHealingController.cleanup();
  }
});
```

## API Reference

### MCP Integration

#### `createMcpController(options)`

Creates an MCP controller for optimizing selectors and integrating with MCP tools.

**Parameters:**

- `options` (Object, optional): Configuration options for the MCP controller.
  - `autoStartPlaywrightMcp` (boolean, default: true): Whether to automatically start the Playwright MCP server.
  - `playwrightMcpPort` (number, default: 8932): Port for the Playwright MCP server.
  - `generateFallbacks` (boolean, default: true): Whether to generate fallback selectors.
  - `prioritizeTestIds` (boolean, default: true): Whether to prioritize test IDs.

**Returns:**

- (Object): MCP controller with the following methods:
  - `optimizeSelectors(selectors)`: Optimizes selectors for better reliability.
  - `selectTool(task)`: Selects the appropriate MCP tool for a task.
  - `analyzeScreenshot(screenshotPath)`: Analyzes a screenshot to identify UI elements.
  - `cleanup()`: Cleans up resources.

**Example:**

```javascript
const { createMcpController } = require('@qawolf/test-framework');

const mcpController = createMcpController({
  autoStartPlaywrightMcp: true,
  generateFallbacks: true,
  prioritizeTestIds: true
});

// Optimize selectors
const selectors = await mcpController.optimizeSelectors([
  '[data-test-id="SignInEmail"]',
  '[data-test-id="SignInPassword"]',
  ':text("Log in with email")'
]);

// Clean up resources
await mcpController.cleanup();
```

### Self-Healing Automation

#### `createSelfHealingController(options)`

Creates a self-healing controller for healing selectors and recovering from failures.

**Parameters:**

- `options` (Object, optional): Configuration options for the self-healing controller.
  - `selectorHealing` (Object, optional): Configuration for selector healing.
    - `enabled` (boolean, default: true): Whether to enable selector healing.
    - `maxAttempts` (number, default: 3): Maximum number of attempts to heal a selector.
    - `strategies` (Array<string>, default: ['css-relaxation', 'attribute-based', 'text-based', 'xpath']): Strategies to use for healing selectors.
  - `recovery` (Object, optional): Configuration for recovery.
    - `enabled` (boolean, default: true): Whether to enable recovery.
    - `maxAttempts` (number, default: 3): Maximum number of attempts to recover from a failure.
    - `strategies` (Array<string>, default: ['retry', 'wait', 'selector', 'refresh', 'screenshot']): Strategies to use for recovery.
  - `feedbackCollection` (Object, optional): Configuration for feedback collection.
    - `enabled` (boolean, default: true): Whether to enable feedback collection.
    - `collectScreenshots` (boolean, default: true): Whether to collect screenshots.
    - `persistFeedback` (boolean, default: true): Whether to persist feedback.
  - `learning` (Object, optional): Configuration for learning.
    - `enabled` (boolean, default: true): Whether to enable learning.
    - `autoOptimize` (boolean, default: true): Whether to automatically optimize based on learning.

**Returns:**

- (Object): Self-healing controller with the following methods:
  - `initialize()`: Initializes the self-healing controller.
  - `createPage(page)`: Creates a self-healing page wrapper.
  - `startTest(testInfo)`: Starts a test run.
  - `endTest(result)`: Ends a test run.
  - `trackPerformance(metrics)`: Tracks performance metrics.
  - `generateReport(options)`: Generates a report.
  - `analyzeFeedback()`: Analyzes feedback data.
  - `suggestImprovements(testPath)`: Suggests improvements for a test script.
  - `cleanup()`: Cleans up resources.

**Example:**

```javascript
const { createSelfHealingController } = require('@qawolf/test-framework');

const selfHealingController = createSelfHealingController({
  selectorHealing: {
    enabled: true,
    maxAttempts: 3,
    strategies: ['css-relaxation', 'attribute-based', 'text-based', 'xpath']
  },
  recovery: {
    enabled: true,
    maxAttempts: 3,
    strategies: ['retry', 'wait', 'selector', 'refresh', 'screenshot']
  }
});

// Start a test run
await selfHealingController.startTest({
  testId: 'example-test',
  testName: 'Example Test'
});

// Create a self-healing page
const selfHealingPage = selfHealingController.createPage(page);

// End the test run
await selfHealingController.endTest({
  success: true
});

// Clean up resources
await selfHealingController.cleanup();
```

### Shared Utilities

#### `config`

Utilities for working with configuration.

**Methods:**

- `getEnv(name, defaultValue)`: Gets an environment variable.
- `getEnvBool(name, defaultValue)`: Gets a boolean environment variable.
- `getEnvNumber(name, defaultValue)`: Gets a number environment variable.
- `setConfig(name, value)`: Sets a configuration value.
- `getConfig(name, defaultValue)`: Gets a configuration value.
- `isCI()`: Checks if running in a CI environment.

**Example:**

```javascript
const { config } = require('@qawolf/test-framework');

// Get an environment variable
const apiKey = config.getEnv('QAWOLF_API_KEY', '');

// Check if running in a CI environment
const isCI = config.isCI();
```

#### `screenshot`

Utilities for working with screenshots.

**Methods:**

- `takeScreenshot(page, options)`: Takes a screenshot.
- `ensureScreenshotDir(testName)`: Ensures the screenshot directory exists.
- `verifyScreenshotStructure()`: Verifies the screenshot directory structure.
- `cleanupScreenshots(options)`: Cleans up screenshots.

**Example:**

```javascript
const { screenshot } = require('@qawolf/test-framework');

// Take a screenshot
const screenshotPath = await screenshot.takeScreenshot(page, {
  testName: 'example-test',
  action: 'login',
  fullPage: true
});
```

#### `test`

Utilities for testing.

**Methods:**

- `calculateAAAComplianceScore(testSummary)`: Calculates the AAA compliance score.
- `printTestSummary(testSummary, testId, testName, startTime)`: Prints a test summary.
- `PerformanceTracker`: Class for tracking performance.

**Example:**

```javascript
const { test } = require('@qawolf/test-framework');

// Create a performance tracker
const performanceTracker = new test.PerformanceTracker();

// Start an operation
performanceTracker.startOperation('login');

// End the operation
performanceTracker.endOperation();

// Get metrics
const metrics = performanceTracker.getMetrics();

// Print test summary
test.printTestSummary(
  {
    success: true,
    performanceMetrics: metrics,
    aaaComplianceScore: test.calculateAAAComplianceScore({
      success: true,
      performanceMetrics: metrics,
      allPerformanceMetricsWithinThresholds: true
    })
  },
  'example-test',
  'Example Test',
  performanceTracker.startTime
);
```