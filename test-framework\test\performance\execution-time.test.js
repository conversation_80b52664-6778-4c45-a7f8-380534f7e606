/**
 * Execution Time Performance Tests
 * 
 * This file contains tests for measuring execution time performance.
 */

const { test, expect } = require('@playwright/test');
const { createMcpController, createSelfHealingController, test: testUtils } = require('../../src');
const { createTestApp, createMetricsCollector, createReportGenerator } = require('../fixtures');

test.describe('Execution Time Performance Tests', () => {
  let testApp;
  let metricsCollector;
  let reportGenerator;
  
  test.beforeEach(async () => {
    testApp = await createTestApp();
    metricsCollector = createMetricsCollector();
    reportGenerator = createReportGenerator();
  });
  
  test.afterEach(async () => {
    await testApp.cleanup();
  });
  
  test('should measure execution time with and without MCP optimization', async () => {
    // Create an MCP controller
    const mcpController = createMcpController({
      autoStartPlaywrightMcp: false // Disable auto-start for tests
    });
    
    try {
      // Run login test without MCP optimization
      const withoutMcpStartTime = Date.now();
      
      await testApp.navigate();
      await testApp.page.fill('[data-test-id="SignInEmail"]', testApp.credentials.email);
      await testApp.page.fill('[data-test-id="SignInPassword"]', testApp.credentials.password);
      await testApp.page.click(':text("Log in with email")');
      await testApp.page.waitForSelector('div[class*="FilesTable"]', { timeout: 15000 });
      
      const withoutMcpEndTime = Date.now();
      const withoutMcpDuration = withoutMcpEndTime - withoutMcpStartTime;
      
      // Record metrics
      metricsCollector.recordExecutionTime('login-without-mcp', withoutMcpDuration);
      metricsCollector.recordTestResult('login-without-mcp', true);
      
      // Navigate back to login page
      await testApp.page.goto(testApp.baseUrl);
      
      // Run login test with MCP optimization (mocked)
      const withMcpStartTime = Date.now();
      
      // Mock MCP optimization
      const selectors = {
        selectors: [
          {
            original: '[data-test-id="SignInEmail"]',
            optimized: '[data-test-id="SignInEmail"]',
            fallbacks: [],
            reliability: 0.95
          },
          {
            original: '[data-test-id="SignInPassword"]',
            optimized: '[data-test-id="SignInPassword"]',
            fallbacks: [],
            reliability: 0.95
          },
          {
            original: ':text("Log in with email")',
            optimized: ':text("Log in with email")',
            fallbacks: [],
            reliability: 0.95
          }
        ]
      };
      
      await testApp.page.fill(selectors.selectors[0].optimized, testApp.credentials.email);
      await testApp.page.fill(selectors.selectors[1].optimized, testApp.credentials.password);
      await testApp.page.click(selectors.selectors[2].optimized);
      await testApp.page.waitForSelector('div[class*="FilesTable"]', { timeout: 15000 });
      
      const withMcpEndTime = Date.now();
      const withMcpDuration = withMcpEndTime - withMcpStartTime;
      
      // Record metrics
      metricsCollector.recordExecutionTime('login-with-mcp', withMcpDuration);
      metricsCollector.recordTestResult('login-with-mcp', true);
      
      // Compare execution times
      console.log(`Execution time without MCP: ${withoutMcpDuration}ms`);
      console.log(`Execution time with MCP: ${withMcpDuration}ms`);
      console.log(`Difference: ${withoutMcpDuration - withMcpDuration}ms`);
      
      // Generate report
      await reportGenerator.generateReport(metricsCollector.getMetrics(), 'execution-time-tests');
    } finally {
      // Clean up MCP resources
      await mcpController.cleanup();
    }
  });
  
  test('should measure execution time with and without self-healing', async () => {
    // Create a self-healing controller
    const selfHealingController = createSelfHealingController({
      selectorHealing: {
        enabled: true,
        persistHistory: false
      },
      recovery: {
        enabled: true
      },
      feedbackCollection: {
        enabled: true,
        persistFeedback: false
      }
    });
    
    try {
      // Run login test without self-healing
      const withoutSelfHealingStartTime = Date.now();
      
      await testApp.navigate();
      await testApp.page.fill('[data-test-id="SignInEmail"]', testApp.credentials.email);
      await testApp.page.fill('[data-test-id="SignInPassword"]', testApp.credentials.password);
      await testApp.page.click(':text("Log in with email")');
      await testApp.page.waitForSelector('div[class*="FilesTable"]', { timeout: 15000 });
      
      const withoutSelfHealingEndTime = Date.now();
      const withoutSelfHealingDuration = withoutSelfHealingEndTime - withoutSelfHealingStartTime;
      
      // Record metrics
      metricsCollector.recordExecutionTime('login-without-self-healing', withoutSelfHealingDuration);
      metricsCollector.recordTestResult('login-without-self-healing', true);
      
      // Navigate back to login page
      await testApp.page.goto(testApp.baseUrl);
      
      // Run login test with self-healing
      const withSelfHealingStartTime = Date.now();
      
      // Start the test run
      await selfHealingController.startTest({
        testId: 'login-with-self-healing',
        testName: 'Login with self-healing'
      });
      
      // Create a self-healing page
      const selfHealingPage = selfHealingController.createPage(testApp.page);
      
      await selfHealingPage.fill('[data-test-id="SignInEmail"]', testApp.credentials.email);
      await selfHealingPage.fill('[data-test-id="SignInPassword"]', testApp.credentials.password);
      await selfHealingPage.click(':text("Log in with email")');
      await selfHealingPage.waitForSelector('div[class*="FilesTable"]', { timeout: 15000 });
      
      // End the test run
      await selfHealingController.endTest({
        success: true
      });
      
      const withSelfHealingEndTime = Date.now();
      const withSelfHealingDuration = withSelfHealingEndTime - withSelfHealingStartTime;
      
      // Record metrics
      metricsCollector.recordExecutionTime('login-with-self-healing', withSelfHealingDuration);
      metricsCollector.recordTestResult('login-with-self-healing', true);
      
      // Compare execution times
      console.log(`Execution time without self-healing: ${withoutSelfHealingDuration}ms`);
      console.log(`Execution time with self-healing: ${withSelfHealingDuration}ms`);
      console.log(`Difference: ${withoutSelfHealingDuration - withSelfHealingDuration}ms`);
      
      // Generate report
      await reportGenerator.generateReport(metricsCollector.getMetrics(), 'execution-time-tests');
    } finally {
      // Clean up resources
      await selfHealingController.cleanup();
    }
  });
});