# Architecture Overview

The QA Wolf testing framework is designed as a monorepo with multiple packages that work together to provide a comprehensive testing solution with MCP integration and self-healing automation.

## Monorepo Structure

The framework is organized as a monorepo with the following packages:

- **shared-utils**: Shared utilities for configuration, screenshots, and testing.
- **mcp-optimizer**: Utilities for optimizing selectors and integrating with MCP tools.
- **test-framework**: The main testing framework with MCP integration and self-healing automation.

## High-Level Architecture

The following diagram shows the high-level architecture of the framework:

```
+----------------------------------+
|          test-framework          |
+----------------------------------+
|                                  |
|  +-------------+  +-----------+  |
|  | MCP         |  | Self-     |  |
|  | Integration |  | Healing   |  |
|  +-------------+  +-----------+  |
|                                  |
+----------------------------------+
            |           |
            v           v
+------------------+  +------------------+
|  mcp-optimizer   |  |   shared-utils   |
+------------------+  +------------------+
|                  |  |                  |
|  +-----------+   |  |  +-----------+   |
|  | Selector  |   |  |  | Config    |   |
|  | Optimizer |   |  |  | Utilities |   |
|  +-----------+   |  |  +-----------+   |
|                  |  |                  |
|  +-----------+   |  |  +-----------+   |
|  | Tool      |   |  |  | Screenshot|   |
|  | Selection |   |  |  | Utilities |   |
|  +-----------+   |  |  +-----------+   |
|                  |  |                  |
|  +-----------+   |  |  +-----------+   |
|  | Screenshot|   |  |  | Test      |   |
|  | Analysis  |   |  |  | Utilities |   |
|  +-----------+   |  |  +-----------+   |
|                  |  |                  |
+------------------+  +------------------+
```

## Component Interactions

The following diagram shows the interactions between the main components of the framework:

```
+------------------+     +------------------+     +------------------+
|                  |     |                  |     |                  |
|     Playwright   |     |   MCP Tools      |     |   Test Scripts   |
|                  |     |                  |     |                  |
+------------------+     +------------------+     +------------------+
        ^                        ^                        |
        |                        |                        |
        |                        |                        v
+------------------+     +------------------+     +------------------+
|                  |     |                  |     |                  |
|  Self-Healing    |<--->|  MCP Integration |<--->|  Test Framework  |
|  Controller      |     |  Controller      |     |                  |
|                  |     |                  |     |                  |
+------------------+     +------------------+     +------------------+
        ^                        ^                        ^
        |                        |                        |
        v                        v                        v
+------------------+     +------------------+     +------------------+
|                  |     |                  |     |                  |
|  Selector        |     |  Tool            |     |  Shared          |
|  Healing         |     |  Selection       |     |  Utilities       |
|                  |     |                  |     |                  |
+------------------+     +------------------+     +------------------+
        ^                        ^                        ^
        |                        |                        |
        v                        v                        v
+------------------+     +------------------+     +------------------+
|                  |     |                  |     |                  |
|  Recovery        |     |  Screenshot      |     |  Configuration   |
|  Strategies      |     |  Analysis        |     |  Management      |
|                  |     |                  |     |                  |
+------------------+     +------------------+     +------------------+
```

## Key Components

### Test Framework

The test framework is the main entry point for users. It provides:

- Integration with Playwright for browser automation.
- MCP integration for optimizing selectors and using MCP tools.
- Self-healing automation for recovering from failures.
- Performance tracking and reporting.

### MCP Integration

The MCP integration component provides:

- Selector optimization using MCP tools.
- Tool selection for different tasks.
- Screenshot analysis for identifying UI elements.
- Integration with Playwright MCP and Browser Tools MCP.

### Self-Healing Automation

The self-healing automation component provides:

- Selector healing for broken selectors.
- Recovery from failures using different strategies.
- Feedback collection for learning from failures.
- Learning from feedback to improve reliability.

### Shared Utilities

The shared utilities component provides:

- Configuration management for environment variables and settings.
- Screenshot utilities for taking and managing screenshots.
- Test utilities for performance tracking and reporting.

## Data Flow

The following diagram shows the data flow through the framework:

```
+------------------+     +------------------+     +------------------+
|                  |     |                  |     |                  |
|  Test Script     |---->|  Test Framework  |---->|  Test Results    |
|                  |     |                  |     |                  |
+------------------+     +------------------+     +------------------+
                               |
                               v
+------------------+     +------------------+     +------------------+
|                  |     |                  |     |                  |
|  MCP Integration |<--->|  Self-Healing    |<--->|  Performance     |
|                  |     |  Automation      |     |  Tracking        |
|                  |     |                  |     |                  |
+------------------+     +------------------+     +------------------+
        |                        |                        |
        v                        v                        v
+------------------+     +------------------+     +------------------+
|                  |     |                  |     |                  |
|  Selector        |     |  Recovery        |     |  Metrics         |
|  Optimization    |     |  Strategies      |     |  Collection      |
|                  |     |                  |     |                  |
+------------------+     +------------------+     +------------------+
        |                        |                        |
        v                        v                        v
+------------------+     +------------------+     +------------------+
|                  |     |                  |     |                  |
|  MCP Tools       |     |  Feedback        |     |  Reports         |
|                  |     |  Collection      |     |                  |
|                  |     |                  |     |                  |
+------------------+     +------------------+     +------------------+
```

## Deployment Architecture

The framework is deployed as npm packages that can be installed and used in any Node.js project. The packages are published to the npm registry under the `@qawolf` scope.

```
+------------------+     +------------------+     +------------------+
|                  |     |                  |     |                  |
|  @qawolf/        |     |  @qawolf/        |     |  @qawolf/        |
|  shared-utils    |     |  mcp-optimizer   |     |  test-framework  |
|                  |     |                  |     |                  |
+------------------+     +------------------+     +------------------+
        ^                        ^                        ^
        |                        |                        |
        v                        v                        v
+----------------------------------------------------------+
|                                                          |
|                      npm registry                        |
|                                                          |
+----------------------------------------------------------+
        ^                        ^                        ^
        |                        |                        |
        v                        v                        v
+------------------+     +------------------+     +------------------+
|                  |     |                  |     |                  |
|  User Project 1  |     |  User Project 2  |     |  User Project 3  |
|                  |     |                  |     |                  |
+------------------+     +------------------+     +------------------+
```

## Conclusion

The QA Wolf testing framework is designed to provide a comprehensive testing solution with MCP integration and self-healing automation. The monorepo architecture allows for easy development and maintenance of the framework, while the modular design allows users to use only the parts they need.