# Shared Utils API

The `@qawolf/shared-utils` package provides shared utilities for the QA Wolf testing framework.

## Installation

```bash
npm install @qawolf/shared-utils
```

## Usage

```javascript
const { config, screenshot, test } = require('@qawolf/shared-utils');

// Get an environment variable
const apiKey = config.getEnv('QAWOLF_API_KEY', '');

// Take a screenshot
const screenshotPath = await screenshot.takeScreenshot(page, {
  testName: 'example-test',
  action: 'login',
  fullPage: true
});

// Create a performance tracker
const performanceTracker = new test.PerformanceTracker();

// Start an operation
performanceTracker.startOperation('login');

// End the operation
performanceTracker.endOperation();

// Get metrics
const metrics = performanceTracker.getMetrics();
```

## API Reference

### Configuration Utilities

#### `config.getEnv(name, defaultValue)`

Gets an environment variable.

**Parameters:**

- `name` (string): Environment variable name.
- `defaultValue` (any, optional): Default value if the environment variable is not set.

**Returns:**

- (string): Environment variable value or default value.

**Example:**

```javascript
const { config } = require('@qawolf/shared-utils');

// Get an environment variable
const apiKey = config.getEnv('QAWOLF_API_KEY', '');
```

#### `config.getEnvBool(name, defaultValue)`

Gets a boolean environment variable.

**Parameters:**

- `name` (string): Environment variable name.
- `defaultValue` (boolean, optional): Default value if the environment variable is not set.

**Returns:**

- (boolean): Environment variable value as a boolean or default value.

**Example:**

```javascript
const { config } = require('@qawolf/shared-utils');

// Get a boolean environment variable
const headless = config.getEnvBool('QAWOLF_HEADLESS', true);
```

#### `config.getEnvNumber(name, defaultValue)`

Gets a number environment variable.

**Parameters:**

- `name` (string): Environment variable name.
- `defaultValue` (number, optional): Default value if the environment variable is not set.

**Returns:**

- (number): Environment variable value as a number or default value.

**Example:**

```javascript
const { config } = require('@qawolf/shared-utils');

// Get a number environment variable
const timeout = config.getEnvNumber('QAWOLF_TIMEOUT', 30000);
```

#### `config.setConfig(name, value)`

Sets a configuration value.

**Parameters:**

- `name` (string): Configuration name.
- `value` (any): Configuration value.

**Returns:**

- (void)

**Example:**

```javascript
const { config } = require('@qawolf/shared-utils');

// Set a configuration value
config.setConfig('screenshotDir', './screenshots');
```

#### `config.getConfig(name, defaultValue)`

Gets a configuration value.

**Parameters:**

- `name` (string): Configuration name.
- `defaultValue` (any, optional): Default value if the configuration is not set.

**Returns:**

- (any): Configuration value or default value.

**Example:**

```javascript
const { config } = require('@qawolf/shared-utils');

// Get a configuration value
const screenshotDir = config.getConfig('screenshotDir', './screenshots');
```

#### `config.isCI()`

Checks if running in a CI environment.

**Returns:**

- (boolean): Whether running in a CI environment.

**Example:**

```javascript
const { config } = require('@qawolf/shared-utils');

// Check if running in a CI environment
const isCI = config.isCI();
```

### Screenshot Utilities

#### `screenshot.takeScreenshot(page, options)`

Takes a screenshot.

**Parameters:**

- `page` (Object): Playwright page.
- `options` (Object, optional): Screenshot options.
  - `testName` (string, optional): Test name.
  - `action` (string, optional): Action name.
  - `fullPage` (boolean, default: false): Whether to take a full page screenshot.
  - `path` (string, optional): Screenshot path.

**Returns:**

- (string): Screenshot path.

**Example:**

```javascript
const { screenshot } = require('@qawolf/shared-utils');

// Take a screenshot
const screenshotPath = await screenshot.takeScreenshot(page, {
  testName: 'example-test',
  action: 'login',
  fullPage: true
});
```

#### `screenshot.ensureScreenshotDir(testName)`

Ensures the screenshot directory exists.

**Parameters:**

- `testName` (string): Test name.

**Returns:**

- (string): Screenshot directory path.

**Example:**

```javascript
const { screenshot } = require('@qawolf/shared-utils');

// Ensure the screenshot directory exists
const screenshotDir = await screenshot.ensureScreenshotDir('example-test');
```

#### `screenshot.verifyScreenshotStructure()`

Verifies the screenshot directory structure.

**Returns:**

- (boolean): Whether the screenshot directory structure is valid.

**Example:**

```javascript
const { screenshot } = require('@qawolf/shared-utils');

// Verify the screenshot directory structure
const isValid = await screenshot.verifyScreenshotStructure();
```

#### `screenshot.cleanupScreenshots(options)`

Cleans up screenshots.

**Parameters:**

- `options` (Object, optional): Cleanup options.
  - `maxAge` (number, default: 7): Maximum age of screenshots in days.
  - `dryRun` (boolean, default: false): Whether to perform a dry run.

**Returns:**

- (Object): Cleanup results with the following properties:
  - `deleted` (number): Number of deleted screenshots.
  - `kept` (number): Number of kept screenshots.
  - `errors` (number): Number of errors.

**Example:**

```javascript
const { screenshot } = require('@qawolf/shared-utils');

// Clean up screenshots
const results = await screenshot.cleanupScreenshots({
  maxAge: 7,
  dryRun: false
});
```

### Test Utilities

#### `test.calculateAAAComplianceScore(testSummary)`

Calculates the AAA compliance score.

**Parameters:**

- `testSummary` (Object): Test summary.
  - `success` (boolean): Whether the test was successful.
  - `performanceMetrics` (Object): Performance metrics.
  - `allPerformanceMetricsWithinThresholds` (boolean): Whether all performance metrics are within thresholds.

**Returns:**

- (number): AAA compliance score (0-100).

**Example:**

```javascript
const { test } = require('@qawolf/shared-utils');

// Calculate AAA compliance score
const score = test.calculateAAAComplianceScore({
  success: true,
  performanceMetrics: {
    'navigation_to_app': 1000,
    'login': 2000
  },
  allPerformanceMetricsWithinThresholds: true
});
```

#### `test.printTestSummary(testSummary, testId, testName, startTime)`

Prints a test summary.

**Parameters:**

- `testSummary` (Object): Test summary.
  - `success` (boolean): Whether the test was successful.
  - `performanceMetrics` (Object): Performance metrics.
  - `aaaComplianceScore` (number): AAA compliance score.
- `testId` (string): Test ID.
- `testName` (string): Test name.
- `startTime` (number): Test start time.

**Returns:**

- (void)

**Example:**

```javascript
const { test } = require('@qawolf/shared-utils');

// Print test summary
test.printTestSummary(
  {
    success: true,
    performanceMetrics: {
      'navigation_to_app': 1000,
      'login': 2000
    },
    aaaComplianceScore: 95
  },
  'example-test',
  'Example Test',
  Date.now() - 5000
);
```

#### `test.PerformanceTracker`

Class for tracking performance.

**Constructor:**

- `new test.PerformanceTracker()`

**Properties:**

- `startTime` (number): Start time.
- `currentOperation` (string): Current operation.
- `currentOperationStartTime` (number): Current operation start time.
- `metrics` (Object): Performance metrics.

**Methods:**

- `startOperation(name)`: Starts an operation.
- `endOperation()`: Ends the current operation.
- `getMetrics()`: Gets performance metrics.
- `areAllMetricsWithinThresholds()`: Checks if all metrics are within thresholds.

**Example:**

```javascript
const { test } = require('@qawolf/shared-utils');

// Create a performance tracker
const performanceTracker = new test.PerformanceTracker();

// Start an operation
performanceTracker.startOperation('login');

// End the operation
performanceTracker.endOperation();

// Get metrics
const metrics = performanceTracker.getMetrics();
```