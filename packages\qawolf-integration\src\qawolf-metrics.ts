/**
 * QA Wolf metrics for QA Wolf integration
 */

import axios, { AxiosInstance } from 'axios';
import { QAWolfMetricsOptions, QAWolfAPIResponse, QAWolfMetrics as QAWolfMetricsType } from './types';
import { EventBus, EventType } from '@qawolf/core';

/**
 * QA Wolf metrics
 */
export class QAWolfMetrics {
  /**
   * API key
   */
  private apiKey: string;
  
  /**
   * Team ID
   */
  private teamId: string;
  
  /**
   * API URL
   */
  private apiUrl: string;
  
  /**
   * Test ID
   */
  private testId: string;
  
  /**
   * Run ID
   */
  private runId: string;
  
  /**
   * Timeout in milliseconds
   */
  private timeout: number;
  
  /**
   * Axios instance
   */
  private axios: AxiosInstance;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options QA Wolf metrics options
   */
  constructor(options: QAWolfMetricsOptions = {}) {
    this.apiKey = options.apiKey || '';
    this.teamId = options.teamId || '';
    this.apiUrl = options.apiUrl || 'https://app.qawolf.com/api';
    this.testId = options.testId || '';
    this.runId = options.runId || '';
    this.timeout = options.timeout || 30000;
    
    this.axios = axios.create({
      baseURL: this.apiUrl,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        'X-Team-ID': this.teamId
      }
    });
    
    this.eventBus = EventBus.getInstance();
  }
  
  /**
   * Get metrics
   * @param testId Test ID
   * @returns Metrics
   */
  async getMetrics(testId?: string): Promise<QAWolfMetricsType[]> {
    try {
      const id = testId || this.testId;
      
      if (!id) {
        throw new Error('Test ID is required');
      }
      
      const response = await this.axios.get<QAWolfAPIResponse<QAWolfMetricsType[]>>(`/tests/${id}/metrics`);
      
      // Emit event
      this.eventBus.emit(EventType.QAWOLF_METRICS_GET_METRICS, {
        metrics: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, `Failed to get metrics for test with ID ${testId || this.testId}`);
      return [];
    }
  }
  
  /**
   * Get metrics by run ID
   * @param runId Run ID
   * @returns Metrics
   */
  async getMetricsByRunId(runId?: string): Promise<QAWolfMetricsType | null> {
    try {
      const id = runId || this.runId;
      
      if (!id) {
        throw new Error('Run ID is required');
      }
      
      const response = await this.axios.get<QAWolfAPIResponse<QAWolfMetricsType>>(`/runs/${id}/metrics`);
      
      // Emit event
      this.eventBus.emit(EventType.QAWOLF_METRICS_GET_METRICS_BY_RUN_ID, {
        metrics: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, `Failed to get metrics for run with ID ${runId || this.runId}`);
      return null;
    }
  }
  
  /**
   * Submit metrics
   * @param testId Test ID
   * @param runId Run ID
   * @param duration Duration in milliseconds
   * @param passRate Pass rate
   * @param flakinessRate Flakiness rate
   * @param executionTime Execution time
   * @param resourceUsage Resource usage
   * @param metadata Metadata
   * @returns Metrics
   */
  async submitMetrics(
    testId?: string,
    runId?: string,
    duration?: number,
    passRate?: number,
    flakinessRate?: number,
    executionTime?: number,
    resourceUsage?: { cpu: number; memory: number; network: number },
    metadata?: Record<string, any>
  ): Promise<QAWolfMetricsType | null> {
    try {
      const testIdToUse = testId || this.testId;
      const runIdToUse = runId || this.runId;
      
      if (!testIdToUse) {
        throw new Error('Test ID is required');
      }
      
      if (!runIdToUse) {
        throw new Error('Run ID is required');
      }
      
      const response = await this.axios.post<QAWolfAPIResponse<QAWolfMetricsType>>('/metrics', {
        testId: testIdToUse,
        runId: runIdToUse,
        duration,
        passRate,
        flakinessRate,
        executionTime,
        resourceUsage,
        metadata
      });
      
      // Emit event
      this.eventBus.emit(EventType.QAWOLF_METRICS_SUBMIT_METRICS, {
        metrics: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, 'Failed to submit metrics');
      return null;
    }
  }
  
  /**
   * Get test metrics summary
   * @param testId Test ID
   * @returns Metrics summary
   */
  async getTestMetricsSummary(testId?: string): Promise<{
    averageDuration: number;
    averagePassRate: number;
    averageFlakinessRate: number;
    averageExecutionTime: number;
    totalRuns: number;
  } | null> {
    try {
      const id = testId || this.testId;
      
      if (!id) {
        throw new Error('Test ID is required');
      }
      
      const metrics = await this.getMetrics(id);
      
      if (metrics.length === 0) {
        return {
          averageDuration: 0,
          averagePassRate: 0,
          averageFlakinessRate: 0,
          averageExecutionTime: 0,
          totalRuns: 0
        };
      }
      
      const totalDuration = metrics.reduce((sum, metric) => sum + metric.duration, 0);
      const totalPassRate = metrics.reduce((sum, metric) => sum + metric.passRate, 0);
      const totalFlakinessRate = metrics.reduce((sum, metric) => sum + metric.flakinessRate, 0);
      const totalExecutionTime = metrics.reduce((sum, metric) => sum + metric.executionTime, 0);
      
      const summary = {
        averageDuration: totalDuration / metrics.length,
        averagePassRate: totalPassRate / metrics.length,
        averageFlakinessRate: totalFlakinessRate / metrics.length,
        averageExecutionTime: totalExecutionTime / metrics.length,
        totalRuns: metrics.length
      };
      
      // Emit event
      this.eventBus.emit(EventType.QAWOLF_METRICS_GET_TEST_METRICS_SUMMARY, {
        summary
      });
      
      return summary;
    } catch (error) {
      this.handleError(error, `Failed to get metrics summary for test with ID ${testId || this.testId}`);
      return null;
    }
  }
  
  /**
   * Handle error
   * @param error Error
   * @param message Error message
   */
  private handleError(error: any, message: string): void {
    console.error(message, error);
    
    // Emit event
    this.eventBus.emit(EventType.QAWOLF_METRICS_ERROR, {
      error,
      message
    });
  }
  
  /**
   * Get API key
   * @returns API key
   */
  getAPIKey(): string {
    return this.apiKey;
  }
  
  /**
   * Get team ID
   * @returns Team ID
   */
  getTeamId(): string {
    return this.teamId;
  }
  
  /**
   * Get API URL
   * @returns API URL
   */
  getAPIUrl(): string {
    return this.apiUrl;
  }
  
  /**
   * Get test ID
   * @returns Test ID
   */
  getTestId(): string {
    return this.testId;
  }
  
  /**
   * Get run ID
   * @returns Run ID
   */
  getRunId(): string {
    return this.runId;
  }
  
  /**
   * Get timeout
   * @returns Timeout in milliseconds
   */
  getTimeout(): number {
    return this.timeout;
  }
  
  /**
   * Set API key
   * @param apiKey API key
   * @returns This instance for chaining
   */
  setAPIKey(apiKey: string): QAWolfMetrics {
    this.apiKey = apiKey;
    
    this.axios = axios.create({
      baseURL: this.apiUrl,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        'X-Team-ID': this.teamId
      }
    });
    
    return this;
  }
  
  /**
   * Set team ID
   * @param teamId Team ID
   * @returns This instance for chaining
   */
  setTeamId(teamId: string): QAWolfMetrics {
    this.teamId = teamId;
    
    this.axios = axios.create({
      baseURL: this.apiUrl,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        'X-Team-ID': this.teamId
      }
    });
    
    return this;
  }
  
  /**
   * Set API URL
   * @param apiUrl API URL
   * @returns This instance for chaining
   */
  setAPIUrl(apiUrl: string): QAWolfMetrics {
    this.apiUrl = apiUrl;
    
    this.axios = axios.create({
      baseURL: this.apiUrl,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        'X-Team-ID': this.teamId
      }
    });
    
    return this;
  }
  
  /**
   * Set test ID
   * @param testId Test ID
   * @returns This instance for chaining
   */
  setTestId(testId: string): QAWolfMetrics {
    this.testId = testId;
    return this;
  }
  
  /**
   * Set run ID
   * @param runId Run ID
   * @returns This instance for chaining
   */
  setRunId(runId: string): QAWolfMetrics {
    this.runId = runId;
    return this;
  }
  
  /**
   * Set timeout
   * @param timeout Timeout in milliseconds
   * @returns This instance for chaining
   */
  setTimeout(timeout: number): QAWolfMetrics {
    this.timeout = timeout;
    
    this.axios = axios.create({
      baseURL: this.apiUrl,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        'X-Team-ID': this.teamId
      }
    });
    
    return this;
  }
}

/**
 * Create QA Wolf metrics
 * @param options QA Wolf metrics options
 * @returns QA Wolf metrics
 */
export function createQAWolfMetrics(options: QAWolfMetricsOptions = {}): QAWolfMetrics {
  return new QAWolfMetrics(options);
}
