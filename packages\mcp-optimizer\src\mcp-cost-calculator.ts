/**
 * MCP cost calculator for MCP optimizer
 */

import { MCPOperation, MCPToolType, MCPOperationType, MCPCostCalculatorOptions } from './types';
import { EventBus, EventType } from '@qawolf/core';

/**
 * MCP cost calculator
 */
export class MCPCostCalculator {
  /**
   * Cost per token
   */
  private costPerToken: number;
  
  /**
   * Cost per operation
   */
  private costPerOperation: Record<string, number>;
  
  /**
   * Cost per tool
   */
  private costPerTool: Record<string, number>;
  
  /**
   * Token count per operation
   */
  private tokenCountPerOperation: Record<string, number>;
  
  /**
   * Token count per tool
   */
  private tokenCountPerTool: Record<string, number>;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options MCP cost calculator options
   */
  constructor(options: MCPCostCalculatorOptions = {}) {
    this.costPerToken = options.costPerToken || 0.00001; // $0.00001 per token
    
    this.costPerOperation = {
      [MCPOperationType.FILE_OPERATION]: 0.0005, // $0.0005 per file operation
      [MCPOperationType.BROWSER_OPERATION]: 0.001, // $0.001 per browser operation
      [MCPOperationType.TERMINAL_OPERATION]: 0.0005, // $0.0005 per terminal operation
      [MCPOperationType.THINKING_OPERATION]: 0.002, // $0.002 per thinking operation
      [MCPOperationType.DOCUMENTATION_OPERATION]: 0.0005, // $0.0005 per documentation operation
      [MCPOperationType.OTHER_OPERATION]: 0.001, // $0.001 per other operation
      ...options.costPerOperation
    };
    
    this.costPerTool = {
      [MCPToolType.DESKTOP_COMMANDER]: 0.0005, // $0.0005 per Desktop Commander operation
      [MCPToolType.BROWSER_TOOLS]: 0.001, // $0.001 per Browser Tools operation
      [MCPToolType.PLAYWRIGHT]: 0.0005, // $0.0005 per Playwright operation
      [MCPToolType.SEQUENTIAL_THINKING]: 0.002, // $0.002 per Sequential Thinking operation
      [MCPToolType.QUILLOPY]: 0.0005, // $0.0005 per Quillopy operation
      [MCPToolType.OTHER]: 0.001, // $0.001 per other operation
      ...options.costPerTool
    };
    
    this.tokenCountPerOperation = {
      [MCPOperationType.FILE_OPERATION]: 100, // 100 tokens per file operation
      [MCPOperationType.BROWSER_OPERATION]: 200, // 200 tokens per browser operation
      [MCPOperationType.TERMINAL_OPERATION]: 100, // 100 tokens per terminal operation
      [MCPOperationType.THINKING_OPERATION]: 500, // 500 tokens per thinking operation
      [MCPOperationType.DOCUMENTATION_OPERATION]: 100, // 100 tokens per documentation operation
      [MCPOperationType.OTHER_OPERATION]: 200 // 200 tokens per other operation
    };
    
    this.tokenCountPerTool = {
      [MCPToolType.DESKTOP_COMMANDER]: 100, // 100 tokens per Desktop Commander operation
      [MCPToolType.BROWSER_TOOLS]: 200, // 200 tokens per Browser Tools operation
      [MCPToolType.PLAYWRIGHT]: 100, // 100 tokens per Playwright operation
      [MCPToolType.SEQUENTIAL_THINKING]: 500, // 500 tokens per Sequential Thinking operation
      [MCPToolType.QUILLOPY]: 100, // 100 tokens per Quillopy operation
      [MCPToolType.OTHER]: 200 // 200 tokens per other operation
    };
    
    this.eventBus = EventBus.getInstance();
  }
  
  /**
   * Calculate costs
   * @param operations Operations
   * @returns Operations with costs
   */
  calculateCosts(operations: MCPOperation[]): MCPOperation[] {
    const operationsWithCosts = operations.map(operation => {
      // Calculate token count
      const tokenCount = this.calculateTokenCount(operation);
      
      // Calculate cost
      const cost = this.calculateCost(operation, tokenCount);
      
      return {
        ...operation,
        cost,
        tokenCount
      };
    });
    
    // Emit event
    this.eventBus.emit(EventType.MCP_COSTS_CALCULATED, {
      operations: operationsWithCosts
    });
    
    return operationsWithCosts;
  }
  
  /**
   * Calculate token count
   * @param operation Operation
   * @returns Token count
   */
  private calculateTokenCount(operation: MCPOperation): number {
    // Get base token count
    const baseTokenCount = this.tokenCountPerOperation[operation.type] || this.tokenCountPerOperation[MCPOperationType.OTHER_OPERATION];
    
    // Get tool token count
    const toolTokenCount = this.tokenCountPerTool[operation.toolType] || this.tokenCountPerTool[MCPToolType.OTHER];
    
    // Calculate token count based on parameters
    let parameterTokenCount = 0;
    
    if (operation.parameters) {
      // Estimate token count based on parameter size
      const parameterString = JSON.stringify(operation.parameters);
      parameterTokenCount = Math.ceil(parameterString.length / 4); // Roughly 4 characters per token
    }
    
    // Calculate token count based on result
    let resultTokenCount = 0;
    
    if (operation.result) {
      // Estimate token count based on result size
      const resultString = JSON.stringify(operation.result);
      resultTokenCount = Math.ceil(resultString.length / 4); // Roughly 4 characters per token
    }
    
    // Calculate total token count
    return baseTokenCount + toolTokenCount + parameterTokenCount + resultTokenCount;
  }
  
  /**
   * Calculate cost
   * @param operation Operation
   * @param tokenCount Token count
   * @returns Cost
   */
  private calculateCost(operation: MCPOperation, tokenCount: number): number {
    // Get base cost
    const baseCost = this.costPerOperation[operation.type] || this.costPerOperation[MCPOperationType.OTHER_OPERATION];
    
    // Get tool cost
    const toolCost = this.costPerTool[operation.toolType] || this.costPerTool[MCPToolType.OTHER];
    
    // Calculate token cost
    const tokenCost = tokenCount * this.costPerToken;
    
    // Calculate total cost
    return baseCost + toolCost + tokenCost;
  }
  
  /**
   * Get cost per token
   * @returns Cost per token
   */
  getCostPerToken(): number {
    return this.costPerToken;
  }
  
  /**
   * Set cost per token
   * @param cost Cost per token
   * @returns This instance for chaining
   */
  setCostPerToken(cost: number): MCPCostCalculator {
    this.costPerToken = cost;
    return this;
  }
  
  /**
   * Get cost per operation
   * @returns Cost per operation
   */
  getCostPerOperation(): Record<string, number> {
    return { ...this.costPerOperation };
  }
  
  /**
   * Set cost per operation
   * @param type Operation type
   * @param cost Cost
   * @returns This instance for chaining
   */
  setCostPerOperation(type: string, cost: number): MCPCostCalculator {
    this.costPerOperation[type] = cost;
    return this;
  }
  
  /**
   * Get cost per tool
   * @returns Cost per tool
   */
  getCostPerTool(): Record<string, number> {
    return { ...this.costPerTool };
  }
  
  /**
   * Set cost per tool
   * @param type Tool type
   * @param cost Cost
   * @returns This instance for chaining
   */
  setCostPerTool(type: string, cost: number): MCPCostCalculator {
    this.costPerTool[type] = cost;
    return this;
  }
  
  /**
   * Get token count per operation
   * @returns Token count per operation
   */
  getTokenCountPerOperation(): Record<string, number> {
    return { ...this.tokenCountPerOperation };
  }
  
  /**
   * Set token count per operation
   * @param type Operation type
   * @param count Token count
   * @returns This instance for chaining
   */
  setTokenCountPerOperation(type: string, count: number): MCPCostCalculator {
    this.tokenCountPerOperation[type] = count;
    return this;
  }
  
  /**
   * Get token count per tool
   * @returns Token count per tool
   */
  getTokenCountPerTool(): Record<string, number> {
    return { ...this.tokenCountPerTool };
  }
  
  /**
   * Set token count per tool
   * @param type Tool type
   * @param count Token count
   * @returns This instance for chaining
   */
  setTokenCountPerTool(type: string, count: number): MCPCostCalculator {
    this.tokenCountPerTool[type] = count;
    return this;
  }
}

/**
 * Create MCP cost calculator
 * @param options MCP cost calculator options
 * @returns MCP cost calculator
 */
export function createMCPCostCalculator(options: MCPCostCalculatorOptions = {}): MCPCostCalculator {
  return new MCPCostCalculator(options);
}
