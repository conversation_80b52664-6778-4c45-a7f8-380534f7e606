/**
 * Screenshot Utilities for QA Wolf Tests
 *
 * This module provides standardized functions for capturing and organizing screenshots
 * during test execution. It creates a consistent directory structure and naming convention
 * for screenshots to make them easier to find and associate with specific tests and actions.
 *
 * Directory structure:
 * screenshots/YYYY-MM-DD/run-<buildNumber>/test_name/action-timestamp.png
 */

const fs = require('fs');
const path = require('path');
const { format } = require('date-fns');
const { execSync } = require('child_process');
const { getConfig } = require('../config/config-loader');

/**
 * Configuration with environment variable support
 */
const config = {
  baseDir: getConfig('screenshotDir', path.join(process.cwd(), 'screenshots')),
  retentionDays: getConfig('screenshotRetentionDays', 7),
  runId: getConfig('buildNumber', `local-${Date.now()}`),
  saveToGit: getConfig('saveToGit', false)
};

/**
 * Base directory for storing screenshots
 * @type {string}
 */
const SCREENSHOTS_BASE_DIR = config.baseDir;

/**
 * Ensures the screenshots directory structure exists
 *
 * @param {string} testName - Name of the test
 * @param {string} [ticketId] - Optional ticket/issue ID associated with the test
 * @returns {string} Path to the screenshot directory for this test
 */
function ensureScreenshotDir(testName, ticketId) {
  // Create base screenshots directory if it doesn't exist
  if (!fs.existsSync(SCREENSHOTS_BASE_DIR)) {
    fs.mkdirSync(SCREENSHOTS_BASE_DIR, { recursive: true });
  }

  // Create a dated directory for today's screenshots
  const dateDir = path.join(SCREENSHOTS_BASE_DIR, format(new Date(), 'yyyy-MM-dd'));
  if (!fs.existsSync(dateDir)) {
    fs.mkdirSync(dateDir, { recursive: true });
  }

  // Create a run directory to prevent collisions when running tests multiple times
  const runDir = path.join(dateDir, `run-${config.runId}`);
  if (!fs.existsSync(runDir)) {
    fs.mkdirSync(runDir, { recursive: true });
  }

  // Create a directory for this specific test
  const testDirName = ticketId ? `${ticketId}_${testName}` : testName;
  const testDir = path.join(runDir, testDirName);
  if (!fs.existsSync(testDir)) {
    fs.mkdirSync(testDir, { recursive: true });
  }

  return testDir;
}

/**
 * Takes a screenshot with standardized naming and organization
 *
 * @param {Object} page - Playwright page object
 * @param {Object} options - Screenshot options
 * @param {string} options.testName - Name of the test
 * @param {string} options.action - Action being performed (e.g., 'before-login', 'after-creation')
 * @param {string} [options.ticketId] - Optional ticket/issue ID associated with the test
 * @param {string} [options.description] - Optional additional description
 * @param {boolean} [options.fullPage=false] - Whether to take a full page screenshot
 * @param {Object} [options.screenshotOptions={}] - Additional Playwright screenshot options
 * @returns {Promise<string>} Path to the saved screenshot
 */
async function takeScreenshot(page, options) {
  const {
    testName,
    action,
    ticketId,
    description,
    fullPage = false,
    screenshotOptions = {}
  } = options;

  if (!testName) {
    throw new Error('testName is required for taking screenshots');
  }

  if (!action) {
    throw new Error('action is required for taking screenshots');
  }

  // Get the screenshot directory for this test
  const screenshotDir = ensureScreenshotDir(testName, ticketId);

  // Create a timestamp for the filename
  const timestamp = format(new Date(), 'HHmmss');

  // Build the filename with all components
  let filename = `${timestamp}_${action}`;
  if (description) {
    filename += `_${description}`;
  }
  filename += '.png';

  const screenshotPath = path.join(screenshotDir, filename);

  // Take the screenshot with Playwright
  await page.screenshot({
    path: screenshotPath,
    fullPage,
    ...screenshotOptions
  });

  console.log(`Screenshot saved: ${screenshotPath}`);
  return screenshotPath;
}

/**
 * Takes a screenshot when an error occurs
 *
 * @param {Object} page - Playwright page object
 * @param {Object} options - Screenshot options
 * @param {string} options.testName - Name of the test
 * @param {string} [options.ticketId] - Optional ticket/issue ID associated with the test
 * @param {Error} [options.error] - The error that occurred
 * @param {boolean} [options.fullPage=true] - Whether to take a full page screenshot
 * @returns {Promise<string>} Path to the saved screenshot
 */
async function takeErrorScreenshot(page, options) {
  const {
    testName,
    ticketId,
    error,
    fullPage = true
  } = options;

  const errorDescription = error ? `${error.name}-${error.message.substring(0, 20).replace(/[^a-zA-Z0-9]/g, '_')}` : 'unknown-error';

  return takeScreenshot(page, {
    testName,
    ticketId,
    action: 'error',
    description: errorDescription,
    fullPage,
    screenshotOptions: {
      timeout: 5000 // Longer timeout for error screenshots
    }
  });
}

/**
 * Gets all screenshots for a specific test
 *
 * @param {string} testName - Name of the test
 * @param {string} [ticketId] - Optional ticket/issue ID associated with the test
 * @param {string} [date] - Optional date in 'yyyy-MM-dd' format (defaults to today)
 * @param {string} [runId] - Optional run ID (defaults to current run ID)
 * @returns {Array<string>} Array of screenshot paths
 */
function getTestScreenshots(testName, ticketId, date, runId) {
  const dateStr = date || format(new Date(), 'yyyy-MM-dd');
  const runIdStr = runId || config.runId;
  const testDirName = ticketId ? `${ticketId}_${testName}` : testName;
  const testDir = path.join(SCREENSHOTS_BASE_DIR, dateStr, `run-${runIdStr}`, testDirName);

  if (!fs.existsSync(testDir)) {
    return [];
  }

  return fs.readdirSync(testDir)
    .filter(file => file.endsWith('.png'))
    .map(file => path.join(testDir, file));
}

/**
 * Clean up screenshots older than the specified number of days
 *
 * @param {number} [olderThanDays=config.retentionDays] - Number of days to keep screenshots
 * @returns {Promise<void>}
 */
async function cleanupScreenshots(olderThanDays = config.retentionDays) {
  try {
    // Check if base directory exists
    if (!fs.existsSync(SCREENSHOTS_BASE_DIR)) {
      console.log(`Screenshot directory ${SCREENSHOTS_BASE_DIR} does not exist, nothing to clean up`);
      return;
    }

    // Get all date directories
    const dateDirs = fs.readdirSync(SCREENSHOTS_BASE_DIR);

    // Calculate cutoff date
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);
    const cutoffDateStr = format(cutoffDate, 'yyyy-MM-dd');

    // Delete directories older than cutoff date
    let deletedCount = 0;

    for (const dateDir of dateDirs) {
      const dateDirPath = path.join(SCREENSHOTS_BASE_DIR, dateDir);
      const stats = fs.statSync(dateDirPath);

      if (stats.isDirectory() && dateDir <= cutoffDateStr) {
        console.log(`Removing old screenshot directory: ${dateDirPath}`);
        fs.rmSync(dateDirPath, { recursive: true, force: true });
        deletedCount++;
      }
    }

    console.log(`Cleaned up ${deletedCount} screenshot directories older than ${olderThanDays} days`);
  } catch (error) {
    console.error('Failed to clean up screenshots:', error);
  }
}

/**
 * Verify that all screenshots are in the correct directory structure
 *
 * @returns {boolean} - Whether all screenshots are in the correct directory
 */
function verifyScreenshotStructure() {
  try {
    // Get all PNG files in the project directory
    let result;
    try {
      // Try using find command (works on Linux/macOS)
      result = execSync('find . -name "*.png" -not -path "./screenshots/*" -not -path "./node_modules/*"').toString();
    } catch (error) {
      // On Windows, use a different approach
      if (process.platform === 'win32') {
        result = execSync('dir /s /b *.png | findstr /v screenshots | findstr /v node_modules').toString();
      } else {
        throw error;
      }
    }

    const strayScreenshots = result.trim().split('\n').filter(Boolean);

    if (strayScreenshots.length > 0) {
      console.error('Found screenshots outside of the screenshots directory:');
      strayScreenshots.forEach(file => console.error(`  ${file}`));
      return false;
    }

    return true;
  } catch (error) {
    console.error('Failed to verify screenshot structure:', error);
    return false;
  }
}

/**
 * Move stray screenshots to the screenshots directory
 *
 * @param {string} testName - Name of the test to associate with the screenshots
 * @returns {Promise<number>} - Number of screenshots moved
 */
async function moveStrayScreenshots(testName) {
  try {
    // Get all PNG files in the project directory
    let result;
    try {
      // Try using find command (works on Linux/macOS)
      result = execSync('find . -name "*.png" -not -path "./screenshots/*" -not -path "./node_modules/*"').toString();
    } catch (error) {
      // On Windows, use a different approach
      if (process.platform === 'win32') {
        result = execSync('dir /s /b *.png | findstr /v screenshots | findstr /v node_modules').toString();
      } else {
        throw error;
      }
    }

    const strayScreenshots = result.trim().split('\n').filter(Boolean);

    if (strayScreenshots.length === 0) {
      console.log('No stray screenshots found');
      return 0;
    }

    // Create a directory for the stray screenshots
    const screenshotDir = ensureScreenshotDir(testName, 'stray');

    // Move each stray screenshot
    let movedCount = 0;
    for (const file of strayScreenshots) {
      try {
        const filename = path.basename(file);
        const destination = path.join(screenshotDir, `migrated_${filename}`);

        // Copy the file
        fs.copyFileSync(file, destination);

        // Delete the original
        fs.unlinkSync(file);

        console.log(`Moved stray screenshot: ${file} -> ${destination}`);
        movedCount++;
      } catch (moveError) {
        console.error(`Failed to move stray screenshot ${file}:`, moveError);
      }
    }

    console.log(`Moved ${movedCount} stray screenshots to ${screenshotDir}`);
    return movedCount;
  } catch (error) {
    console.error('Failed to move stray screenshots:', error);
    return 0;
  }
}

module.exports = {
  takeScreenshot,
  takeErrorScreenshot,
  getTestScreenshots,
  cleanupScreenshots,
  verifyScreenshotStructure,
  moveStrayScreenshots,
  SCREENSHOTS_BASE_DIR,
  config
};