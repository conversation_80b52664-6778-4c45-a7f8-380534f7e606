/**
 * MCP analyzer for MCP optimizer
 */

import { v4 as uuidv4 } from 'uuid';
import { MC<PERSON><PERSON><PERSON>, MCPOperationPattern, MCPAnalyzerOptions } from './types';
import { EventBus, EventType } from '@qawolf/core';

/**
 * MCP analyzer
 */
export class MCPAnalyzer {
  /**
   * Whether to analyze patterns
   */
  private analyzePatterns: boolean;
  
  /**
   * Minimum pattern frequency
   */
  private minPatternFrequency: number;
  
  /**
   * Maximum operations to analyze
   */
  private maxOperations?: number;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options MCP analyzer options
   */
  constructor(options: MCPAnalyzerOptions = {}) {
    this.analyzePatterns = options.analyzePatterns !== false;
    this.minPatternFrequency = options.minPatternFrequency || 2;
    this.maxOperations = options.maxOperations;
    this.eventBus = EventBus.getInstance();
  }
  
  /**
   * Analyze patterns
   * @param operations Operations to analyze
   * @returns Operation patterns
   */
  analyzePatterns(operations: MCPOperation[]): MCPOperationPattern[] {
    if (!this.analyzePatterns) {
      return [];
    }
    
    // Limit operations if maxOperations is set
    const limitedOperations = this.maxOperations ? operations.slice(0, this.maxOperations) : operations;
    
    // Find sequences of operations
    const sequences = this.findSequences(limitedOperations);
    
    // Filter sequences by frequency
    const filteredSequences = sequences.filter(sequence => sequence.frequency >= this.minPatternFrequency);
    
    // Convert sequences to patterns
    const patterns = filteredSequences.map(sequence => {
      const patternOperations = sequence.operations.map(operationIndex => limitedOperations[operationIndex]);
      
      // Calculate pattern cost
      const cost = patternOperations.reduce((sum, operation) => sum + (operation.cost || 0), 0);
      
      // Calculate pattern token count
      const tokenCount = patternOperations.reduce((sum, operation) => sum + (operation.tokenCount || 0), 0);
      
      return {
        id: uuidv4(),
        name: `Pattern ${sequence.id}`,
        description: `Sequence of ${patternOperations.length} operations`,
        operations: patternOperations,
        frequency: sequence.frequency,
        cost,
        tokenCount
      };
    });
    
    // Emit event
    this.eventBus.emit(EventType.MCP_PATTERNS_ANALYZED, {
      patterns
    });
    
    return patterns;
  }
  
  /**
   * Find sequences
   * @param operations Operations to analyze
   * @returns Sequences
   */
  private findSequences(operations: MCPOperation[]): { id: string; operations: number[]; frequency: number }[] {
    const sequences: { id: string; operations: number[]; frequency: number }[] = [];
    
    // Find sequences of 2 operations
    for (let i = 0; i < operations.length - 1; i++) {
      const sequence = [i, i + 1];
      const sequenceKey = this.getSequenceKey(operations, sequence);
      
      let found = false;
      
      for (const existingSequence of sequences) {
        const existingSequenceKey = this.getSequenceKey(operations, existingSequence.operations);
        
        if (sequenceKey === existingSequenceKey) {
          existingSequence.frequency++;
          found = true;
          break;
        }
      }
      
      if (!found) {
        sequences.push({
          id: uuidv4(),
          operations: sequence,
          frequency: 1
        });
      }
    }
    
    // Find sequences of 3 operations
    for (let i = 0; i < operations.length - 2; i++) {
      const sequence = [i, i + 1, i + 2];
      const sequenceKey = this.getSequenceKey(operations, sequence);
      
      let found = false;
      
      for (const existingSequence of sequences) {
        const existingSequenceKey = this.getSequenceKey(operations, existingSequence.operations);
        
        if (sequenceKey === existingSequenceKey) {
          existingSequence.frequency++;
          found = true;
          break;
        }
      }
      
      if (!found) {
        sequences.push({
          id: uuidv4(),
          operations: sequence,
          frequency: 1
        });
      }
    }
    
    // Find sequences of 4 operations
    for (let i = 0; i < operations.length - 3; i++) {
      const sequence = [i, i + 1, i + 2, i + 3];
      const sequenceKey = this.getSequenceKey(operations, sequence);
      
      let found = false;
      
      for (const existingSequence of sequences) {
        const existingSequenceKey = this.getSequenceKey(operations, existingSequence.operations);
        
        if (sequenceKey === existingSequenceKey) {
          existingSequence.frequency++;
          found = true;
          break;
        }
      }
      
      if (!found) {
        sequences.push({
          id: uuidv4(),
          operations: sequence,
          frequency: 1
        });
      }
    }
    
    return sequences;
  }
  
  /**
   * Get sequence key
   * @param operations Operations
   * @param sequence Sequence
   * @returns Sequence key
   */
  private getSequenceKey(operations: MCPOperation[], sequence: number[]): string {
    return sequence.map(index => {
      const operation = operations[index];
      return `${operation.type}:${operation.toolType}:${operation.toolName}`;
    }).join('|');
  }
  
  /**
   * Get minimum pattern frequency
   * @returns Minimum pattern frequency
   */
  getMinPatternFrequency(): number {
    return this.minPatternFrequency;
  }
  
  /**
   * Set minimum pattern frequency
   * @param frequency Minimum pattern frequency
   * @returns This instance for chaining
   */
  setMinPatternFrequency(frequency: number): MCPAnalyzer {
    this.minPatternFrequency = frequency;
    return this;
  }
  
  /**
   * Get maximum operations
   * @returns Maximum operations
   */
  getMaxOperations(): number | undefined {
    return this.maxOperations;
  }
  
  /**
   * Set maximum operations
   * @param maxOperations Maximum operations
   * @returns This instance for chaining
   */
  setMaxOperations(maxOperations?: number): MCPAnalyzer {
    this.maxOperations = maxOperations;
    return this;
  }
  
  /**
   * Enable pattern analysis
   * @returns This instance for chaining
   */
  enablePatternAnalysis(): MCPAnalyzer {
    this.analyzePatterns = true;
    return this;
  }
  
  /**
   * Disable pattern analysis
   * @returns This instance for chaining
   */
  disablePatternAnalysis(): MCPAnalyzer {
    this.analyzePatterns = false;
    return this;
  }
}

/**
 * Create MCP analyzer
 * @param options MCP analyzer options
 * @returns MCP analyzer
 */
export function createMCPAnalyzer(options: MCPAnalyzerOptions = {}): MCPAnalyzer {
  return new MCPAnalyzer(options);
}
