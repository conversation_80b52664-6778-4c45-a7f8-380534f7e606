/**
 * File Operations End-to-End Tests
 * 
 * This file contains end-to-end tests for file operations.
 */

const { test, expect } = require('@playwright/test');
const { createSelfHealingController, test: testUtils } = require('../../src');
const { createTestApp, createMetricsCollector, createReportGenerator } = require('../fixtures');

test.describe('File Operations End-to-End Tests', () => {
  let testApp;
  let metricsCollector;
  let reportGenerator;
  
  test.beforeEach(async () => {
    testApp = await createTestApp();
    metricsCollector = createMetricsCollector();
    reportGenerator = createReportGenerator();
    
    // Log in before each test
    await testApp.login();
  });
  
  test.afterEach(async () => {
    await testApp.cleanup();
  });
  
  test('should create a new file', async () => {
    const testName = 'file-create';
    const startTime = Date.now();
    
    // Create a self-healing controller
    const selfHealingController = createSelfHealingController({
      selectorHealing: {
        enabled: true,
        persistHistory: false
      },
      recovery: {
        enabled: true
      },
      feedbackCollection: {
        enabled: true,
        persistFeedback: false
      }
    });
    
    try {
      // Start the test run
      await selfHealingController.startTest({
        testId: testName,
        testName: 'Create a new file'
      });
      
      // Create a self-healing page
      const selfHealingPage = selfHealingController.createPage(testApp.page);
      
      // Start performance tracking
      const performanceTracker = new testUtils.PerformanceTracker();
      
      // Create a new file
      performanceTracker.startOperation('file_creation');
      
      // Click on the "New file" button
      await selfHealingPage.click('div[class*="pages__NewFileButton"]');
      
      // Wait for the file to be created
      await selfHealingPage.waitForTimeout(3000);
      
      performanceTracker.endOperation();
      
      // Verify file was created
      const fileCount = await selfHealingPage.locator('div[class*="FilesTable"] div[class*="FilesTable__row"]').count();
      expect(fileCount).toBeGreaterThan(0);
      
      // Track performance metrics
      await selfHealingController.trackPerformance(performanceTracker.getMetrics());
      
      // End the test run
      await selfHealingController.endTest({
        success: true
      });
      
      // Record metrics
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      metricsCollector.recordExecutionTime(testName, duration);
      metricsCollector.recordTestResult(testName, true);
      metricsCollector.recordResourceUsage(testName, { cpu: 15, memory: 250 }); // Mock resource usage
      
      // Generate report
      await reportGenerator.generateReport(metricsCollector.getMetrics(), 'file-operations-tests');
    } catch (error) {
      // End the test run with failure
      await selfHealingController.endTest({
        success: false,
        error
      });
      
      throw error;
    } finally {
      // Clean up resources
      await selfHealingController.cleanup();
    }
  });
  
  test('should delete a file', async () => {
    const testName = 'file-delete';
    const startTime = Date.now();
    
    // Create a self-healing controller
    const selfHealingController = createSelfHealingController({
      selectorHealing: {
        enabled: true,
        persistHistory: false
      },
      recovery: {
        enabled: true
      },
      feedbackCollection: {
        enabled: true,
        persistFeedback: false
      }
    });
    
    try {
      // Start the test run
      await selfHealingController.startTest({
        testId: testName,
        testName: 'Delete a file'
      });
      
      // Create a self-healing page
      const selfHealingPage = selfHealingController.createPage(testApp.page);
      
      // Start performance tracking
      const performanceTracker = new testUtils.PerformanceTracker();
      
      // Create a new file first
      await testApp.createFile();
      
      // Get the file name
      const fileName = await selfHealingPage.evaluate(() => {
        const fileElements = document.querySelectorAll('div[class*="FilesTable"] div[class*="FilesTable__row"]');
        return fileElements[0]?.textContent || 'Unknown';
      });
      
      // Delete the file
      performanceTracker.startOperation('file_deletion');
      
      // Find the file
      const fileRow = selfHealingPage.locator(`div[class*="FilesTable"] div[class*="FilesTable__row"]:has-text("${fileName}")`);
      
      // Click on the file to select it
      await fileRow.click();
      
      // Click on the delete button
      await selfHealingPage.click('button:has-text("Delete")');
      
      // Confirm deletion
      await selfHealingPage.click('button:has-text("Delete file")');
      
      // Wait for the file to be deleted
      await selfHealingPage.waitForTimeout(3000);
      
      performanceTracker.endOperation();
      
      // Verify file was deleted
      const fileStillExists = await selfHealingPage.locator(`div[class*="FilesTable"] div[class*="FilesTable__row"]:has-text("${fileName}")`).count() > 0;
      expect(fileStillExists).toBe(false);
      
      // Track performance metrics
      await selfHealingController.trackPerformance(performanceTracker.getMetrics());
      
      // End the test run
      await selfHealingController.endTest({
        success: true
      });
      
      // Record metrics
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      metricsCollector.recordExecutionTime(testName, duration);
      metricsCollector.recordTestResult(testName, true);
      metricsCollector.recordResourceUsage(testName, { cpu: 18, memory: 270 }); // Mock resource usage
      
      // Generate report
      await reportGenerator.generateReport(metricsCollector.getMetrics(), 'file-operations-tests');
    } catch (error) {
      // End the test run with failure
      await selfHealingController.endTest({
        success: false,
        error
      });
      
      throw error;
    } finally {
      // Clean up resources
      await selfHealingController.cleanup();
    }
  });
});