{"timestamp": "2025-05-21T01:37:40.082Z", "baseUrl": "https://sneezyxl.github.io/QAWolfeesojc", "allPassed": true, "results": [{"url": "https://sneezyxl.github.io/QAWolfeesojc/", "statusCode": 200, "success": true, "data": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content..."}, {"url": "https://sneezyxl.github.io/QAWolfeesojc/framework/", "statusCode": 200, "success": true, "data": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content..."}, {"url": "https://sneezyxl.github.io/QAWolfeesojc/framework/environment/", "statusCode": 200, "success": true, "data": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content..."}, {"url": "https://sneezyxl.github.io/QAWolfeesojc/comprehensive/", "statusCode": 200, "success": true, "data": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content..."}, {"url": "https://sneezyxl.github.io/QAWolfeesojc/reports/executive_summary.html", "statusCode": 200, "success": true, "data": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n    <meta charset=\"UTF-8\">\n    <meta name=\"viewport\" content..."}, {"url": "https://sneezyxl.github.io/QAWolfeesojc/reports/test-report.html", "statusCode": 200, "success": true, "data": "\n<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"w..."}, {"url": "https://sneezyxl.github.io/QAWolfeesojc/reports/test-results-dashboard.html", "statusCode": 200, "success": true, "data": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n  <meta charset=\"UTF-8\">\n  <meta name=\"viewport\" content=\"wi..."}]}