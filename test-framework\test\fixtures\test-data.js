/**
 * Test Data Generator
 * 
 * This module provides utilities for generating test data.
 */

/**
 * Generate a random string
 * @param {number} [length=8] - Length of the string
 * @returns {string} - Random string
 */
function randomString(length = 8) {
  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
  let result = '';
  
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  return result;
}

/**
 * Generate a random email
 * @returns {string} - Random email
 */
function randomEmail() {
  return `test-${randomString(8)}@example.com`;
}

/**
 * Generate a random password
 * @param {number} [length=12] - Length of the password
 * @returns {string} - Random password
 */
function randomPassword(length = 12) {
  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()';
  let result = '';
  
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  return result;
}

/**
 * Generate random user credentials
 * @returns {Object} - Random user credentials
 */
function randomCredentials() {
  return {
    email: randomEmail(),
    password: randomPassword()
  };
}

/**
 * Generate a random file name
 * @returns {string} - Random file name
 */
function randomFileName() {
  return `test-file-${randomString(8)}.txt`;
}

/**
 * Generate random test data
 * @returns {Object} - Random test data
 */
function randomTestData() {
  return {
    user: {
      email: randomEmail(),
      password: randomPassword(),
      name: `Test User ${randomString(4)}`
    },
    file: {
      name: randomFileName(),
      content: `Test content ${randomString(16)}`
    }
  };
}

module.exports = {
  randomString,
  randomEmail,
  randomPassword,
  randomCredentials,
  randomFileName,
  randomTestData
};