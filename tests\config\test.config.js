/**
 * Test Configuration
 * 
 * This module provides configuration options for tests.
 */

/**
 * Default test configuration
 */
const DEFAULT_CONFIG = {
  // Test environment
  environment: process.env.TEST_ENV || 'staging',
  
  // Base URL
  baseUrl: process.env.URL || 'https://app.lidostaging.com',
  
  // Authentication
  auth: {
    email: process.env.EMAIL || '<EMAIL>',
    password: process.env.PASSWORD || 'vhc!tGK289IS&'
  },
  
  // Timeouts
  timeouts: {
    defaultTimeout: 30000,
    navigationTimeout: 30000,
    actionTimeout: 10000,
    assertionTimeout: 5000
  },
  
  // Retry configuration
  retry: {
    count: 2,
    delay: 1000
  },
  
  // Screenshot configuration
  screenshot: {
    takeOnFailure: true,
    takeOnSuccess: false,
    directory: './screenshots'
  },
  
  // MCP configuration
  mcp: {
    enabled: true,
    autoStartPlaywrightMcp: true,
    playwrightMcpPort: 8932,
    generateFallbacks: true,
    prioritizeTestIds: true,
    tokenOptimized: true
  },
  
  // Self-healing configuration
  selfHealing: {
    enabled: true,
    selectorHealing: {
      enabled: true,
      maxAttempts: 3
    },
    recovery: {
      enabled: true,
      maxAttempts: 3
    },
    feedbackCollection: {
      enabled: true,
      collectScreenshots: true
    }
  },
  
  // Performance tracking configuration
  performance: {
    trackExecutionTime: true,
    trackTokenUsage: true,
    trackResourceUsage: true,
    thresholds: {
      executionTime: 15000, // 15 seconds
      tokenUsage: 1000, // 1000 tokens
      resourceUsage: {
        cpu: 80, // 80% CPU usage
        memory: 500 // 500 MB memory usage
      }
    }
  },
  
  // AAA compliance configuration
  aaaCompliance: {
    validateOnRun: true,
    minimumScore: 0.9,
    generateRecommendations: true
  }
};

/**
 * Get test configuration
 * 
 * @param {Object} [overrides] - Configuration overrides
 * @returns {Object} - Test configuration
 */
function getConfig(overrides = {}) {
  return {
    ...DEFAULT_CONFIG,
    ...overrides,
    // Merge nested objects
    auth: {
      ...DEFAULT_CONFIG.auth,
      ...overrides.auth
    },
    timeouts: {
      ...DEFAULT_CONFIG.timeouts,
      ...overrides.timeouts
    },
    retry: {
      ...DEFAULT_CONFIG.retry,
      ...overrides.retry
    },
    screenshot: {
      ...DEFAULT_CONFIG.screenshot,
      ...overrides.screenshot
    },
    mcp: {
      ...DEFAULT_CONFIG.mcp,
      ...overrides.mcp
    },
    selfHealing: {
      ...DEFAULT_CONFIG.selfHealing,
      ...overrides.selfHealing
    },
    performance: {
      ...DEFAULT_CONFIG.performance,
      ...overrides.performance
    },
    aaaCompliance: {
      ...DEFAULT_CONFIG.aaaCompliance,
      ...overrides.aaaCompliance
    }
  };
}

module.exports = {
  DEFAULT_CONFIG,
  getConfig
};
