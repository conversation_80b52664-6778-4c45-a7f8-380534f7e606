/**
 * Linear attachments for Linear integration
 */

import { LinearClient, Attachment, AttachmentConnection } from '@linear/sdk';
import { LinearAttachmentsOptions, LinearAttachmentFilter, LinearAttachmentCreateInput, LinearAttachmentUpdateInput } from './types';
import { EventBus, EventType } from '@qawolf/core';

/**
 * Linear attachments
 */
export class LinearAttachments {
  /**
   * Linear client
   */
  private client: LinearClient;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options Linear attachments options
   */
  constructor(options: LinearAttachmentsOptions = {}) {
    if (!options.client && !options.apiKey) {
      throw new Error('Linear client or API key is required');
    }
    
    if (options.client) {
      this.client = options.client;
    } else {
      this.client = new LinearClient({
        apiKey: options.apiKey
      });
    }
    
    this.eventBus = EventBus.getInstance();
  }
  
  /**
   * Get attachments
   * @param filter Attachment filter
   * @returns Attachments
   */
  async getAttachments(filter?: LinearAttachmentFilter): Promise<Attachment[]> {
    try {
      // Build filter
      const queryFilter: Record<string, any> = {};
      
      if (filter?.id) {
        queryFilter.id = { eq: filter.id };
      }
      
      if (filter?.title) {
        queryFilter.title = { contains: filter.title };
      }
      
      if (filter?.subtitle) {
        queryFilter.subtitle = { contains: filter.subtitle };
      }
      
      if (filter?.url) {
        queryFilter.url = { contains: filter.url };
      }
      
      if (filter?.issue) {
        queryFilter.issue = { id: { eq: filter.issue } };
      }
      
      if (filter?.user) {
        queryFilter.user = { id: { eq: filter.user } };
      }
      
      if (filter?.source) {
        queryFilter.source = { eq: filter.source };
      }
      
      if (filter?.createdAfter) {
        queryFilter.createdAt = { ...queryFilter.createdAt, gt: filter.createdAfter };
      }
      
      if (filter?.createdBefore) {
        queryFilter.createdAt = { ...queryFilter.createdAt, lt: filter.createdBefore };
      }
      
      if (filter?.updatedAfter) {
        queryFilter.updatedAt = { ...queryFilter.updatedAt, gt: filter.updatedAfter };
      }
      
      if (filter?.updatedBefore) {
        queryFilter.updatedAt = { ...queryFilter.updatedAt, lt: filter.updatedBefore };
      }
      
      // Build order by
      const orderBy = filter?.sortBy ? `${filter.sortBy}_${filter.sortOrder || 'ASC'}` : undefined;
      
      // Get attachments
      const attachments = await this.client.attachments({
        filter: queryFilter,
        orderBy
      });
      
      // Get all attachments
      const allAttachments = await this.getAllAttachments(attachments);
      
      // Emit event
      this.eventBus.emit(EventType.LINEAR_ATTACHMENTS_GET_ATTACHMENTS, {
        attachments: allAttachments
      });
      
      return allAttachments;
    } catch (error) {
      this.handleError(error, 'Failed to get attachments');
      return [];
    }
  }
  
  /**
   * Get attachment by ID
   * @param id Attachment ID
   * @returns Attachment
   */
  async getAttachmentById(id: string): Promise<Attachment | null> {
    try {
      const attachment = await this.client.attachment(id);
      
      // Emit event
      this.eventBus.emit(EventType.LINEAR_ATTACHMENTS_GET_ATTACHMENT_BY_ID, {
        attachment
      });
      
      return attachment;
    } catch (error) {
      this.handleError(error, `Failed to get attachment with ID ${id}`);
      return null;
    }
  }
  
  /**
   * Get attachments by issue ID
   * @param issueId Issue ID
   * @returns Attachments
   */
  async getAttachmentsByIssueId(issueId: string): Promise<Attachment[]> {
    try {
      const attachments = await this.getAttachments({
        issue: issueId
      });
      
      // Emit event
      this.eventBus.emit(EventType.LINEAR_ATTACHMENTS_GET_ATTACHMENTS_BY_ISSUE_ID, {
        attachments,
        issueId
      });
      
      return attachments;
    } catch (error) {
      this.handleError(error, `Failed to get attachments for issue with ID ${issueId}`);
      return [];
    }
  }
  
  /**
   * Create attachment
   * @param input Attachment create input
   * @returns Attachment
   */
  async createAttachment(input: LinearAttachmentCreateInput): Promise<Attachment | null> {
    try {
      const attachment = await this.client.attachmentCreate({
        title: input.title,
        subtitle: input.subtitle,
        url: input.url,
        issueId: input.issueId,
        icon: input.icon,
        metadata: input.metadata
      });
      
      // Emit event
      this.eventBus.emit(EventType.LINEAR_ATTACHMENTS_CREATE_ATTACHMENT, {
        attachment: attachment.attachment
      });
      
      return attachment.attachment;
    } catch (error) {
      this.handleError(error, 'Failed to create attachment');
      return null;
    }
  }
  
  /**
   * Update attachment
   * @param id Attachment ID
   * @param input Attachment update input
   * @returns Attachment
   */
  async updateAttachment(id: string, input: LinearAttachmentUpdateInput): Promise<Attachment | null> {
    try {
      const attachment = await this.client.attachmentUpdate(id, {
        title: input.title,
        subtitle: input.subtitle,
        url: input.url,
        icon: input.icon,
        metadata: input.metadata
      });
      
      // Emit event
      this.eventBus.emit(EventType.LINEAR_ATTACHMENTS_UPDATE_ATTACHMENT, {
        attachment: attachment.attachment
      });
      
      return attachment.attachment;
    } catch (error) {
      this.handleError(error, `Failed to update attachment with ID ${id}`);
      return null;
    }
  }
  
  /**
   * Delete attachment
   * @param id Attachment ID
   * @returns True if deleted, false otherwise
   */
  async deleteAttachment(id: string): Promise<boolean> {
    try {
      const result = await this.client.attachmentDelete(id);
      
      // Emit event
      this.eventBus.emit(EventType.LINEAR_ATTACHMENTS_DELETE_ATTACHMENT, {
        id,
        success: result.success
      });
      
      return result.success;
    } catch (error) {
      this.handleError(error, `Failed to delete attachment with ID ${id}`);
      return false;
    }
  }
  
  /**
   * Get all attachments
   * @param attachments Attachment connection
   * @returns Attachments
   */
  private async getAllAttachments(attachments: AttachmentConnection): Promise<Attachment[]> {
    const allAttachments: Attachment[] = [];
    let currentPage = attachments;
    
    while (true) {
      const nodes = await currentPage.nodes;
      allAttachments.push(...nodes);
      
      if (!(await currentPage.hasNextPage)) {
        break;
      }
      
      currentPage = await currentPage.next();
    }
    
    return allAttachments;
  }
  
  /**
   * Handle error
   * @param error Error
   * @param message Error message
   */
  private handleError(error: any, message: string): void {
    console.error(message, error);
    
    // Emit event
    this.eventBus.emit(EventType.LINEAR_ATTACHMENTS_ERROR, {
      error,
      message
    });
  }
}

/**
 * Create Linear attachments
 * @param options Linear attachments options
 * @returns Linear attachments
 */
export function createLinearAttachments(options: LinearAttachmentsOptions = {}): LinearAttachments {
  return new LinearAttachments(options);
}
