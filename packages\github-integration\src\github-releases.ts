/**
 * GitHub releases for GitHub integration
 */

import { Octokit } from '@octokit/rest';
import { GitHubReleasesOptions, GitHubRelease, GitHubTag, GitHubReleaseFilter, GitHubReleaseType } from './types';
import { EventBus, EventType } from '@qawolf/core';

/**
 * GitHub releases
 */
export class GitHubReleases {
  /**
   * API token
   */
  private token: string;
  
  /**
   * Owner
   */
  private owner: string;
  
  /**
   * Repository
   */
  private repo: string;
  
  /**
   * Base URL
   */
  private baseUrl: string;
  
  /**
   * Timeout in milliseconds
   */
  private timeout: number;
  
  /**
   * Octokit instance
   */
  private octokit: Octokit;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options GitHub releases options
   */
  constructor(options: GitHubReleasesOptions = {}) {
    this.token = options.token || '';
    this.owner = options.owner || '';
    this.repo = options.repo || '';
    this.baseUrl = options.baseUrl || 'https://api.github.com';
    this.timeout = options.timeout || 30000;
    
    this.octokit = new Octokit({
      auth: this.token,
      baseUrl: this.baseUrl,
      request: {
        timeout: this.timeout
      }
    });
    
    this.eventBus = EventBus.getInstance();
  }
  
  /**
   * Get releases
   * @param filter Release filter
   * @param owner Owner
   * @param repo Repository
   * @returns Releases
   */
  async getReleases(filter?: GitHubReleaseFilter, owner?: string, repo?: string): Promise<GitHubRelease[]> {
    try {
      const response = await this.octokit.repos.listReleases({
        owner: owner || this.owner,
        repo: repo || this.repo
      });
      
      let releases = response.data;
      
      // Filter by type
      if (filter?.type) {
        if (filter.type === GitHubReleaseType.DRAFT) {
          releases = releases.filter(release => release.draft);
        } else if (filter.type === GitHubReleaseType.PRERELEASE) {
          releases = releases.filter(release => release.prerelease);
        } else if (filter.type === GitHubReleaseType.RELEASE) {
          releases = releases.filter(release => !release.draft && !release.prerelease);
        }
      }
      
      // Sort
      if (filter?.sort) {
        releases = releases.sort((a, b) => {
          if (filter.sort === 'created') {
            return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
          } else if (filter.sort === 'updated') {
            return new Date(a.published_at || a.created_at).getTime() - new Date(b.published_at || b.created_at).getTime();
          }
          
          return 0;
        });
      }
      
      // Direction
      if (filter?.direction === 'desc') {
        releases = releases.reverse();
      }
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_RELEASES_GET_RELEASES, {
        releases
      });
      
      return releases;
    } catch (error) {
      this.handleError(error, `Failed to get releases for repository ${owner || this.owner}/${repo || this.repo}`);
      return [];
    }
  }
  
  /**
   * Get release
   * @param releaseId Release ID
   * @param owner Owner
   * @param repo Repository
   * @returns Release
   */
  async getRelease(releaseId: number, owner?: string, repo?: string): Promise<GitHubRelease | null> {
    try {
      const response = await this.octokit.repos.getRelease({
        owner: owner || this.owner,
        repo: repo || this.repo,
        release_id: releaseId
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_RELEASES_GET_RELEASE, {
        release: response.data
      });
      
      return response.data;
    } catch (error) {
      this.handleError(error, `Failed to get release ${releaseId} for repository ${owner || this.owner}/${repo || this.repo}`);
      return null;
    }
  }
  
  /**
   * Get release by tag
   * @param tag Tag
   * @param owner Owner
   * @param repo Repository
   * @returns Release
   */
  async getReleaseByTag(tag: string, owner?: string, repo?: string): Promise<GitHubRelease | null> {
    try {
      const response = await this.octokit.repos.getReleaseByTag({
        owner: owner || this.owner,
        repo: repo || this.repo,
        tag
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_RELEASES_GET_RELEASE_BY_TAG, {
        release: response.data
      });
      
      return response.data;
    } catch (error) {
      this.handleError(error, `Failed to get release by tag ${tag} for repository ${owner || this.owner}/${repo || this.repo}`);
      return null;
    }
  }
  
  /**
   * Get latest release
   * @param owner Owner
   * @param repo Repository
   * @returns Latest release
   */
  async getLatestRelease(owner?: string, repo?: string): Promise<GitHubRelease | null> {
    try {
      const response = await this.octokit.repos.getLatestRelease({
        owner: owner || this.owner,
        repo: repo || this.repo
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_RELEASES_GET_LATEST_RELEASE, {
        release: response.data
      });
      
      return response.data;
    } catch (error) {
      this.handleError(error, `Failed to get latest release for repository ${owner || this.owner}/${repo || this.repo}`);
      return null;
    }
  }
  
  /**
   * Create release
   * @param tag Tag
   * @param name Name
   * @param body Body
   * @param draft Draft
   * @param prerelease Prerelease
   * @param targetCommitish Target commitish
   * @param owner Owner
   * @param repo Repository
   * @returns Release
   */
  async createRelease(tag: string, name?: string, body?: string, draft?: boolean, prerelease?: boolean, targetCommitish?: string, owner?: string, repo?: string): Promise<GitHubRelease | null> {
    try {
      const response = await this.octokit.repos.createRelease({
        owner: owner || this.owner,
        repo: repo || this.repo,
        tag_name: tag,
        name: name || tag,
        body,
        draft,
        prerelease,
        target_commitish: targetCommitish
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_RELEASES_CREATE_RELEASE, {
        release: response.data
      });
      
      return response.data;
    } catch (error) {
      this.handleError(error, `Failed to create release ${tag} for repository ${owner || this.owner}/${repo || this.repo}`);
      return null;
    }
  }
  
  /**
   * Update release
   * @param releaseId Release ID
   * @param tag Tag
   * @param name Name
   * @param body Body
   * @param draft Draft
   * @param prerelease Prerelease
   * @param targetCommitish Target commitish
   * @param owner Owner
   * @param repo Repository
   * @returns Release
   */
  async updateRelease(releaseId: number, tag?: string, name?: string, body?: string, draft?: boolean, prerelease?: boolean, targetCommitish?: string, owner?: string, repo?: string): Promise<GitHubRelease | null> {
    try {
      const response = await this.octokit.repos.updateRelease({
        owner: owner || this.owner,
        repo: repo || this.repo,
        release_id: releaseId,
        tag_name: tag,
        name,
        body,
        draft,
        prerelease,
        target_commitish: targetCommitish
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_RELEASES_UPDATE_RELEASE, {
        release: response.data
      });
      
      return response.data;
    } catch (error) {
      this.handleError(error, `Failed to update release ${releaseId} for repository ${owner || this.owner}/${repo || this.repo}`);
      return null;
    }
  }
  
  /**
   * Delete release
   * @param releaseId Release ID
   * @param owner Owner
   * @param repo Repository
   * @returns True if deleted, false otherwise
   */
  async deleteRelease(releaseId: number, owner?: string, repo?: string): Promise<boolean> {
    try {
      await this.octokit.repos.deleteRelease({
        owner: owner || this.owner,
        repo: repo || this.repo,
        release_id: releaseId
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_RELEASES_DELETE_RELEASE, {
        releaseId
      });
      
      return true;
    } catch (error) {
      this.handleError(error, `Failed to delete release ${releaseId} for repository ${owner || this.owner}/${repo || this.repo}`);
      return false;
    }
  }
  
  /**
   * Get tags
   * @param owner Owner
   * @param repo Repository
   * @returns Tags
   */
  async getTags(owner?: string, repo?: string): Promise<GitHubTag[]> {
    try {
      const response = await this.octokit.repos.listTags({
        owner: owner || this.owner,
        repo: repo || this.repo
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_RELEASES_GET_TAGS, {
        tags: response.data
      });
      
      return response.data as GitHubTag[];
    } catch (error) {
      this.handleError(error, `Failed to get tags for repository ${owner || this.owner}/${repo || this.repo}`);
      return [];
    }
  }
  
  /**
   * Get tag
   * @param sha SHA
   * @param owner Owner
   * @param repo Repository
   * @returns Tag
   */
  async getTag(sha: string, owner?: string, repo?: string): Promise<GitHubTag | null> {
    try {
      const response = await this.octokit.git.getTag({
        owner: owner || this.owner,
        repo: repo || this.repo,
        tag_sha: sha
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_RELEASES_GET_TAG, {
        tag: response.data
      });
      
      return response.data as GitHubTag;
    } catch (error) {
      this.handleError(error, `Failed to get tag ${sha} for repository ${owner || this.owner}/${repo || this.repo}`);
      return null;
    }
  }
  
  /**
   * Create tag
   * @param tag Tag
   * @param message Message
   * @param object Object
   * @param type Type
   * @param tagger Tagger
   * @param owner Owner
   * @param repo Repository
   * @returns Tag
   */
  async createTag(tag: string, message: string, object: string, type: 'commit' | 'tree' | 'blob', tagger?: { name: string; email: string; date?: string }, owner?: string, repo?: string): Promise<GitHubTag | null> {
    try {
      const response = await this.octokit.git.createTag({
        owner: owner || this.owner,
        repo: repo || this.repo,
        tag,
        message,
        object,
        type,
        tagger
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_RELEASES_CREATE_TAG, {
        tag: response.data
      });
      
      return response.data as GitHubTag;
    } catch (error) {
      this.handleError(error, `Failed to create tag ${tag} for repository ${owner || this.owner}/${repo || this.repo}`);
      return null;
    }
  }
  
  /**
   * Handle error
   * @param error Error
   * @param message Error message
   */
  private handleError(error: any, message: string): void {
    console.error(message, error);
    
    // Emit event
    this.eventBus.emit(EventType.GITHUB_RELEASES_ERROR, {
      error,
      message
    });
  }
  
  /**
   * Get token
   * @returns API token
   */
  getToken(): string {
    return this.token;
  }
  
  /**
   * Get owner
   * @returns Owner
   */
  getOwner(): string {
    return this.owner;
  }
  
  /**
   * Get repository
   * @returns Repository
   */
  getRepo(): string {
    return this.repo;
  }
  
  /**
   * Get base URL
   * @returns Base URL
   */
  getBaseUrl(): string {
    return this.baseUrl;
  }
  
  /**
   * Get timeout
   * @returns Timeout in milliseconds
   */
  getTimeout(): number {
    return this.timeout;
  }
  
  /**
   * Set token
   * @param token API token
   * @returns This instance for chaining
   */
  setToken(token: string): GitHubReleases {
    this.token = token;
    
    this.octokit = new Octokit({
      auth: this.token,
      baseUrl: this.baseUrl,
      request: {
        timeout: this.timeout
      }
    });
    
    return this;
  }
  
  /**
   * Set owner
   * @param owner Owner
   * @returns This instance for chaining
   */
  setOwner(owner: string): GitHubReleases {
    this.owner = owner;
    return this;
  }
  
  /**
   * Set repository
   * @param repo Repository
   * @returns This instance for chaining
   */
  setRepo(repo: string): GitHubReleases {
    this.repo = repo;
    return this;
  }
  
  /**
   * Set base URL
   * @param baseUrl Base URL
   * @returns This instance for chaining
   */
  setBaseUrl(baseUrl: string): GitHubReleases {
    this.baseUrl = baseUrl;
    
    this.octokit = new Octokit({
      auth: this.token,
      baseUrl: this.baseUrl,
      request: {
        timeout: this.timeout
      }
    });
    
    return this;
  }
  
  /**
   * Set timeout
   * @param timeout Timeout in milliseconds
   * @returns This instance for chaining
   */
  setTimeout(timeout: number): GitHubReleases {
    this.timeout = timeout;
    
    this.octokit = new Octokit({
      auth: this.token,
      baseUrl: this.baseUrl,
      request: {
        timeout: this.timeout
      }
    });
    
    return this;
  }
}

/**
 * Create GitHub releases
 * @param options GitHub releases options
 * @returns GitHub releases
 */
export function createGitHubReleases(options: GitHubReleasesOptions = {}): GitHubReleases {
  return new GitHubReleases(options);
}
