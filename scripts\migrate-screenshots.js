/**
 * Screenshot Migration Script
 * 
 * This script migrates existing screenshots to the new directory structure.
 * It finds all PNG files in the project directory and moves them to the screenshots directory.
 * 
 * Usage:
 * node migrate-screenshots.js
 * 
 * @module migrate-screenshots
 * <AUTHOR> Wolf Team
 * @date 2025-01-01
 */

const { moveStrayScreenshots } = require('../src/utils/screenshot-utils');

async function main() {
  try {
    console.log('Migrating existing screenshots to the new directory structure...');
    
    // Move stray screenshots
    const movedCount = await moveStrayScreenshots('migration');
    
    if (movedCount > 0) {
      console.log(`Successfully migrated ${movedCount} screenshots`);
    } else {
      console.log('No screenshots to migrate');
    }
  } catch (error) {
    console.error('Screenshot migration failed:', error);
    process.exit(1);
  }
}

// Run the script
main();
