/**
 * Performance tracker for QA Wolf Metrics Framework
 */

import { v4 as uuidv4 } from 'uuid';
import { PerformanceMetrics, PerformanceTrackerOptions, Operation, OperationType, OperationMetadata, ResourceUsage } from './types';
import { OperationTracker } from './operation-tracker';
import { ResourceMonitor } from './resource-monitor';
import { calculateOperationStats, calculateResourceStats } from './utils';
import { writeJsonToFile, ensureDirectoryExists } from '@qawolf/shared-utils';
import { EventBus, EventType } from '@qawolf/core';

/**
 * Performance tracker
 */
export class PerformanceTracker {
  /**
   * Test name
   */
  private testName: string;
  
  /**
   * Test ID
   */
  private testId: string;
  
  /**
   * Start time
   */
  private startTime: number | null = null;
  
  /**
   * End time
   */
  private endTime: number | null = null;
  
  /**
   * Operation tracker
   */
  private operationTracker: OperationTracker;
  
  /**
   * Resource monitor
   */
  private resourceMonitor: ResourceMonitor;
  
  /**
   * Whether to save metrics to file
   */
  private saveMetrics: boolean;
  
  /**
   * Metrics file path
   */
  private metricsFilePath: string;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options Performance tracker options
   */
  constructor(options: PerformanceTrackerOptions = {}) {
    this.testName = options.testName || 'unknown';
    this.testId = options.testId || uuidv4();
    this.saveMetrics = options.saveMetrics !== false;
    this.metricsFilePath = options.metricsFilePath || `performance-reports/${this.testId}.json`;
    
    this.operationTracker = new OperationTracker();
    this.resourceMonitor = new ResourceMonitor({
      monitorResources: options.monitorResources !== false,
      monitorInterval: options.resourceMonitorInterval || 1000
    });
    
    this.eventBus = EventBus.getInstance();
  }
  
  /**
   * Start tracking performance
   * @returns This instance for chaining
   */
  start(): PerformanceTracker {
    this.startTime = Date.now();
    this.resourceMonitor.start();
    
    // Emit event
    this.eventBus.emit(EventType.PERFORMANCE_TRACKING_STARTED, {
      testName: this.testName,
      testId: this.testId,
      startTime: this.startTime
    });
    
    return this;
  }
  
  /**
   * Stop tracking performance
   * @returns Performance metrics
   */
  stop(): PerformanceMetrics {
    this.endTime = Date.now();
    this.resourceMonitor.stop();
    
    const metrics = this.getMetrics();
    
    // Save metrics to file
    if (this.saveMetrics) {
      this.saveMetricsToFile(metrics);
    }
    
    // Emit event
    this.eventBus.emit(EventType.PERFORMANCE_TRACKING_STOPPED, {
      testName: this.testName,
      testId: this.testId,
      endTime: this.endTime,
      metrics
    });
    
    return metrics;
  }
  
  /**
   * Track operation
   * @param operation Operation to track
   * @returns This instance for chaining
   */
  trackOperation(operation: Omit<Operation, 'startTime' | 'endTime'>): PerformanceTracker {
    this.operationTracker.trackOperation({
      ...operation,
      startTime: Date.now() - (operation.duration || 0),
      endTime: Date.now()
    });
    
    return this;
  }
  
  /**
   * Start operation
   * @param name Operation name
   * @param type Operation type
   * @param metadata Operation metadata
   * @returns Operation ID
   */
  startOperation(name: string, type: OperationType | string, metadata?: OperationMetadata): string {
    return this.operationTracker.startOperation(name, type, metadata);
  }
  
  /**
   * End operation
   * @param operationId Operation ID
   * @returns Operation
   */
  endOperation(operationId: string): Operation {
    return this.operationTracker.endOperation(operationId);
  }
  
  /**
   * Track navigation
   * @param url URL
   * @param duration Duration in milliseconds
   * @param metadata Operation metadata
   * @returns This instance for chaining
   */
  trackNavigation(url: string, duration: number, metadata?: OperationMetadata): PerformanceTracker {
    return this.trackOperation({
      name: `Navigate to ${url}`,
      type: OperationType.NAVIGATION,
      duration,
      metadata: {
        url,
        ...metadata
      }
    });
  }
  
  /**
   * Track click
   * @param selector Selector
   * @param duration Duration in milliseconds
   * @param metadata Operation metadata
   * @returns This instance for chaining
   */
  trackClick(selector: string, duration: number, metadata?: OperationMetadata): PerformanceTracker {
    return this.trackOperation({
      name: `Click ${selector}`,
      type: OperationType.CLICK,
      duration,
      metadata: {
        selector,
        ...metadata
      }
    });
  }
  
  /**
   * Track fill
   * @param selector Selector
   * @param value Value
   * @param duration Duration in milliseconds
   * @param metadata Operation metadata
   * @returns This instance for chaining
   */
  trackFill(selector: string, value: string, duration: number, metadata?: OperationMetadata): PerformanceTracker {
    return this.trackOperation({
      name: `Fill ${selector}`,
      type: OperationType.FILL,
      duration,
      metadata: {
        selector,
        value,
        ...metadata
      }
    });
  }
  
  /**
   * Track select
   * @param selector Selector
   * @param value Value
   * @param duration Duration in milliseconds
   * @param metadata Operation metadata
   * @returns This instance for chaining
   */
  trackSelect(selector: string, value: string, duration: number, metadata?: OperationMetadata): PerformanceTracker {
    return this.trackOperation({
      name: `Select ${selector}`,
      type: OperationType.SELECT,
      duration,
      metadata: {
        selector,
        value,
        ...metadata
      }
    });
  }
  
  /**
   * Track hover
   * @param selector Selector
   * @param duration Duration in milliseconds
   * @param metadata Operation metadata
   * @returns This instance for chaining
   */
  trackHover(selector: string, duration: number, metadata?: OperationMetadata): PerformanceTracker {
    return this.trackOperation({
      name: `Hover ${selector}`,
      type: OperationType.HOVER,
      duration,
      metadata: {
        selector,
        ...metadata
      }
    });
  }
  
  /**
   * Track wait
   * @param description Wait description
   * @param duration Duration in milliseconds
   * @param metadata Operation metadata
   * @returns This instance for chaining
   */
  trackWait(description: string, duration: number, metadata?: OperationMetadata): PerformanceTracker {
    return this.trackOperation({
      name: `Wait for ${description}`,
      type: OperationType.WAIT,
      duration,
      metadata
    });
  }
  
  /**
   * Track screenshot
   * @param name Screenshot name
   * @param duration Duration in milliseconds
   * @param metadata Operation metadata
   * @returns This instance for chaining
   */
  trackScreenshot(name: string, duration: number, metadata?: OperationMetadata): PerformanceTracker {
    return this.trackOperation({
      name: `Screenshot ${name}`,
      type: OperationType.SCREENSHOT,
      duration,
      metadata
    });
  }
  
  /**
   * Track assertion
   * @param description Assertion description
   * @param duration Duration in milliseconds
   * @param metadata Operation metadata
   * @returns This instance for chaining
   */
  trackAssertion(description: string, duration: number, metadata?: OperationMetadata): PerformanceTracker {
    return this.trackOperation({
      name: `Assert ${description}`,
      type: OperationType.ASSERTION,
      duration,
      metadata
    });
  }
  
  /**
   * Track custom operation
   * @param name Operation name
   * @param duration Duration in milliseconds
   * @param metadata Operation metadata
   * @returns This instance for chaining
   */
  trackCustomOperation(name: string, duration: number, metadata?: OperationMetadata): PerformanceTracker {
    return this.trackOperation({
      name,
      type: OperationType.CUSTOM,
      duration,
      metadata
    });
  }
  
  /**
   * Get operations
   * @returns Operations
   */
  getOperations(): Operation[] {
    return this.operationTracker.getOperations();
  }
  
  /**
   * Get resource usage
   * @returns Resource usage
   */
  getResourceUsage(): ResourceUsage[] {
    return this.resourceMonitor.getResourceUsage();
  }
  
  /**
   * Get metrics
   * @returns Performance metrics
   */
  getMetrics(): PerformanceMetrics {
    if (!this.startTime) {
      throw new Error('Performance tracking has not been started');
    }
    
    const endTime = this.endTime || Date.now();
    const duration = endTime - this.startTime;
    
    // Get operations
    const operations = this.getOperations();
    
    // Calculate operation statistics
    const operationStats = calculateOperationStats(operations);
    
    // Get resource usage
    const resourceUsage = this.getResourceUsage();
    
    // Calculate resource statistics
    const resourceStats = calculateResourceStats(resourceUsage);
    
    return {
      testName: this.testName,
      testId: this.testId,
      startTime: this.startTime,
      endTime,
      duration,
      operations,
      operationStats,
      resourceUsage,
      resourceStats
    };
  }
  
  /**
   * Save metrics to file
   * @param metrics Performance metrics
   */
  private saveMetricsToFile(metrics: PerformanceMetrics): void {
    try {
      // Create directory if it doesn't exist
      ensureDirectoryExists(this.metricsFilePath.substring(0, this.metricsFilePath.lastIndexOf('/')));
      
      // Write metrics to file
      writeJsonToFile(this.metricsFilePath, metrics);
    } catch (error) {
      console.error('Error saving metrics to file:', error);
    }
  }
}

/**
 * Create performance tracker
 * @param options Performance tracker options
 * @returns Performance tracker
 */
export function createPerformanceTracker(options: PerformanceTrackerOptions = {}): PerformanceTracker {
  return new PerformanceTracker(options);
}
