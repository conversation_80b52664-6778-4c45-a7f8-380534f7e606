/**
 * Playwright MCP Integration
 * 
 * This module provides integration with Playwright MCP,
 * allowing for AI-assisted testing and debugging.
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');
const http = require('http');

/**
 * Start the Playwright MCP server
 * @param {Object} options - Server options
 * @param {number} options.port - Port to run the server on
 * @param {boolean} options.headless - Whether to run in headless mode
 * @param {string} options.configPath - Path to the config file
 * @returns {Promise<Object>} - Server process and info
 */
async function startPlaywrightMCP(options = {}) {
  const defaultOptions = {
    port: 8932,
    headless: false,
    configPath: path.join(process.cwd(), 'config/playwright-mcp-config.json')
  };

  const serverOptions = { ...defaultOptions, ...options };

  // Check if config file exists
  if (!fs.existsSync(serverOptions.configPath)) {
    throw new Error(`Config file not found: ${serverOptions.configPath}`);
  }

  // Build the command arguments
  const mcpArgs = [
    '@playwright/mcp@latest',
    '--port', serverOptions.port.toString(),
    '--config', serverOptions.configPath
  ];

  if (serverOptions.headless) {
    mcpArgs.push('--headless');
  }

  if (serverOptions.vision) {
    mcpArgs.push('--vision');
  }

  console.log('Starting Playwright MCP server with options:', serverOptions);

  // Start the MCP server
  const mcpProcess = spawn('npx', mcpArgs, { 
    stdio: ['ignore', 'pipe', 'pipe'],
    shell: true,
    detached: true
  });

  // Collect output
  let output = '';
  mcpProcess.stdout.on('data', (data) => {
    output += data.toString();
    console.log('[Playwright MCP]', data.toString().trim());
  });

  mcpProcess.stderr.on('data', (data) => {
    output += data.toString();
    console.error('[Playwright MCP Error]', data.toString().trim());
  });

  // Wait for server to start
  await new Promise((resolve, reject) => {
    // Set a timeout
    const timeout = setTimeout(() => {
      reject(new Error('Timeout waiting for Playwright MCP server to start'));
    }, 30000);

    // Check if server is running
    const checkInterval = setInterval(() => {
      http.get(`http://localhost:${serverOptions.port}/health`, (res) => {
        if (res.statusCode === 200) {
          clearInterval(checkInterval);
          clearTimeout(timeout);
          resolve();
        }
      }).on('error', () => {
        // Server not ready yet, continue waiting
      });
    }, 1000);

    // Handle process errors
    mcpProcess.on('error', (error) => {
      clearInterval(checkInterval);
      clearTimeout(timeout);
      reject(error);
    });

    mcpProcess.on('exit', (code) => {
      if (code !== 0) {
        clearInterval(checkInterval);
        clearTimeout(timeout);
        reject(new Error(`Playwright MCP server exited with code ${code}\nOutput: ${output}`));
      }
    });
  });

  return {
    process: mcpProcess,
    port: serverOptions.port,
    url: `http://localhost:${serverOptions.port}`,
    pid: mcpProcess.pid,
    stop: () => {
      if (mcpProcess && !mcpProcess.killed) {
        mcpProcess.kill();
      }
    }
  };
}

/**
 * Check if Playwright MCP server is running
 * @param {number} port - Port to check
 * @returns {Promise<boolean>} - Whether the server is running
 */
async function isPlaywrightMCPRunning(port = 8932) {
  return new Promise((resolve) => {
    http.get(`http://localhost:${port}/health`, (res) => {
      resolve(res.statusCode === 200);
    }).on('error', () => {
      resolve(false);
    });
  });
}

/**
 * Generate a Playwright test from a QA Wolf test
 * @param {string} qaWolfTestPath - Path to the QA Wolf test
 * @param {Object} options - Options for test generation
 * @returns {Promise<string>} - Generated Playwright test
 */
async function generatePlaywrightTest(qaWolfTestPath, options = {}) {
  // Implementation will depend on how we want to convert QA Wolf tests to Playwright tests
  // This is a placeholder for now
  console.log(`Generating Playwright test from QA Wolf test: ${qaWolfTestPath}`);
  return "// Generated Playwright test\n// This is a placeholder";
}

/**
 * Generate an optimized selector using Playwright MCP
 * @param {string} selector - Original selector
 * @param {Object} pageContext - Page context
 * @returns {Promise<string|null>} - Optimized selector or null if generation failed
 */
async function generateOptimizedSelector(selector, pageContext, options = {}) {
  try {
    // This is a placeholder implementation
    // In a real implementation, this would call the Playwright MCP API
    
    console.log(`Generating optimized selector for: ${selector}`);
    
    // Simulate a delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Return a slightly modified selector as a placeholder
    return selector.includes('[') ? selector : `[data-testid="${selector.replace(/[^a-zA-Z0-9]/g, '-')}"]`;
  } catch (error) {
    console.error('Error generating optimized selector:', error);
    return null;
  }
}

module.exports = {
  startPlaywrightMCP,
  isPlaywrightMCPRunning,
  generatePlaywrightTest,
  generateOptimizedSelector
};