/**
 * Screenshot storage for screenshot manager
 */

import * as fs from 'fs';
import * as path from 'path';
import { ScreenshotFormat, ScreenshotInfo, ScreenshotStorageOptions } from './types';
import { getScreenshotInfo } from './utils';
import { ensureDirectoryExists, fileExists, listFiles, getFileModificationTime } from '@qawolf/shared-utils';
import { EventBus, EventType } from '@qawolf/core';

/**
 * Screenshot save options
 */
interface ScreenshotSaveOptions {
  /**
   * Screenshot ID
   */
  id: string;
  
  /**
   * Screenshot name
   */
  name: string;
  
  /**
   * Screenshot format
   */
  format: ScreenshotFormat;
  
  /**
   * Test name
   */
  testName?: string;
  
  /**
   * Test ID
   */
  testId?: string;
  
  /**
   * Browser name
   */
  browserName?: string;
  
  /**
   * Browser version
   */
  browserVersion?: string;
}

/**
 * Screenshot storage
 */
export class ScreenshotStorage {
  /**
   * Base directory for screenshots
   */
  private baseDir: string;
  
  /**
   * Whether to organize screenshots by date
   */
  private organizeByDate: boolean;
  
  /**
   * Whether to organize screenshots by test name
   */
  private organizeByTestName: boolean;
  
  /**
   * Whether to organize screenshots by test ID
   */
  private organizeByTestId: boolean;
  
  /**
   * Whether to organize screenshots by browser
   */
  private organizeByBrowser: boolean;
  
  /**
   * Maximum age of screenshots in days
   */
  private maxAge: number;
  
  /**
   * Maximum number of screenshots to keep
   */
  private maxCount?: number;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options Screenshot storage options
   */
  constructor(options: ScreenshotStorageOptions = {}) {
    this.baseDir = options.baseDir || 'screenshots';
    this.organizeByDate = options.organizeByDate !== false;
    this.organizeByTestName = options.organizeByTestName !== false;
    this.organizeByTestId = options.organizeByTestId !== false;
    this.organizeByBrowser = options.organizeByBrowser !== false;
    this.maxAge = options.maxAge || 7;
    this.maxCount = options.maxCount;
    this.eventBus = EventBus.getInstance();
    
    // Create base directory if it doesn't exist
    ensureDirectoryExists(this.baseDir);
  }
  
  /**
   * Save screenshot
   * @param buffer Screenshot buffer
   * @param options Screenshot save options
   * @returns Screenshot path
   */
  async saveScreenshot(buffer: Buffer, options: ScreenshotSaveOptions): Promise<string> {
    // Get screenshot directory
    const screenshotDir = this.getScreenshotDirectory(options);
    
    // Create directory if it doesn't exist
    ensureDirectoryExists(screenshotDir);
    
    // Get screenshot file name
    const screenshotFileName = this.getScreenshotFileName(options);
    
    // Get screenshot path
    const screenshotPath = path.join(screenshotDir, screenshotFileName);
    
    // Write screenshot to file
    fs.writeFileSync(screenshotPath, buffer);
    
    // Emit event
    this.eventBus.emit(EventType.SCREENSHOT_SAVED, {
      path: screenshotPath,
      options
    });
    
    return screenshotPath;
  }
  
  /**
   * Get screenshot
   * @param id Screenshot ID
   * @returns Screenshot buffer or null if not found
   */
  async getScreenshot(id: string): Promise<Buffer | null> {
    const screenshotPath = await this.findScreenshotPath(id);
    
    if (!screenshotPath) {
      return null;
    }
    
    return fs.readFileSync(screenshotPath);
  }
  
  /**
   * Get screenshot info
   * @param id Screenshot ID
   * @returns Screenshot info or null if not found
   */
  async getScreenshotInfo(id: string): Promise<ScreenshotInfo | null> {
    const screenshotPath = await this.findScreenshotPath(id);
    
    if (!screenshotPath) {
      return null;
    }
    
    return getScreenshotInfo(screenshotPath);
  }
  
  /**
   * Delete screenshot
   * @param id Screenshot ID
   * @returns True if screenshot was deleted, false otherwise
   */
  async deleteScreenshot(id: string): Promise<boolean> {
    const screenshotPath = await this.findScreenshotPath(id);
    
    if (!screenshotPath) {
      return false;
    }
    
    fs.unlinkSync(screenshotPath);
    
    // Emit event
    this.eventBus.emit(EventType.SCREENSHOT_DELETED, {
      id,
      path: screenshotPath
    });
    
    return true;
  }
  
  /**
   * List screenshots
   * @returns Screenshot info list
   */
  async listScreenshots(): Promise<ScreenshotInfo[]> {
    const screenshotFiles = this.findScreenshotFiles();
    const screenshotInfos: ScreenshotInfo[] = [];
    
    for (const screenshotFile of screenshotFiles) {
      try {
        const screenshotInfo = await getScreenshotInfo(screenshotFile);
        screenshotInfos.push(screenshotInfo);
      } catch (error) {
        console.error(`Error getting screenshot info for ${screenshotFile}:`, error);
      }
    }
    
    return screenshotInfos;
  }
  
  /**
   * Clean up old screenshots
   * @param maxAge Maximum age in days
   * @returns Number of deleted screenshots
   */
  async cleanupOldScreenshots(maxAge?: number): Promise<number> {
    const maxAgeMs = (maxAge || this.maxAge) * 24 * 60 * 60 * 1000;
    const now = Date.now();
    const screenshotFiles = this.findScreenshotFiles();
    let deletedCount = 0;
    
    for (const screenshotFile of screenshotFiles) {
      try {
        const modificationTime = getFileModificationTime(screenshotFile).getTime();
        
        if (now - modificationTime > maxAgeMs) {
          fs.unlinkSync(screenshotFile);
          deletedCount++;
          
          // Emit event
          this.eventBus.emit(EventType.SCREENSHOT_DELETED, {
            path: screenshotFile
          });
        }
      } catch (error) {
        console.error(`Error cleaning up screenshot ${screenshotFile}:`, error);
      }
    }
    
    return deletedCount;
  }
  
  /**
   * Enforce maximum screenshot count
   * @param maxCount Maximum number of screenshots to keep
   * @returns Number of deleted screenshots
   */
  async enforceMaxCount(maxCount?: number): Promise<number> {
    const limit = maxCount || this.maxCount;
    
    if (!limit) {
      return 0;
    }
    
    const screenshotFiles = this.findScreenshotFiles();
    
    if (screenshotFiles.length <= limit) {
      return 0;
    }
    
    // Sort screenshot files by modification time (oldest first)
    const sortedFiles = screenshotFiles.map(file => ({
      path: file,
      modificationTime: getFileModificationTime(file).getTime()
    })).sort((a, b) => a.modificationTime - b.modificationTime);
    
    // Delete oldest screenshots
    const filesToDelete = sortedFiles.slice(0, sortedFiles.length - limit);
    let deletedCount = 0;
    
    for (const file of filesToDelete) {
      try {
        fs.unlinkSync(file.path);
        deletedCount++;
        
        // Emit event
        this.eventBus.emit(EventType.SCREENSHOT_DELETED, {
          path: file.path
        });
      } catch (error) {
        console.error(`Error deleting screenshot ${file.path}:`, error);
      }
    }
    
    return deletedCount;
  }
  
  /**
   * Find screenshot path
   * @param id Screenshot ID
   * @returns Screenshot path or null if not found
   */
  private async findScreenshotPath(id: string): Promise<string | null> {
    const screenshotFiles = this.findScreenshotFiles();
    
    for (const screenshotFile of screenshotFiles) {
      if (screenshotFile.includes(id)) {
        return screenshotFile;
      }
    }
    
    return null;
  }
  
  /**
   * Find screenshot files
   * @returns Screenshot files
   */
  private findScreenshotFiles(): string[] {
    return listFiles(this.baseDir, true).filter(file => {
      const extension = path.extname(file).toLowerCase();
      return extension === '.png' || extension === '.jpg' || extension === '.jpeg';
    });
  }
  
  /**
   * Get screenshot directory
   * @param options Screenshot save options
   * @returns Screenshot directory
   */
  private getScreenshotDirectory(options: ScreenshotSaveOptions): string {
    const pathParts: string[] = [this.baseDir];
    
    // Add date
    if (this.organizeByDate) {
      const date = new Date();
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      
      pathParts.push(`${year}-${month}-${day}`);
    }
    
    // Add test name
    if (this.organizeByTestName && options.testName) {
      pathParts.push(options.testName.replace(/[^a-zA-Z0-9_-]/g, '_'));
    }
    
    // Add test ID
    if (this.organizeByTestId && options.testId) {
      pathParts.push(options.testId);
    }
    
    // Add browser
    if (this.organizeByBrowser && options.browserName) {
      pathParts.push(options.browserName.toLowerCase());
    }
    
    return path.join(...pathParts);
  }
  
  /**
   * Get screenshot file name
   * @param options Screenshot save options
   * @returns Screenshot file name
   */
  private getScreenshotFileName(options: ScreenshotSaveOptions): string {
    const timestamp = Date.now();
    const extension = options.format === ScreenshotFormat.JPEG ? 'jpg' : 'png';
    
    return `${options.name.replace(/[^a-zA-Z0-9_-]/g, '_')}_${options.id}_${timestamp}.${extension}`;
  }
  
  /**
   * Get base directory
   * @returns Base directory
   */
  getBaseDir(): string {
    return this.baseDir;
  }
  
  /**
   * Set base directory
   * @param baseDir Base directory
   * @returns This instance for chaining
   */
  setBaseDir(baseDir: string): ScreenshotStorage {
    this.baseDir = baseDir;
    
    // Create base directory if it doesn't exist
    ensureDirectoryExists(this.baseDir);
    
    return this;
  }
}

/**
 * Create screenshot storage
 * @param options Screenshot storage options
 * @returns Screenshot storage
 */
export function createScreenshotStorage(options: ScreenshotStorageOptions = {}): ScreenshotStorage {
  return new ScreenshotStorage(options);
}
