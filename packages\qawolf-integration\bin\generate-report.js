#!/usr/bin/env node

/**
 * QA Wolf Report Generator CLI
 * 
 * This script generates a report from QA Wolf test results.
 */

const { createQAWolfReportGenerator } = require('../dist/report-generator');

// Parse command line arguments
const args = process.argv.slice(2);
const options = {};

for (let i = 0; i < args.length; i++) {
  const arg = args[i];
  
  if (arg === '--api-key' && i + 1 < args.length) {
    options.apiKey = args[++i];
  } else if (arg === '--team-id' && i + 1 < args.length) {
    options.teamId = args[++i];
  } else if (arg === '--api-url' && i + 1 < args.length) {
    options.apiUrl = args[++i];
  } else if (arg === '--reports-dir' && i + 1 < args.length) {
    options.reportsDir = args[++i];
  } else if (arg === '--environment' && i + 1 < args.length) {
    options.environment = args[++i];
  } else if (arg === '--branch' && i + 1 < args.length) {
    options.branch = args[++i];
  } else if (arg === '--title' && i + 1 < args.length) {
    options.title = args[++i];
  } else if (arg === '--description' && i + 1 < args.length) {
    options.description = args[++i];
  } else if (arg === '--format' && i + 1 < args.length) {
    options.format = args[++i];
  } else if (arg === '--template' && i + 1 < args.length) {
    options.template = args[++i];
  }
}

// Create report generator instance
const reportGenerator = createQAWolfReportGenerator(options);

// Generate report
reportGenerator.generateReport()
  .then(filePath => {
    console.log('QA Wolf test report generated successfully!');
    console.log(`Report saved to: ${filePath}`);
    process.exit(0);
  })
  .catch(error => {
    console.error('Error generating QA Wolf test report:', error.message);
    process.exit(1);
  });
