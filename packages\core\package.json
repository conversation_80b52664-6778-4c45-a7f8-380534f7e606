{"name": "@qawolf/core", "version": "0.1.0", "description": "Core package for QA Wolf Metrics Framework", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "test": "jest", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "testing", "core"], "author": "", "license": "MIT", "dependencies": {"dotenv": "^16.5.0", "winston": "^3.11.0"}, "devDependencies": {"@types/jest": "^29.5.12", "@types/node": "^20.11.30", "jest": "^29.7.0", "ts-jest": "^29.1.2", "typescript": "^5.4.3"}}