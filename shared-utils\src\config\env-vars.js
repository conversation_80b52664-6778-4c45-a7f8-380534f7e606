/**
 * Environment Variables Utilities
 * 
 * This module provides utilities for working with environment variables.
 */

require('dotenv').config();

/**
 * Get environment variable
 * 
 * @param {string} name - Environment variable name
 * @param {*} [defaultValue] - Default value if environment variable is not set
 * @returns {string|*} - Environment variable value or default value
 */
function getEnv(name, defaultValue) {
  return process.env[name] !== undefined ? process.env[name] : defaultValue;
}

/**
 * Get environment variable as boolean
 * 
 * @param {string} name - Environment variable name
 * @param {boolean} [defaultValue=false] - Default value if environment variable is not set
 * @returns {boolean} - Environment variable value as boolean
 */
function getEnvBool(name, defaultValue = false) {
  const value = process.env[name];
  
  if (value === undefined) {
    return defaultValue;
  }
  
  return value === 'true' || value === '1' || value === 'yes';
}

/**
 * Get environment variable as number
 * 
 * @param {string} name - Environment variable name
 * @param {number} [defaultValue=0] - Default value if environment variable is not set or not a number
 * @returns {number} - Environment variable value as number
 */
function getEnvNumber(name, defaultValue = 0) {
  const value = process.env[name];
  
  if (value === undefined) {
    return defaultValue;
  }
  
  const parsed = parseFloat(value);
  
  return isNaN(parsed) ? defaultValue : parsed;
}

/**
 * Get environment variable as array
 * 
 * @param {string} name - Environment variable name
 * @param {string} [separator=','] - Separator to split the string
 * @param {Array} [defaultValue=[]] - Default value if environment variable is not set
 * @returns {Array} - Environment variable value as array
 */
function getEnvArray(name, separator = ',', defaultValue = []) {
  const value = process.env[name];
  
  if (value === undefined) {
    return defaultValue;
  }
  
  return value.split(separator).map(item => item.trim()).filter(Boolean);
}

/**
 * Check if running in CI environment
 * 
 * @returns {boolean} - Whether running in CI environment
 */
function isCI() {
  return getEnvBool('CI') || 
         !!process.env.GITHUB_ACTIONS || 
         !!process.env.GITLAB_CI || 
         !!process.env.JENKINS_URL;
}

/**
 * Check if running in production environment
 * 
 * @returns {boolean} - Whether running in production environment
 */
function isProduction() {
  return process.env.NODE_ENV === 'production';
}

/**
 * Check if running in development environment
 * 
 * @returns {boolean} - Whether running in development environment
 */
function isDevelopment() {
  return process.env.NODE_ENV === 'development' || !process.env.NODE_ENV;
}

/**
 * Check if running in test environment
 * 
 * @returns {boolean} - Whether running in test environment
 */
function isTest() {
  return process.env.NODE_ENV === 'test';
}

module.exports = {
  getEnv,
  getEnvBool,
  getEnvNumber,
  getEnvArray,
  isCI,
  isProduction,
  isDevelopment,
  isTest
};