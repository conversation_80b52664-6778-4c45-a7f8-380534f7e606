/**
 * Test Evaluator Tool
 *
 * This tool analyzes test files for AAA compliance and provides feedback on how to improve them.
 * It checks for:
 * - Proper AAA structure
 * - Performance tracking
 * - Error handling
 * - Visual verification
 * - Test state tracking
 * - Test summary reporting
 *
 * @module test-evaluator
 * <AUTHOR> Wolf Team
 * @date 2025-01-01
 */

const fs = require('fs');
const path = require('path');

/**
 * Criteria for evaluating AAA compliance
 */
const evaluationCriteria = {
  // Structure criteria
  structure: {
    hasArrangeSection: { weight: 5, description: 'Has a clearly defined Arrange section' },
    hasActSection: { weight: 5, description: 'Has a clearly defined Act section' },
    hasAssertSection: { weight: 5, description: 'Has a clearly defined Assert section' },
    hasCleanupSection: { weight: 3, description: 'Has a clearly defined Cleanup section' },
    hasSectionComments: { weight: 2, description: 'Uses section comments to mark AAA sections' }
  },

  // Documentation criteria
  documentation: {
    hasJSDocHeader: { weight: 3, description: 'Has a JSDoc header with test description' },
    hasTestMetadata: { weight: 3, description: 'Includes test metadata (ID, priority, etc.)' },
    hasUserStory: { weight: 2, description: 'References a user story or requirement' },
    hasAcceptanceCriteria: { weight: 2, description: 'Lists acceptance criteria' },
    hasExpectedOutcomes: { weight: 2, description: 'Describes expected outcomes' },
    hasPerformanceExpectations: { weight: 2, description: 'Specifies performance expectations' }
  },

  // Performance tracking criteria
  performanceTracking: {
    usesPerformanceTracker: { weight: 4, description: 'Uses PerformanceTracker class' },
    tracksOperations: { weight: 3, description: 'Tracks individual operations' },
    hasThresholds: { weight: 2, description: 'Defines performance thresholds' },
    generatesReport: { weight: 2, description: 'Generates a performance report' }
  },

  // Error handling criteria
  errorHandling: {
    hasTryCatch: { weight: 4, description: 'Uses try-catch blocks for error handling' },
    takesErrorScreenshots: { weight: 3, description: 'Takes screenshots on error' },
    hasRecoveryMechanisms: { weight: 4, description: 'Implements recovery mechanisms' },
    tracksRecoveryAttempts: { weight: 2, description: 'Tracks recovery attempts' },
    hasDetailedErrorReporting: { weight: 2, description: 'Provides detailed error reporting' }
  },

  // Verification criteria
  verification: {
    hasMultipleVerificationApproaches: { weight: 4, description: 'Uses multiple verification approaches' },
    tracksVisualVerification: { weight: 3, description: 'Tracks visual verification' },
    takesScreenshots: { weight: 3, description: 'Takes screenshots at key points' },
    usesExpect: { weight: 3, description: 'Uses expect assertions' },
    hasDetailedAssertionMessages: { weight: 2, description: 'Provides detailed assertion messages' }
  },

  // Test state tracking criteria
  stateTracking: {
    usesTestState: { weight: 4, description: 'Uses a testState object' },
    tracksSuccess: { weight: 2, description: 'Tracks test success' },
    tracksVisualVerification: { weight: 2, description: 'Tracks visual verification in state' },
    tracksRecoveryAttempts: { weight: 2, description: 'Tracks recovery attempts in state' },
    tracksTestSteps: { weight: 2, description: 'Tracks test steps' }
  },

  // Test summary criteria
  testSummary: {
    calculatesAAAComplianceScore: { weight: 3, description: 'Calculates AAA compliance score' },
    printsSummary: { weight: 3, description: 'Prints a test summary' },
    includesPerformanceMetrics: { weight: 2, description: 'Includes performance metrics in summary' },
    includesVisualVerification: { weight: 2, description: 'Includes visual verification in summary' },
    includesRecoveryAttempts: { weight: 2, description: 'Includes recovery attempts in summary' }
  },

  // Standardization criteria
  standardization: {
    usesSharedUtilities: { weight: 4, description: 'Uses shared utilities module' },
    followsNamingConventions: { weight: 2, description: 'Follows naming conventions' },
    usesSelectors: { weight: 3, description: 'Uses selectors module' },
    usesEnvironmentVariables: { weight: 2, description: 'Uses environment variables' },
    exportsTestFunction: { weight: 2, description: 'Exports test function' }
  }
};

/**
 * Analyze a test file for AAA compliance
 *
 * @param {string} filePath - Path to the test file
 * @returns {Object} - Analysis results
 */
function analyzeTestFile(filePath) {
  // Read the file
  const fileContent = fs.readFileSync(filePath, 'utf8');

  // Initialize results
  const results = {
    filePath,
    fileName: path.basename(filePath),
    criteria: {},
    score: 0,
    maxScore: 0,
    percentage: 0,
    suggestions: []
  };

  // Check each criterion
  for (const [category, criteria] of Object.entries(evaluationCriteria)) {
    results.criteria[category] = {};

    for (const [criterion, details] of Object.entries(criteria)) {
      const { weight, description } = details;
      const passed = checkCriterion(fileContent, criterion);

      results.criteria[category][criterion] = {
        description,
        weight,
        passed
      };

      results.maxScore += weight;
      if (passed) {
        results.score += weight;
      } else {
        results.suggestions.push(`Add ${description.toLowerCase()} to improve AAA compliance`);
      }
    }
  }

  // Calculate percentage
  results.percentage = Math.round((results.score / results.maxScore) * 100);

  return results;
}

/**
 * Check if a file meets a specific criterion
 *
 * @param {string} fileContent - Content of the test file
 * @param {string} criterion - Criterion to check
 * @returns {boolean} - Whether the file meets the criterion
 */
function checkCriterion(fileContent, criterion) {
  // Structure criteria
  if (criterion === 'hasArrangeSection') {
    return /\/\/ ?={10,} ?ARRANGE ?={10,}|\/\*+ ?ARRANGE ?[\*\/]+|console\.log\(['"]ARRANGE:/.test(fileContent);
  }

  if (criterion === 'hasActSection') {
    return /\/\/ ?={10,} ?ACT ?={10,}|\/\*+ ?ACT ?[\*\/]+|console\.log\(['"]ACT:/.test(fileContent);
  }

  if (criterion === 'hasAssertSection') {
    return /\/\/ ?={10,} ?ASSERT ?={10,}|\/\*+ ?ASSERT ?[\*\/]+|console\.log\(['"]ASSERT:/.test(fileContent);
  }

  if (criterion === 'hasCleanupSection') {
    return /\/\/ ?={10,} ?CLEANUP ?={10,}|\/\*+ ?CLEANUP ?[\*\/]+|console\.log\(['"]CLEANUP:/.test(fileContent);
  }

  if (criterion === 'hasSectionComments') {
    return /\/\/ ?={10,}|\/\*+ ?[A-Z]+ ?[\*\/]+/.test(fileContent);
  }

  // Documentation criteria
  if (criterion === 'hasJSDocHeader') {
    return /\/\*\*[\s\S]*?\*\//.test(fileContent);
  }

  if (criterion === 'hasTestMetadata') {
    return /Test Metadata:|Test ID:|Priority:|Category:|Estimated Duration:/.test(fileContent);
  }

  if (criterion === 'hasUserStory') {
    return /User Story:|QW-US-\d+/.test(fileContent);
  }

  if (criterion === 'hasAcceptanceCriteria') {
    return /Acceptance Criteria:/.test(fileContent);
  }

  if (criterion === 'hasExpectedOutcomes') {
    return /Expected Outcomes:/.test(fileContent);
  }

  if (criterion === 'hasPerformanceExpectations') {
    return /Performance Expectations:/.test(fileContent);
  }

  // Performance tracking criteria
  if (criterion === 'usesPerformanceTracker') {
    return /PerformanceTracker|performanceTracker/.test(fileContent);
  }

  if (criterion === 'tracksOperations') {
    return /startOperation\(|endOperation\(/.test(fileContent);
  }

  if (criterion === 'hasThresholds') {
    return /thresholds|threshold:/.test(fileContent);
  }

  if (criterion === 'generatesReport') {
    return /generateReport\(|performanceReport/.test(fileContent);
  }

  // Error handling criteria
  if (criterion === 'hasTryCatch') {
    return /try {[\s\S]*?} catch \(/.test(fileContent);
  }

  if (criterion === 'takesErrorScreenshots') {
    return /takeErrorScreenshot|screenshot.*error|error.*screenshot/.test(fileContent);
  }

  if (criterion === 'hasRecoveryMechanisms') {
    return /recovery|retry|fallback|alternative/.test(fileContent);
  }

  if (criterion === 'tracksRecoveryAttempts') {
    return /recoveryAttempts|retryCount/.test(fileContent);
  }

  if (criterion === 'hasDetailedErrorReporting') {
    return /console\.error\(|error\.message|error\.stack/.test(fileContent);
  }

  // Verification criteria
  if (criterion === 'hasMultipleVerificationApproaches') {
    return /multiple|alternative|fallback|approach/.test(fileContent);
  }

  if (criterion === 'tracksVisualVerification') {
    return /visualVerification|verified/.test(fileContent);
  }

  if (criterion === 'takesScreenshots') {
    return /takeScreenshot|screenshot|page\.screenshot/.test(fileContent);
  }

  if (criterion === 'usesExpect') {
    return /expect\(/.test(fileContent);
  }

  if (criterion === 'hasDetailedAssertionMessages') {
    return /expect\(.*\)\..*\(.*,.*\)/.test(fileContent);
  }

  // Test state tracking criteria
  if (criterion === 'usesTestState') {
    return /testState =|let testState/.test(fileContent);
  }

  if (criterion === 'tracksSuccess') {
    return /success:|success =/.test(fileContent);
  }

  if (criterion === 'tracksVisualVerification') {
    return /visualVerification:|visualVerification =/.test(fileContent);
  }

  if (criterion === 'tracksRecoveryAttempts') {
    return /recoveryAttempts:|recoveryAttempts =/.test(fileContent);
  }

  if (criterion === 'tracksTestSteps') {
    return /testSteps:|testSteps =/.test(fileContent);
  }

  // Test summary criteria
  if (criterion === 'calculatesAAAComplianceScore') {
    return /calculateAAAComplianceScore|aaaComplianceScore/.test(fileContent);
  }

  if (criterion === 'printsSummary') {
    return /printTestSummary|TEST SUMMARY/.test(fileContent);
  }

  if (criterion === 'includesPerformanceMetrics') {
    return /performanceMetrics:|Performance Metrics:/.test(fileContent);
  }

  if (criterion === 'includesVisualVerification') {
    return /Visual Verification:/.test(fileContent);
  }

  if (criterion === 'includesRecoveryAttempts') {
    return /Recovery Attempts:/.test(fileContent);
  }

  // Standardization criteria
  if (criterion === 'usesSharedUtilities') {
    return /require\(['"].*utils.*['"]\)|import.*from ['"].*utils.*['"]/i.test(fileContent);
  }

  if (criterion === 'followsNamingConventions') {
    // Use the file name from the file content
    const fileName = fileContent.includes('node20') || fileContent.includes('Node20');
    return fileName;
  }

  if (criterion === 'usesSelectors') {
    return /selectors|require\(['"]\.\/selectors['"]\)/.test(fileContent);
  }

  if (criterion === 'usesEnvironmentVariables') {
    return /process\.env|dotenv/.test(fileContent);
  }

  if (criterion === 'exportsTestFunction') {
    return /module\.exports =|export /.test(fileContent);
  }

  return false;
}

/**
 * Generate a report for a test file
 *
 * @param {Object} results - Analysis results
 * @returns {string} - Report
 */
function generateReport(results) {
  const { fileName, score, maxScore, percentage, suggestions } = results;

  let report = `\n========== TEST EVALUATION REPORT ==========\n`;
  report += `File: ${fileName}\n`;
  report += `Score: ${score}/${maxScore} (${percentage}%)\n`;
  report += `AAA Compliance: ${percentage >= 90 ? 'EXCELLENT ✅' : percentage >= 80 ? 'GOOD ✅' : percentage >= 70 ? 'AVERAGE ⚠️' : 'POOR ❌'}\n`;

  report += `\nCategory Scores:\n`;
  for (const [category, criteria] of Object.entries(results.criteria)) {
    const categoryScore = Object.values(criteria).reduce((sum, { weight, passed }) => sum + (passed ? weight : 0), 0);
    const categoryMaxScore = Object.values(criteria).reduce((sum, { weight }) => sum + weight, 0);
    const categoryPercentage = Math.round((categoryScore / categoryMaxScore) * 100);

    report += `- ${category}: ${categoryScore}/${categoryMaxScore} (${categoryPercentage}%)\n`;
  }

  if (suggestions.length > 0) {
    report += `\nSuggestions for Improvement:\n`;
    suggestions.forEach((suggestion, index) => {
      report += `${index + 1}. ${suggestion}\n`;
    });
  }

  report += `\nConclusion: `;
  if (percentage >= 90) {
    report += `This test file meets the high AAA compliance standard (${percentage}%). Great job! 🎉\n`;
  } else if (percentage >= 80) {
    report += `This test file is close to meeting the high AAA compliance standard (${percentage}%). A few improvements would push it over 90%. 👍\n`;
  } else if (percentage >= 70) {
    report += `This test file needs improvement to meet the high AAA compliance standard (${percentage}%). Follow the suggestions to improve it. ⚠️\n`;
  } else {
    report += `This test file needs significant improvement to meet the high AAA compliance standard (${percentage}%). Follow the suggestions to improve it. ❌\n`;
  }

  report += `===========================================\n`;

  return report;
}

/**
 * Evaluate a test file and print a report
 *
 * @param {string} filePath - Path to the test file
 */
function evaluateTestFile(filePath) {
  const results = analyzeTestFile(filePath);
  const report = generateReport(results);
  console.log(report);
  return results;
}

/**
 * Evaluate all test files in a directory
 *
 * @param {string} dirPath - Path to the directory
 * @param {string} [pattern] - Pattern to match file names
 */
function evaluateTestDirectory(dirPath, pattern = /.*\.js$/) {
  const files = fs.readdirSync(dirPath);
  const testFiles = files.filter(file => pattern.test(file));

  const results = [];

  for (const file of testFiles) {
    const filePath = path.join(dirPath, file);
    results.push(evaluateTestFile(filePath));
  }

  // Print summary
  console.log('\n========== DIRECTORY EVALUATION SUMMARY ==========');
  console.log(`Directory: ${dirPath}`);
  console.log(`Files evaluated: ${results.length}`);

  const averagePercentage = results.reduce((sum, result) => sum + result.percentage, 0) / results.length;
  console.log(`Average AAA Compliance: ${Math.round(averagePercentage)}%`);

  const compliantFiles = results.filter(result => result.percentage >= 90).length;
  console.log(`Files meeting high AAA compliance standard (90%+): ${compliantFiles}/${results.length} (${Math.round((compliantFiles / results.length) * 100)}%)`);

  console.log('===================================================');

  return results;
}

// Export functions
module.exports = {
  analyzeTestFile,
  generateReport,
  evaluateTestFile,
  evaluateTestDirectory
};
