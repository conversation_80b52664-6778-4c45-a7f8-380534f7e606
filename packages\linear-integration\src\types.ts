/**
 * Types for Linear integration
 */

import { LinearClient, Issue, Project, Team, User, Comment, Attachment, IssueLabel, Workflow, WorkflowState, Cycle, IssueHistory, IssueRelation, Organization } from '@linear/sdk';

/**
 * Linear client options
 */
export interface LinearClientOptions {
  /**
   * API key
   */
  apiKey?: string;
  
  /**
   * Team ID
   */
  teamId?: string;
  
  /**
   * Project ID
   */
  projectId?: string;
}

/**
 * Linear issues options
 */
export interface LinearIssuesOptions {
  /**
   * Linear client
   */
  client?: LinearClient;
  
  /**
   * API key
   */
  apiKey?: string;
  
  /**
   * Team ID
   */
  teamId?: string;
  
  /**
   * Project ID
   */
  projectId?: string;
}

/**
 * Linear projects options
 */
export interface LinearProjectsOptions {
  /**
   * Linear client
   */
  client?: LinearClient;
  
  /**
   * API key
   */
  apiKey?: string;
  
  /**
   * Team ID
   */
  teamId?: string;
}

/**
 * Linear teams options
 */
export interface LinearTeamsOptions {
  /**
   * Linear client
   */
  client?: LinearClient;
  
  /**
   * API key
   */
  apiKey?: string;
}

/**
 * Linear users options
 */
export interface LinearUsersOptions {
  /**
   * Linear client
   */
  client?: LinearClient;
  
  /**
   * API key
   */
  apiKey?: string;
}

/**
 * Linear comments options
 */
export interface LinearCommentsOptions {
  /**
   * Linear client
   */
  client?: LinearClient;
  
  /**
   * API key
   */
  apiKey?: string;
}

/**
 * Linear attachments options
 */
export interface LinearAttachmentsOptions {
  /**
   * Linear client
   */
  client?: LinearClient;
  
  /**
   * API key
   */
  apiKey?: string;
}

/**
 * Linear issue filter
 */
export interface LinearIssueFilter {
  /**
   * Issue ID
   */
  id?: string;
  
  /**
   * Issue number
   */
  number?: number;
  
  /**
   * Issue title
   */
  title?: string;
  
  /**
   * Issue description
   */
  description?: string;
  
  /**
   * Issue state
   */
  state?: string;
  
  /**
   * Issue priority
   */
  priority?: number;
  
  /**
   * Issue labels
   */
  labels?: string[];
  
  /**
   * Issue assignee
   */
  assignee?: string;
  
  /**
   * Issue creator
   */
  creator?: string;
  
  /**
   * Issue team
   */
  team?: string;
  
  /**
   * Issue project
   */
  project?: string;
  
  /**
   * Issue cycle
   */
  cycle?: string;
  
  /**
   * Issue created after
   */
  createdAfter?: Date;
  
  /**
   * Issue created before
   */
  createdBefore?: Date;
  
  /**
   * Issue updated after
   */
  updatedAfter?: Date;
  
  /**
   * Issue updated before
   */
  updatedBefore?: Date;
  
  /**
   * Issue due date
   */
  dueDate?: Date;
  
  /**
   * Issue due date after
   */
  dueDateAfter?: Date;
  
  /**
   * Issue due date before
   */
  dueDateBefore?: Date;
  
  /**
   * Issue sort by
   */
  sortBy?: 'createdAt' | 'updatedAt' | 'priority' | 'dueDate';
  
  /**
   * Issue sort order
   */
  sortOrder?: 'ASC' | 'DESC';
}

/**
 * Linear project filter
 */
export interface LinearProjectFilter {
  /**
   * Project ID
   */
  id?: string;
  
  /**
   * Project name
   */
  name?: string;
  
  /**
   * Project description
   */
  description?: string;
  
  /**
   * Project state
   */
  state?: 'planned' | 'started' | 'paused' | 'completed' | 'canceled';
  
  /**
   * Project team
   */
  team?: string;
  
  /**
   * Project lead
   */
  lead?: string;
  
  /**
   * Project members
   */
  members?: string[];
  
  /**
   * Project created after
   */
  createdAfter?: Date;
  
  /**
   * Project created before
   */
  createdBefore?: Date;
  
  /**
   * Project updated after
   */
  updatedAfter?: Date;
  
  /**
   * Project updated before
   */
  updatedBefore?: Date;
  
  /**
   * Project start date
   */
  startDate?: Date;
  
  /**
   * Project start date after
   */
  startDateAfter?: Date;
  
  /**
   * Project start date before
   */
  startDateBefore?: Date;
  
  /**
   * Project target date
   */
  targetDate?: Date;
  
  /**
   * Project target date after
   */
  targetDateAfter?: Date;
  
  /**
   * Project target date before
   */
  targetDateBefore?: Date;
  
  /**
   * Project sort by
   */
  sortBy?: 'createdAt' | 'updatedAt' | 'startDate' | 'targetDate';
  
  /**
   * Project sort order
   */
  sortOrder?: 'ASC' | 'DESC';
}

/**
 * Linear team filter
 */
export interface LinearTeamFilter {
  /**
   * Team ID
   */
  id?: string;
  
  /**
   * Team name
   */
  name?: string;
  
  /**
   * Team key
   */
  key?: string;
  
  /**
   * Team description
   */
  description?: string;
  
  /**
   * Team lead
   */
  lead?: string;
  
  /**
   * Team members
   */
  members?: string[];
  
  /**
   * Team created after
   */
  createdAfter?: Date;
  
  /**
   * Team created before
   */
  createdBefore?: Date;
  
  /**
   * Team updated after
   */
  updatedAfter?: Date;
  
  /**
   * Team updated before
   */
  updatedBefore?: Date;
  
  /**
   * Team sort by
   */
  sortBy?: 'createdAt' | 'updatedAt' | 'name';
  
  /**
   * Team sort order
   */
  sortOrder?: 'ASC' | 'DESC';
}

/**
 * Linear user filter
 */
export interface LinearUserFilter {
  /**
   * User ID
   */
  id?: string;
  
  /**
   * User name
   */
  name?: string;
  
  /**
   * User email
   */
  email?: string;
  
  /**
   * User display name
   */
  displayName?: string;
  
  /**
   * User active
   */
  active?: boolean;
  
  /**
   * User admin
   */
  admin?: boolean;
  
  /**
   * User teams
   */
  teams?: string[];
  
  /**
   * User created after
   */
  createdAfter?: Date;
  
  /**
   * User created before
   */
  createdBefore?: Date;
  
  /**
   * User updated after
   */
  updatedAfter?: Date;
  
  /**
   * User updated before
   */
  updatedBefore?: Date;
  
  /**
   * User sort by
   */
  sortBy?: 'createdAt' | 'updatedAt' | 'name';
  
  /**
   * User sort order
   */
  sortOrder?: 'ASC' | 'DESC';
}

/**
 * Linear comment filter
 */
export interface LinearCommentFilter {
  /**
   * Comment ID
   */
  id?: string;
  
  /**
   * Comment body
   */
  body?: string;
  
  /**
   * Comment issue
   */
  issue?: string;
  
  /**
   * Comment user
   */
  user?: string;
  
  /**
   * Comment created after
   */
  createdAfter?: Date;
  
  /**
   * Comment created before
   */
  createdBefore?: Date;
  
  /**
   * Comment updated after
   */
  updatedAfter?: Date;
  
  /**
   * Comment updated before
   */
  updatedBefore?: Date;
  
  /**
   * Comment sort by
   */
  sortBy?: 'createdAt' | 'updatedAt';
  
  /**
   * Comment sort order
   */
  sortOrder?: 'ASC' | 'DESC';
}

/**
 * Linear attachment filter
 */
export interface LinearAttachmentFilter {
  /**
   * Attachment ID
   */
  id?: string;
  
  /**
   * Attachment title
   */
  title?: string;
  
  /**
   * Attachment subtitle
   */
  subtitle?: string;
  
  /**
   * Attachment url
   */
  url?: string;
  
  /**
   * Attachment issue
   */
  issue?: string;
  
  /**
   * Attachment user
   */
  user?: string;
  
  /**
   * Attachment source
   */
  source?: string;
  
  /**
   * Attachment created after
   */
  createdAfter?: Date;
  
  /**
   * Attachment created before
   */
  createdBefore?: Date;
  
  /**
   * Attachment updated after
   */
  updatedAfter?: Date;
  
  /**
   * Attachment updated before
   */
  updatedBefore?: Date;
  
  /**
   * Attachment sort by
   */
  sortBy?: 'createdAt' | 'updatedAt';
  
  /**
   * Attachment sort order
   */
  sortOrder?: 'ASC' | 'DESC';
}

/**
 * Linear issue create input
 */
export interface LinearIssueCreateInput {
  /**
   * Issue title
   */
  title: string;
  
  /**
   * Issue description
   */
  description?: string;
  
  /**
   * Issue state
   */
  stateId?: string;
  
  /**
   * Issue priority
   */
  priority?: number;
  
  /**
   * Issue labels
   */
  labelIds?: string[];
  
  /**
   * Issue assignee
   */
  assigneeId?: string;
  
  /**
   * Issue team
   */
  teamId?: string;
  
  /**
   * Issue project
   */
  projectId?: string;
  
  /**
   * Issue cycle
   */
  cycleId?: string;
  
  /**
   * Issue parent
   */
  parentId?: string;
  
  /**
   * Issue due date
   */
  dueDate?: Date;
  
  /**
   * Issue estimate
   */
  estimate?: number;
}

/**
 * Linear issue update input
 */
export interface LinearIssueUpdateInput {
  /**
   * Issue title
   */
  title?: string;
  
  /**
   * Issue description
   */
  description?: string;
  
  /**
   * Issue state
   */
  stateId?: string;
  
  /**
   * Issue priority
   */
  priority?: number;
  
  /**
   * Issue labels
   */
  labelIds?: string[];
  
  /**
   * Issue assignee
   */
  assigneeId?: string;
  
  /**
   * Issue team
   */
  teamId?: string;
  
  /**
   * Issue project
   */
  projectId?: string;
  
  /**
   * Issue cycle
   */
  cycleId?: string;
  
  /**
   * Issue parent
   */
  parentId?: string;
  
  /**
   * Issue due date
   */
  dueDate?: Date;
  
  /**
   * Issue estimate
   */
  estimate?: number;
}

/**
 * Linear project create input
 */
export interface LinearProjectCreateInput {
  /**
   * Project name
   */
  name: string;
  
  /**
   * Project description
   */
  description?: string;
  
  /**
   * Project state
   */
  state?: 'planned' | 'started' | 'paused' | 'completed' | 'canceled';
  
  /**
   * Project team
   */
  teamId?: string;
  
  /**
   * Project lead
   */
  leadId?: string;
  
  /**
   * Project members
   */
  memberIds?: string[];
  
  /**
   * Project start date
   */
  startDate?: Date;
  
  /**
   * Project target date
   */
  targetDate?: Date;
  
  /**
   * Project icon
   */
  icon?: string;
  
  /**
   * Project color
   */
  color?: string;
}

/**
 * Linear project update input
 */
export interface LinearProjectUpdateInput {
  /**
   * Project name
   */
  name?: string;
  
  /**
   * Project description
   */
  description?: string;
  
  /**
   * Project state
   */
  state?: 'planned' | 'started' | 'paused' | 'completed' | 'canceled';
  
  /**
   * Project team
   */
  teamId?: string;
  
  /**
   * Project lead
   */
  leadId?: string;
  
  /**
   * Project members
   */
  memberIds?: string[];
  
  /**
   * Project start date
   */
  startDate?: Date;
  
  /**
   * Project target date
   */
  targetDate?: Date;
  
  /**
   * Project icon
   */
  icon?: string;
  
  /**
   * Project color
   */
  color?: string;
}

/**
 * Linear comment create input
 */
export interface LinearCommentCreateInput {
  /**
   * Comment body
   */
  body: string;
  
  /**
   * Comment issue
   */
  issueId: string;
}

/**
 * Linear comment update input
 */
export interface LinearCommentUpdateInput {
  /**
   * Comment body
   */
  body?: string;
}

/**
 * Linear attachment create input
 */
export interface LinearAttachmentCreateInput {
  /**
   * Attachment title
   */
  title: string;
  
  /**
   * Attachment subtitle
   */
  subtitle?: string;
  
  /**
   * Attachment url
   */
  url: string;
  
  /**
   * Attachment issue
   */
  issueId: string;
  
  /**
   * Attachment icon
   */
  icon?: string;
  
  /**
   * Attachment metadata
   */
  metadata?: Record<string, any>;
}

/**
 * Linear attachment update input
 */
export interface LinearAttachmentUpdateInput {
  /**
   * Attachment title
   */
  title?: string;
  
  /**
   * Attachment subtitle
   */
  subtitle?: string;
  
  /**
   * Attachment url
   */
  url?: string;
  
  /**
   * Attachment icon
   */
  icon?: string;
  
  /**
   * Attachment metadata
   */
  metadata?: Record<string, any>;
}
