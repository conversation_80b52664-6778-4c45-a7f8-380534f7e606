{"name": "@qawolf/test-framework", "version": "1.0.0", "description": "Core QA Wolf testing framework", "main": "src/index.js", "scripts": {"test": "playwright test", "lint": "eslint .", "build": "mkdir -p dist && cp -r src/* dist/"}, "publishConfig": {"registry": "https://npm.pkg.github.com/"}, "repository": {"type": "git", "url": "git+https://github.com/sneezyxl/QAWolfeesojc.git", "directory": "test-framework"}, "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "playwright", "testing", "mcp"], "author": "", "license": "MIT", "dependencies": {"@playwright/test": "^1.52.0", "@qawolf/ci-sdk": "^1.0.1", "@qawolf/shared-utils": "1.0.0", "playwright": "^1.52.0"}, "devDependencies": {"eslint": "^9.27.0"}}