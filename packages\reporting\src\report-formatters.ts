/**
 * Report formatters for reporting
 */

import * as MarkdownIt from 'markdown-it';
import { ReportFormat, ReportFormatterOptions } from './types';
import { EventBus, EventType } from '@qawolf/core';

/**
 * Report formatters
 */
export class ReportFormatters {
  /**
   * Formatters
   */
  private formatters: Map<ReportFormat, ReportFormatterOptions>;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   */
  constructor() {
    this.formatters = new Map<ReportFormat, ReportFormatterOptions>();
    this.eventBus = EventBus.getInstance();
    
    // Register default formatters
    this.registerDefaultFormatters();
  }
  
  /**
   * Register formatter
   * @param options Formatter options
   * @returns This instance for chaining
   */
  registerFormatter(options: ReportFormatterOptions): ReportFormatters {
    this.formatters.set(options.format, options);
    
    // Emit event
    this.eventBus.emit(EventType.REPORT_FORMATTERS_REGISTER_FORMATTER, {
      formatter: options
    });
    
    return this;
  }
  
  /**
   * Get formatter
   * @param format Format
   * @returns Formatter options
   */
  getFormatter(format: ReportFormat): ReportFormatterOptions | undefined {
    return this.formatters.get(format);
  }
  
  /**
   * Get formatters
   * @returns Formatters
   */
  getFormatters(): Map<ReportFormat, ReportFormatterOptions> {
    return this.formatters;
  }
  
  /**
   * Format content
   * @param content Content
   * @param format Format
   * @returns Formatted content
   */
  async format(content: string, format: ReportFormat): Promise<string> {
    try {
      const formatter = this.formatters.get(format);
      
      if (!formatter) {
        throw new Error(`Formatter for format ${format} not found`);
      }
      
      let formatted = '';
      
      switch (format) {
        case ReportFormat.HTML:
          formatted = this.formatHTML(content, formatter);
          break;
        case ReportFormat.MARKDOWN:
          formatted = this.formatMarkdown(content, formatter);
          break;
        case ReportFormat.PDF:
          formatted = this.formatPDF(content, formatter);
          break;
        case ReportFormat.JSON:
          formatted = this.formatJSON(content, formatter);
          break;
        case ReportFormat.XML:
          formatted = this.formatXML(content, formatter);
          break;
        case ReportFormat.CSV:
          formatted = this.formatCSV(content, formatter);
          break;
        default:
          throw new Error(`Format ${format} not supported`);
      }
      
      // Emit event
      this.eventBus.emit(EventType.REPORT_FORMATTERS_FORMAT, {
        content,
        format,
        formatted
      });
      
      return formatted;
    } catch (error) {
      this.handleError(error, `Failed to format content to ${format}`);
      return content;
    }
  }
  
  /**
   * Format HTML
   * @param content Content
   * @param formatter Formatter options
   * @returns Formatted content
   */
  private formatHTML(content: string, formatter: ReportFormatterOptions): string {
    // HTML formatting logic
    return content;
  }
  
  /**
   * Format Markdown
   * @param content Content
   * @param formatter Formatter options
   * @returns Formatted content
   */
  private formatMarkdown(content: string, formatter: ReportFormatterOptions): string {
    // Markdown formatting logic
    return content;
  }
  
  /**
   * Format PDF
   * @param content Content
   * @param formatter Formatter options
   * @returns Formatted content
   */
  private formatPDF(content: string, formatter: ReportFormatterOptions): string {
    // PDF formatting logic
    return content;
  }
  
  /**
   * Format JSON
   * @param content Content
   * @param formatter Formatter options
   * @returns Formatted content
   */
  private formatJSON(content: string, formatter: ReportFormatterOptions): string {
    // JSON formatting logic
    try {
      // Try to parse content as JSON
      const json = JSON.parse(content);
      return JSON.stringify(json, null, 2);
    } catch (error) {
      // If content is not JSON, return as is
      return content;
    }
  }
  
  /**
   * Format XML
   * @param content Content
   * @param formatter Formatter options
   * @returns Formatted content
   */
  private formatXML(content: string, formatter: ReportFormatterOptions): string {
    // XML formatting logic
    return content;
  }
  
  /**
   * Format CSV
   * @param content Content
   * @param formatter Formatter options
   * @returns Formatted content
   */
  private formatCSV(content: string, formatter: ReportFormatterOptions): string {
    // CSV formatting logic
    return content;
  }
  
  /**
   * Register default formatters
   */
  private registerDefaultFormatters(): void {
    // Register HTML formatter
    this.registerFormatter({
      name: 'html',
      description: 'HTML formatter',
      format: ReportFormat.HTML,
      options: {}
    });
    
    // Register Markdown formatter
    this.registerFormatter({
      name: 'markdown',
      description: 'Markdown formatter',
      format: ReportFormat.MARKDOWN,
      options: {}
    });
    
    // Register PDF formatter
    this.registerFormatter({
      name: 'pdf',
      description: 'PDF formatter',
      format: ReportFormat.PDF,
      options: {}
    });
    
    // Register JSON formatter
    this.registerFormatter({
      name: 'json',
      description: 'JSON formatter',
      format: ReportFormat.JSON,
      options: {}
    });
    
    // Register XML formatter
    this.registerFormatter({
      name: 'xml',
      description: 'XML formatter',
      format: ReportFormat.XML,
      options: {}
    });
    
    // Register CSV formatter
    this.registerFormatter({
      name: 'csv',
      description: 'CSV formatter',
      format: ReportFormat.CSV,
      options: {}
    });
  }
  
  /**
   * Handle error
   * @param error Error
   * @param message Error message
   */
  private handleError(error: any, message: string): void {
    console.error(message, error);
    
    // Emit event
    this.eventBus.emit(EventType.REPORT_FORMATTERS_ERROR, {
      error,
      message
    });
  }
}

/**
 * Create report formatters
 * @returns Report formatters
 */
export function createReportFormatters(): ReportFormatters {
  return new ReportFormatters();
}
