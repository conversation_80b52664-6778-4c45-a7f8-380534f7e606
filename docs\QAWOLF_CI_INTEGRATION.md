# QA Wolf CI Integration

This document describes our integration with QA Wolf's CI system for automated test execution and reporting.

## Overview

QA Wolf is a powerful testing platform that allows us to run end-to-end tests in the cloud. Our integration with QA Wolf's CI system automates the process of:

1. Notifying QA Wolf of new deployments
2. Running tests against the deployed application
3. Checking if tests have passed (CI greenlight)
4. Generating reports of test results

This integration is implemented through a set of scripts and GitHub Actions workflows.

## Integration Components

### 1. Deployment Notification

The `scripts/qawolf-notify-deployment.js` script notifies QA Wolf of a new deployment, which triggers the test runs.

```javascript
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  apiKey: process.env.QA_WOLF_API_KEY,
  teamId: process.env.QA_WOLF_TEAM_ID || 'clux0gjs50sb3ak01fnh7wvja',
  apiUrl: 'https://app.qawolf.com/api/ci',
  reportsDir: './qawolf-reports'
};

// Get deployment information
const deploymentInfo = {
  teamId: config.teamId,
  environment: process.env.ENVIRONMENT || 'staging',
  branch: process.env.GITHUB_REF_NAME || 'main',
  commit: process.env.GITHUB_SHA || 'latest',
  buildUrl: process.env.GITHUB_SERVER_URL ? `${process.env.GITHUB_SERVER_URL}/${process.env.GITHUB_REPOSITORY}/actions/runs/${process.env.GITHUB_RUN_ID}` : null,
  timestamp: new Date().toISOString()
};

// Notify QA Wolf of deployment
async function notifyDeployment() {
  try {
    console.log('Notifying QA Wolf of deployment...');
    
    const response = await axios.post(
      `${config.apiUrl}/deployments`,
      deploymentInfo,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.apiKey}`
        }
      }
    );
    
    console.log('QA Wolf deployment notification successful!');
    
    return response.data;
  } catch (error) {
    console.error('Error notifying QA Wolf of deployment:', error.message);
    process.exit(1);
  }
}
```

### 2. CI Greenlight Polling

The `scripts/qawolf-ci-greenlight.js` script polls the QA Wolf API to check if the tests have passed (greenlight).

```javascript
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  apiKey: process.env.QA_WOLF_API_KEY,
  teamId: process.env.QA_WOLF_TEAM_ID || 'clux0gjs50sb3ak01fnh7wvja',
  apiUrl: 'https://app.qawolf.com/api/ci',
  reportsDir: './qawolf-reports',
  maxPolls: 30, // Maximum number of polling attempts
  pollInterval: 60000 // Polling interval in milliseconds (1 minute)
};

// Poll for CI greenlight
async function pollForGreenlight() {
  console.log('Polling for QA Wolf CI greenlight...');
  
  let pollCount = 0;
  let greenlightStatus = null;
  
  while (pollCount < config.maxPolls) {
    pollCount++;
    
    try {
      console.log(`Polling attempt ${pollCount}/${config.maxPolls}...`);
      
      const response = await axios.get(
        `${config.apiUrl}/greenlight?teamId=${config.teamId}&environment=${deploymentInfo.environment}&branch=${deploymentInfo.branch}`,
        {
          headers: {
            'Authorization': `Bearer ${config.apiKey}`
          }
        }
      );
      
      const result = response.data;
      
      console.log('Poll result:', result);
      
      // Check if we have a greenlight
      if (result.runStage === 'complete') {
        greenlightStatus = result.greenlight;
        
        console.log(`QA Wolf tests completed with greenlight status: ${greenlightStatus}`);
        
        break;
      }
      
      // If tests are still running, wait and poll again
      console.log(`Tests are still running (stage: ${result.runStage}). Waiting ${config.pollInterval / 1000} seconds before next poll...`);
      
      await new Promise(resolve => setTimeout(resolve, config.pollInterval));
    } catch (error) {
      console.error('Error polling for greenlight:', error.message);
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, config.pollInterval));
    }
  }
  
  // Exit with appropriate status code based on greenlight
  if (greenlightStatus === true) {
    console.log('QA Wolf tests passed! CI greenlight received.');
    process.exit(0);
  } else {
    console.error('QA Wolf tests failed! No CI greenlight received.');
    process.exit(1);
  }
}
```

### 3. Report Generation

The `scripts/qawolf-generate-report.js` script generates a comprehensive report of QA Wolf test results.

```javascript
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  apiKey: process.env.QA_WOLF_API_KEY,
  teamId: process.env.QA_WOLF_TEAM_ID || 'clux0gjs50sb3ak01fnh7wvja',
  apiUrl: 'https://app.qawolf.com/api/ci',
  reportsDir: './qawolf-reports'
};

// Generate report
async function generateReport() {
  try {
    console.log('Generating QA Wolf test report...');
    
    // Fetch test results
    const response = await axios.get(
      `${config.apiUrl}/results?teamId=${config.teamId}&environment=${deploymentInfo.environment}&branch=${deploymentInfo.branch}`,
      {
        headers: {
          'Authorization': `Bearer ${config.apiKey}`
        }
      }
    );
    
    const testResults = response.data;
    
    console.log(`Fetched ${testResults.length} test results.`);
    
    // Calculate metrics
    const metrics = calculateMetrics(testResults);
    
    // Generate HTML report
    const htmlReport = generateHtmlReport(testResults, metrics, greenlightResult);
    
    // Save HTML report to file
    fs.writeFileSync(
      path.join(config.reportsDir, 'report.html'),
      htmlReport
    );
    
    console.log('QA Wolf test report generated successfully!');
    
    return { testResults, metrics };
  } catch (error) {
    console.error('Error generating QA Wolf test report:', error.message);
    process.exit(1);
  }
}
```

## GitHub Actions Workflow

We've integrated QA Wolf CI into our GitHub Actions workflow. The workflow file is located at `.github/workflows/qawolf-ci.yml`.

```yaml
name: QA Wolf CI Integration

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

jobs:
  qawolf-ci:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Install QA Wolf CI SDK
        run: npm install @qawolf/ci-sdk
      
      - name: Notify QA Wolf of deployment
        run: node scripts/qawolf-notify-deployment.js
        env:
          QA_WOLF_API_KEY: ${{ secrets.QA_WOLF_API_KEY }}
          QA_WOLF_TEAM_ID: ${{ secrets.QA_WOLF_TEAM_ID }}
      
      - name: Run tests and wait for CI greenlight
        run: node scripts/qawolf-ci-greenlight.js
        env:
          QA_WOLF_API_KEY: ${{ secrets.QA_WOLF_API_KEY }}
          QA_WOLF_TEAM_ID: ${{ secrets.QA_WOLF_TEAM_ID }}
      
      - name: Generate QA Wolf metrics report
        run: node scripts/qawolf-generate-report.js
        env:
          QA_WOLF_API_KEY: ${{ secrets.QA_WOLF_API_KEY }}
          QA_WOLF_TEAM_ID: ${{ secrets.QA_WOLF_TEAM_ID }}
      
      - name: Upload QA Wolf report
        uses: actions/upload-artifact@v3
        with:
          name: qawolf-report
          path: qawolf-reports/
          retention-days: 30
```

## Environment Variables

The following environment variables are required for the QA Wolf CI integration:

| Variable | Description |
|----------|-------------|
| `QA_WOLF_API_KEY` | API key for authenticating with QA Wolf's API |
| `QA_WOLF_TEAM_ID` | QA Wolf team ID (default: 'clux0gjs50sb3ak01fnh7wvja') |
| `ENVIRONMENT` | Environment name (default: 'staging') |

These variables should be set as secrets in your GitHub repository.

## Reports

The QA Wolf CI integration generates reports in the `qawolf-reports` directory. These reports include:

- Test results in JSON format
- Metrics like pass rate and average duration
- HTML report with detailed test information

The reports are uploaded as artifacts in the GitHub Actions workflow, making them easily accessible for review.

## Troubleshooting

If you encounter issues with the QA Wolf CI integration, check the following:

1. Verify that the environment variables are set correctly
2. Check the logs in the GitHub Actions workflow
3. Ensure that the QA Wolf API is accessible
4. Verify that the tests are configured correctly in QA Wolf

For more information, refer to the [QA Wolf documentation](https://docs.qawolf.com/).
