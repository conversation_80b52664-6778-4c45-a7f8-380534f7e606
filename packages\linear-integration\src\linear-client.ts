/**
 * Linear client for Linear integration
 */

import * as dotenv from 'dotenv';
import { LinearClient as LinearSDKClient } from '@linear/sdk';
import { LinearClientOptions } from './types';
import { LinearIssues } from './linear-issues';
import { LinearProjects } from './linear-projects';
import { LinearTeams } from './linear-teams';
import { LinearUsers } from './linear-users';
import { LinearComments } from './linear-comments';
import { LinearAttachments } from './linear-attachments';
import { EventBus, EventType } from '@qawolf/core';

// Load environment variables
dotenv.config();

/**
 * Linear client
 */
export class LinearClient {
  /**
   * API key
   */
  private apiKey: string;
  
  /**
   * Team ID
   */
  private teamId: string;
  
  /**
   * Project ID
   */
  private projectId: string;
  
  /**
   * Linear SDK client
   */
  private client: LinearSDKClient;
  
  /**
   * Linear issues
   */
  private issues: LinearIssues;
  
  /**
   * Linear projects
   */
  private projects: LinearProjects;
  
  /**
   * Linear teams
   */
  private teams: LinearTeams;
  
  /**
   * Linear users
   */
  private users: LinearUsers;
  
  /**
   * Linear comments
   */
  private comments: LinearComments;
  
  /**
   * Linear attachments
   */
  private attachments: LinearAttachments;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options Linear client options
   */
  constructor(options: LinearClientOptions = {}) {
    this.apiKey = options.apiKey || process.env.LINEAR_API_KEY || '';
    this.teamId = options.teamId || process.env.LINEAR_TEAM_ID || '';
    this.projectId = options.projectId || process.env.LINEAR_PROJECT_ID || '';
    
    if (!this.apiKey) {
      throw new Error('Linear API key is required');
    }
    
    this.client = new LinearSDKClient({
      apiKey: this.apiKey
    });
    
    this.issues = new LinearIssues({
      client: this.client,
      teamId: this.teamId,
      projectId: this.projectId
    });
    
    this.projects = new LinearProjects({
      client: this.client,
      teamId: this.teamId
    });
    
    this.teams = new LinearTeams({
      client: this.client
    });
    
    this.users = new LinearUsers({
      client: this.client
    });
    
    this.comments = new LinearComments({
      client: this.client
    });
    
    this.attachments = new LinearAttachments({
      client: this.client
    });
    
    this.eventBus = EventBus.getInstance();
    
    // Emit event
    this.eventBus.emit(EventType.LINEAR_CLIENT_INITIALIZED, {
      apiKey: this.apiKey,
      teamId: this.teamId,
      projectId: this.projectId
    });
  }
  
  /**
   * Get Linear SDK client
   * @returns Linear SDK client
   */
  getClient(): LinearSDKClient {
    return this.client;
  }
  
  /**
   * Get issues
   * @returns Linear issues
   */
  getIssues(): LinearIssues {
    return this.issues;
  }
  
  /**
   * Get projects
   * @returns Linear projects
   */
  getProjects(): LinearProjects {
    return this.projects;
  }
  
  /**
   * Get teams
   * @returns Linear teams
   */
  getTeams(): LinearTeams {
    return this.teams;
  }
  
  /**
   * Get users
   * @returns Linear users
   */
  getUsers(): LinearUsers {
    return this.users;
  }
  
  /**
   * Get comments
   * @returns Linear comments
   */
  getComments(): LinearComments {
    return this.comments;
  }
  
  /**
   * Get attachments
   * @returns Linear attachments
   */
  getAttachments(): LinearAttachments {
    return this.attachments;
  }
  
  /**
   * Get API key
   * @returns API key
   */
  getAPIKey(): string {
    return this.apiKey;
  }
  
  /**
   * Get team ID
   * @returns Team ID
   */
  getTeamId(): string {
    return this.teamId;
  }
  
  /**
   * Get project ID
   * @returns Project ID
   */
  getProjectId(): string {
    return this.projectId;
  }
  
  /**
   * Set API key
   * @param apiKey API key
   * @returns This instance for chaining
   */
  setAPIKey(apiKey: string): LinearClient {
    this.apiKey = apiKey;
    
    this.client = new LinearSDKClient({
      apiKey: this.apiKey
    });
    
    this.issues = new LinearIssues({
      client: this.client,
      teamId: this.teamId,
      projectId: this.projectId
    });
    
    this.projects = new LinearProjects({
      client: this.client,
      teamId: this.teamId
    });
    
    this.teams = new LinearTeams({
      client: this.client
    });
    
    this.users = new LinearUsers({
      client: this.client
    });
    
    this.comments = new LinearComments({
      client: this.client
    });
    
    this.attachments = new LinearAttachments({
      client: this.client
    });
    
    return this;
  }
  
  /**
   * Set team ID
   * @param teamId Team ID
   * @returns This instance for chaining
   */
  setTeamId(teamId: string): LinearClient {
    this.teamId = teamId;
    
    this.issues = new LinearIssues({
      client: this.client,
      teamId: this.teamId,
      projectId: this.projectId
    });
    
    this.projects = new LinearProjects({
      client: this.client,
      teamId: this.teamId
    });
    
    return this;
  }
  
  /**
   * Set project ID
   * @param projectId Project ID
   * @returns This instance for chaining
   */
  setProjectId(projectId: string): LinearClient {
    this.projectId = projectId;
    
    this.issues = new LinearIssues({
      client: this.client,
      teamId: this.teamId,
      projectId: this.projectId
    });
    
    return this;
  }
}

/**
 * Create Linear client
 * @param options Linear client options
 * @returns Linear client
 */
export function createLinearClient(options: LinearClientOptions = {}): LinearClient {
  return new LinearClient(options);
}
