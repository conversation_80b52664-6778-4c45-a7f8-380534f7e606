{"name": "@qawolf/reporting", "version": "0.1.0", "description": "Reporting for QA Wolf Metrics Framework", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "test": "jest", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "testing", "reporting"], "author": "", "license": "MIT", "dependencies": {"@qawolf/core": "^0.1.0", "@qawolf/github-integration": "^0.1.0", "@qawolf/linear-integration": "^0.1.0", "@qawolf/qawolf-integration": "^0.1.0", "@qawolf/shared-utils": "^0.1.0", "@qawolf/testmo-integration": "^0.1.0", "chart.js": "^4.4.1", "dotenv": "^16.4.5", "ejs": "^3.1.9", "handlebars": "^4.7.8", "markdown-it": "^14.0.0", "pug": "^3.0.2"}, "devDependencies": {"@types/ejs": "^3.1.5", "@types/handlebars": "^4.1.0", "@types/jest": "^29.5.12", "@types/markdown-it": "^13.0.7", "@types/node": "^20.11.30", "@types/pug": "^2.0.10", "jest": "^29.7.0", "ts-jest": "^29.1.2", "typescript": "^5.4.3"}}