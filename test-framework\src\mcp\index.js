/**
 * MCP Module
 * 
 * This module provides integration with the MCP optimizer.
 */

const McpController = require('./controller');
const adapters = require('./adapters');
const config = require('./config');

module.exports = {
  McpController,
  ...adapters,
  ...config,
  
  /**
   * Create a new MCP controller with the specified configuration
   * @param {Object} [config] - Configuration options
   * @returns {McpController} - MCP controller
   */
  createController: (config = {}) => new McpController(config)
};