/**
 * MCP Adapters
 * 
 * This module provides adapter functions for translating between
 * test-framework and mcp-optimizer data structures.
 */

/**
 * Adapt selectors to the format expected by the MCP optimizer
 * @param {string|string[]} selectors - Selector(s) to adapt
 * @returns {string[]} - Adapted selectors
 */
function adaptSelectors(selectors) {
  if (!selectors) {
    throw new Error('Selectors are required');
  }
  
  // Convert single selector to array
  if (typeof selectors === 'string') {
    return [selectors];
  }
  
  // Ensure all selectors are strings
  if (!Array.isArray(selectors)) {
    throw new Error('Selectors must be a string or an array of strings');
  }
  
  return selectors.map(selector => {
    if (typeof selector !== 'string') {
      throw new Error(`Invalid selector: ${selector}`);
    }
    return selector;
  });
}

/**
 * Adapt a task to the format expected by the MCP optimizer
 * @param {Object} task - Task to adapt
 * @returns {Object} - Adapted task
 */
function adaptTask(task) {
  if (!task) {
    throw new Error('Task is required');
  }
  
  if (typeof task !== 'object') {
    throw new Error('Task must be an object');
  }
  
  if (!task.type) {
    throw new Error('Task must have a type');
  }
  
  // Create a copy of the task to avoid modifying the original
  const adaptedTask = { ...task };
  
  // Normalize task type
  adaptedTask.type = normalizeTaskType(task.type);
  
  // Normalize task subtype if present
  if (task.subtype) {
    adaptedTask.subtype = normalizeTaskSubtype(task.subtype);
  }
  
  // Add context if not present
  if (!adaptedTask.context) {
    adaptedTask.context = {};
  }
  
  return adaptedTask;
}

/**
 * Normalize task type
 * @param {string} type - Task type
 * @returns {string} - Normalized task type
 */
function normalizeTaskType(type) {
  const typeMap = {
    'browser': 'browser_interaction',
    'browser_interaction': 'browser_interaction',
    'file': 'file_operation',
    'file_operation': 'file_operation',
    'code': 'code_generation',
    'code_generation': 'code_generation',
    'docs': 'documentation',
    'documentation': 'documentation',
    'debug': 'debugging',
    'debugging': 'debugging',
    'performance': 'performance_analysis',
    'performance_analysis': 'performance_analysis',
    'accessibility': 'accessibility_testing',
    'accessibility_testing': 'accessibility_testing',
    'test': 'test_maintenance',
    'test_maintenance': 'test_maintenance',
    'problem': 'complex_problem_solving',
    'complex_problem_solving': 'complex_problem_solving'
  };
  
  return typeMap[type.toLowerCase()] || type;
}

/**
 * Normalize task subtype
 * @param {string} subtype - Task subtype
 * @returns {string} - Normalized task subtype
 */
function normalizeTaskSubtype(subtype) {
  const subtypeMap = {
    'click': 'click',
    'type': 'type',
    'read': 'read',
    'write': 'write',
    'debug': 'debugging',
    'visual': 'visual_analysis',
    'analyze': 'visual_analysis'
  };
  
  return subtypeMap[subtype.toLowerCase()] || subtype;
}

/**
 * Adapt results from the MCP optimizer to the format expected by the test framework
 * @param {Object} results - Results to adapt
 * @returns {Object} - Adapted results
 */
function adaptResults(results) {
  if (!results) {
    return null;
  }
  
  // For now, just return the results as-is
  // In the future, we may need to transform the results
  return results;
}

module.exports = {
  adaptSelectors,
  adaptTask,
  adaptResults
};