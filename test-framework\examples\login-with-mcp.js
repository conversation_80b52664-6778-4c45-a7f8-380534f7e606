/**
 * Example Test Script with MCP Integration
 * 
 * This script demonstrates how to use the MCP integration in a test script.
 */

const { test } = require('@playwright/test');
const { createMcpController, test: testUtils } = require('../src');

test('Login with MCP integration', async ({ page }) => {
  // Create an MCP controller
  const mcpController = createMcpController({
    autoStartPlaywrightMcp: true,
    generateFallbacks: true,
    prioritizeTestIds: true
  });
  
  try {
    // Start performance tracking
    const performanceTracker = new testUtils.PerformanceTracker();
    
    // Navigate to the app
    performanceTracker.startOperation('navigation_to_app');
    await page.goto('https://app.lidostaging.com');
    performanceTracker.endOperation();
    
    // Optimize selectors for login form
    const selectors = await mcpController.optimizeSelectors([
      '[data-test-id="SignInEmail"]',
      '[data-test-id="SignInPassword"]',
      ':text("Log in with email")'
    ]);
    
    console.log('Optimized selectors:', selectors);
    
    // Fill in login form
    performanceTracker.startOperation('login');
    await page.fill(selectors.selectors[0].optimized, '<EMAIL>');
    await page.fill(selectors.selectors[1].optimized, 'vhc!tGK289IS&');
    
    // Select the appropriate MCP tool for clicking the login button
    const toolSelection = await mcpController.selectMcpTool({
      type: 'browser',
      subtype: 'click',
      context: {
        element: 'login-button'
      }
    });
    
    console.log('Selected tool:', toolSelection);
    
    // Click the login button
    await page.click(selectors.selectors[2].optimized);
    
    // Wait for login to complete
    await page.waitForSelector('div[class*="FilesTable"]', { timeout: 15000 });
    performanceTracker.endOperation();
    
    // Take a screenshot
    const screenshotPath = await testUtils.takeScreenshot(page, {
      testName: 'login-with-mcp',
      action: 'after-login',
      fullPage: false
    });
    
    // Analyze the screenshot
    const analysis = await mcpController.analyzeScreenshot(screenshotPath);
    
    console.log('Screenshot analysis:', analysis);
    
    // Generate a performance report
    const performanceMetrics = performanceTracker.getMetrics();
    const report = await mcpController.generateReport({
      performanceMetrics,
      selectors,
      toolSelection,
      analysis
    });
    
    console.log('MCP report:', report);
    
    // Print test summary
    testUtils.printTestSummary(
      {
        success: true,
        performanceMetrics,
        aaaComplianceScore: testUtils.calculateAAAComplianceScore({
          success: true,
          performanceMetrics,
          allPerformanceMetricsWithinThresholds: performanceTracker.areAllMetricsWithinThresholds()
        })
      },
      'login-with-mcp',
      'Login with MCP integration',
      performanceTracker.startTime
    );
  } finally {
    // Clean up MCP resources
    await mcpController.cleanup();
  }
});