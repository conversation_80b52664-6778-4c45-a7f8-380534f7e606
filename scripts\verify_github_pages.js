/**
 * GitHub Pages Verification Script
 * 
 * This script verifies that all GitHub Pages are working correctly.
 * It checks for 404 errors and other issues.
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

// Base URL for GitHub Pages
const baseUrl = 'https://sneezyxl.github.io/QAWolfeesojc';

// List of pages to check
const pagesToCheck = [
  '/',
  '/framework/',
  '/framework/environment/',
  '/comprehensive/',
  '/reports/executive_summary.html',
  '/reports/test-report.html',
  '/reports/test-results-dashboard.html'
];

/**
 * Check if a URL is accessible
 * @param {string} url - URL to check
 * @returns {Promise<Object>} - Result object
 */
function checkUrl(url) {
  return new Promise((resolve) => {
    https.get(url, (res) => {
      const { statusCode } = res;
      let data = '';

      res.on('data', (chunk) => {
        data += chunk;
      });

      res.on('end', () => {
        resolve({
          url,
          statusCode,
          success: statusCode === 200,
          data: data.length > 100 ? data.substring(0, 100) + '...' : data
        });
      });
    }).on('error', (err) => {
      resolve({
        url,
        statusCode: 0,
        success: false,
        error: err.message
      });
    });
  });
}

/**
 * Main function
 */
async function main() {
  console.log('Verifying GitHub Pages...');
  console.log(`Base URL: ${baseUrl}`);
  console.log('-----------------------------------');

  const results = [];
  let allPassed = true;

  for (const page of pagesToCheck) {
    const url = `${baseUrl}${page}`;
    console.log(`Checking: ${url}`);
    
    const result = await checkUrl(url);
    results.push(result);
    
    if (result.success) {
      console.log(`✅ ${url} - Status: ${result.statusCode}`);
    } else {
      console.error(`❌ ${url} - Status: ${result.statusCode}`);
      if (result.error) {
        console.error(`   Error: ${result.error}`);
      }
      allPassed = false;
    }
  }

  console.log('-----------------------------------');
  console.log(`Results: ${allPassed ? '✅ All pages are working' : '❌ Some pages have issues'}`);
  
  // Generate a report
  const reportPath = path.join(__dirname, '..', 'reports', 'github-pages-verification.json');
  fs.writeFileSync(reportPath, JSON.stringify({
    timestamp: new Date().toISOString(),
    baseUrl,
    allPassed,
    results
  }, null, 2));
  
  console.log(`Report saved to: ${reportPath}`);
}

// Run the script
main().catch(console.error);
