/**
 * MCP patterns for MCP optimizer
 */

import { MCPOperationPattern, MCPToolType, MCPOperationType } from './types';

/**
 * Common MCP patterns
 */
export const commonMCPPatterns: MCPOperationPattern[] = [
  {
    id: 'file-read-edit-write',
    name: 'File Read-Edit-Write Pattern',
    description: 'Read a file, edit its content, and write it back',
    operations: [
      {
        id: 'read-file',
        name: 'Read File',
        type: MCPOperationType.FILE_OPERATION,
        toolType: MCPToolType.DESKTOP_COMMANDER,
        toolName: 'read_file_Desktop_Commander',
        parameters: {
          path: 'file.txt'
        },
        duration: 100,
        timestamp: Date.now(),
        cost: 0.0005,
        tokenCount: 100
      },
      {
        id: 'edit-content',
        name: 'Edit Content',
        type: MCPOperationType.THINKING_OPERATION,
        toolType: MCPToolType.SEQUENTIAL_THINKING,
        toolName: 'sequentialthinking_Sequential_Thinking',
        parameters: {
          thought: 'Editing file content'
        },
        duration: 200,
        timestamp: Date.now() + 100,
        cost: 0.002,
        tokenCount: 500
      },
      {
        id: 'write-file',
        name: 'Write File',
        type: MCPOperationType.FILE_OPERATION,
        toolType: MCPToolType.DESKTOP_COMMANDER,
        toolName: 'write_file_Desktop_Commander',
        parameters: {
          path: 'file.txt',
          content: 'Updated content'
        },
        duration: 100,
        timestamp: Date.now() + 300,
        cost: 0.0005,
        tokenCount: 100
      }
    ],
    frequency: 10,
    cost: 0.003,
    tokenCount: 700
  },
  {
    id: 'browser-navigate-click-fill',
    name: 'Browser Navigate-Click-Fill Pattern',
    description: 'Navigate to a page, click an element, and fill a form',
    operations: [
      {
        id: 'navigate',
        name: 'Navigate',
        type: MCPOperationType.BROWSER_OPERATION,
        toolType: MCPToolType.BROWSER_TOOLS,
        toolName: 'browser_navigate_Playwright',
        parameters: {
          url: 'https://example.com'
        },
        duration: 500,
        timestamp: Date.now(),
        cost: 0.001,
        tokenCount: 200
      },
      {
        id: 'click',
        name: 'Click',
        type: MCPOperationType.BROWSER_OPERATION,
        toolType: MCPToolType.BROWSER_TOOLS,
        toolName: 'browser_click_Playwright',
        parameters: {
          element: 'Button',
          ref: 'button-1'
        },
        duration: 100,
        timestamp: Date.now() + 500,
        cost: 0.001,
        tokenCount: 200
      },
      {
        id: 'fill',
        name: 'Fill',
        type: MCPOperationType.BROWSER_OPERATION,
        toolType: MCPToolType.BROWSER_TOOLS,
        toolName: 'browser_type_Playwright',
        parameters: {
          element: 'Input',
          ref: 'input-1',
          text: 'Hello, world!'
        },
        duration: 100,
        timestamp: Date.now() + 600,
        cost: 0.001,
        tokenCount: 200
      }
    ],
    frequency: 8,
    cost: 0.003,
    tokenCount: 600
  },
  {
    id: 'terminal-execute-read',
    name: 'Terminal Execute-Read Pattern',
    description: 'Execute a command and read its output',
    operations: [
      {
        id: 'execute-command',
        name: 'Execute Command',
        type: MCPOperationType.TERMINAL_OPERATION,
        toolType: MCPToolType.DESKTOP_COMMANDER,
        toolName: 'execute_command_Desktop_Commander',
        parameters: {
          command: 'ls -la'
        },
        duration: 200,
        timestamp: Date.now(),
        cost: 0.0005,
        tokenCount: 100
      },
      {
        id: 'read-output',
        name: 'Read Output',
        type: MCPOperationType.TERMINAL_OPERATION,
        toolType: MCPToolType.DESKTOP_COMMANDER,
        toolName: 'read_output_Desktop_Commander',
        parameters: {
          pid: 12345
        },
        duration: 100,
        timestamp: Date.now() + 200,
        cost: 0.0005,
        tokenCount: 100
      }
    ],
    frequency: 5,
    cost: 0.001,
    tokenCount: 200
  },
  {
    id: 'documentation-search-analyze',
    name: 'Documentation Search-Analyze Pattern',
    description: 'Search for documentation and analyze it',
    operations: [
      {
        id: 'search-documentation',
        name: 'Search Documentation',
        type: MCPOperationType.DOCUMENTATION_OPERATION,
        toolType: MCPToolType.QUILLOPY,
        toolName: 'quillopy_search_Quillopy',
        parameters: {
          query: 'How to use Playwright',
          documentation_name: 'playwright'
        },
        duration: 300,
        timestamp: Date.now(),
        cost: 0.0005,
        tokenCount: 100
      },
      {
        id: 'analyze-documentation',
        name: 'Analyze Documentation',
        type: MCPOperationType.THINKING_OPERATION,
        toolType: MCPToolType.SEQUENTIAL_THINKING,
        toolName: 'sequentialthinking_Sequential_Thinking',
        parameters: {
          thought: 'Analyzing documentation'
        },
        duration: 200,
        timestamp: Date.now() + 300,
        cost: 0.002,
        tokenCount: 500
      }
    ],
    frequency: 3,
    cost: 0.0025,
    tokenCount: 600
  },
  {
    id: 'browser-screenshot-analyze',
    name: 'Browser Screenshot-Analyze Pattern',
    description: 'Take a screenshot and analyze it',
    operations: [
      {
        id: 'take-screenshot',
        name: 'Take Screenshot',
        type: MCPOperationType.BROWSER_OPERATION,
        toolType: MCPToolType.BROWSER_TOOLS,
        toolName: 'browser_take_screenshot_Playwright',
        parameters: {
          filename: 'screenshot.png'
        },
        duration: 200,
        timestamp: Date.now(),
        cost: 0.001,
        tokenCount: 200
      },
      {
        id: 'analyze-screenshot',
        name: 'Analyze Screenshot',
        type: MCPOperationType.THINKING_OPERATION,
        toolType: MCPToolType.SEQUENTIAL_THINKING,
        toolName: 'sequentialthinking_Sequential_Thinking',
        parameters: {
          thought: 'Analyzing screenshot'
        },
        duration: 300,
        timestamp: Date.now() + 200,
        cost: 0.002,
        tokenCount: 500
      }
    ],
    frequency: 4,
    cost: 0.003,
    tokenCount: 700
  }
];

/**
 * Get common MCP patterns
 * @returns Common MCP patterns
 */
export function getCommonMCPPatterns(): MCPOperationPattern[] {
  return [...commonMCPPatterns];
}

/**
 * Get MCP pattern by ID
 * @param id Pattern ID
 * @returns MCP pattern or undefined if not found
 */
export function getMCPPatternById(id: string): MCPOperationPattern | undefined {
  return commonMCPPatterns.find(pattern => pattern.id === id);
}

/**
 * Get MCP patterns by tool type
 * @param toolType Tool type
 * @returns MCP patterns
 */
export function getMCPPatternsByToolType(toolType: MCPToolType): MCPOperationPattern[] {
  return commonMCPPatterns.filter(pattern => {
    return pattern.operations.some(operation => operation.toolType === toolType);
  });
}

/**
 * Get MCP patterns by operation type
 * @param operationType Operation type
 * @returns MCP patterns
 */
export function getMCPPatternsByOperationType(operationType: MCPOperationType): MCPOperationPattern[] {
  return commonMCPPatterns.filter(pattern => {
    return pattern.operations.some(operation => operation.type === operationType);
  });
}

/**
 * Get MCP patterns by frequency
 * @param minFrequency Minimum frequency
 * @returns MCP patterns
 */
export function getMCPPatternsByFrequency(minFrequency: number): MCPOperationPattern[] {
  return commonMCPPatterns.filter(pattern => pattern.frequency >= minFrequency);
}

/**
 * Get MCP patterns by cost
 * @param maxCost Maximum cost
 * @returns MCP patterns
 */
export function getMCPPatternsByCost(maxCost: number): MCPOperationPattern[] {
  return commonMCPPatterns.filter(pattern => pattern.cost <= maxCost);
}

/**
 * Get MCP patterns by token count
 * @param maxTokenCount Maximum token count
 * @returns MCP patterns
 */
export function getMCPPatternsByTokenCount(maxTokenCount: number): MCPOperationPattern[] {
  return commonMCPPatterns.filter(pattern => pattern.tokenCount <= maxTokenCount);
}
