#!/usr/bin/env node

/**
 * Configure Script
 * 
 * This script configures the QA Wolf testing framework.
 */

const path = require('path');
const fs = require('fs');
const readline = require('readline');

// Create readline interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Configuration
const config = {
  defaultConfig: {
    apiKey: '',
    baseUrl: 'https://app.lidostaging.com',
    screenshotDir: './screenshots',
    reportDir: './reports',
    headless: true,
    retries: 2,
    timeout: 30000,
    workers: 1,
    browser: 'chromium',
    viewport: {
      width: 1280,
      height: 720
    },
    credentials: {
      email: '',
      password: ''
    },
    mcp: {
      enabled: true,
      autoStartPlaywrightMcp: true,
      playwrightMcpPort: 8932,
      generateFallbacks: true,
      prioritizeTestIds: true
    },
    selfHealing: {
      enabled: true,
      selectorHealing: {
        enabled: true,
        maxAttempts: 3,
        strategies: ['css-relaxation', 'attribute-based', 'text-based', 'xpath']
      },
      recovery: {
        enabled: true,
        maxAttempts: 3,
        strategies: ['retry', 'wait', 'selector', 'refresh', 'screenshot']
      },
      feedbackCollection: {
        enabled: true,
        collectScreenshots: true,
        persistFeedback: true
      },
      learning: {
        enabled: true,
        autoOptimize: true
      }
    }
  }
};

/**
 * Ask a question
 * @param {string} question - Question to ask
 * @param {string} [defaultValue] - Default value
 * @returns {Promise<string>} - User's answer
 */
function ask(question, defaultValue) {
  const defaultText = defaultValue ? ` (${defaultValue})` : '';
  return new Promise((resolve) => {
    rl.question(`${question}${defaultText}: `, (answer) => {
      resolve(answer || defaultValue);
    });
  });
}

/**
 * Ask a yes/no question
 * @param {string} question - Question to ask
 * @param {boolean} [defaultValue=true] - Default value
 * @returns {Promise<boolean>} - User's answer
 */
async function askYesNo(question, defaultValue = true) {
  const defaultText = defaultValue ? 'Y/n' : 'y/N';
  const answer = await ask(`${question} [${defaultText}]`);
  
  if (!answer) {
    return defaultValue;
  }
  
  return answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes';
}

/**
 * Create a QA Wolf configuration file
 * @param {Object} config - Configuration object
 * @returns {void}
 */
function createConfigFile(config) {
  const configPath = path.join(process.cwd(), 'qawolf.config.js');
  
  console.log('Creating QA Wolf configuration file...');
  
  const configContent = `
module.exports = ${JSON.stringify(config, null, 2)};
`;
  
  fs.writeFileSync(configPath, configContent, 'utf8');
}

/**
 * Update the .env file
 * @param {string} apiKey - QA Wolf API key
 * @param {string} email - Email
 * @param {string} password - Password
 * @returns {void}
 */
function updateEnvFile(apiKey, email, password) {
  const envPath = path.join(process.cwd(), '.env');
  
  let envContent = '';
  
  if (fs.existsSync(envPath)) {
    envContent = fs.readFileSync(envPath, 'utf8');
  }
  
  // Update or add API key
  if (!envContent.includes('QAWOLF_API_KEY')) {
    envContent += `\nQAWOLF_API_KEY=${apiKey}\n`;
  } else {
    envContent = envContent.replace(/QAWOLF_API_KEY=.*/, `QAWOLF_API_KEY=${apiKey}`);
  }
  
  // Update or add credentials
  if (!envContent.includes('QAWOLF_EMAIL')) {
    envContent += `\nQAWOLF_EMAIL=${email}\n`;
  } else {
    envContent = envContent.replace(/QAWOLF_EMAIL=.*/, `QAWOLF_EMAIL=${email}`);
  }
  
  if (!envContent.includes('QAWOLF_PASSWORD')) {
    envContent += `\nQAWOLF_PASSWORD=${password}\n`;
  } else {
    envContent = envContent.replace(/QAWOLF_PASSWORD=.*/, `QAWOLF_PASSWORD=${password}`);
  }
  
  fs.writeFileSync(envPath, envContent, 'utf8');
}

/**
 * Run the configuration
 * @returns {Promise<void>}
 */
async function runConfiguration() {
  try {
    console.log('Configuring QA Wolf testing framework...');
    
    // Clone the default configuration
    const userConfig = JSON.parse(JSON.stringify(config.defaultConfig));
    
    // Ask for API key
    userConfig.apiKey = await ask('Enter your QA Wolf API key (Team ID)', userConfig.apiKey);
    
    // Ask for base URL
    userConfig.baseUrl = await ask('Enter the base URL for your application', userConfig.baseUrl);
    
    // Ask for credentials
    userConfig.credentials.email = await ask('Enter your test account email', '<EMAIL>');
    userConfig.credentials.password = await ask('Enter your test account password', 'vhc!tGK289IS&');
    
    // Ask for headless mode
    userConfig.headless = await askYesNo('Run tests in headless mode?', userConfig.headless);
    
    // Ask for MCP configuration
    userConfig.mcp.enabled = await askYesNo('Enable MCP optimization?', userConfig.mcp.enabled);
    
    if (userConfig.mcp.enabled) {
      userConfig.mcp.autoStartPlaywrightMcp = await askYesNo('Auto-start Playwright MCP?', userConfig.mcp.autoStartPlaywrightMcp);
      userConfig.mcp.generateFallbacks = await askYesNo('Generate fallback selectors?', userConfig.mcp.generateFallbacks);
      userConfig.mcp.prioritizeTestIds = await askYesNo('Prioritize test IDs?', userConfig.mcp.prioritizeTestIds);
    }
    
    // Ask for self-healing configuration
    userConfig.selfHealing.enabled = await askYesNo('Enable self-healing automation?', userConfig.selfHealing.enabled);
    
    if (userConfig.selfHealing.enabled) {
      userConfig.selfHealing.selectorHealing.enabled = await askYesNo('Enable selector healing?', userConfig.selfHealing.selectorHealing.enabled);
      userConfig.selfHealing.recovery.enabled = await askYesNo('Enable recovery from failures?', userConfig.selfHealing.recovery.enabled);
      userConfig.selfHealing.feedbackCollection.enabled = await askYesNo('Enable feedback collection?', userConfig.selfHealing.feedbackCollection.enabled);
      userConfig.selfHealing.learning.enabled = await askYesNo('Enable learning from feedback?', userConfig.selfHealing.learning.enabled);
    }
    
    // Create configuration file
    createConfigFile(userConfig);
    
    // Update .env file
    updateEnvFile(userConfig.apiKey, userConfig.credentials.email, userConfig.credentials.password);
    
    console.log('Configuration complete!');
    
    rl.close();
  } catch (error) {
    console.error('Configuration failed:', error.message);
    process.exit(1);
  }
}

// Run the script
runConfiguration();