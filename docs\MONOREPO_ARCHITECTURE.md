# Monorepo Architecture

This document describes the monorepo architecture for the QA Wolf Metrics Framework.

*Last updated: 5/19/2025, 1:23:38 PM*

## Overview

A monorepo (monolithic repository) is a software development strategy where code for multiple projects is stored in the same repository. This approach offers several benefits:

1. **Code Sharing**: Easier sharing of code between packages
2. **Consistent Tooling**: Same tools and configurations across all packages
3. **Atomic Changes**: Changes across multiple packages can be committed together
4. **Simplified Dependency Management**: Dependencies between packages are explicit and versioned
5. **Coordinated Releases**: Easier to coordinate releases of related packages

Our monorepo architecture organizes the codebase into separate packages with clear responsibilities and dependencies.

## Repository Structure

```
QAWolfeesojc/
├── packages/                 # Monorepo packages
│   ├── core/                 # Core functionality and shared interfaces
│   │   ├── src/              # Source code
│   │   │   ├── event-bus.ts  # Event bus for inter-package communication
│   │   │   ├── types.ts      # Common types
│   │   │   └── index.ts      # Package entry point
│   │   └── dist/             # Compiled code
│   ├── mcp-optimizer/        # MCP operation tracking and optimization
│   │   ├── src/              # Source code
│   │   │   ├── operation-tracker.ts # MCP operation tracking
│   │   │   ├── pattern-analyzer.ts  # Pattern analysis
│   │   │   ├── optimizer.ts         # Optimization suggestions
│   │   │   └── index.ts             # Package entry point
│   │   └── dist/             # Compiled code
│   ├── shared-utils/         # Shared utilities used across packages
│   │   ├── src/              # Source code
│   │   │   ├── file-utils.ts # File utilities
│   │   │   ├── data-utils.ts # Data utilities
│   │   │   └── index.ts      # Package entry point
│   │   └── dist/             # Compiled code
│   ├── test-framework/       # Core test framework functionality
│   │   ├── src/              # Source code
│   │   │   ├── test-runner.ts # Test runner
│   │   │   ├── assertions.ts  # Assertions
│   │   │   └── index.ts       # Package entry point
│   │   └── dist/             # Compiled code
│   ├── qawolf-integration/   # QA Wolf API and services integration
│   │   ├── src/              # Source code
│   │   │   ├── ci-greenlight.ts      # CI greenlight functionality
│   │   │   ├── deployment-notification.ts # Deployment notification
│   │   │   ├── report-generator.ts   # Report generation
│   │   │   └── index.ts              # Package entry point
│   │   ├── bin/              # CLI tools
│   │   │   ├── ci-greenlight.js      # CI greenlight CLI
│   │   │   ├── notify-deployment.js  # Deployment notification CLI
│   │   │   └── generate-report.js    # Report generation CLI
│   │   └── dist/             # Compiled code
│   ├── testmo-integration/   # Testmo API and services integration
│   │   ├── src/              # Source code
│   │   │   ├── testmo-client.ts      # Testmo API client
│   │   │   ├── testmo-reporter.ts    # Test result reporting
│   │   │   └── index.ts              # Package entry point
│   │   ├── bin/              # CLI tools
│   │   │   └── testmo-integration.js # Testmo integration CLI
│   │   └── dist/             # Compiled code
│   ├── github-integration/   # GitHub API and services integration
│   │   ├── src/              # Source code
│   │   │   ├── github-client.ts      # GitHub API client
│   │   │   ├── github-actions.ts     # GitHub Actions integration
│   │   │   ├── github-pages.ts       # GitHub Pages integration
│   │   │   ├── github-releases.ts    # GitHub Releases integration
│   │   │   └── index.ts              # Package entry point
│   │   └── dist/             # Compiled code
│   ├── linear-integration/   # Linear API and services integration
│   │   ├── src/              # Source code
│   │   │   ├── linear-client.ts      # Linear API client
│   │   │   ├── linear-issues.ts      # Issue management
│   │   │   ├── linear-projects.ts    # Project management
│   │   │   ├── linear-teams.ts       # Team management
│   │   │   ├── linear-users.ts       # User management
│   │   │   ├── linear-comments.ts    # Comment management
│   │   │   ├── linear-attachments.ts # Attachment management
│   │   │   └── index.ts              # Package entry point
│   │   └── dist/             # Compiled code
│   └── reporting/            # Report generation and publishing
│       ├── src/              # Source code
│       │   ├── report-generator.ts   # Report generation
│       │   ├── report-templates.ts   # Report templates
│       │   ├── report-formatters.ts  # Report formatters
│       │   ├── report-exporters.ts   # Report exporters
│       │   ├── report-publishers.ts  # Report publishers
│       │   └── index.ts              # Package entry point
│       └── dist/             # Compiled code
├── scripts/                  # Utility scripts
│   ├── migrate-scripts.js    # Script migration utility
│   ├── qawolf-ci-greenlight.js # Legacy QA Wolf CI greenlight script
│   ├── qawolf-notify-deployment.js # Legacy deployment notification script
│   ├── qawolf-generate-report.js # Legacy report generation script
│   └── testmo-integration.js # Legacy Testmo integration script
├── tests/                    # Test files
│   ├── qawolf/               # QA Wolf tests
│   ├── playwright/           # Playwright tests
│   ├── playwright-mcp/       # Playwright MCP tests
│   └── mocks/                # Mock tests
├── docs/                     # Documentation
│   ├── README.md             # Documentation overview
│   ├── MONOREPO_ARCHITECTURE.md # Monorepo architecture documentation
│   ├── api/                  # API documentation
│   ├── guides/               # User guides
│   └── tutorials/            # Tutorials
├── .github/                  # GitHub Actions workflows
├── local_kb/                 # Local knowledge base
│   ├── qa_wolf_metrics_framework_documentation.md # Comprehensive documentation
│   ├── framework_self_review.md # Framework self-review
│   └── qa_wolf_metrics_framework_overview.md # Framework overview
├── package.json              # Root package.json for managing the monorepo
├── lerna.json                # Lerna configuration
├── tsconfig.json             # TypeScript configuration
├── jest.config.js            # Jest configuration
├── .eslintrc.js              # ESLint configuration
└── README.md                 # Main README file
```

## Packages

### Core Package

The core package provides the foundation for the QA Wolf Metrics Framework. It includes:

- **Configuration Management**: Loading and validating configuration
- **Logging**: Centralized logging system
- **Error Handling**: Error handling and reporting
- **Event System**: Event-driven architecture for communication between packages

### Test Framework Package

The test framework package provides the foundation for writing and running tests. It includes:

- **Test Runner**: Running tests with Playwright
- **Test Utilities**: Utilities for writing tests
- **Test Fixtures**: Reusable test fixtures
- **Test Assertions**: Custom assertions for testing

### MCP Optimizer Package

The MCP optimizer package provides utilities for optimizing the use of MCP tools. It includes:

- **MCP Integration**: Integration with Playwright MCP and Browser Tools MCP
- **MCP Optimization**: Optimizing MCP usage patterns
- **MCP Utilities**: Utilities for working with MCP tools

### Self-Healing Package

The self-healing package provides utilities for creating self-healing tests. It includes:

- **Selector Healing**: Healing broken selectors
- **Selector History**: Tracking selector history
- **Healing Strategies**: Different strategies for healing selectors
- **Self-Healing Page**: Wrapper around Playwright's Page class with self-healing capabilities

### Performance Tracker Package

The performance tracker package provides utilities for tracking test performance. It includes:

- **Performance Tracking**: Tracking test execution time
- **Operation Tracking**: Tracking specific operations within tests
- **Resource Monitoring**: Monitoring CPU and memory usage
- **Performance Reporting**: Generating reports of performance metrics

### Screenshot Manager Package

The screenshot manager package provides utilities for managing screenshots. It includes:

- **Screenshot Capture**: Taking screenshots during test execution
- **Screenshot Organization**: Organizing screenshots by date and test name
- **Screenshot Cleanup**: Cleaning up old screenshots
- **Screenshot Reporting**: Generating reports of screenshots

### QA Wolf Integration Package

The QA Wolf integration package provides utilities for integrating with QA Wolf. It includes:

- **Deployment Notification**: Notifying QA Wolf of deployments
- **CI Greenlight**: Checking if tests have passed
- **Results Retrieval**: Retrieving test results from QA Wolf
- **Report Generation**: Generating reports of QA Wolf test results

### Testmo Integration Package

The Testmo integration package provides utilities for integrating with Testmo. It includes:

- **Test Run Management**: Creating and managing test runs in Testmo
- **Results Submission**: Submitting test results to Testmo
- **Test Case Management**: Managing test cases in Testmo
- **Report Generation**: Generating reports of Testmo test results

### GitHub Integration Package

The GitHub integration package provides utilities for integrating with GitHub. It includes:

- **GitHub API**: Interacting with GitHub's API
- **GitHub Actions**: Integrating with GitHub Actions
- **GitHub Pages**: Deploying documentation and reports to GitHub Pages
- **GitHub Issues**: Creating and managing GitHub issues

### Linear Integration Package

The Linear integration package provides utilities for integrating with Linear. It includes:

- **Linear API**: Interacting with Linear's API
- **Issue Management**: Creating and managing issues in Linear
- **Issue Linking**: Linking tests to issues in Linear
- **Report Generation**: Generating reports of Linear issues

### Reporting Package

The reporting package provides utilities for generating reports. It includes:

- **Report Generation**: Generating HTML and JSON reports
- **Report Templates**: Templates for different types of reports
- **Report Aggregation**: Aggregating reports from different sources
- **Report Deployment**: Deploying reports to GitHub Pages

### Shared Utils Package

The shared utils package provides utilities that are used by multiple packages. It includes:

- **File System Utilities**: Utilities for working with the file system
- **HTTP Utilities**: Utilities for making HTTP requests
- **Date Utilities**: Utilities for working with dates
- **String Utilities**: Utilities for working with strings

## Dependencies

The dependencies between packages are as follows:

```
Core <-- Shared Utils <-- Test Framework <-- QA Wolf Integration
                                         <-- Testmo Integration
                                         <-- GitHub Integration
                                         <-- Linear Integration
                                         <-- Reporting
```

- **Core**: No dependencies
- **Shared Utils**: Depends on Core
- **Test Framework**: Depends on Core, Shared Utils
- **MCP Optimizer**: Depends on Core, Shared Utils
- **QA Wolf Integration**: Depends on Core, Shared Utils, Test Framework
- **Testmo Integration**: Depends on Core, Shared Utils, Test Framework
- **GitHub Integration**: Depends on Core, Shared Utils
- **Linear Integration**: Depends on Core, Shared Utils
- **Reporting**: Depends on Core, Shared Utils

## Migration Process

The QA Wolf Metrics Framework was migrated from a previous structure to the new monorepo architecture. This section describes the migration process, including the previous structure, migration steps, and backward compatibility.

### Previous Structure

The previous structure consisted of a flat layout with scripts and utilities in the root directory. This made it difficult to maintain and extend the codebase, and it lacked proper organization and modularity.

```
QAWolfeesojc/
├── scripts/
│   ├── qawolf-ci-greenlight.js
│   ├── qawolf-notify-deployment.js
│   ├── qawolf-generate-report.js
│   └── testmo-integration.js
└── ... (other files and directories)
```

### Migration Steps

The migration process involved the following steps:

1. **Creating the monorepo package structure**
   ```javascript
   // Create directory structure
   mkdir -p packages/{core,mcp-optimizer,shared-utils,test-framework,qawolf-integration,testmo-integration,github-integration,linear-integration,reporting}
   ```

2. **Implementing core packages**
   ```typescript
   // Example: Core package event bus implementation
   export class EventBus {
     private static instance: EventBus;
     private listeners: Map<string, Function[]> = new Map();

     private constructor() {}

     public static getInstance(): EventBus {
       if (!EventBus.instance) {
         EventBus.instance = new EventBus();
       }
       return EventBus.instance;
     }

     public on(event: string, listener: Function): void {
       if (!this.listeners.has(event)) {
         this.listeners.set(event, []);
       }
       this.listeners.get(event)!.push(listener);
     }

     public emit(event: string, data: any): void {
       if (this.listeners.has(event)) {
         this.listeners.get(event)!.forEach(listener => listener(data));
       }
     }
   }
   ```

3. **Implementing integration packages**
   ```typescript
   // Example: QA Wolf Integration package
   import { EventBus } from '@qawolf/core';

   export class QAWolfClient {
     private apiKey: string;
     private teamId: string;
     private eventBus: EventBus;

     constructor(apiKey: string, teamId: string) {
       this.apiKey = apiKey;
       this.teamId = teamId;
       this.eventBus = EventBus.getInstance();
     }

     // API methods...
   }
   ```

4. **Implementing the reporting package**
   ```typescript
   // Example: Report generator
   import { EventBus } from '@qawolf/core';

   export class ReportGenerator {
     private eventBus: EventBus;

     constructor() {
       this.eventBus = EventBus.getInstance();
     }

     // Report generation methods...
   }
   ```

5. **Migrating existing scripts**
   ```javascript
   // Example: Script migration utility
   const fs = require('fs');
   const path = require('path');

   // Define migration mappings
   const migrations = [
     {
       source: 'scripts/qawolf-ci-greenlight.js',
       target: 'packages/qawolf-integration/bin/ci-greenlight.js',
       content: `#!/usr/bin/env node

   const { createCIGreenlight } = require('../dist/ci-greenlight');

   // Parse command line arguments
   const args = process.argv.slice(2);
   const options = {};

   // ... rest of the script
   `
     },
     // Other migrations...
   ];

   // Migrate scripts
   migrations.forEach(migration => {
     console.log(`Migrating ${migration.source} to ${migration.target}...`);

     // Create directory if it doesn't exist
     const targetDir = path.dirname(migration.target);
     if (!fs.existsSync(targetDir)) {
       fs.mkdirSync(targetDir, { recursive: true });
     }

     // Write the new file
     fs.writeFileSync(migration.target, migration.content);

     console.log(`Migrated ${migration.source} to ${migration.target}`);
   });
   ```

6. **Updating dependencies**
   ```json
   // Example: QA Wolf Integration package.json
   {
     "name": "@qawolf/qawolf-integration",
     "version": "0.1.0",
     "description": "QA Wolf integration for QA Wolf Metrics Framework",
     "main": "dist/index.js",
     "types": "dist/index.d.ts",
     "bin": {
       "qawolf-ci-greenlight": "./bin/ci-greenlight.js",
       "qawolf-notify-deployment": "./bin/notify-deployment.js",
       "qawolf-generate-report": "./bin/generate-report.js"
     },
     "scripts": {
       "build": "tsc",
       "test": "jest",
       "lint": "eslint ."
     },
     "dependencies": {
       "@qawolf/core": "^0.1.0",
       "axios": "^0.21.1"
     }
   }
   ```

7. **Testing and validating**
   ```bash
   # Build all packages
   for package in packages/*; do
     cd $package
     npm run build
     cd ../..
   done

   # Run tests
   for package in packages/*; do
     cd $package
     npm test
     cd ../..
   done
   ```

### Backward Compatibility

To ensure backward compatibility, the migration process included the following measures:

1. **Script Migration**: Existing scripts were migrated to the new package structure with the same functionality.
   ```javascript
   // Example: Legacy script wrapper
   #!/usr/bin/env node

   // This is a wrapper for backward compatibility
   // It uses the new package structure internally
   const { createCIGreenlight } = require('@qawolf/qawolf-integration');

   // Parse command line arguments
   const args = process.argv.slice(2);
   const options = {};

   // ... rest of the script
   ```

2. **CLI Compatibility**: CLI commands and options were preserved to ensure existing workflows continue to work.
   ```javascript
   // Example: Preserving CLI options
   for (let i = 0; i < args.length; i++) {
     const arg = args[i];

     if (arg === '--api-key' && i + 1 < args.length) {
       options.apiKey = args[++i];
     } else if (arg === '--team-id' && i + 1 < args.length) {
       options.teamId = args[++i];
     }
     // ... other options
   }
   ```

3. **API Compatibility**: APIs were designed to be compatible with existing code that might be using them.
   ```typescript
   // Example: Backward compatible API
   export function notifyDeployment(options) {
     // For backward compatibility
     return createDeploymentNotification(options).notifyDeployment();
   }
   ```

4. **Documentation**: Comprehensive documentation was provided to help users understand the changes and how to migrate their code.
   ```markdown
   # Migration Guide

   This guide helps you migrate from the previous structure to the new monorepo architecture.

   ## Using the New Packages

   Instead of using the legacy scripts directly, you can now use the new packages:

   ```javascript
   const { createCIGreenlight } = require('@qawolf/qawolf-integration');

   const ciGreenlight = createCIGreenlight({
     apiKey: 'your-api-key',
     teamId: 'your-team-id'
   });

   ciGreenlight.pollForGreenlight()
     .then(greenlight => {
       console.log(`Greenlight: ${greenlight}`);
     });
   ```
   ```

## Benefits of the New Architecture

The new monorepo architecture provides several benefits over the previous implementation:

### Modularity

The new architecture is highly modular, with each package having a specific responsibility. This makes it easier to understand, maintain, and extend the codebase.

**Benefits:**
- Each package has a specific responsibility
- Packages can be developed, tested, and deployed independently
- Easier to maintain and extend
- Clear boundaries between components

**Example:**
```typescript
// Before: Monolithic approach
function notifyDeploymentAndPollGreenlight() {
  // Notify deployment
  // Poll for greenlight
  // Generate report
}

// After: Modular approach
// In QA Wolf Integration package
function notifyDeployment() {
  // Notify deployment
}

function pollGreenlight() {
  // Poll for greenlight
}

// In Reporting package
function generateReport() {
  // Generate report
}
```

### Code Reusability

The new architecture promotes code reusability, with common functionality shared across packages. This reduces code duplication and improves consistency.

**Benefits:**
- Common functionality is shared across packages
- Reduces code duplication
- Improves consistency
- Easier to maintain and update

**Example:**
```typescript
// Before: Duplicated code
// In QA Wolf integration
function formatDate(date) {
  // Format date
}

// In Testmo integration
function formatDate(date) {
  // Format date (duplicated)
}

// After: Shared code
// In Shared Utils package
export function formatDate(date) {
  // Format date
}

// In QA Wolf integration
import { formatDate } from '@qawolf/shared-utils';

// In Testmo integration
import { formatDate } from '@qawolf/shared-utils';
```

### Scalability

The new architecture is designed to be scalable, with the ability to add new integrations and features as separate packages. This makes it easier to extend the framework without affecting existing functionality.

**Benefits:**
- New integrations can be added as separate packages
- Existing packages can be extended without affecting others
- Easier to add new features
- Better support for growing teams and projects

**Example:**
```typescript
// Adding a new integration package
// Create a new package for Jira integration
// packages/jira-integration/src/index.ts
import { EventBus } from '@qawolf/core';

export class JiraClient {
  private apiKey: string;
  private url: string;
  private eventBus: EventBus;

  constructor(apiKey: string, url: string) {
    this.apiKey = apiKey;
    this.url = url;
    this.eventBus = EventBus.getInstance();
  }

  // Jira API methods...
}
```

### Maintainability

The new architecture is designed to be maintainable, with clear separation of concerns and well-defined interfaces between packages. This makes it easier to understand and debug the codebase.

**Benefits:**
- Clear separation of concerns
- Well-defined interfaces between packages
- Easier to understand and debug
- Better support for team collaboration

**Example:**
```typescript
// Before: Unclear responsibilities
function processTestResults(results) {
  // Process results
  // Report to Testmo
  // Create issues in Linear
  // Generate report
}

// After: Clear responsibilities
// In QA Wolf Integration package
function processTestResults(results) {
  // Process results
  eventBus.emit('test-results-processed', results);
}

// In Testmo Integration package
eventBus.on('test-results-processed', results => {
  // Report to Testmo
});

// In Linear Integration package
eventBus.on('test-results-processed', results => {
  // Create issues in Linear
});

// In Reporting package
eventBus.on('test-results-processed', results => {
  // Generate report
});
```

### Testability

The new architecture is designed to be testable, with each package being testable independently. This makes it easier to write unit tests and improve test coverage.

**Benefits:**
- Each package can be tested independently
- Easier to write unit tests
- Improved test coverage
- Better support for test-driven development

**Example:**
```typescript
// Testing a package independently
// In QA Wolf Integration package tests
import { createCIGreenlight } from '../src/ci-greenlight';

describe('CI Greenlight', () => {
  it('should poll for greenlight', async () => {
    // Mock API responses
    const mockAxios = jest.spyOn(axios, 'get').mockResolvedValue({
      data: {
        runStage: 'complete',
        greenlight: true
      }
    });

    const ciGreenlight = createCIGreenlight({
      apiKey: 'test-api-key',
      teamId: 'test-team-id'
    });

    const result = await ciGreenlight.pollForGreenlight();

    expect(result).toBe(true);
    expect(mockAxios).toHaveBeenCalledWith(
      expect.stringContaining('/greenlight'),
      expect.objectContaining({
        headers: expect.objectContaining({
          'Authorization': 'Bearer test-api-key'
        })
      })
    );
  });
});
```

## Challenges and Mitigations

The monorepo architecture also presents some challenges, but these can be mitigated with proper tooling and processes:

### Increased Complexity

**Challenge**: More complex build and deployment processes.

**Mitigation**:
- Use Lerna to manage the monorepo
- Set up CI/CD pipelines to handle the complexity
- Provide clear documentation for developers

### Larger Repository Size

**Challenge**: Repository size increases with more packages.

**Mitigation**:
- Use Git LFS for large files
- Implement shallow cloning for CI/CD
- Optimize repository structure

### Slower CI/CD

**Challenge**: CI/CD pipelines may take longer to run.

**Mitigation**:
- Implement incremental builds
- Use caching for dependencies
- Parallelize CI/CD jobs

### Versioning Complexity

**Challenge**: Versioning becomes more complex with multiple packages.

**Mitigation**:
- Use Lerna for versioning
- Implement semantic versioning
- Automate version bumping
