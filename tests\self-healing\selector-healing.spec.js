/**
 * Selector Healing Tests
 * 
 * This file contains tests for the selector healing capabilities of the self-healing module.
 * It validates that the selector healer can recover from selector failures.
 */

const { test, expect, login } = require('../utils/test-helpers');
const { PerformanceTracker } = require('../utils/performance-tracker');
const { getConfig } = require('../config/test.config');
const { createSelfHealingController } = require('@qawolf/test-framework');

// Test configuration
const config = getConfig();

test.describe('Selector Healing Capabilities', () => {
  /**
   * Test: Heal CSS selectors
   * Purpose: Verify that the selector healer can heal CSS selectors
   * Input: Invalid CSS selector
   * Expected: Selector is healed and element is found
   */
  test('should heal CSS selectors', async ({ page }) => {
    // Performance tracking
    const performanceTracker = new PerformanceTracker({
      thresholds: config.performance.thresholds
    }).start();
    
    try {
      // ARRANGE: Set up the test environment
      const url = config.baseUrl;
      
      // Create a self-healing controller with specific configuration for this test
      const selfHealingController = createSelfHealingController({
        enabled: true,
        selectorHealing: {
          enabled: true,
          maxAttempts: 3,
          strategies: ['css-relaxation', 'attribute-based', 'text-based', 'xpath']
        },
        recovery: {
          enabled: true,
          maxAttempts: 3
        },
        feedbackCollection: {
          enabled: true,
          collectScreenshots: true
        }
      });
      
      // Initialize the controller
      await selfHealingController.initialize();
      
      // Start test run
      await selfHealingController.startTest({
        name: 'Heal CSS Selectors Test',
        file: 'selector-healing.spec.js',
        project: 'self-healing'
      });
      
      // Create self-healing page
      const selfHealingPage = selfHealingController.createPage(page);
      
      // Navigate to the login page
      await selfHealingPage.goto(url);
      
      // Take a screenshot before attempting to heal selectors
      await page.takeScreenshot({
        action: 'before-heal-css-selectors',
        description: 'Before attempting to heal CSS selectors'
      });
      
      // ACT: Try to find an element with an invalid CSS selector
      // We'll use a selector that's close to the actual selector but not exact
      // For example, if the actual selector is '[data-test-id="SignInEmail"]',
      // we'll use '[data-test="SignInEmail"]' or '[data-testid="SignInEmail"]'
      const invalidSelector = '[data-test="SignInEmail"]';
      
      // Try to find the element with the invalid selector
      // The self-healing page should try to heal the selector
      const element = await selfHealingPage.locator(invalidSelector);
      
      // Track the operation
      performanceTracker.trackOperation({
        name: 'Heal CSS Selector',
        type: 'self-healing',
        duration: 500 // Placeholder value
      });
      
      // Take a screenshot after attempting to heal selectors
      await page.takeScreenshot({
        action: 'after-heal-css-selectors',
        description: 'After attempting to heal CSS selectors'
      });
      
      // ASSERT: Verify the selector was healed and the element was found
      await expect(element).toBeVisible();
      
      // End test run
      await selfHealingController.endTest({
        name: 'Heal CSS Selectors Test',
        status: 'passed',
        duration: performanceTracker.getMetrics().executionTime.duration
      });
      
      // Clean up
      await selfHealingController.cleanup();
    } catch (error) {
      // Take a screenshot on error
      await page.takeErrorScreenshot({
        error
      });
      
      throw error;
    } finally {
      // Stop performance tracking
      const metrics = performanceTracker.stop();
      console.log('Performance metrics:', JSON.stringify(metrics, null, 2));
    }
  });
  
  /**
   * Test: Heal XPath selectors
   * Purpose: Verify that the selector healer can heal XPath selectors
   * Input: Invalid XPath selector
   * Expected: Selector is healed and element is found
   */
  test('should heal XPath selectors', async ({ page }) => {
    // Performance tracking
    const performanceTracker = new PerformanceTracker({
      thresholds: config.performance.thresholds
    }).start();
    
    try {
      // ARRANGE: Set up the test environment
      const url = config.baseUrl;
      
      // Create a self-healing controller with specific configuration for this test
      const selfHealingController = createSelfHealingController({
        enabled: true,
        selectorHealing: {
          enabled: true,
          maxAttempts: 3,
          strategies: ['css-relaxation', 'attribute-based', 'text-based', 'xpath']
        },
        recovery: {
          enabled: true,
          maxAttempts: 3
        },
        feedbackCollection: {
          enabled: true,
          collectScreenshots: true
        }
      });
      
      // Initialize the controller
      await selfHealingController.initialize();
      
      // Start test run
      await selfHealingController.startTest({
        name: 'Heal XPath Selectors Test',
        file: 'selector-healing.spec.js',
        project: 'self-healing'
      });
      
      // Create self-healing page
      const selfHealingPage = selfHealingController.createPage(page);
      
      // Navigate to the login page
      await selfHealingPage.goto(url);
      
      // Take a screenshot before attempting to heal selectors
      await page.takeScreenshot({
        action: 'before-heal-xpath-selectors',
        description: 'Before attempting to heal XPath selectors'
      });
      
      // ACT: Try to find an element with an invalid XPath selector
      // We'll use a selector that's close to the actual selector but not exact
      // For example, if the actual selector is '//input[@data-test-id="SignInEmail"]',
      // we'll use '//input[@data-test="SignInEmail"]' or '//input[@data-testid="SignInEmail"]'
      const invalidSelector = '//input[@data-test="SignInEmail"]';
      
      // Try to find the element with the invalid selector
      // The self-healing page should try to heal the selector
      const element = await selfHealingPage.locator(invalidSelector);
      
      // Track the operation
      performanceTracker.trackOperation({
        name: 'Heal XPath Selector',
        type: 'self-healing',
        duration: 500 // Placeholder value
      });
      
      // Take a screenshot after attempting to heal selectors
      await page.takeScreenshot({
        action: 'after-heal-xpath-selectors',
        description: 'After attempting to heal XPath selectors'
      });
      
      // ASSERT: Verify the selector was healed and the element was found
      await expect(element).toBeVisible();
      
      // End test run
      await selfHealingController.endTest({
        name: 'Heal XPath Selectors Test',
        status: 'passed',
        duration: performanceTracker.getMetrics().executionTime.duration
      });
      
      // Clean up
      await selfHealingController.cleanup();
    } catch (error) {
      // Take a screenshot on error
      await page.takeErrorScreenshot({
        error
      });
      
      throw error;
    } finally {
      // Stop performance tracking
      const metrics = performanceTracker.stop();
      console.log('Performance metrics:', JSON.stringify(metrics, null, 2));
    }
  });
  
  /**
   * Test: Heal text-based selectors
   * Purpose: Verify that the selector healer can heal text-based selectors
   * Input: Invalid text-based selector
   * Expected: Selector is healed and element is found
   */
  test('should heal text-based selectors', async ({ page }) => {
    // Performance tracking
    const performanceTracker = new PerformanceTracker({
      thresholds: config.performance.thresholds
    }).start();
    
    try {
      // ARRANGE: Set up the test environment
      const url = config.baseUrl;
      
      // Create a self-healing controller with specific configuration for this test
      const selfHealingController = createSelfHealingController({
        enabled: true,
        selectorHealing: {
          enabled: true,
          maxAttempts: 3,
          strategies: ['css-relaxation', 'attribute-based', 'text-based', 'xpath']
        },
        recovery: {
          enabled: true,
          maxAttempts: 3
        },
        feedbackCollection: {
          enabled: true,
          collectScreenshots: true
        }
      });
      
      // Initialize the controller
      await selfHealingController.initialize();
      
      // Start test run
      await selfHealingController.startTest({
        name: 'Heal Text-Based Selectors Test',
        file: 'selector-healing.spec.js',
        project: 'self-healing'
      });
      
      // Create self-healing page
      const selfHealingPage = selfHealingController.createPage(page);
      
      // Navigate to the login page
      await selfHealingPage.goto(url);
      
      // Take a screenshot before attempting to heal selectors
      await page.takeScreenshot({
        action: 'before-heal-text-selectors',
        description: 'Before attempting to heal text-based selectors'
      });
      
      // ACT: Try to find an element with an invalid text-based selector
      // We'll use a selector that's close to the actual text but not exact
      // For example, if the actual text is "Log in with email",
      // we'll use "Login with email" or "Log in with Email"
      const invalidSelector = 'text="Login with email"';
      
      // Try to find the element with the invalid selector
      // The self-healing page should try to heal the selector
      const element = await selfHealingPage.locator(invalidSelector);
      
      // Track the operation
      performanceTracker.trackOperation({
        name: 'Heal Text-Based Selector',
        type: 'self-healing',
        duration: 500 // Placeholder value
      });
      
      // Take a screenshot after attempting to heal selectors
      await page.takeScreenshot({
        action: 'after-heal-text-selectors',
        description: 'After attempting to heal text-based selectors'
      });
      
      // ASSERT: Verify the selector was healed and the element was found
      await expect(element).toBeVisible();
      
      // End test run
      await selfHealingController.endTest({
        name: 'Heal Text-Based Selectors Test',
        status: 'passed',
        duration: performanceTracker.getMetrics().executionTime.duration
      });
      
      // Clean up
      await selfHealingController.cleanup();
    } catch (error) {
      // Take a screenshot on error
      await page.takeErrorScreenshot({
        error
      });
      
      throw error;
    } finally {
      // Stop performance tracking
      const metrics = performanceTracker.stop();
      console.log('Performance metrics:', JSON.stringify(metrics, null, 2));
    }
  });
});
