/**
 * Test Metrics Dashboard
 * 
 * This module provides a dashboard for test metrics.
 */

const fs = require('fs');
const path = require('path');
const { createReportGenerator } = require('../fixtures/report-generator');

/**
 * Create a test metrics dashboard
 * @param {Object} options - Options for the dashboard
 * @returns {Object} - Test metrics dashboard
 */
function createDashboard(options = {}) {
  const {
    reportsDir = path.join(process.cwd(), 'reports'),
    outputDir = path.join(process.cwd(), 'dashboard'),
    format = 'markdown'
  } = options;
  
  return {
    /**
     * Collect metrics from all reports
     * @returns {Promise<Object>} - Collected metrics
     */
    async collectMetrics() {
      // Create output directory if it doesn't exist
      if (!fs.existsSync(outputDir)) {
        await fs.promises.mkdir(outputDir, { recursive: true });
      }
      
      // Get all report files
      const reportFiles = await fs.promises.readdir(reportsDir);
      const jsonReports = reportFiles.filter(file => file.endsWith('.json'));
      
      // Collect metrics from all reports
      const allMetrics = {
        executionTime: {
          total: 0,
          tests: {}
        },
        successRate: {
          total: 0,
          success: 0,
          failure: 0,
          tests: {}
        },
        selfHealing: {
          total: 0,
          success: 0,
          failure: 0,
          tests: {}
        },
        tokenUsage: {
          total: 0,
          tests: {}
        },
        resourceUsage: {
          cpu: {
            average: 0,
            peak: 0,
            tests: {}
          },
          memory: {
            average: 0,
            peak: 0,
            tests: {}
          }
        }
      };
      
      // Read and merge all reports
      for (const reportFile of jsonReports) {
        const reportPath = path.join(reportsDir, reportFile);
        const reportContent = await fs.promises.readFile(reportPath, 'utf8');
        const report = JSON.parse(reportContent);
        
        // Merge execution time
        for (const [testName, duration] of Object.entries(report.executionTime.tests)) {
          allMetrics.executionTime.tests[testName] = duration;
          allMetrics.executionTime.total += duration;
        }
        
        // Merge success rate
        for (const [testName, success] of Object.entries(report.successRate.tests)) {
          allMetrics.successRate.tests[testName] = success;
          allMetrics.successRate.total++;
          
          if (success) {
            allMetrics.successRate.success++;
          } else {
            allMetrics.successRate.failure++;
          }
        }
        
        // Merge self-healing
        if (report.selfHealing) {
          for (const [testName, data] of Object.entries(report.selfHealing.tests)) {
            if (!allMetrics.selfHealing.tests[testName]) {
              allMetrics.selfHealing.tests[testName] = {
                total: 0,
                success: 0,
                failure: 0
              };
            }
            
            allMetrics.selfHealing.tests[testName].total += data.total;
            allMetrics.selfHealing.tests[testName].success += data.success;
            allMetrics.selfHealing.tests[testName].failure += data.failure;
            
            allMetrics.selfHealing.total += data.total;
            allMetrics.selfHealing.success += data.success;
            allMetrics.selfHealing.failure += data.failure;
          }
        }
        
        // Merge token usage
        if (report.tokenUsage) {
          for (const [testName, tokens] of Object.entries(report.tokenUsage.tests)) {
            allMetrics.tokenUsage.tests[testName] = tokens;
            allMetrics.tokenUsage.total += tokens;
          }
        }
        
        // Merge resource usage
        if (report.resourceUsage) {
          for (const [testName, cpu] of Object.entries(report.resourceUsage.cpu.tests)) {
            allMetrics.resourceUsage.cpu.tests[testName] = cpu;
            allMetrics.resourceUsage.cpu.peak = Math.max(allMetrics.resourceUsage.cpu.peak, cpu);
          }
          
          for (const [testName, memory] of Object.entries(report.resourceUsage.memory.tests)) {
            allMetrics.resourceUsage.memory.tests[testName] = memory;
            allMetrics.resourceUsage.memory.peak = Math.max(allMetrics.resourceUsage.memory.peak, memory);
          }
          
          // Update average values
          const cpuTests = Object.values(allMetrics.resourceUsage.cpu.tests);
          allMetrics.resourceUsage.cpu.average = cpuTests.reduce((a, b) => a + b, 0) / cpuTests.length;
          
          const memoryTests = Object.values(allMetrics.resourceUsage.memory.tests);
          allMetrics.resourceUsage.memory.average = memoryTests.reduce((a, b) => a + b, 0) / memoryTests.length;
        }
      }
      
      return allMetrics;
    },
    
    /**
     * Generate a dashboard
     * @returns {Promise<string>} - Dashboard file path
     */
    async generateDashboard() {
      // Collect metrics
      const metrics = await this.collectMetrics();
      
      // Create report generator
      const reportGenerator = createReportGenerator({
        outputDir,
        format
      });
      
      // Generate dashboard
      const dashboardPath = await reportGenerator.generateReport(metrics, 'test-metrics-dashboard');
      
      return dashboardPath;
    },
    
    /**
     * Calculate performance improvements
     * @param {Object} metrics - Collected metrics
     * @returns {Object} - Performance improvements
     */
    calculateImprovements(metrics) {
      const improvements = {
        executionTime: {},
        flakiness: {},
        tokenUsage: {}
      };
      
      // Calculate execution time improvements
      const withoutMcpTests = Object.keys(metrics.executionTime.tests).filter(name => name.includes('without-mcp'));
      const withMcpTests = Object.keys(metrics.executionTime.tests).filter(name => name.includes('with-mcp'));
      
      if (withoutMcpTests.length > 0 && withMcpTests.length > 0) {
        const withoutMcpAvg = withoutMcpTests.reduce((sum, name) => sum + metrics.executionTime.tests[name], 0) / withoutMcpTests.length;
        const withMcpAvg = withMcpTests.reduce((sum, name) => sum + metrics.executionTime.tests[name], 0) / withMcpTests.length;
        
        improvements.executionTime.mcp = {
          withoutMcp: withoutMcpAvg,
          withMcp: withMcpAvg,
          improvement: (withoutMcpAvg - withMcpAvg) / withoutMcpAvg
        };
      }
      
      const withoutSelfHealingTests = Object.keys(metrics.executionTime.tests).filter(name => name.includes('without-self-healing'));
      const withSelfHealingTests = Object.keys(metrics.executionTime.tests).filter(name => name.includes('with-self-healing'));
      
      if (withoutSelfHealingTests.length > 0 && withSelfHealingTests.length > 0) {
        const withoutSelfHealingAvg = withoutSelfHealingTests.reduce((sum, name) => sum + metrics.executionTime.tests[name], 0) / withoutSelfHealingTests.length;
        const withSelfHealingAvg = withSelfHealingTests.reduce((sum, name) => sum + metrics.executionTime.tests[name], 0) / withSelfHealingTests.length;
        
        improvements.executionTime.selfHealing = {
          withoutSelfHealing: withoutSelfHealingAvg,
          withSelfHealing: withSelfHealingAvg,
          improvement: (withoutSelfHealingAvg - withSelfHealingAvg) / withoutSelfHealingAvg
        };
      }
      
      // Calculate flakiness improvements
      const withoutSelfHealingFlakyTests = Object.keys(metrics.successRate.tests).filter(name => name.includes('flaky-test-without-self-healing'));
      const withSelfHealingFlakyTests = Object.keys(metrics.successRate.tests).filter(name => name.includes('flaky-test-with-self-healing'));
      
      if (withoutSelfHealingFlakyTests.length > 0 && withSelfHealingFlakyTests.length > 0) {
        const withoutSelfHealingSuccessCount = withoutSelfHealingFlakyTests.filter(name => metrics.successRate.tests[name]).length;
        const withSelfHealingSuccessCount = withSelfHealingFlakyTests.filter(name => metrics.successRate.tests[name]).length;
        
        const withoutSelfHealingSuccessRate = withoutSelfHealingSuccessCount / withoutSelfHealingFlakyTests.length;
        const withSelfHealingSuccessRate = withSelfHealingSuccessCount / withSelfHealingFlakyTests.length;
        
        improvements.flakiness = {
          withoutSelfHealing: 1 - withoutSelfHealingSuccessRate,
          withSelfHealing: 1 - withSelfHealingSuccessRate,
          improvement: ((1 - withoutSelfHealingSuccessRate) - (1 - withSelfHealingSuccessRate)) / (1 - withoutSelfHealingSuccessRate)
        };
      }
      
      return improvements;
    },
    
    /**
     * Generate an improvements report
     * @returns {Promise<string>} - Improvements report file path
     */
    async generateImprovementsReport() {
      // Collect metrics
      const metrics = await this.collectMetrics();
      
      // Calculate improvements
      const improvements = this.calculateImprovements(metrics);
      
      // Create output directory if it doesn't exist
      if (!fs.existsSync(outputDir)) {
        await fs.promises.mkdir(outputDir, { recursive: true });
      }
      
      // Generate report content
      let content = '# Performance Improvements Report\n\n';
      content += `Generated: ${new Date().toISOString()}\n\n`;
      
      // Execution Time Improvements
      content += '## Execution Time Improvements\n\n';
      
      if (improvements.executionTime.mcp) {
        content += '### MCP Optimization\n\n';
        content += `- Without MCP: ${improvements.executionTime.mcp.withoutMcp.toFixed(2)}ms\n`;
        content += `- With MCP: ${improvements.executionTime.mcp.withMcp.toFixed(2)}ms\n`;
        content += `- Improvement: ${(improvements.executionTime.mcp.improvement * 100).toFixed(2)}%\n\n`;
      }
      
      if (improvements.executionTime.selfHealing) {
        content += '### Self-Healing\n\n';
        content += `- Without Self-Healing: ${improvements.executionTime.selfHealing.withoutSelfHealing.toFixed(2)}ms\n`;
        content += `- With Self-Healing: ${improvements.executionTime.selfHealing.withSelfHealing.toFixed(2)}ms\n`;
        content += `- Improvement: ${(improvements.executionTime.selfHealing.improvement * 100).toFixed(2)}%\n\n`;
      }
      
      // Flakiness Improvements
      if (improvements.flakiness) {
        content += '## Flakiness Improvements\n\n';
        content += `- Without Self-Healing: ${(improvements.flakiness.withoutSelfHealing * 100).toFixed(2)}%\n`;
        content += `- With Self-Healing: ${(improvements.flakiness.withSelfHealing * 100).toFixed(2)}%\n`;
        content += `- Improvement: ${(improvements.flakiness.improvement * 100).toFixed(2)}%\n\n`;
      }
      
      // Write report to file
      const timestamp = new Date().toISOString().replace(/:/g, '-');
      const fileName = `improvements-report-${timestamp}.md`;
      const filePath = path.join(outputDir, fileName);
      
      await fs.promises.writeFile(filePath, content, 'utf8');
      
      return filePath;
    }
  };
}

module.exports = {
  createDashboard
};