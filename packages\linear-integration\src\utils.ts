/**
 * Utilities for Linear integration
 */

import * as dotenv from 'dotenv';
import { Issue, Project, Team, User, Comment, Attachment, IssueLabel, Workflow, WorkflowState, Cycle, IssueHistory, IssueRelation, Organization } from '@linear/sdk';

// Load environment variables
dotenv.config();

/**
 * Get API key from environment
 * @returns API key
 */
export function getAPIKeyFromEnv(): string {
  return process.env.LINEAR_API_KEY || '';
}

/**
 * Get team ID from environment
 * @returns Team ID
 */
export function getTeamIdFromEnv(): string {
  return process.env.LINEAR_TEAM_ID || '';
}

/**
 * Get project ID from environment
 * @returns Project ID
 */
export function getProjectIdFromEnv(): string {
  return process.env.LINEAR_PROJECT_ID || '';
}

/**
 * Format date
 * @param date Date
 * @returns Formatted date
 */
export function formatDate(date: string | Date): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toISOString();
}

/**
 * Get issue URL
 * @param issue Issue
 * @returns Issue URL
 */
export function getIssueURL(issue: Issue): string {
  return issue.url;
}

/**
 * Get issue identifier
 * @param issue Issue
 * @returns Issue identifier
 */
export function getIssueIdentifier(issue: Issue): string {
  return `${issue.team?.key}-${issue.number}`;
}

/**
 * Get issue status
 * @param issue Issue
 * @returns Issue status
 */
export function getIssueStatus(issue: Issue): string {
  return issue.state?.name || '';
}

/**
 * Get issue priority
 * @param issue Issue
 * @returns Issue priority
 */
export function getIssuePriority(issue: Issue): string {
  switch (issue.priority) {
    case 0:
      return 'No priority';
    case 1:
      return 'Urgent';
    case 2:
      return 'High';
    case 3:
      return 'Medium';
    case 4:
      return 'Low';
    default:
      return 'Unknown';
  }
}

/**
 * Get issue labels
 * @param issue Issue
 * @returns Issue labels
 */
export async function getIssueLabels(issue: Issue): Promise<string[]> {
  const labels = await issue.labels();
  const nodes = await labels.nodes;
  return nodes.map(label => label.name);
}

/**
 * Get issue assignee
 * @param issue Issue
 * @returns Issue assignee
 */
export function getIssueAssignee(issue: Issue): User | null {
  return issue.assignee;
}

/**
 * Get issue creator
 * @param issue Issue
 * @returns Issue creator
 */
export function getIssueCreator(issue: Issue): User | null {
  return issue.creator;
}

/**
 * Get issue team
 * @param issue Issue
 * @returns Issue team
 */
export function getIssueTeam(issue: Issue): Team | null {
  return issue.team;
}

/**
 * Get issue project
 * @param issue Issue
 * @returns Issue project
 */
export function getIssueProject(issue: Issue): Project | null {
  return issue.project;
}

/**
 * Get issue cycle
 * @param issue Issue
 * @returns Issue cycle
 */
export function getIssueCycle(issue: Issue): Cycle | null {
  return issue.cycle;
}

/**
 * Get issue parent
 * @param issue Issue
 * @returns Issue parent
 */
export function getIssueParent(issue: Issue): Issue | null {
  return issue.parent;
}

/**
 * Get issue children
 * @param issue Issue
 * @returns Issue children
 */
export async function getIssueChildren(issue: Issue): Promise<Issue[]> {
  const children = await issue.children();
  return await children.nodes;
}

/**
 * Get issue comments
 * @param issue Issue
 * @returns Issue comments
 */
export async function getIssueComments(issue: Issue): Promise<Comment[]> {
  const comments = await issue.comments();
  return await comments.nodes;
}

/**
 * Get issue attachments
 * @param issue Issue
 * @returns Issue attachments
 */
export async function getIssueAttachments(issue: Issue): Promise<Attachment[]> {
  const attachments = await issue.attachments();
  return await attachments.nodes;
}

/**
 * Get issue history
 * @param issue Issue
 * @returns Issue history
 */
export async function getIssueHistory(issue: Issue): Promise<IssueHistory[]> {
  const history = await issue.history();
  return await history.nodes;
}

/**
 * Get issue relations
 * @param issue Issue
 * @returns Issue relations
 */
export async function getIssueRelations(issue: Issue): Promise<IssueRelation[]> {
  const relations = await issue.relations();
  return await relations.nodes;
}

/**
 * Get project URL
 * @param project Project
 * @returns Project URL
 */
export function getProjectURL(project: Project): string {
  return project.url;
}

/**
 * Get project status
 * @param project Project
 * @returns Project status
 */
export function getProjectStatus(project: Project): string {
  return project.state || '';
}

/**
 * Get project lead
 * @param project Project
 * @returns Project lead
 */
export function getProjectLead(project: Project): User | null {
  return project.lead;
}

/**
 * Get project members
 * @param project Project
 * @returns Project members
 */
export async function getProjectMembers(project: Project): Promise<User[]> {
  const members = await project.members();
  return await members.nodes;
}

/**
 * Get project issues
 * @param project Project
 * @returns Project issues
 */
export async function getProjectIssues(project: Project): Promise<Issue[]> {
  const issues = await project.issues();
  return await issues.nodes;
}

/**
 * Get team URL
 * @param team Team
 * @returns Team URL
 */
export function getTeamURL(team: Team): string {
  return team.url;
}

/**
 * Get team key
 * @param team Team
 * @returns Team key
 */
export function getTeamKey(team: Team): string {
  return team.key;
}

/**
 * Get team lead
 * @param team Team
 * @returns Team lead
 */
export function getTeamLead(team: Team): User | null {
  return team.lead;
}

/**
 * Get team members
 * @param team Team
 * @returns Team members
 */
export async function getTeamMembers(team: Team): Promise<User[]> {
  const members = await team.members();
  return await members.nodes;
}

/**
 * Get team projects
 * @param team Team
 * @returns Team projects
 */
export async function getTeamProjects(team: Team): Promise<Project[]> {
  const projects = await team.projects();
  return await projects.nodes;
}

/**
 * Get team issues
 * @param team Team
 * @returns Team issues
 */
export async function getTeamIssues(team: Team): Promise<Issue[]> {
  const issues = await team.issues();
  return await issues.nodes;
}

/**
 * Get team labels
 * @param team Team
 * @returns Team labels
 */
export async function getTeamLabels(team: Team): Promise<IssueLabel[]> {
  const labels = await team.labels();
  return await labels.nodes;
}

/**
 * Get team states
 * @param team Team
 * @returns Team states
 */
export async function getTeamStates(team: Team): Promise<WorkflowState[]> {
  const states = await team.states();
  return await states.nodes;
}

/**
 * Get team workflows
 * @param team Team
 * @returns Team workflows
 */
export async function getTeamWorkflows(team: Team): Promise<Workflow[]> {
  const workflows = await team.workflows();
  return await workflows.nodes;
}

/**
 * Get team cycles
 * @param team Team
 * @returns Team cycles
 */
export async function getTeamCycles(team: Team): Promise<Cycle[]> {
  const cycles = await team.cycles();
  return await cycles.nodes;
}

/**
 * Get user URL
 * @param user User
 * @returns User URL
 */
export function getUserURL(user: User): string {
  return user.url;
}

/**
 * Get user display name
 * @param user User
 * @returns User display name
 */
export function getUserDisplayName(user: User): string {
  return user.displayName;
}

/**
 * Get user email
 * @param user User
 * @returns User email
 */
export function getUserEmail(user: User): string {
  return user.email;
}

/**
 * Get user avatar URL
 * @param user User
 * @returns User avatar URL
 */
export function getUserAvatarURL(user: User): string {
  return user.avatarUrl;
}

/**
 * Get user teams
 * @param user User
 * @returns User teams
 */
export async function getUserTeams(user: User): Promise<Team[]> {
  const teams = await user.teams();
  return await teams.nodes;
}

/**
 * Get user assigned issues
 * @param user User
 * @returns User assigned issues
 */
export async function getUserAssignedIssues(user: User): Promise<Issue[]> {
  const issues = await user.assignedIssues();
  return await issues.nodes;
}

/**
 * Get user created issues
 * @param user User
 * @returns User created issues
 */
export async function getUserCreatedIssues(user: User): Promise<Issue[]> {
  const issues = await user.createdIssues();
  return await issues.nodes;
}

/**
 * Get organization
 * @param user User
 * @returns Organization
 */
export function getOrganization(user: User): Organization | null {
  return user.organization;
}
