{"name": "@qawolf/docs", "version": "1.0.0", "description": "Documentation for QA Wolf testing framework", "private": true, "scripts": {"lint": "eslint .", "build": "echo 'No build step for docs'"}, "repository": {"type": "git", "url": "git+https://github.com/sneezyxl/QAWolfeesojc.git", "directory": "docs"}, "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "playwright", "testing", "documentation"], "author": "", "license": "MIT", "devDependencies": {"eslint": "^9.27.0"}}