#!/usr/bin/env node

/**
 * Build Packages Script
 * 
 * This script builds packages in the correct order.
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Configuration
const config = {
  packages: [
    'shared-utils',
    'mcp-optimizer',
    'test-framework'
  ]
};

/**
 * Build a package
 * @param {string} packageName - Package name
 * @returns {void}
 */
function buildPackage(packageName) {
  const packagePath = path.join(process.cwd(), packageName);
  const packageJsonPath = path.join(packagePath, 'package.json');
  
  // Check if package exists
  if (!fs.existsSync(packageJsonPath)) {
    console.error(`Package ${packageName} does not exist at ${packagePath}`);
    process.exit(1);
  }
  
  // Read package.json
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  // Check if package has a build script
  if (!packageJson.scripts || !packageJson.scripts.build) {
    console.log(`Package ${packageName} does not have a build script, skipping`);
    return;
  }
  
  // Build package
  console.log(`Building package ${packageName}...`);
  execSync('npm run build', { cwd: packagePath, stdio: 'inherit' });
  
  console.log(`Successfully built ${packageName}`);
}

/**
 * Build all packages
 * @returns {void}
 */
function buildAllPackages() {
  // Build packages in order
  for (const packageName of config.packages) {
    buildPackage(packageName);
  }
  
  console.log('All packages built successfully!');
}

// Run the script
buildAllPackages();