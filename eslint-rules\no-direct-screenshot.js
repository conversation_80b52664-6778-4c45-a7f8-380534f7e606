/**
 * ESLint rule to prevent direct usage of page.screenshot()
 *
 * This rule enforces the use of the screenshot utilities from screenshot-utils.js
 * instead of direct calls to page.screenshot().
 *
 * @module no-direct-screenshot
 * <AUTHOR> Wolf Team
 * @date 2025-01-01
 */

module.exports = {
  meta: {
    type: 'suggestion',
    docs: {
      description: 'Disallow direct usage of page.screenshot()',
      category: 'Best Practices',
      recommended: true,
    },
    fixable: 'code',
    schema: []
  },
  create: function(context) {
    return {
      CallExpression(node) {
        if (
          node.callee.type === 'MemberExpression' &&
          node.callee.property.name === 'screenshot' &&
          !context.getFilename().includes('screenshot-utils.js')
        ) {
          // Check if we're in the screenshot-utils.js file
          if (context.getFilename().includes('screenshot-utils.js')) {
            return;
          }

          // Check if we're calling page.screenshot()
          if (node.callee.object.type === 'Identifier') {
            context.report({
              node,
              message: 'Direct usage of page.screenshot() is not allowed. Use takeScreenshot() from screenshot-utils.js instead.',
              fix: function(fixer) {
                // Try to extract the path from the screenshot call
                let path = '';
                let fullPage = false;

                if (node.arguments.length > 0 && node.arguments[0].type === 'ObjectExpression') {
                  const properties = node.arguments[0].properties;

                  for (const prop of properties) {
                    if (prop.key.name === 'path' && prop.value.type === 'Literal') {
                      path = prop.value.value;
                    }

                    if (prop.key.name === 'fullPage' && prop.value.type === 'Literal') {
                      fullPage = prop.value.value;
                    }
                  }
                }

                // Extract the action from the path
                let action = 'screenshot';
                if (path) {
                  action = path.replace('.png', '').replace(/[-_]/g, '-');
                }

                // Create the replacement code
                const replacement = `takeScreenshot(${node.callee.object.name}, {
  testName: 'unknown_test',
  action: '${action}',
  fullPage: ${fullPage}
})`;

                return fixer.replaceText(node, replacement);
              }
            });
          }
        }
      }
    };
  }
};
