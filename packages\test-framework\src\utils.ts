/**
 * Utilities for QA Wolf Test Framework
 */

import { Page, Locator, ElementHandle } from 'playwright';
import { getConfig } from '@qawolf/core';

// Get configuration
const config = getConfig();

/**
 * Wait for element to be visible
 * @param page Page
 * @param selector Selector
 * @param timeout Timeout in milliseconds
 * @returns Locator
 */
export async function waitForElement(page: Page, selector: string, timeout: number = config.testTimeout): Promise<Locator> {
  const locator = page.locator(selector);
  await locator.waitFor({ state: 'visible', timeout });
  return locator;
}

/**
 * Wait for element to be hidden
 * @param page Page
 * @param selector Selector
 * @param timeout Timeout in milliseconds
 * @returns Locator
 */
export async function waitForElementToBeHidden(page: Page, selector: string, timeout: number = config.testTimeout): Promise<Locator> {
  const locator = page.locator(selector);
  await locator.waitFor({ state: 'hidden', timeout });
  return locator;
}

/**
 * Wait for element to be enabled
 * @param page Page
 * @param selector Selector
 * @param timeout Timeout in milliseconds
 * @returns Locator
 */
export async function waitForElementToBeEnabled(page: Page, selector: string, timeout: number = config.testTimeout): Promise<Locator> {
  const locator = page.locator(selector);
  await locator.waitFor({ state: 'enabled', timeout });
  return locator;
}

/**
 * Wait for element to be disabled
 * @param page Page
 * @param selector Selector
 * @param timeout Timeout in milliseconds
 * @returns Locator
 */
export async function waitForElementToBeDisabled(page: Page, selector: string, timeout: number = config.testTimeout): Promise<Locator> {
  const locator = page.locator(selector);
  await locator.waitFor({ state: 'disabled', timeout });
  return locator;
}

/**
 * Wait for element to have text
 * @param page Page
 * @param selector Selector
 * @param text Text to wait for
 * @param timeout Timeout in milliseconds
 * @returns Locator
 */
export async function waitForElementToHaveText(page: Page, selector: string, text: string | RegExp, timeout: number = config.testTimeout): Promise<Locator> {
  const locator = page.locator(selector);
  await locator.waitFor({ state: 'visible', timeout });
  
  if (typeof text === 'string') {
    await page.waitForFunction(
      ([selector, expectedText]) => {
        const element = document.querySelector(selector);
        return element && element.textContent && element.textContent.includes(expectedText);
      },
      [selector, text],
      { timeout }
    );
  } else {
    await page.waitForFunction(
      ([selector, expectedTextPattern]) => {
        const element = document.querySelector(selector);
        return element && element.textContent && new RegExp(expectedTextPattern).test(element.textContent);
      },
      [selector, text.source],
      { timeout }
    );
  }
  
  return locator;
}

/**
 * Wait for element to have value
 * @param page Page
 * @param selector Selector
 * @param value Value to wait for
 * @param timeout Timeout in milliseconds
 * @returns Locator
 */
export async function waitForElementToHaveValue(page: Page, selector: string, value: string | RegExp, timeout: number = config.testTimeout): Promise<Locator> {
  const locator = page.locator(selector);
  await locator.waitFor({ state: 'visible', timeout });
  
  if (typeof value === 'string') {
    await page.waitForFunction(
      ([selector, expectedValue]) => {
        const element = document.querySelector(selector) as HTMLInputElement;
        return element && element.value === expectedValue;
      },
      [selector, value],
      { timeout }
    );
  } else {
    await page.waitForFunction(
      ([selector, expectedValuePattern]) => {
        const element = document.querySelector(selector) as HTMLInputElement;
        return element && new RegExp(expectedValuePattern).test(element.value);
      },
      [selector, value.source],
      { timeout }
    );
  }
  
  return locator;
}

/**
 * Wait for navigation
 * @param page Page
 * @param options Options
 * @returns Navigation response
 */
export async function waitForNavigation(page: Page, options: { timeout?: number, url?: string | RegExp, waitUntil?: 'load' | 'domcontentloaded' | 'networkidle' } = {}): Promise<any> {
  return page.waitForNavigation({
    timeout: options.timeout || config.testTimeout,
    url: options.url,
    waitUntil: options.waitUntil || 'networkidle'
  });
}

/**
 * Wait for network idle
 * @param page Page
 * @param timeout Timeout in milliseconds
 */
export async function waitForNetworkIdle(page: Page, timeout: number = config.testTimeout): Promise<void> {
  await page.waitForLoadState('networkidle', { timeout });
}

/**
 * Wait for function
 * @param page Page
 * @param fn Function to wait for
 * @param args Function arguments
 * @param options Options
 * @returns Function result
 */
export async function waitForFunction<T>(page: Page, fn: Function, args: any[] = [], options: { timeout?: number, polling?: number } = {}): Promise<T> {
  return page.waitForFunction(
    fn,
    args,
    {
      timeout: options.timeout || config.testTimeout,
      polling: options.polling || 100
    }
  ) as Promise<T>;
}

/**
 * Wait for timeout
 * @param timeout Timeout in milliseconds
 */
export async function waitForTimeout(timeout: number): Promise<void> {
  await new Promise(resolve => setTimeout(resolve, timeout));
}

/**
 * Get element text
 * @param page Page
 * @param selector Selector
 * @returns Element text
 */
export async function getElementText(page: Page, selector: string): Promise<string> {
  const locator = page.locator(selector);
  return locator.textContent() || '';
}

/**
 * Get element value
 * @param page Page
 * @param selector Selector
 * @returns Element value
 */
export async function getElementValue(page: Page, selector: string): Promise<string> {
  const locator = page.locator(selector);
  return locator.inputValue();
}

/**
 * Get element attribute
 * @param page Page
 * @param selector Selector
 * @param attribute Attribute name
 * @returns Element attribute value
 */
export async function getElementAttribute(page: Page, selector: string, attribute: string): Promise<string | null> {
  const locator = page.locator(selector);
  return locator.getAttribute(attribute);
}

/**
 * Get element count
 * @param page Page
 * @param selector Selector
 * @returns Element count
 */
export async function getElementCount(page: Page, selector: string): Promise<number> {
  const locator = page.locator(selector);
  return locator.count();
}

/**
 * Check if element exists
 * @param page Page
 * @param selector Selector
 * @returns True if element exists, false otherwise
 */
export async function elementExists(page: Page, selector: string): Promise<boolean> {
  const count = await getElementCount(page, selector);
  return count > 0;
}

/**
 * Check if element is visible
 * @param page Page
 * @param selector Selector
 * @returns True if element is visible, false otherwise
 */
export async function elementIsVisible(page: Page, selector: string): Promise<boolean> {
  const locator = page.locator(selector);
  return locator.isVisible();
}

/**
 * Check if element is enabled
 * @param page Page
 * @param selector Selector
 * @returns True if element is enabled, false otherwise
 */
export async function elementIsEnabled(page: Page, selector: string): Promise<boolean> {
  const locator = page.locator(selector);
  return locator.isEnabled();
}

/**
 * Check if element is checked
 * @param page Page
 * @param selector Selector
 * @returns True if element is checked, false otherwise
 */
export async function elementIsChecked(page: Page, selector: string): Promise<boolean> {
  const locator = page.locator(selector);
  return locator.isChecked();
}

/**
 * Get page title
 * @param page Page
 * @returns Page title
 */
export async function getPageTitle(page: Page): Promise<string> {
  return page.title();
}

/**
 * Get page URL
 * @param page Page
 * @returns Page URL
 */
export async function getPageUrl(page: Page): Promise<string> {
  return page.url();
}

/**
 * Take screenshot
 * @param page Page
 * @param path Screenshot path
 * @param options Screenshot options
 * @returns Screenshot buffer
 */
export async function takeScreenshot(page: Page, path?: string, options: { fullPage?: boolean, clip?: { x: number, y: number, width: number, height: number } } = {}): Promise<Buffer> {
  return page.screenshot({
    path,
    fullPage: options.fullPage,
    clip: options.clip
  });
}

/**
 * Get element screenshot
 * @param page Page
 * @param selector Selector
 * @param path Screenshot path
 * @returns Screenshot buffer
 */
export async function getElementScreenshot(page: Page, selector: string, path?: string): Promise<Buffer> {
  const locator = page.locator(selector);
  return locator.screenshot({
    path
  });
}

/**
 * Get page HTML
 * @param page Page
 * @returns Page HTML
 */
export async function getPageHtml(page: Page): Promise<string> {
  return page.content();
}

/**
 * Get element HTML
 * @param page Page
 * @param selector Selector
 * @returns Element HTML
 */
export async function getElementHtml(page: Page, selector: string): Promise<string> {
  return page.evaluate((selector) => {
    const element = document.querySelector(selector);
    return element ? element.outerHTML : '';
  }, selector);
}

/**
 * Get element inner HTML
 * @param page Page
 * @param selector Selector
 * @returns Element inner HTML
 */
export async function getElementInnerHtml(page: Page, selector: string): Promise<string> {
  return page.evaluate((selector) => {
    const element = document.querySelector(selector);
    return element ? element.innerHTML : '';
  }, selector);
}

/**
 * Get element bounding box
 * @param page Page
 * @param selector Selector
 * @returns Element bounding box
 */
export async function getElementBoundingBox(page: Page, selector: string): Promise<{ x: number, y: number, width: number, height: number } | null> {
  const locator = page.locator(selector);
  return locator.boundingBox();
}

/**
 * Get element position
 * @param page Page
 * @param selector Selector
 * @returns Element position
 */
export async function getElementPosition(page: Page, selector: string): Promise<{ x: number, y: number } | null> {
  const boundingBox = await getElementBoundingBox(page, selector);
  
  if (!boundingBox) {
    return null;
  }
  
  return {
    x: boundingBox.x,
    y: boundingBox.y
  };
}

/**
 * Get element size
 * @param page Page
 * @param selector Selector
 * @returns Element size
 */
export async function getElementSize(page: Page, selector: string): Promise<{ width: number, height: number } | null> {
  const boundingBox = await getElementBoundingBox(page, selector);
  
  if (!boundingBox) {
    return null;
  }
  
  return {
    width: boundingBox.width,
    height: boundingBox.height
  };
}
