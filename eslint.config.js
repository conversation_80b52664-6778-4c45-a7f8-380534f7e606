/**
 * ESLint Configuration for QA Wolf Metrics Framework
 * 
 * This configuration includes custom rules to enforce best practices
 * for screenshot management and other QA Wolf-specific patterns.
 * 
 * @module eslint.config.js
 * <AUTHOR> Wolf Team
 * @date 2025-01-01
 */

import js from '@eslint/js';
import globals from 'globals';

// Import custom rules
import noDirectScreenshot from './eslint-rules/no-direct-screenshot.js';

export default [
  js.configs.recommended,
  {
    languageOptions: {
      ecmaVersion: 2022,
      sourceType: 'commonjs',
      globals: {
        ...globals.node,
        ...globals.jest,
      },
    },
    rules: {
      // Custom rules
      'no-direct-screenshot': 'error',
      
      // Standard rules
      'no-unused-vars': ['warn', { 
        'argsIgnorePattern': '^_',
        'varsIgnorePattern': '^_' 
      }],
      'no-console': 'off',
      'no-debugger': 'warn',
      'no-alert': 'warn',
      'no-constant-condition': 'warn',
      'no-empty': 'warn',
      'no-extra-boolean-cast': 'warn',
      'no-fallthrough': 'warn',
      'no-irregular-whitespace': 'warn',
      'no-mixed-spaces-and-tabs': 'warn',
      'no-multiple-empty-lines': ['warn', { 'max': 2 }],
      'no-trailing-spaces': 'warn',
      'no-undef': 'error',
      'no-unreachable': 'warn',
      'no-var': 'warn',
      'prefer-const': 'warn',
      'semi': ['warn', 'always'],
      'quotes': ['warn', 'single', { 'avoidEscape': true }],
    },
    plugins: {
      'no-direct-screenshot': {
        rules: {
          'no-direct-screenshot': noDirectScreenshot,
        },
      },
    },
  },
];
