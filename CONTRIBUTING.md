# Contributing to QA Wolf Testing Framework

Thank you for your interest in contributing to the QA Wolf Testing Framework! This document provides guidelines for contributing to the project, including versioning, releases, and development workflow.

## Monorepo Structure

This project is organized as a monorepo with the following packages:

- **test-framework**: Core QA Wolf testing framework
- **mcp-optimizer**: MCP optimization and orchestration for testing
- **shared-utils**: Shared utilities used across packages
- **docs**: Documentation for the framework

## Development Workflow

### Setting Up the Development Environment

1. Clone the repository:
   ```bash
   git clone https://github.com/sneezyxl/QAWolfeesojc.git
   cd QAWolfeesojc
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up GitHub Packages authentication:
   - Create a GitHub personal access token with `read:packages` and `write:packages` scopes
   - Add the token to your `.npmrc` file:
     ```
     @qawolf:registry=https://npm.pkg.github.com
     //npm.pkg.github.com/:_authToken=YOUR_GITHUB_TOKEN
     ```

### Making Changes

1. Create a new branch for your changes:
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. Make your changes to the appropriate package(s)

3. Run tests to ensure your changes don't break existing functionality:
   ```bash
   npm test
   ```

4. Run linting to ensure your code follows the project's style guidelines:
   ```bash
   npm run lint
   ```

5. Commit your changes with a descriptive commit message:
   ```bash
   git commit -m "feat: add new feature to test-framework"
   ```

6. Push your changes to GitHub:
   ```bash
   git push origin feature/your-feature-name
   ```

7. Create a pull request on GitHub

## Versioning

This project follows [Semantic Versioning](https://semver.org/) (SemVer) for all packages.

### Versioning Strategy

- **Lockstep Versioning**: All packages start at version 1.0.0 and are updated together
- **Independent Versioning**: The mcp-optimizer package may be versioned independently if needed

### Version Bump Script

To bump the version of packages, use the version-bump script:

```bash
# Bump all packages to the next patch version
npm run version-bump patch

# Bump all packages to the next minor version
npm run version-bump minor

# Bump all packages to the next major version
npm run version-bump major

# Bump a specific package to the next patch version
npm run version-bump patch test-framework

# Bump a specific package to a specific version
npm run version-bump 1.2.3 mcp-optimizer
```

## Releases

### Release Process

1. Bump the version of the packages you want to release:
   ```bash
   npm run version-bump patch
   ```

2. Push the changes and tags to GitHub:
   ```bash
   git push && git push --tags
   ```

3. The release workflow will automatically:
   - Build all packages
   - Publish packages to GitHub Packages
   - Create a GitHub release with a changelog

### Manual Release

You can also trigger a release manually from the GitHub Actions tab:

1. Go to the "Actions" tab in the GitHub repository
2. Select the "Release" workflow
3. Click "Run workflow"
4. Enter the version to release (major, minor, patch, or specific version)
5. Enter the package to release (all, test-framework, mcp-optimizer, shared-utils)
6. Click "Run workflow"

## CI/CD Pipeline

This project uses GitHub Actions for continuous integration and deployment:

- **CI Workflow**: Runs on every push to the main branch and pull requests
  - Detects which packages have changed
  - Runs linting, testing, and building for affected packages
  - Uses a build matrix to parallelize jobs
  - Implements caching for node_modules and build artifacts

- **Release Workflow**: Runs when a tag is pushed or manually triggered
  - Builds all packages
  - Publishes packages to GitHub Packages
  - Creates GitHub releases with changelogs

## Code Style and Guidelines

- Follow the existing code style and patterns
- Write tests for new features and bug fixes
- Document new features and changes
- Keep the codebase clean and maintainable

## License

By contributing to this project, you agree that your contributions will be licensed under the project's MIT license.