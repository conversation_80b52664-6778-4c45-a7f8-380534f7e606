/**
 * Run Test Improver
 * 
 * This script runs the test improver on test files in the specified directory.
 * It automatically applies improvements to test files to increase their AAA compliance.
 * 
 * Usage:
 * node run-test-improver.js [directory] [pattern] [--dry-run]
 * 
 * Example:
 * node run-test-improver.js ../../tests/qawolf ".*_node20\.js$"
 * 
 * @module run-test-improver
 * <AUTHOR> Wolf Team
 * @date 2025-01-01
 */

const path = require('path');
const fs = require('fs');
const { improveTestFile } = require('./test-improver');
const { evaluateTestFile } = require('./test-evaluator');

// Get command line arguments
const args = process.argv.slice(2);
const dirPath = args[0] || '../../tests/qawolf';
const patternArg = args.find(arg => !arg.startsWith('--') && arg !== dirPath);
const pattern = patternArg ? new RegExp(patternArg) : /.*\.js$/;
const dryRun = args.includes('--dry-run');

// Resolve the directory path
const resolvedDirPath = path.resolve(__dirname, dirPath);

console.log(`Improving test files in ${resolvedDirPath} matching pattern ${pattern}`);
console.log(`Dry run: ${dryRun}`);

// Get all test files
const files = fs.readdirSync(resolvedDirPath);
const testFiles = files.filter(file => pattern.test(file));

// Improve each test file
const results = [];

for (const file of testFiles) {
  const filePath = path.join(resolvedDirPath, file);
  
  // Skip selectors.js
  if (file === 'selectors.js') {
    console.log(`Skipping ${file} (not a test file)`);
    continue;
  }
  
  // Skip already improved files
  if (file.includes('improved') || file.includes('standardized') || file === 'test-template.js') {
    console.log(`Skipping ${file} (already improved)`);
    continue;
  }
  
  // Evaluate the file before improvement
  console.log(`\nEvaluating ${file} before improvement...`);
  const beforeEvaluation = evaluateTestFile(filePath);
  
  // Skip files that already have high AAA compliance
  if (beforeEvaluation.percentage >= 90) {
    console.log(`Skipping ${file} (already has high AAA compliance: ${beforeEvaluation.percentage}%)`);
    continue;
  }
  
  // Improve the file
  console.log(`Improving ${file}...`);
  const result = improveTestFile(filePath, { dryRun });
  
  // Evaluate the file after improvement
  if (!dryRun) {
    console.log(`Evaluating ${file} after improvement...`);
    const afterEvaluation = evaluateTestFile(filePath);
    result.afterScore = afterEvaluation.percentage;
    result.improvement = afterEvaluation.percentage - beforeEvaluation.percentage;
  } else {
    result.afterScore = beforeEvaluation.percentage;
    result.improvement = 0;
  }
  
  results.push(result);
}

// Print summary
console.log('\n========== IMPROVEMENT SUMMARY ==========');
console.log(`Files improved: ${results.length}`);

if (results.length > 0) {
  const totalImprovement = results.reduce((sum, result) => sum + result.improvement, 0);
  const averageImprovement = totalImprovement / results.length;
  console.log(`Average improvement: ${averageImprovement.toFixed(2)}%`);
  
  // Print details for each file
  console.log('\nFile details:');
  for (const result of results) {
    console.log(`- ${result.fileName}: ${result.originalScore}% -> ${result.afterScore}% (${result.improvement.toFixed(2)}% improvement)`);
    console.log(`  Improvements: ${result.improvements.join(', ')}`);
  }
}

console.log('\n=========================================');

// Print next steps
console.log('\nNext Steps:');
console.log('1. Review the improved files and make any necessary adjustments');
console.log('2. Run the test evaluator to check the new AAA compliance scores');
console.log('3. Run the tests to make sure they still work');
console.log('4. Commit the changes');

console.log('\n=========================================');
