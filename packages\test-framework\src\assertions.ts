/**
 * Assertions for QA Wolf Test Framework
 */

import { <PERSON>, Locator, expect as playwrightExpect } from '@playwright/test';

/**
 * Assertion error
 */
export class AssertionError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'AssertionError';
  }
}

/**
 * Assert that a condition is true
 * @param condition Condition to check
 * @param message Error message
 */
export function assertTrue(condition: boolean, message: string = 'Expected condition to be true'): void {
  if (!condition) {
    throw new AssertionError(message);
  }
}

/**
 * Assert that a condition is false
 * @param condition Condition to check
 * @param message Error message
 */
export function assertFalse(condition: boolean, message: string = 'Expected condition to be false'): void {
  if (condition) {
    throw new AssertionError(message);
  }
}

/**
 * Assert that a value is equal to another value
 * @param actual Actual value
 * @param expected Expected value
 * @param message Error message
 */
export function assertEqual<T>(actual: T, expected: T, message: string = `Expected ${actual} to equal ${expected}`): void {
  if (actual !== expected) {
    throw new AssertionError(message);
  }
}

/**
 * Assert that a value is not equal to another value
 * @param actual Actual value
 * @param expected Expected value
 * @param message Error message
 */
export function assertNotEqual<T>(actual: T, expected: T, message: string = `Expected ${actual} to not equal ${expected}`): void {
  if (actual === expected) {
    throw new AssertionError(message);
  }
}

/**
 * Assert that a value is null
 * @param value Value to check
 * @param message Error message
 */
export function assertNull(value: any, message: string = 'Expected value to be null'): void {
  if (value !== null) {
    throw new AssertionError(message);
  }
}

/**
 * Assert that a value is not null
 * @param value Value to check
 * @param message Error message
 */
export function assertNotNull(value: any, message: string = 'Expected value to not be null'): void {
  if (value === null) {
    throw new AssertionError(message);
  }
}

/**
 * Assert that a value is undefined
 * @param value Value to check
 * @param message Error message
 */
export function assertUndefined(value: any, message: string = 'Expected value to be undefined'): void {
  if (value !== undefined) {
    throw new AssertionError(message);
  }
}

/**
 * Assert that a value is not undefined
 * @param value Value to check
 * @param message Error message
 */
export function assertNotUndefined(value: any, message: string = 'Expected value to not be undefined'): void {
  if (value === undefined) {
    throw new AssertionError(message);
  }
}

/**
 * Assert that a value is defined (not null or undefined)
 * @param value Value to check
 * @param message Error message
 */
export function assertDefined(value: any, message: string = 'Expected value to be defined'): void {
  if (value === null || value === undefined) {
    throw new AssertionError(message);
  }
}

/**
 * Assert that a string contains a substring
 * @param str String to check
 * @param substring Substring to check for
 * @param message Error message
 */
export function assertContains(str: string, substring: string, message: string = `Expected "${str}" to contain "${substring}"`): void {
  if (!str.includes(substring)) {
    throw new AssertionError(message);
  }
}

/**
 * Assert that a string does not contain a substring
 * @param str String to check
 * @param substring Substring to check for
 * @param message Error message
 */
export function assertNotContains(str: string, substring: string, message: string = `Expected "${str}" to not contain "${substring}"`): void {
  if (str.includes(substring)) {
    throw new AssertionError(message);
  }
}

/**
 * Assert that a string matches a regular expression
 * @param str String to check
 * @param regex Regular expression to match
 * @param message Error message
 */
export function assertMatches(str: string, regex: RegExp, message: string = `Expected "${str}" to match ${regex}`): void {
  if (!regex.test(str)) {
    throw new AssertionError(message);
  }
}

/**
 * Assert that a string does not match a regular expression
 * @param str String to check
 * @param regex Regular expression to match
 * @param message Error message
 */
export function assertNotMatches(str: string, regex: RegExp, message: string = `Expected "${str}" to not match ${regex}`): void {
  if (regex.test(str)) {
    throw new AssertionError(message);
  }
}

/**
 * Assert that a value is greater than another value
 * @param actual Actual value
 * @param expected Expected value
 * @param message Error message
 */
export function assertGreaterThan<T>(actual: T, expected: T, message: string = `Expected ${actual} to be greater than ${expected}`): void {
  if (actual <= expected) {
    throw new AssertionError(message);
  }
}

/**
 * Assert that a value is greater than or equal to another value
 * @param actual Actual value
 * @param expected Expected value
 * @param message Error message
 */
export function assertGreaterThanOrEqual<T>(actual: T, expected: T, message: string = `Expected ${actual} to be greater than or equal to ${expected}`): void {
  if (actual < expected) {
    throw new AssertionError(message);
  }
}

/**
 * Assert that a value is less than another value
 * @param actual Actual value
 * @param expected Expected value
 * @param message Error message
 */
export function assertLessThan<T>(actual: T, expected: T, message: string = `Expected ${actual} to be less than ${expected}`): void {
  if (actual >= expected) {
    throw new AssertionError(message);
  }
}

/**
 * Assert that a value is less than or equal to another value
 * @param actual Actual value
 * @param expected Expected value
 * @param message Error message
 */
export function assertLessThanOrEqual<T>(actual: T, expected: T, message: string = `Expected ${actual} to be less than or equal to ${expected}`): void {
  if (actual > expected) {
    throw new AssertionError(message);
  }
}

/**
 * Assert that a page has a URL
 * @param page Page to check
 * @param url URL to check for
 * @param message Error message
 */
export async function assertPageHasURL(page: Page, url: string | RegExp, message?: string): Promise<void> {
  await playwrightExpect(page).toHaveURL(url, { message });
}

/**
 * Assert that a page does not have a URL
 * @param page Page to check
 * @param url URL to check for
 * @param message Error message
 */
export async function assertPageNotHasURL(page: Page, url: string | RegExp, message?: string): Promise<void> {
  await playwrightExpect(page).not.toHaveURL(url, { message });
}

/**
 * Assert that a page has a title
 * @param page Page to check
 * @param title Title to check for
 * @param message Error message
 */
export async function assertPageHasTitle(page: Page, title: string | RegExp, message?: string): Promise<void> {
  await playwrightExpect(page).toHaveTitle(title, { message });
}

/**
 * Assert that a page does not have a title
 * @param page Page to check
 * @param title Title to check for
 * @param message Error message
 */
export async function assertPageNotHasTitle(page: Page, title: string | RegExp, message?: string): Promise<void> {
  await playwrightExpect(page).not.toHaveTitle(title, { message });
}

/**
 * Assert that a locator is visible
 * @param locator Locator to check
 * @param message Error message
 */
export async function assertVisible(locator: Locator, message?: string): Promise<void> {
  await playwrightExpect(locator).toBeVisible({ message });
}

/**
 * Assert that a locator is not visible
 * @param locator Locator to check
 * @param message Error message
 */
export async function assertNotVisible(locator: Locator, message?: string): Promise<void> {
  await playwrightExpect(locator).not.toBeVisible({ message });
}

/**
 * Assert that a locator has text
 * @param locator Locator to check
 * @param text Text to check for
 * @param message Error message
 */
export async function assertHasText(locator: Locator, text: string | RegExp, message?: string): Promise<void> {
  await playwrightExpect(locator).toHaveText(text, { message });
}

/**
 * Assert that a locator does not have text
 * @param locator Locator to check
 * @param text Text to check for
 * @param message Error message
 */
export async function assertNotHasText(locator: Locator, text: string | RegExp, message?: string): Promise<void> {
  await playwrightExpect(locator).not.toHaveText(text, { message });
}

/**
 * Assert that a locator has value
 * @param locator Locator to check
 * @param value Value to check for
 * @param message Error message
 */
export async function assertHasValue(locator: Locator, value: string | RegExp, message?: string): Promise<void> {
  await playwrightExpect(locator).toHaveValue(value, { message });
}

/**
 * Assert that a locator does not have value
 * @param locator Locator to check
 * @param value Value to check for
 * @param message Error message
 */
export async function assertNotHasValue(locator: Locator, value: string | RegExp, message?: string): Promise<void> {
  await playwrightExpect(locator).not.toHaveValue(value, { message });
}

/**
 * Assert that a locator has attribute
 * @param locator Locator to check
 * @param name Attribute name
 * @param value Attribute value
 * @param message Error message
 */
export async function assertHasAttribute(locator: Locator, name: string, value: string | RegExp, message?: string): Promise<void> {
  await playwrightExpect(locator).toHaveAttribute(name, value, { message });
}

/**
 * Assert that a locator does not have attribute
 * @param locator Locator to check
 * @param name Attribute name
 * @param value Attribute value
 * @param message Error message
 */
export async function assertNotHasAttribute(locator: Locator, name: string, value: string | RegExp, message?: string): Promise<void> {
  await playwrightExpect(locator).not.toHaveAttribute(name, value, { message });
}

/**
 * Assert that a locator is enabled
 * @param locator Locator to check
 * @param message Error message
 */
export async function assertEnabled(locator: Locator, message?: string): Promise<void> {
  await playwrightExpect(locator).toBeEnabled({ message });
}

/**
 * Assert that a locator is disabled
 * @param locator Locator to check
 * @param message Error message
 */
export async function assertDisabled(locator: Locator, message?: string): Promise<void> {
  await playwrightExpect(locator).toBeDisabled({ message });
}

/**
 * Assert that a locator is checked
 * @param locator Locator to check
 * @param message Error message
 */
export async function assertChecked(locator: Locator, message?: string): Promise<void> {
  await playwrightExpect(locator).toBeChecked({ message });
}

/**
 * Assert that a locator is not checked
 * @param locator Locator to check
 * @param message Error message
 */
export async function assertNotChecked(locator: Locator, message?: string): Promise<void> {
  await playwrightExpect(locator).not.toBeChecked({ message });
}

/**
 * Assert that a locator is focused
 * @param locator Locator to check
 * @param message Error message
 */
export async function assertFocused(locator: Locator, message?: string): Promise<void> {
  await playwrightExpect(locator).toBeFocused({ message });
}

/**
 * Assert that a locator is not focused
 * @param locator Locator to check
 * @param message Error message
 */
export async function assertNotFocused(locator: Locator, message?: string): Promise<void> {
  await playwrightExpect(locator).not.toBeFocused({ message });
}

/**
 * Assert that a locator is empty
 * @param locator Locator to check
 * @param message Error message
 */
export async function assertEmpty(locator: Locator, message?: string): Promise<void> {
  await playwrightExpect(locator).toBeEmpty({ message });
}

/**
 * Assert that a locator is not empty
 * @param locator Locator to check
 * @param message Error message
 */
export async function assertNotEmpty(locator: Locator, message?: string): Promise<void> {
  await playwrightExpect(locator).not.toBeEmpty({ message });
}

/**
 * Export Playwright expect
 */
export { playwrightExpect as expect };
