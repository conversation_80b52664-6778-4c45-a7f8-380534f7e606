/**
 * Testmo Integration Script
 * 
 * This script integrates QA Wolf test results with Testmo for test management.
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  testmoUrl: process.env.TESTMO_URL || 'https://your-instance.testmo.net',
  testmoApiKey: process.env.TESTMO_API_KEY,
  testmoProjectId: process.env.TESTMO_PROJECT_ID || '1',
  testmoSourceId: process.env.TESTMO_SOURCE_ID || 'qawolf',
  reportsDir: './testmo-reports'
};

// Create reports directory if it doesn't exist
if (!fs.existsSync(config.reportsDir)) {
  fs.mkdirSync(config.reportsDir, { recursive: true });
}

/**
 * Create a new test run in Testmo
 * @param {Object} options - Test run options
 * @param {string} options.name - Name of the test run
 * @param {string} options.description - Description of the test run
 * @param {string} options.source - Source of the test run (e.g., 'qawolf')
 * @param {string} options.branch - Branch name
 * @param {string} options.commit - Commit hash
 * @param {string} options.environment - Environment name
 * @returns {Promise<Object>} - Test run data
 */
async function createTestRun(options) {
  try {
    console.log('Creating test run in Testmo...');
    
    const response = await axios.post(
      `${config.testmoUrl}/api/v1/projects/${config.testmoProjectId}/automation/runs`,
      {
        name: options.name || `QA Wolf Test Run - ${new Date().toISOString()}`,
        description: options.description || 'Automated test run from QA Wolf',
        source: options.source || config.testmoSourceId,
        branch: options.branch || 'main',
        commit: options.commit || 'latest',
        environment: options.environment || 'staging'
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.testmoApiKey}`
        }
      }
    );
    
    console.log('Test run created successfully!');
    console.log('Test run ID:', response.data.id);
    
    // Save test run data to file
    fs.writeFileSync(
      path.join(config.reportsDir, `test-run-${response.data.id}.json`),
      JSON.stringify(response.data, null, 2)
    );
    
    return response.data;
  } catch (error) {
    console.error('Error creating test run in Testmo:', error.message);
    
    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
    }
    
    // Save error to file
    fs.writeFileSync(
      path.join(config.reportsDir, 'create-test-run-error.json'),
      JSON.stringify({
        error: error.message,
        response: error.response ? {
          data: error.response.data,
          status: error.response.status
        } : null,
        timestamp: new Date().toISOString()
      }, null, 2)
    );
    
    throw error;
  }
}

/**
 * Submit test results to Testmo
 * @param {number} runId - Test run ID
 * @param {Array<Object>} results - Test results
 * @returns {Promise<Object>} - Submission data
 */
async function submitTestResults(runId, results) {
  try {
    console.log(`Submitting test results to Testmo for run ID ${runId}...`);
    
    const response = await axios.post(
      `${config.testmoUrl}/api/v1/projects/${config.testmoProjectId}/automation/runs/${runId}/results`,
      {
        results
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.testmoApiKey}`
        }
      }
    );
    
    console.log('Test results submitted successfully!');
    console.log('Submission ID:', response.data.id);
    
    // Save submission data to file
    fs.writeFileSync(
      path.join(config.reportsDir, `submission-${response.data.id}.json`),
      JSON.stringify(response.data, null, 2)
    );
    
    return response.data;
  } catch (error) {
    console.error('Error submitting test results to Testmo:', error.message);
    
    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
    }
    
    // Save error to file
    fs.writeFileSync(
      path.join(config.reportsDir, 'submit-results-error.json'),
      JSON.stringify({
        error: error.message,
        response: error.response ? {
          data: error.response.data,
          status: error.response.status
        } : null,
        timestamp: new Date().toISOString()
      }, null, 2)
    );
    
    throw error;
  }
}

/**
 * Complete a test run in Testmo
 * @param {number} runId - Test run ID
 * @returns {Promise<Object>} - Test run data
 */
async function completeTestRun(runId) {
  try {
    console.log(`Completing test run ${runId} in Testmo...`);
    
    const response = await axios.post(
      `${config.testmoUrl}/api/v1/projects/${config.testmoProjectId}/automation/runs/${runId}/complete`,
      {},
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.testmoApiKey}`
        }
      }
    );
    
    console.log('Test run completed successfully!');
    
    // Save completion data to file
    fs.writeFileSync(
      path.join(config.reportsDir, `test-run-${runId}-complete.json`),
      JSON.stringify(response.data, null, 2)
    );
    
    return response.data;
  } catch (error) {
    console.error('Error completing test run in Testmo:', error.message);
    
    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
    }
    
    // Save error to file
    fs.writeFileSync(
      path.join(config.reportsDir, 'complete-test-run-error.json'),
      JSON.stringify({
        error: error.message,
        response: error.response ? {
          data: error.response.data,
          status: error.response.status
        } : null,
        timestamp: new Date().toISOString()
      }, null, 2)
    );
    
    throw error;
  }
}

/**
 * Convert QA Wolf test results to Testmo format
 * @param {Array<Object>} qaWolfResults - QA Wolf test results
 * @returns {Array<Object>} - Testmo test results
 */
function convertToTestmoFormat(qaWolfResults) {
  return qaWolfResults.map(result => {
    const testmoResult = {
      name: result.name || 'Unnamed Test',
      status: result.status === 'passed' ? 'passed' : 'failed',
      duration: result.duration || 0,
      automation_content: result.id || result.name || 'unknown',
      comment: result.errorMessage || ''
    };
    
    // Add steps if available
    if (result.steps && result.steps.length > 0) {
      testmoResult.steps = result.steps.map(step => ({
        description: step.name || 'Unnamed Step',
        expected: step.expected || '',
        actual: step.actual || '',
        status: step.status || 'passed'
      }));
    }
    
    // Add attachments if available
    if (result.attachments && result.attachments.length > 0) {
      testmoResult.attachments = result.attachments.map(attachment => ({
        name: attachment.name || 'Unnamed Attachment',
        content_type: attachment.contentType || 'text/plain',
        data: attachment.data || ''
      }));
    }
    
    return testmoResult;
  });
}

/**
 * Process QA Wolf test results and submit to Testmo
 * @param {string} resultsFile - Path to QA Wolf test results file
 * @param {Object} options - Test run options
 * @returns {Promise<Object>} - Test run data
 */
async function processTestResults(resultsFile, options = {}) {
  try {
    console.log(`Processing QA Wolf test results from ${resultsFile}...`);
    
    // Read QA Wolf test results
    const qaWolfResults = JSON.parse(fs.readFileSync(resultsFile, 'utf8'));
    
    // Create a new test run in Testmo
    const testRun = await createTestRun(options);
    
    // Convert QA Wolf test results to Testmo format
    const testmoResults = convertToTestmoFormat(qaWolfResults);
    
    // Submit test results to Testmo
    await submitTestResults(testRun.id, testmoResults);
    
    // Complete the test run in Testmo
    const completedTestRun = await completeTestRun(testRun.id);
    
    console.log('Test results processed and submitted to Testmo successfully!');
    console.log('Test run URL:', `${config.testmoUrl}/projects/${config.testmoProjectId}/automation/runs/${testRun.id}`);
    
    return completedTestRun;
  } catch (error) {
    console.error('Error processing test results:', error.message);
    throw error;
  }
}

// Export functions for use in other scripts
module.exports = {
  createTestRun,
  submitTestResults,
  completeTestRun,
  convertToTestmoFormat,
  processTestResults
};

// Run the script if called directly
if (require.main === module) {
  const resultsFile = process.argv[2];
  const options = {
    name: process.argv[3] || `QA Wolf Test Run - ${new Date().toISOString()}`,
    description: process.argv[4] || 'Automated test run from QA Wolf',
    source: process.argv[5] || config.testmoSourceId,
    branch: process.argv[6] || 'main',
    commit: process.argv[7] || 'latest',
    environment: process.argv[8] || 'staging'
  };
  
  if (!resultsFile) {
    console.error('Error: No results file specified');
    console.error('Usage: node testmo-integration.js <results-file> [name] [description] [source] [branch] [commit] [environment]');
    process.exit(1);
  }
  
  if (!config.testmoApiKey) {
    console.error('Error: No Testmo API key specified');
    console.error('Please set the TESTMO_API_KEY environment variable');
    process.exit(1);
  }
  
  processTestResults(resultsFile, options)
    .then(() => {
      console.log('Done!');
      process.exit(0);
    })
    .catch(error => {
      console.error('Error:', error.message);
      process.exit(1);
    });
}
