/**
 * Screenshot comparator for screenshot manager
 */

import * as fs from 'fs';
import * as path from 'path';
import * as sharp from 'sharp';
import { ScreenshotComparisonOptions, ScreenshotComparisonResult } from './types';
import { ensureDirectoryExists } from '@qawolf/shared-utils';
import { EventBus, EventType } from '@qawolf/core';

/**
 * Screenshot comparator
 */
export class ScreenshotComparator {
  /**
   * Default comparison options
   */
  private defaultOptions: ScreenshotComparisonOptions;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options Screenshot comparison options
   */
  constructor(options: ScreenshotComparisonOptions = {}) {
    this.defaultOptions = {
      threshold: options.threshold || 0.1,
      ignoreAntialiasing: options.ignoreAntialiasing !== false,
      ignoreColors: options.ignoreColors || false,
      ignoreAlpha: options.ignoreAlpha || false,
      ignoreLess: options.ignoreLess || false,
      ignoreNothing: options.ignoreNothing || false
    };
    
    this.eventBus = EventBus.getInstance();
  }
  
  /**
   * Compare screenshots
   * @param screenshotPath1 First screenshot path
   * @param screenshotPath2 Second screenshot path
   * @param options Comparison options
   * @returns Comparison result
   */
  async compareScreenshots(screenshotPath1: string, screenshotPath2: string, options: Partial<ScreenshotComparisonOptions> = {}): Promise<ScreenshotComparisonResult> {
    // Merge options with defaults
    const mergedOptions: ScreenshotComparisonOptions = {
      ...this.defaultOptions,
      ...options
    };
    
    // Read screenshots
    const screenshot1 = fs.readFileSync(screenshotPath1);
    const screenshot2 = fs.readFileSync(screenshotPath2);
    
    // Get screenshot metadata
    const metadata1 = await sharp(screenshot1).metadata();
    const metadata2 = await sharp(screenshot2).metadata();
    
    // Resize screenshots to the same dimensions if needed
    let resizedScreenshot1 = screenshot1;
    let resizedScreenshot2 = screenshot2;
    
    if (metadata1.width !== metadata2.width || metadata1.height !== metadata2.height) {
      // Use the smaller dimensions
      const width = Math.min(metadata1.width || 0, metadata2.width || 0);
      const height = Math.min(metadata1.height || 0, metadata2.height || 0);
      
      resizedScreenshot1 = await sharp(screenshot1)
        .resize(width, height)
        .toBuffer();
      
      resizedScreenshot2 = await sharp(screenshot2)
        .resize(width, height)
        .toBuffer();
    }
    
    // Convert screenshots to raw pixel data
    const image1 = await sharp(resizedScreenshot1)
      .raw()
      .toBuffer({ resolveWithObject: true });
    
    const image2 = await sharp(resizedScreenshot2)
      .raw()
      .toBuffer({ resolveWithObject: true });
    
    // Compare pixels
    const { diffPixels, diffPercentage, diffImageBuffer } = this.comparePixels(
      image1.data,
      image2.data,
      image1.info.width,
      image1.info.height,
      image1.info.channels,
      mergedOptions
    );
    
    // Save diff image
    let diffImagePath: string | undefined;
    
    if (diffImageBuffer) {
      const diffDir = path.dirname(screenshotPath1);
      const diffFileName = `diff_${path.basename(screenshotPath1)}`;
      diffImagePath = path.join(diffDir, diffFileName);
      
      // Create directory if it doesn't exist
      ensureDirectoryExists(diffDir);
      
      // Write diff image
      fs.writeFileSync(diffImagePath, diffImageBuffer);
    }
    
    // Create comparison result
    const result: ScreenshotComparisonResult = {
      match: diffPercentage <= mergedOptions.threshold!,
      diffPercentage,
      diffPixels,
      totalPixels: image1.info.width * image1.info.height,
      diffImagePath
    };
    
    // Emit event
    this.eventBus.emit(EventType.SCREENSHOT_COMPARISON_COMPLETED, {
      screenshotPath1,
      screenshotPath2,
      result
    });
    
    return result;
  }
  
  /**
   * Compare pixels
   * @param data1 First image data
   * @param data2 Second image data
   * @param width Image width
   * @param height Image height
   * @param channels Number of channels
   * @param options Comparison options
   * @returns Comparison result
   */
  private comparePixels(
    data1: Buffer,
    data2: Buffer,
    width: number,
    height: number,
    channels: number,
    options: ScreenshotComparisonOptions
  ): { diffPixels: number; diffPercentage: number; diffImageBuffer?: Buffer } {
    // Create diff image buffer
    const diffBuffer = Buffer.alloc(data1.length);
    let diffPixels = 0;
    
    // Compare pixels
    for (let i = 0; i < data1.length; i += channels) {
      let diff = false;
      
      // Compare channels
      for (let j = 0; j < channels; j++) {
        // Skip alpha channel if ignoreAlpha is true
        if (j === 3 && options.ignoreAlpha) {
          continue;
        }
        
        // Compare channel values
        const value1 = data1[i + j];
        const value2 = data2[i + j];
        
        // Check if values are different
        if (Math.abs(value1 - value2) > 5) {
          diff = true;
          break;
        }
      }
      
      // If pixels are different
      if (diff) {
        diffPixels++;
        
        // Mark pixel as different in diff image
        diffBuffer[i] = 255; // R
        diffBuffer[i + 1] = 0; // G
        diffBuffer[i + 2] = 0; // B
        
        if (channels === 4) {
          diffBuffer[i + 3] = 255; // A
        }
      } else {
        // Copy pixel from first image
        for (let j = 0; j < channels; j++) {
          diffBuffer[i + j] = data1[i + j];
        }
      }
    }
    
    // Calculate diff percentage
    const totalPixels = width * height;
    const diffPercentage = (diffPixels / totalPixels) * 100;
    
    // Create diff image
    const diffImageBuffer = sharp(diffBuffer, {
      raw: {
        width,
        height,
        channels
      }
    }).toFormat('png').toBuffer();
    
    return {
      diffPixels,
      diffPercentage,
      diffImageBuffer: diffPixels > 0 ? diffImageBuffer : undefined
    };
  }
  
  /**
   * Get default options
   * @returns Default options
   */
  getDefaultOptions(): ScreenshotComparisonOptions {
    return { ...this.defaultOptions };
  }
  
  /**
   * Set default options
   * @param options Default options
   * @returns This instance for chaining
   */
  setDefaultOptions(options: Partial<ScreenshotComparisonOptions>): ScreenshotComparator {
    this.defaultOptions = {
      ...this.defaultOptions,
      ...options
    };
    
    return this;
  }
}

/**
 * Create screenshot comparator
 * @param options Screenshot comparison options
 * @returns Screenshot comparator
 */
export function createScreenshotComparator(options: ScreenshotComparisonOptions = {}): ScreenshotComparator {
  return new ScreenshotComparator(options);
}
