/**
 * Login End-to-End Tests
 * 
 * This file contains end-to-end tests for the login functionality.
 */

const { test, expect } = require('@playwright/test');
const { createMcpController, createSelfHealingController, test: testUtils } = require('../../src');
const { createTestApp, createMetricsCollector, createReportGenerator } = require('../fixtures');

test.describe('Login End-to-End Tests', () => {
  let testApp;
  let metricsCollector;
  let reportGenerator;
  
  test.beforeEach(async () => {
    testApp = await createTestApp();
    metricsCollector = createMetricsCollector();
    reportGenerator = createReportGenerator();
  });
  
  test.afterEach(async () => {
    await testApp.cleanup();
  });
  
  test('should login with valid credentials using MCP', async () => {
    const testName = 'login-valid-mcp';
    const startTime = Date.now();
    
    // Create an MCP controller
    const mcpController = createMcpController({
      autoStartPlaywrightMcp: false // Disable auto-start for tests
    });
    
    try {
      // Start performance tracking
      const performanceTracker = new testUtils.PerformanceTracker();
      
      // Navigate to the app
      performanceTracker.startOperation('navigation_to_app');
      await testApp.navigate();
      performanceTracker.endOperation();
      
      // Mock MCP optimization
      const selectors = {
        selectors: [
          {
            original: '[data-test-id="SignInEmail"]',
            optimized: '[data-test-id="SignInEmail"]',
            fallbacks: [],
            reliability: 0.95
          },
          {
            original: '[data-test-id="SignInPassword"]',
            optimized: '[data-test-id="SignInPassword"]',
            fallbacks: [],
            reliability: 0.95
          },
          {
            original: ':text("Log in with email")',
            optimized: ':text("Log in with email")',
            fallbacks: [],
            reliability: 0.95
          }
        ]
      };
      
      // Fill in login form
      performanceTracker.startOperation('login');
      await testApp.page.fill(selectors.selectors[0].optimized, testApp.credentials.email);
      await testApp.page.fill(selectors.selectors[1].optimized, testApp.credentials.password);
      await testApp.page.click(selectors.selectors[2].optimized);
      
      // Wait for login to complete
      await testApp.page.waitForSelector('div[class*="FilesTable"]', { timeout: 15000 });
      performanceTracker.endOperation();
      
      // Verify login was successful
      const isLoggedIn = await testApp.page.isVisible('div[class*="FilesTable"]');
      expect(isLoggedIn).toBe(true);
      
      // Record metrics
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      metricsCollector.recordExecutionTime(testName, duration);
      metricsCollector.recordTestResult(testName, true);
      metricsCollector.recordTokenUsage(testName, 1000); // Mock token usage
      metricsCollector.recordResourceUsage(testName, { cpu: 10, memory: 200 }); // Mock resource usage
      
      // Generate report
      await reportGenerator.generateReport(metricsCollector.getMetrics(), 'login-tests');
    } finally {
      // Clean up MCP resources
      await mcpController.cleanup();
    }
  });
  
  test('should login with valid credentials using self-healing', async () => {
    const testName = 'login-valid-self-healing';
    const startTime = Date.now();
    
    // Create a self-healing controller
    const selfHealingController = createSelfHealingController({
      selectorHealing: {
        enabled: true,
        persistHistory: false
      },
      recovery: {
        enabled: true
      },
      feedbackCollection: {
        enabled: true,
        persistFeedback: false
      }
    });
    
    try {
      // Start the test run
      await selfHealingController.startTest({
        testId: testName,
        testName: 'Login with valid credentials using self-healing'
      });
      
      // Create a self-healing page
      const selfHealingPage = selfHealingController.createPage(testApp.page);
      
      // Start performance tracking
      const performanceTracker = new testUtils.PerformanceTracker();
      
      // Navigate to the app
      performanceTracker.startOperation('navigation_to_app');
      await selfHealingPage.goto(testApp.baseUrl);
      performanceTracker.endOperation();
      
      // Fill in login form
      performanceTracker.startOperation('login');
      await selfHealingPage.fill('[data-test-id="SignInEmail"]', testApp.credentials.email);
      await selfHealingPage.fill('[data-test-id="SignInPassword"]', testApp.credentials.password);
      
      // Use a locator with self-healing
      const loginButton = selfHealingPage.locator(':text("Log in with email")');
      await loginButton.click();
      
      // Wait for login to complete
      await selfHealingPage.waitForSelector('div[class*="FilesTable"]', { timeout: 15000 });
      performanceTracker.endOperation();
      
      // Verify login was successful
      const isLoggedIn = await selfHealingPage.isVisible('div[class*="FilesTable"]');
      expect(isLoggedIn).toBe(true);
      
      // Track performance metrics
      await selfHealingController.trackPerformance(performanceTracker.getMetrics());
      
      // End the test run
      await selfHealingController.endTest({
        success: true
      });
      
      // Record metrics
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      metricsCollector.recordExecutionTime(testName, duration);
      metricsCollector.recordTestResult(testName, true);
      metricsCollector.recordSelfHealingResult(testName, true);
      metricsCollector.recordResourceUsage(testName, { cpu: 12, memory: 220 }); // Mock resource usage
      
      // Generate report
      await reportGenerator.generateReport(metricsCollector.getMetrics(), 'login-tests');
    } catch (error) {
      // End the test run with failure
      await selfHealingController.endTest({
        success: false,
        error
      });
      
      throw error;
    } finally {
      // Clean up resources
      await selfHealingController.cleanup();
    }
  });
  
  test('should handle invalid credentials', async () => {
    const testName = 'login-invalid';
    const startTime = Date.now();
    
    // Create a self-healing controller
    const selfHealingController = createSelfHealingController({
      selectorHealing: {
        enabled: true,
        persistHistory: false
      },
      recovery: {
        enabled: true
      },
      feedbackCollection: {
        enabled: true,
        persistFeedback: false
      }
    });
    
    try {
      // Start the test run
      await selfHealingController.startTest({
        testId: testName,
        testName: 'Login with invalid credentials'
      });
      
      // Create a self-healing page
      const selfHealingPage = selfHealingController.createPage(testApp.page);
      
      // Start performance tracking
      const performanceTracker = new testUtils.PerformanceTracker();
      
      // Navigate to the app
      performanceTracker.startOperation('navigation_to_app');
      await selfHealingPage.goto(testApp.baseUrl);
      performanceTracker.endOperation();
      
      // Fill in login form with invalid credentials
      performanceTracker.startOperation('login_attempt');
      await selfHealingPage.fill('[data-test-id="SignInEmail"]', '<EMAIL>');
      await selfHealingPage.fill('[data-test-id="SignInPassword"]', 'invalid-password');
      
      // Use a locator with self-healing
      const loginButton = selfHealingPage.locator(':text("Log in with email")');
      await loginButton.click();
      
      // Wait for error message
      await selfHealingPage.waitForSelector('text=Invalid email or password', { timeout: 15000 });
      performanceTracker.endOperation();
      
      // Verify error message is displayed
      const errorMessage = await selfHealingPage.textContent('text=Invalid email or password');
      expect(errorMessage).toContain('Invalid email or password');
      
      // Track performance metrics
      await selfHealingController.trackPerformance(performanceTracker.getMetrics());
      
      // End the test run
      await selfHealingController.endTest({
        success: true
      });
      
      // Record metrics
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      metricsCollector.recordExecutionTime(testName, duration);
      metricsCollector.recordTestResult(testName, true);
      metricsCollector.recordResourceUsage(testName, { cpu: 8, memory: 180 }); // Mock resource usage
      
      // Generate report
      await reportGenerator.generateReport(metricsCollector.getMetrics(), 'login-tests');
    } catch (error) {
      // End the test run with failure
      await selfHealingController.endTest({
        success: false,
        error
      });
      
      throw error;
    } finally {
      // Clean up resources
      await selfHealingController.cleanup();
    }
  });
});