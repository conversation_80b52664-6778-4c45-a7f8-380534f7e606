/**
 * @qawolf/test-framework
 * Core QA Wolf testing framework
 */

// Import shared utilities
// Use local imports instead of package imports
const { config, screenshot, test } = require('../../shared-utils/src');

// Import MCP module
const mcp = require('./mcp');

// Import self-healing module
const selfHealing = require('./self-healing');

module.exports = {
  // Export shared utilities
  config,
  screenshot,
  test,

  // Export MCP module
  mcp,

  // Export self-healing module
  selfHealing,

  // Create a default MCP controller
  createMcpController: mcp.createController,

  // Create a default self-healing controller
  createSelfHealingController: selfHealing.createSelfHealingController,

  // Export version
  version: require('../package.json').version
};