/**
 * Self-Healing Types
 * 
 * This module provides TypeScript-like type definitions for the self-healing automation system.
 * These are used for documentation purposes and are not enforced at runtime.
 */

/**
 * @typedef {Object} SelectorHistory
 * @property {string} selector - The selector
 * @property {number} attempts - Number of attempts
 * @property {number} successes - Number of successful attempts
 * @property {number} failures - Number of failed attempts
 * @property {number} successRate - Success rate (0-1)
 * @property {Array<string>} alternativesUsed - Alternative selectors that were used
 * @property {Object} contexts - Contexts in which the selector was used
 * @property {Date} lastUsed - Last time the selector was used
 * @property {Date} created - When the history was created
 */

/**
 * @typedef {Object} RecoveryStrategy
 * @property {string} name - Strategy name
 * @property {Function} execute - Function to execute the strategy
 * @property {number} priority - Priority (higher = tried first)
 * @property {number} successRate - Success rate (0-1)
 * @property {number} attempts - Number of attempts
 * @property {number} successes - Number of successful attempts
 * @property {number} failures - Number of failed attempts
 */

/**
 * @typedef {Object} RecoveryResult
 * @property {boolean} success - Whether the recovery was successful
 * @property {string} strategy - Strategy that was used
 * @property {number} attempts - Number of attempts
 * @property {number} duration - Duration of recovery in milliseconds
 * @property {Error} [error] - Error if recovery failed
 */

/**
 * @typedef {Object} FeedbackEvent
 * @property {string} type - Event type
 * @property {Date} timestamp - When the event occurred
 * @property {Object} data - Event data
 * @property {string} testId - Test ID
 * @property {string} testName - Test name
 * @property {string} [screenshotPath] - Path to screenshot
 */

/**
 * @typedef {Object} TestRunFeedback
 * @property {string} testId - Test ID
 * @property {string} testName - Test name
 * @property {Date} startTime - When the test started
 * @property {Date} endTime - When the test ended
 * @property {boolean} success - Whether the test was successful
 * @property {number} duration - Duration of the test in milliseconds
 * @property {Array<FeedbackEvent>} events - Events that occurred during the test
 * @property {Object} performance - Performance metrics
 * @property {Array<SelectorHistory>} selectors - Selectors used in the test
 * @property {Array<RecoveryResult>} recoveries - Recoveries attempted in the test
 */

/**
 * @typedef {Object} LearningData
 * @property {Object} selectorPatterns - Patterns in selector usage
 * @property {Object} recoveryPatterns - Patterns in recovery usage
 * @property {Object} errorPatterns - Patterns in errors
 * @property {Object} performancePatterns - Patterns in performance
 * @property {Array<Object>} improvements - Suggested improvements
 */

/**
 * @typedef {Object} SelfHealingContext
 * @property {Object} page - Playwright page
 * @property {string} testId - Test ID
 * @property {string} testName - Test name
 * @property {string} action - Action being performed
 * @property {Object} element - Element information
 * @property {Object} options - Options for the action
 */

// Export empty object since this is just for documentation
module.exports = {};