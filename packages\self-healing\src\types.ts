/**
 * Types for self-healing tests
 */

import { Page, Locator } from 'playwright';

/**
 * Selector attributes
 */
export interface SelectorAttributes {
  id?: string;
  class?: string;
  name?: string;
  type?: string;
  role?: string;
  text?: string;
  title?: string;
  placeholder?: string;
  value?: string;
  href?: string;
  src?: string;
  alt?: string;
  [key: string]: string | undefined;
}

/**
 * Element position
 */
export interface ElementPosition {
  x: number;
  y: number;
  width: number;
  height: number;
}

/**
 * Element info
 */
export interface ElementInfo {
  selector: string;
  attributes: SelectorAttributes;
  position?: ElementPosition;
  textContent?: string;
  innerHTML?: string;
  outerHTML?: string;
}

/**
 * Healing strategy
 */
export interface HealingStrategy {
  /**
   * Strategy name
   */
  name: string;
  
  /**
   * Heal selector
   * @param page Page
   * @param selector Original selector
   * @param options Healing options
   * @returns Healed selector or null if healing failed
   */
  heal(page: Page, selector: string, options?: HealingOptions): Promise<string | null>;
}

/**
 * Healing options
 */
export interface HealingOptions {
  /**
   * Similarity threshold
   */
  similarityThreshold?: number;
  
  /**
   * Maximum number of candidates to consider
   */
  maxCandidates?: number;
  
  /**
   * Timeout for healing
   */
  timeout?: number;
  
  /**
   * Whether to log healing attempts
   */
  logging?: boolean;
  
  /**
   * Additional context for healing
   */
  context?: any;
}

/**
 * Healing result
 */
export interface HealingResult {
  /**
   * Original selector
   */
  originalSelector: string;
  
  /**
   * Healed selector
   */
  healedSelector: string | null;
  
  /**
   * Strategy used for healing
   */
  strategy: string | null;
  
  /**
   * Similarity score
   */
  similarityScore?: number;
  
  /**
   * Healing time in milliseconds
   */
  healingTime: number;
  
  /**
   * Whether healing was successful
   */
  success: boolean;
  
  /**
   * Error message if healing failed
   */
  error?: string;
}

/**
 * Self-healing page options
 */
export interface SelfHealingPageOptions {
  /**
   * Whether to enable self-healing
   */
  enabled?: boolean;
  
  /**
   * Healing strategies to use
   */
  strategies?: HealingStrategy[];
  
  /**
   * Default healing options
   */
  healingOptions?: HealingOptions;
  
  /**
   * Selector history file path
   */
  selectorHistoryPath?: string;
  
  /**
   * Whether to save selector history
   */
  saveSelectorHistory?: boolean;
  
  /**
   * Whether to log healing attempts
   */
  logging?: boolean;
}

/**
 * Self-healing locator options
 */
export interface SelfHealingLocatorOptions {
  /**
   * Whether to enable self-healing
   */
  enabled?: boolean;
  
  /**
   * Healing strategies to use
   */
  strategies?: HealingStrategy[];
  
  /**
   * Default healing options
   */
  healingOptions?: HealingOptions;
  
  /**
   * Whether to log healing attempts
   */
  logging?: boolean;
}

/**
 * Selector history entry
 */
export interface SelectorHistoryEntry {
  /**
   * Original selector
   */
  originalSelector: string;
  
  /**
   * Healed selector
   */
  healedSelector: string;
  
  /**
   * Strategy used for healing
   */
  strategy: string;
  
  /**
   * Similarity score
   */
  similarityScore?: number;
  
  /**
   * Timestamp when the entry was created
   */
  timestamp: number;
  
  /**
   * Number of times the entry was used
   */
  usageCount: number;
  
  /**
   * Last time the entry was used
   */
  lastUsed: number;
}

/**
 * Selector history
 */
export interface SelectorHistory {
  /**
   * Selector history entries
   */
  entries: { [originalSelector: string]: SelectorHistoryEntry };
  
  /**
   * Last time the history was updated
   */
  lastUpdated: number;
}

/**
 * Selector parser
 */
export interface SelectorParser {
  /**
   * Parse selector
   * @param selector Selector to parse
   * @returns Parsed selector attributes
   */
  parse(selector: string): SelectorAttributes;
}

/**
 * Selector builder
 */
export interface SelectorBuilder {
  /**
   * Build selector from attributes
   * @param attributes Selector attributes
   * @returns Built selector
   */
  build(attributes: SelectorAttributes): string;
}
