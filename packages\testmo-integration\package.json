{"name": "@qawolf/testmo-integration", "version": "0.1.0", "description": "Testmo integration for QA Wolf Metrics Framework", "main": "dist/index.js", "types": "dist/index.d.ts", "bin": {"testmo-integration": "./bin/testmo-integration.js"}, "scripts": {"build": "tsc", "test": "jest", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "testing", "testmo", "integration"], "author": "", "license": "MIT", "dependencies": {"@playwright/test": "^1.52.0", "@qawolf/core": "^0.1.0", "@qawolf/shared-utils": "^0.1.0", "@qawolf/test-framework": "^0.1.0", "axios": "^1.6.7", "dotenv": "^16.4.5", "playwright": "^1.52.0"}, "devDependencies": {"@types/jest": "^29.5.12", "@types/node": "^20.11.30", "jest": "^29.7.0", "ts-jest": "^29.1.2", "typescript": "^5.4.3"}}