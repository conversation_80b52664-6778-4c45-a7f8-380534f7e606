/**
 * Testmo API for Testmo integration
 */

import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { TestmoAPIOptions, TestmoAPIResponse, TestmoProject, TestmoTestCase, TestmoTestRun, TestmoTestResult } from './types';
import { EventBus, EventType } from '@qawolf/core';

/**
 * Testmo API
 */
export class TestmoAPI {
  /**
   * API key
   */
  private apiKey: string;
  
  /**
   * Host URL
   */
  private host: string;
  
  /**
   * Project ID
   */
  private projectId: number;
  
  /**
   * Timeout in milliseconds
   */
  private timeout: number;
  
  /**
   * Axios instance
   */
  private axios: AxiosInstance;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options Testmo API options
   */
  constructor(options: TestmoAPIOptions = {}) {
    this.apiKey = options.apiKey || '';
    this.host = options.host || 'https://api.testmo.io';
    this.projectId = options.projectId || 0;
    this.timeout = options.timeout || 30000;
    
    this.axios = axios.create({
      baseURL: this.host,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      }
    });
    
    this.eventBus = EventBus.getInstance();
  }
  
  /**
   * Get projects
   * @returns Projects
   */
  async getProjects(): Promise<TestmoProject[]> {
    try {
      const response = await this.axios.get<TestmoAPIResponse<TestmoProject[]>>('/api/v1/projects');
      
      // Emit event
      this.eventBus.emit(EventType.TESTMO_API_GET_PROJECTS, {
        projects: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, 'Failed to get projects');
      return [];
    }
  }
  
  /**
   * Get project by ID
   * @param projectId Project ID
   * @returns Project
   */
  async getProjectById(projectId: number): Promise<TestmoProject | null> {
    try {
      const response = await this.axios.get<TestmoAPIResponse<TestmoProject>>(`/api/v1/projects/${projectId}`);
      
      // Emit event
      this.eventBus.emit(EventType.TESTMO_API_GET_PROJECT_BY_ID, {
        project: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, `Failed to get project with ID ${projectId}`);
      return null;
    }
  }
  
  /**
   * Get test cases
   * @param projectId Project ID
   * @returns Test cases
   */
  async getTestCases(projectId?: number): Promise<TestmoTestCase[]> {
    try {
      const id = projectId || this.projectId;
      
      if (!id) {
        throw new Error('Project ID is required');
      }
      
      const response = await this.axios.get<TestmoAPIResponse<TestmoTestCase[]>>(`/api/v1/projects/${id}/cases`);
      
      // Emit event
      this.eventBus.emit(EventType.TESTMO_API_GET_TEST_CASES, {
        testCases: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, `Failed to get test cases for project with ID ${projectId || this.projectId}`);
      return [];
    }
  }
  
  /**
   * Get test case by ID
   * @param testCaseId Test case ID
   * @param projectId Project ID
   * @returns Test case
   */
  async getTestCaseById(testCaseId: number, projectId?: number): Promise<TestmoTestCase | null> {
    try {
      const id = projectId || this.projectId;
      
      if (!id) {
        throw new Error('Project ID is required');
      }
      
      const response = await this.axios.get<TestmoAPIResponse<TestmoTestCase>>(`/api/v1/projects/${id}/cases/${testCaseId}`);
      
      // Emit event
      this.eventBus.emit(EventType.TESTMO_API_GET_TEST_CASE_BY_ID, {
        testCase: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, `Failed to get test case with ID ${testCaseId}`);
      return null;
    }
  }
  
  /**
   * Create test case
   * @param title Test case title
   * @param sectionId Test case section ID
   * @param typeId Test case type ID
   * @param priorityId Test case priority ID
   * @param steps Test case steps
   * @param preconditions Test case preconditions
   * @param expected Test case expected result
   * @param refs Test case references
   * @param customFields Test case custom fields
   * @param projectId Project ID
   * @returns Created test case
   */
  async createTestCase(
    title: string,
    sectionId?: number,
    typeId?: number,
    priorityId?: number,
    steps?: { content: string; expected?: string; refs?: string }[],
    preconditions?: string,
    expected?: string,
    refs?: string,
    customFields?: Record<string, any>,
    projectId?: number
  ): Promise<TestmoTestCase | null> {
    try {
      const id = projectId || this.projectId;
      
      if (!id) {
        throw new Error('Project ID is required');
      }
      
      const response = await this.axios.post<TestmoAPIResponse<TestmoTestCase>>(`/api/v1/projects/${id}/cases`, {
        title,
        section_id: sectionId,
        type_id: typeId,
        priority_id: priorityId,
        steps,
        preconditions,
        expected,
        refs,
        custom_fields: customFields
      });
      
      // Emit event
      this.eventBus.emit(EventType.TESTMO_API_CREATE_TEST_CASE, {
        testCase: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, `Failed to create test case with title ${title}`);
      return null;
    }
  }
  
  /**
   * Update test case
   * @param testCaseId Test case ID
   * @param title Test case title
   * @param sectionId Test case section ID
   * @param typeId Test case type ID
   * @param priorityId Test case priority ID
   * @param steps Test case steps
   * @param preconditions Test case preconditions
   * @param expected Test case expected result
   * @param refs Test case references
   * @param customFields Test case custom fields
   * @param projectId Project ID
   * @returns Updated test case
   */
  async updateTestCase(
    testCaseId: number,
    title?: string,
    sectionId?: number,
    typeId?: number,
    priorityId?: number,
    steps?: { content: string; expected?: string; refs?: string }[],
    preconditions?: string,
    expected?: string,
    refs?: string,
    customFields?: Record<string, any>,
    projectId?: number
  ): Promise<TestmoTestCase | null> {
    try {
      const id = projectId || this.projectId;
      
      if (!id) {
        throw new Error('Project ID is required');
      }
      
      const response = await this.axios.put<TestmoAPIResponse<TestmoTestCase>>(`/api/v1/projects/${id}/cases/${testCaseId}`, {
        title,
        section_id: sectionId,
        type_id: typeId,
        priority_id: priorityId,
        steps,
        preconditions,
        expected,
        refs,
        custom_fields: customFields
      });
      
      // Emit event
      this.eventBus.emit(EventType.TESTMO_API_UPDATE_TEST_CASE, {
        testCase: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, `Failed to update test case with ID ${testCaseId}`);
      return null;
    }
  }
  
  /**
   * Delete test case
   * @param testCaseId Test case ID
   * @param projectId Project ID
   * @returns True if deleted, false otherwise
   */
  async deleteTestCase(testCaseId: number, projectId?: number): Promise<boolean> {
    try {
      const id = projectId || this.projectId;
      
      if (!id) {
        throw new Error('Project ID is required');
      }
      
      await this.axios.delete<TestmoAPIResponse<void>>(`/api/v1/projects/${id}/cases/${testCaseId}`);
      
      // Emit event
      this.eventBus.emit(EventType.TESTMO_API_DELETE_TEST_CASE, {
        testCaseId
      });
      
      return true;
    } catch (error) {
      this.handleError(error, `Failed to delete test case with ID ${testCaseId}`);
      return false;
    }
  }
  
  /**
   * Get test runs
   * @param projectId Project ID
   * @returns Test runs
   */
  async getTestRuns(projectId?: number): Promise<TestmoTestRun[]> {
    try {
      const id = projectId || this.projectId;
      
      if (!id) {
        throw new Error('Project ID is required');
      }
      
      const response = await this.axios.get<TestmoAPIResponse<TestmoTestRun[]>>(`/api/v1/projects/${id}/runs`);
      
      // Emit event
      this.eventBus.emit(EventType.TESTMO_API_GET_TEST_RUNS, {
        testRuns: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, `Failed to get test runs for project with ID ${projectId || this.projectId}`);
      return [];
    }
  }
  
  /**
   * Get test run by ID
   * @param testRunId Test run ID
   * @param projectId Project ID
   * @returns Test run
   */
  async getTestRunById(testRunId: number, projectId?: number): Promise<TestmoTestRun | null> {
    try {
      const id = projectId || this.projectId;
      
      if (!id) {
        throw new Error('Project ID is required');
      }
      
      const response = await this.axios.get<TestmoAPIResponse<TestmoTestRun>>(`/api/v1/projects/${id}/runs/${testRunId}`);
      
      // Emit event
      this.eventBus.emit(EventType.TESTMO_API_GET_TEST_RUN_BY_ID, {
        testRun: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, `Failed to get test run with ID ${testRunId}`);
      return null;
    }
  }
  
  /**
   * Create test run
   * @param name Test run name
   * @param description Test run description
   * @param milestoneId Test run milestone ID
   * @param assignedtoId Test run assignedto ID
   * @param configIds Test run config IDs
   * @param customFields Test run custom fields
   * @param projectId Project ID
   * @returns Created test run
   */
  async createTestRun(
    name: string,
    description?: string,
    milestoneId?: number,
    assignedtoId?: number,
    configIds?: number[],
    customFields?: Record<string, any>,
    projectId?: number
  ): Promise<TestmoTestRun | null> {
    try {
      const id = projectId || this.projectId;
      
      if (!id) {
        throw new Error('Project ID is required');
      }
      
      const response = await this.axios.post<TestmoAPIResponse<TestmoTestRun>>(`/api/v1/projects/${id}/runs`, {
        name,
        description,
        milestone_id: milestoneId,
        assignedto_id: assignedtoId,
        config_ids: configIds,
        custom_fields: customFields
      });
      
      // Emit event
      this.eventBus.emit(EventType.TESTMO_API_CREATE_TEST_RUN, {
        testRun: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, `Failed to create test run with name ${name}`);
      return null;
    }
  }
  
  /**
   * Update test run
   * @param testRunId Test run ID
   * @param name Test run name
   * @param description Test run description
   * @param milestoneId Test run milestone ID
   * @param assignedtoId Test run assignedto ID
   * @param isCompleted Test run is completed
   * @param customFields Test run custom fields
   * @param projectId Project ID
   * @returns Updated test run
   */
  async updateTestRun(
    testRunId: number,
    name?: string,
    description?: string,
    milestoneId?: number,
    assignedtoId?: number,
    isCompleted?: boolean,
    customFields?: Record<string, any>,
    projectId?: number
  ): Promise<TestmoTestRun | null> {
    try {
      const id = projectId || this.projectId;
      
      if (!id) {
        throw new Error('Project ID is required');
      }
      
      const response = await this.axios.put<TestmoAPIResponse<TestmoTestRun>>(`/api/v1/projects/${id}/runs/${testRunId}`, {
        name,
        description,
        milestone_id: milestoneId,
        assignedto_id: assignedtoId,
        is_completed: isCompleted,
        custom_fields: customFields
      });
      
      // Emit event
      this.eventBus.emit(EventType.TESTMO_API_UPDATE_TEST_RUN, {
        testRun: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, `Failed to update test run with ID ${testRunId}`);
      return null;
    }
  }
  
  /**
   * Delete test run
   * @param testRunId Test run ID
   * @param projectId Project ID
   * @returns True if deleted, false otherwise
   */
  async deleteTestRun(testRunId: number, projectId?: number): Promise<boolean> {
    try {
      const id = projectId || this.projectId;
      
      if (!id) {
        throw new Error('Project ID is required');
      }
      
      await this.axios.delete<TestmoAPIResponse<void>>(`/api/v1/projects/${id}/runs/${testRunId}`);
      
      // Emit event
      this.eventBus.emit(EventType.TESTMO_API_DELETE_TEST_RUN, {
        testRunId
      });
      
      return true;
    } catch (error) {
      this.handleError(error, `Failed to delete test run with ID ${testRunId}`);
      return false;
    }
  }
  
  /**
   * Get test results
   * @param testRunId Test run ID
   * @param projectId Project ID
   * @returns Test results
   */
  async getTestResults(testRunId: number, projectId?: number): Promise<TestmoTestResult[]> {
    try {
      const id = projectId || this.projectId;
      
      if (!id) {
        throw new Error('Project ID is required');
      }
      
      const response = await this.axios.get<TestmoAPIResponse<TestmoTestResult[]>>(`/api/v1/projects/${id}/runs/${testRunId}/results`);
      
      // Emit event
      this.eventBus.emit(EventType.TESTMO_API_GET_TEST_RESULTS, {
        testResults: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, `Failed to get test results for test run with ID ${testRunId}`);
      return [];
    }
  }
  
  /**
   * Add test result
   * @param testRunId Test run ID
   * @param testId Test ID
   * @param statusId Status ID
   * @param comment Comment
   * @param version Version
   * @param elapsed Elapsed
   * @param defects Defects
   * @param assignedtoId Assignedto ID
   * @param projectId Project ID
   * @returns Test result
   */
  async addTestResult(
    testRunId: number,
    testId: number,
    statusId: number,
    comment?: string,
    version?: string,
    elapsed?: string,
    defects?: string,
    assignedtoId?: number,
    projectId?: number
  ): Promise<TestmoTestResult | null> {
    try {
      const id = projectId || this.projectId;
      
      if (!id) {
        throw new Error('Project ID is required');
      }
      
      const response = await this.axios.post<TestmoAPIResponse<TestmoTestResult>>(`/api/v1/projects/${id}/runs/${testRunId}/results`, {
        test_id: testId,
        status_id: statusId,
        comment,
        version,
        elapsed,
        defects,
        assignedto_id: assignedtoId
      });
      
      // Emit event
      this.eventBus.emit(EventType.TESTMO_API_ADD_TEST_RESULT, {
        testResult: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, `Failed to add test result for test with ID ${testId}`);
      return null;
    }
  }
  
  /**
   * Handle error
   * @param error Error
   * @param message Error message
   */
  private handleError(error: any, message: string): void {
    console.error(message, error);
    
    // Emit event
    this.eventBus.emit(EventType.TESTMO_API_ERROR, {
      error,
      message
    });
  }
  
  /**
   * Get API key
   * @returns API key
   */
  getAPIKey(): string {
    return this.apiKey;
  }
  
  /**
   * Get host URL
   * @returns Host URL
   */
  getHost(): string {
    return this.host;
  }
  
  /**
   * Get project ID
   * @returns Project ID
   */
  getProjectId(): number {
    return this.projectId;
  }
  
  /**
   * Get timeout
   * @returns Timeout in milliseconds
   */
  getTimeout(): number {
    return this.timeout;
  }
  
  /**
   * Set API key
   * @param apiKey API key
   * @returns This instance for chaining
   */
  setAPIKey(apiKey: string): TestmoAPI {
    this.apiKey = apiKey;
    
    this.axios = axios.create({
      baseURL: this.host,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      }
    });
    
    return this;
  }
  
  /**
   * Set host URL
   * @param host Host URL
   * @returns This instance for chaining
   */
  setHost(host: string): TestmoAPI {
    this.host = host;
    
    this.axios = axios.create({
      baseURL: this.host,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      }
    });
    
    return this;
  }
  
  /**
   * Set project ID
   * @param projectId Project ID
   * @returns This instance for chaining
   */
  setProjectId(projectId: number): TestmoAPI {
    this.projectId = projectId;
    return this;
  }
  
  /**
   * Set timeout
   * @param timeout Timeout in milliseconds
   * @returns This instance for chaining
   */
  setTimeout(timeout: number): TestmoAPI {
    this.timeout = timeout;
    
    this.axios = axios.create({
      baseURL: this.host,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      }
    });
    
    return this;
  }
}

/**
 * Create Testmo API
 * @param options Testmo API options
 * @returns Testmo API
 */
export function createTestmoAPI(options: TestmoAPIOptions = {}): TestmoAPI {
  return new TestmoAPI(options);
}
