name: Unified GitHub Pages Deployment

on:
  # Manual trigger
  workflow_dispatch:

  # Run on push to main branch
  push:
    branches:
      - main
    paths:
      - 'public/**'  # Only run when the public directory changes

# Sets permissions of the GITHUB_TOKEN to allow deployment to GitHub Pages
permissions:
  contents: read
  pages: write
  id-token: write

# Allow only one concurrent deployment
concurrency:
  group: "pages"
  cancel-in-progress: false

jobs:
  deploy:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: main  # Ensure we're using the latest main branch

      - name: Setup Pages
        uses: actions/configure-pages@v4

      # Simple deployment - just upload the public directory
      - name: Upload artifact
        uses: actions/upload-pages-artifact@v2
        with:
          path: './public'

      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v3
