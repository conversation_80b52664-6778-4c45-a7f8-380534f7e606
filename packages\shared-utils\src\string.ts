/**
 * String utilities for QA Wolf Metrics Framework
 */

/**
 * Check if string is empty
 * @param str String to check
 * @returns True if string is empty, false otherwise
 */
export function isEmpty(str: string): boolean {
  return str === '';
}

/**
 * Check if string is null or empty
 * @param str String to check
 * @returns True if string is null or empty, false otherwise
 */
export function isNullOrEmpty(str: string | null | undefined): boolean {
  return str === null || str === undefined || str === '';
}

/**
 * Check if string is null, empty or whitespace
 * @param str String to check
 * @returns True if string is null, empty or whitespace, false otherwise
 */
export function isNullOrWhitespace(str: string | null | undefined): boolean {
  return isNullOrEmpty(str) || str!.trim() === '';
}

/**
 * Truncate string
 * @param str String to truncate
 * @param maxLength Maximum length
 * @param suffix Suffix to add if string is truncated
 * @returns Truncated string
 */
export function truncate(str: string, maxLength: number, suffix: string = '...'): string {
  if (str.length <= maxLength) {
    return str;
  }
  
  return str.substring(0, maxLength) + suffix;
}

/**
 * Pad string
 * @param str String to pad
 * @param length Length to pad to
 * @param char Character to pad with
 * @param padEnd Pad at the end
 * @returns Padded string
 */
export function pad(str: string, length: number, char: string = ' ', padEnd: boolean = true): string {
  if (str.length >= length) {
    return str;
  }
  
  const padding = char.repeat(length - str.length);
  
  return padEnd ? str + padding : padding + str;
}

/**
 * Pad start
 * @param str String to pad
 * @param length Length to pad to
 * @param char Character to pad with
 * @returns Padded string
 */
export function padStart(str: string, length: number, char: string = ' '): string {
  return pad(str, length, char, false);
}

/**
 * Pad end
 * @param str String to pad
 * @param length Length to pad to
 * @param char Character to pad with
 * @returns Padded string
 */
export function padEnd(str: string, length: number, char: string = ' '): string {
  return pad(str, length, char, true);
}

/**
 * Capitalize string
 * @param str String to capitalize
 * @returns Capitalized string
 */
export function capitalize(str: string): string {
  if (isNullOrEmpty(str)) {
    return str;
  }
  
  return str.charAt(0).toUpperCase() + str.slice(1);
}

/**
 * Capitalize words in string
 * @param str String to capitalize
 * @returns String with capitalized words
 */
export function capitalizeWords(str: string): string {
  if (isNullOrEmpty(str)) {
    return str;
  }
  
  return str.split(' ').map(word => capitalize(word)).join(' ');
}

/**
 * Convert string to camel case
 * @param str String to convert
 * @returns Camel case string
 */
export function toCamelCase(str: string): string {
  if (isNullOrEmpty(str)) {
    return str;
  }
  
  return str
    .replace(/[^a-zA-Z0-9]+(.)/g, (_, chr) => chr.toUpperCase())
    .replace(/^[A-Z]/, chr => chr.toLowerCase());
}

/**
 * Convert string to pascal case
 * @param str String to convert
 * @returns Pascal case string
 */
export function toPascalCase(str: string): string {
  if (isNullOrEmpty(str)) {
    return str;
  }
  
  return capitalize(toCamelCase(str));
}

/**
 * Convert string to kebab case
 * @param str String to convert
 * @returns Kebab case string
 */
export function toKebabCase(str: string): string {
  if (isNullOrEmpty(str)) {
    return str;
  }
  
  return str
    .replace(/([a-z])([A-Z])/g, '$1-$2')
    .replace(/[\s_]+/g, '-')
    .toLowerCase();
}

/**
 * Convert string to snake case
 * @param str String to convert
 * @returns Snake case string
 */
export function toSnakeCase(str: string): string {
  if (isNullOrEmpty(str)) {
    return str;
  }
  
  return str
    .replace(/([a-z])([A-Z])/g, '$1_$2')
    .replace(/[\s-]+/g, '_')
    .toLowerCase();
}

/**
 * Escape HTML
 * @param str String to escape
 * @returns Escaped string
 */
export function escapeHtml(str: string): string {
  if (isNullOrEmpty(str)) {
    return str;
  }
  
  return str
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
}

/**
 * Unescape HTML
 * @param str String to unescape
 * @returns Unescaped string
 */
export function unescapeHtml(str: string): string {
  if (isNullOrEmpty(str)) {
    return str;
  }
  
  return str
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#039;/g, "'");
}

/**
 * Generate random string
 * @param length Length of random string
 * @param chars Characters to use
 * @returns Random string
 */
export function randomString(length: number, chars: string = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'): string {
  let result = '';
  
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  return result;
}

/**
 * Format string with arguments
 * @param str String to format
 * @param args Arguments to format with
 * @returns Formatted string
 */
export function format(str: string, ...args: any[]): string {
  return str.replace(/{(\d+)}/g, (match, index) => {
    return typeof args[index] !== 'undefined' ? args[index] : match;
  });
}

/**
 * Format string with named arguments
 * @param str String to format
 * @param args Named arguments to format with
 * @returns Formatted string
 */
export function formatNamed(str: string, args: Record<string, any>): string {
  return str.replace(/{([^{}]*)}/g, (match, key) => {
    const value = args[key];
    return typeof value !== 'undefined' ? value : match;
  });
}

/**
 * Count occurrences of substring in string
 * @param str String to search in
 * @param substring Substring to search for
 * @returns Number of occurrences
 */
export function countOccurrences(str: string, substring: string): number {
  if (isNullOrEmpty(str) || isNullOrEmpty(substring)) {
    return 0;
  }
  
  return str.split(substring).length - 1;
}

/**
 * Reverse string
 * @param str String to reverse
 * @returns Reversed string
 */
export function reverse(str: string): string {
  if (isNullOrEmpty(str)) {
    return str;
  }
  
  return str.split('').reverse().join('');
}

/**
 * Check if string contains substring
 * @param str String to search in
 * @param substring Substring to search for
 * @param ignoreCase Ignore case
 * @returns True if string contains substring, false otherwise
 */
export function contains(str: string, substring: string, ignoreCase: boolean = false): boolean {
  if (isNullOrEmpty(str) || isNullOrEmpty(substring)) {
    return false;
  }
  
  if (ignoreCase) {
    return str.toLowerCase().includes(substring.toLowerCase());
  }
  
  return str.includes(substring);
}

/**
 * Check if string starts with substring
 * @param str String to check
 * @param substring Substring to check for
 * @param ignoreCase Ignore case
 * @returns True if string starts with substring, false otherwise
 */
export function startsWith(str: string, substring: string, ignoreCase: boolean = false): boolean {
  if (isNullOrEmpty(str) || isNullOrEmpty(substring)) {
    return false;
  }
  
  if (ignoreCase) {
    return str.toLowerCase().startsWith(substring.toLowerCase());
  }
  
  return str.startsWith(substring);
}

/**
 * Check if string ends with substring
 * @param str String to check
 * @param substring Substring to check for
 * @param ignoreCase Ignore case
 * @returns True if string ends with substring, false otherwise
 */
export function endsWith(str: string, substring: string, ignoreCase: boolean = false): boolean {
  if (isNullOrEmpty(str) || isNullOrEmpty(substring)) {
    return false;
  }
  
  if (ignoreCase) {
    return str.toLowerCase().endsWith(substring.toLowerCase());
  }
  
  return str.endsWith(substring);
}
