#!/usr/bin/env node

/**
 * QA Wolf Deployment Notification CLI
 * 
 * This script notifies QA Wolf of a new deployment.
 */

const { createDeploymentNotification } = require('../dist/deployment-notification');

// Parse command line arguments
const args = process.argv.slice(2);
const options = {};

for (let i = 0; i < args.length; i++) {
  const arg = args[i];
  
  if (arg === '--api-key' && i + 1 < args.length) {
    options.apiKey = args[++i];
  } else if (arg === '--team-id' && i + 1 < args.length) {
    options.teamId = args[++i];
  } else if (arg === '--api-url' && i + 1 < args.length) {
    options.apiUrl = args[++i];
  } else if (arg === '--reports-dir' && i + 1 < args.length) {
    options.reportsDir = args[++i];
  } else if (arg === '--environment' && i + 1 < args.length) {
    options.environment = args[++i];
  } else if (arg === '--branch' && i + 1 < args.length) {
    options.branch = args[++i];
  } else if (arg === '--commit' && i + 1 < args.length) {
    options.commit = args[++i];
  } else if (arg === '--build-url' && i + 1 < args.length) {
    options.buildUrl = args[++i];
  }
}

// Create deployment notification instance
const deploymentNotification = createDeploymentNotification(options);

// Notify deployment
deploymentNotification.notifyDeployment()
  .then(response => {
    console.log('QA Wolf deployment notification successful!');
    process.exit(0);
  })
  .catch(error => {
    console.error('Error notifying QA Wolf of deployment:', error.message);
    process.exit(1);
  });
