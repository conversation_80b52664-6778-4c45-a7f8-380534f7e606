/**
 * Report generator for reporting
 */

import * as fs from 'fs';
import * as path from 'path';
import { ReportGeneratorOptions, ReportFormat, ReportSection, ReportChart, ReportTable, ReportImage, ReportLink, ReportAttachment } from './types';
import { ReportTemplates } from './report-templates';
import { ReportFormatters } from './report-formatters';
import { ReportExporters } from './report-exporters';
import { ReportPublishers } from './report-publishers';
import { EventBus, EventType } from '@qawolf/core';

/**
 * Report generator
 */
export class ReportGenerator {
  /**
   * Report title
   */
  private title: string;
  
  /**
   * Report description
   */
  private description: string;
  
  /**
   * Report template
   */
  private template: string;
  
  /**
   * Report format
   */
  private format: ReportFormat;
  
  /**
   * Report data
   */
  private data: Record<string, any>;
  
  /**
   * Report metadata
   */
  private metadata: Record<string, any>;
  
  /**
   * Report output directory
   */
  private outputDir: string;
  
  /**
   * Report filename
   */
  private filename: string;
  
  /**
   * Report date
   */
  private date: Date;
  
  /**
   * Report author
   */
  private author: string;
  
  /**
   * Report version
   */
  private version: string;
  
  /**
   * Report logo
   */
  private logo: string;
  
  /**
   * Report stylesheet
   */
  private stylesheet: string;
  
  /**
   * Report scripts
   */
  private scripts: string[];
  
  /**
   * Report sections
   */
  private sections: ReportSection[];
  
  /**
   * Report charts
   */
  private charts: ReportChart[];
  
  /**
   * Report tables
   */
  private tables: ReportTable[];
  
  /**
   * Report images
   */
  private images: ReportImage[];
  
  /**
   * Report links
   */
  private links: ReportLink[];
  
  /**
   * Report attachments
   */
  private attachments: ReportAttachment[];
  
  /**
   * Report templates
   */
  private reportTemplates: ReportTemplates;
  
  /**
   * Report formatters
   */
  private reportFormatters: ReportFormatters;
  
  /**
   * Report exporters
   */
  private reportExporters: ReportExporters;
  
  /**
   * Report publishers
   */
  private reportPublishers: ReportPublishers;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options Report generator options
   */
  constructor(options: ReportGeneratorOptions = {}) {
    this.title = options.title || 'QA Wolf Report';
    this.description = options.description || 'QA Wolf Report';
    this.template = options.template || 'default';
    this.format = options.format || ReportFormat.HTML;
    this.data = options.data || {};
    this.metadata = options.metadata || {};
    this.outputDir = options.outputDir || 'reports';
    this.filename = options.filename || `report-${Date.now()}`;
    this.date = options.date || new Date();
    this.author = options.author || 'QA Wolf';
    this.version = options.version || '1.0.0';
    this.logo = options.logo || '';
    this.stylesheet = options.stylesheet || '';
    this.scripts = options.scripts || [];
    this.sections = options.sections || [];
    this.charts = options.charts || [];
    this.tables = options.tables || [];
    this.images = options.images || [];
    this.links = options.links || [];
    this.attachments = options.attachments || [];
    
    this.reportTemplates = new ReportTemplates();
    this.reportFormatters = new ReportFormatters();
    this.reportExporters = new ReportExporters();
    this.reportPublishers = new ReportPublishers();
    
    this.eventBus = EventBus.getInstance();
  }
  
  /**
   * Generate report
   * @returns Report content
   */
  async generate(): Promise<string> {
    try {
      // Get template
      const template = this.reportTemplates.getTemplate(this.template);
      
      if (!template) {
        throw new Error(`Template ${this.template} not found`);
      }
      
      // Prepare data
      const data = {
        title: this.title,
        description: this.description,
        date: this.date,
        author: this.author,
        version: this.version,
        logo: this.logo,
        stylesheet: this.stylesheet,
        scripts: this.scripts,
        sections: this.sections,
        charts: this.charts,
        tables: this.tables,
        images: this.images,
        links: this.links,
        attachments: this.attachments,
        data: this.data,
        metadata: this.metadata
      };
      
      // Render template
      const content = await this.reportTemplates.render(this.template, data);
      
      // Format content
      const formatted = await this.reportFormatters.format(content, this.format);
      
      // Emit event
      this.eventBus.emit(EventType.REPORT_GENERATOR_GENERATE, {
        content: formatted
      });
      
      return formatted;
    } catch (error) {
      this.handleError(error, 'Failed to generate report');
      return '';
    }
  }
  
  /**
   * Export report
   * @returns Report file path
   */
  async export(): Promise<string> {
    try {
      // Generate report
      const content = await this.generate();
      
      if (!content) {
        throw new Error('Failed to generate report content');
      }
      
      // Export report
      const filePath = await this.reportExporters.export(content, this.format, this.outputDir, this.filename);
      
      // Emit event
      this.eventBus.emit(EventType.REPORT_GENERATOR_EXPORT, {
        filePath
      });
      
      return filePath;
    } catch (error) {
      this.handleError(error, 'Failed to export report');
      return '';
    }
  }
  
  /**
   * Publish report
   * @returns Published URL
   */
  async publish(): Promise<string> {
    try {
      // Export report
      const filePath = await this.export();
      
      if (!filePath) {
        throw new Error('Failed to export report');
      }
      
      // Publish report
      const url = await this.reportPublishers.publish(filePath, this.format, this.metadata.publishOptions);
      
      // Emit event
      this.eventBus.emit(EventType.REPORT_GENERATOR_PUBLISH, {
        url
      });
      
      return url;
    } catch (error) {
      this.handleError(error, 'Failed to publish report');
      return '';
    }
  }
  
  /**
   * Add section
   * @param section Section
   * @returns This instance for chaining
   */
  addSection(section: ReportSection): ReportGenerator {
    this.sections.push(section);
    return this;
  }
  
  /**
   * Add chart
   * @param chart Chart
   * @returns This instance for chaining
   */
  addChart(chart: ReportChart): ReportGenerator {
    this.charts.push(chart);
    return this;
  }
  
  /**
   * Add table
   * @param table Table
   * @returns This instance for chaining
   */
  addTable(table: ReportTable): ReportGenerator {
    this.tables.push(table);
    return this;
  }
  
  /**
   * Add image
   * @param image Image
   * @returns This instance for chaining
   */
  addImage(image: ReportImage): ReportGenerator {
    this.images.push(image);
    return this;
  }
  
  /**
   * Add link
   * @param link Link
   * @returns This instance for chaining
   */
  addLink(link: ReportLink): ReportGenerator {
    this.links.push(link);
    return this;
  }
  
  /**
   * Add attachment
   * @param attachment Attachment
   * @returns This instance for chaining
   */
  addAttachment(attachment: ReportAttachment): ReportGenerator {
    this.attachments.push(attachment);
    return this;
  }
  
  /**
   * Set data
   * @param key Key
   * @param value Value
   * @returns This instance for chaining
   */
  setData(key: string, value: any): ReportGenerator {
    this.data[key] = value;
    return this;
  }
  
  /**
   * Set metadata
   * @param key Key
   * @param value Value
   * @returns This instance for chaining
   */
  setMetadata(key: string, value: any): ReportGenerator {
    this.metadata[key] = value;
    return this;
  }
  
  /**
   * Handle error
   * @param error Error
   * @param message Error message
   */
  private handleError(error: any, message: string): void {
    console.error(message, error);
    
    // Emit event
    this.eventBus.emit(EventType.REPORT_GENERATOR_ERROR, {
      error,
      message
    });
  }
  
  /**
   * Get title
   * @returns Title
   */
  getTitle(): string {
    return this.title;
  }
  
  /**
   * Get description
   * @returns Description
   */
  getDescription(): string {
    return this.description;
  }
  
  /**
   * Get template
   * @returns Template
   */
  getTemplate(): string {
    return this.template;
  }
  
  /**
   * Get format
   * @returns Format
   */
  getFormat(): ReportFormat {
    return this.format;
  }
  
  /**
   * Get data
   * @returns Data
   */
  getData(): Record<string, any> {
    return this.data;
  }
  
  /**
   * Get metadata
   * @returns Metadata
   */
  getMetadata(): Record<string, any> {
    return this.metadata;
  }
  
  /**
   * Get output directory
   * @returns Output directory
   */
  getOutputDir(): string {
    return this.outputDir;
  }
  
  /**
   * Get filename
   * @returns Filename
   */
  getFilename(): string {
    return this.filename;
  }
  
  /**
   * Set title
   * @param title Title
   * @returns This instance for chaining
   */
  setTitle(title: string): ReportGenerator {
    this.title = title;
    return this;
  }
  
  /**
   * Set description
   * @param description Description
   * @returns This instance for chaining
   */
  setDescription(description: string): ReportGenerator {
    this.description = description;
    return this;
  }
  
  /**
   * Set template
   * @param template Template
   * @returns This instance for chaining
   */
  setTemplate(template: string): ReportGenerator {
    this.template = template;
    return this;
  }
  
  /**
   * Set format
   * @param format Format
   * @returns This instance for chaining
   */
  setFormat(format: ReportFormat): ReportGenerator {
    this.format = format;
    return this;
  }
  
  /**
   * Set output directory
   * @param outputDir Output directory
   * @returns This instance for chaining
   */
  setOutputDir(outputDir: string): ReportGenerator {
    this.outputDir = outputDir;
    return this;
  }
  
  /**
   * Set filename
   * @param filename Filename
   * @returns This instance for chaining
   */
  setFilename(filename: string): ReportGenerator {
    this.filename = filename;
    return this;
  }
}

/**
 * Create report generator
 * @param options Report generator options
 * @returns Report generator
 */
export function createReportGenerator(options: ReportGeneratorOptions = {}): ReportGenerator {
  return new ReportGenerator(options);
}
