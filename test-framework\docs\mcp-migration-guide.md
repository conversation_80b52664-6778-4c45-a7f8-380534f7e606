# MCP Migration Guide

This guide explains how to migrate existing tests to use the MCP integration in the test-framework package.

## Why Migrate?

The MCP integration provides several benefits:

- **Optimized Selectors**: Automatically optimize selectors for better reliability
- **Tool Selection**: Automatically select the appropriate MCP tool for each task
- **Screenshot Analysis**: Analyze screenshots to identify UI elements
- **Performance Reporting**: Generate reports on MCP usage and performance

## Migration Steps

### 1. Import the MCP Controller

Replace direct MCP imports with the MCP controller from the test-framework package:

```javascript
// Before
const mcpOptimizer = require('@qawolf/mcp-optimizer');

// After
const { createMcpController } = require('@qawolf/test-framework');
```

### 2. Create an MCP Controller

Create an MCP controller at the beginning of your test:

```javascript
// Create an MCP controller
const mcpController = createMcpController({
  autoStartPlaywrightMcp: true,
  generateFallbacks: true,
  prioritizeTestIds: true
});
```

### 3. Replace Direct MCP Calls

Replace direct calls to the MCP optimizer with calls to the MCP controller:

```javascript
// Before
const result = await mcpOptimizer.optimizeSelectors({
  selectors: ['button.login-button'],
  options: {
    generateFallbacks: true,
    prioritizeTestIds: true
  }
});

// After
const result = await mcpController.optimizeSelectors(['button.login-button'], {
  generateFallbacks: true,
  prioritizeTestIds: true
});
```

### 4. Clean Up Resources

Make sure to clean up MCP resources at the end of your test:

```javascript
// Clean up MCP resources
await mcpController.cleanup();
```

### 5. Use the try/finally Pattern

To ensure resources are cleaned up even if the test fails, use the try/finally pattern:

```javascript
const mcpController = createMcpController();

try {
  // Test code here
} finally {
  await mcpController.cleanup();
}
```

## Example

Here's a complete example of a test that uses the MCP integration:

```javascript
const { test } = require('@playwright/test');
const { createMcpController, test: testUtils } = require('@qawolf/test-framework');

test('Login with MCP integration', async ({ page }) => {
  // Create an MCP controller
  const mcpController = createMcpController({
    autoStartPlaywrightMcp: true,
    generateFallbacks: true,
    prioritizeTestIds: true
  });
  
  try {
    // Start performance tracking
    const performanceTracker = new testUtils.PerformanceTracker();
    
    // Navigate to the app
    performanceTracker.startOperation('navigation_to_app');
    await page.goto('https://app.lidostaging.com');
    performanceTracker.endOperation();
    
    // Optimize selectors for login form
    const selectors = await mcpController.optimizeSelectors([
      '[data-test-id="SignInEmail"]',
      '[data-test-id="SignInPassword"]',
      ':text("Log in with email")'
    ]);
    
    // Fill in login form
    performanceTracker.startOperation('login');
    await page.fill(selectors.selectors[0].optimized, '<EMAIL>');
    await page.fill(selectors.selectors[1].optimized, 'vhc!tGK289IS&');
    
    // Click the login button
    await page.click(selectors.selectors[2].optimized);
    
    // Wait for login to complete
    await page.waitForSelector('div[class*="FilesTable"]', { timeout: 15000 });
    performanceTracker.endOperation();
    
    // Print test summary
    testUtils.printTestSummary(
      {
        success: true,
        performanceMetrics: performanceTracker.getMetrics(),
        aaaComplianceScore: testUtils.calculateAAAComplianceScore({
          success: true,
          performanceMetrics: performanceTracker.getMetrics(),
          allPerformanceMetricsWithinThresholds: performanceTracker.areAllMetricsWithinThresholds()
        })
      },
      'login-with-mcp',
      'Login with MCP integration',
      performanceTracker.startTime
    );
  } finally {
    // Clean up MCP resources
    await mcpController.cleanup();
  }
});
```

## Configuration Options

The MCP controller accepts the following configuration options:

```javascript
const mcpController = createMcpController({
  // General
  enabled: true,
  
  // Playwright MCP
  autoStartPlaywrightMcp: true,
  playwrightMcpPort: 8932,
  playwrightMcpConfigPath: null, // Will use default if null
  headless: false,
  
  // Selector optimization
  generateFallbacks: true,
  prioritizeTestIds: true,
  
  // Tool selection
  tokenOptimized: true,
  performanceOptimized: false,
  
  // Reporting
  reportFormat: 'markdown',
  includeTimestamps: true,
  includeTokenUsage: true,
  
  // Logging
  logLevel: 'info', // 'debug', 'info', 'warn', 'error'
  
  // Advanced
  retryCount: 3,
  retryDelay: 1000,
  timeout: 30000
});
```

## API Reference

### McpController

- `optimizeSelectors(selectors, options)`: Optimize selectors for better reliability
- `selectMcpTool(task, options)`: Select the appropriate MCP tool for a task
- `analyzeScreenshot(screenshot, options)`: Analyze a screenshot to identify UI elements
- `generateReport(results, options)`: Generate a report on MCP usage
- `initialize()`: Initialize MCP tools
- `cleanup()`: Clean up MCP resources

For more information, see the [API documentation](./api.md).