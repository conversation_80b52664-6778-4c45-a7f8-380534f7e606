/**
 * Self-Healing Module
 * 
 * This module provides self-healing automation capabilities for tests.
 */

const SelectorHealer = require('./selector-healer');
const RecoveryManager = require('./recovery-manager');
const FeedbackCollector = require('./feedback-collector');
const LearningEngine = require('./learning-engine');
const { createSelfHealingPage, createSelfHealingLocator } = require('./playwright-wrappers');
const { createDefaultConfig, validateConfig, mergeConfigs } = require('./config');

/**
 * Create a self-healing controller
 * @param {Object} [config] - Configuration options
 * @returns {Object} - Self-healing controller
 */
function createSelfHealingController(config = {}) {
  // Merge with default config
  const mergedConfig = mergeConfigs(createDefaultConfig(), config);
  
  // Validate config
  validateConfig(mergedConfig);
  
  // Create components
  const selectorHealer = new SelectorHealer(mergedConfig.selectorHealing);
  const recoveryManager = new RecoveryManager(mergedConfig.recovery);
  const feedbackCollector = new FeedbackCollector(mergedConfig.feedbackCollection);
  const learningEngine = new LearningEngine(mergedConfig.learning);
  
  // Initialize components
  const initialize = async () => {
    await Promise.all([
      selectorHealer.initialize(),
      recoveryManager.initialize(),
      feedbackCollector.initialize(),
      learningEngine.initialize()
    ]);
  };
  
  // Create controller
  return {
    config: mergedConfig,
    selectorHealer,
    recoveryManager,
    feedbackCollector,
    learningEngine,
    
    /**
     * Initialize the self-healing controller
     * @returns {Promise<void>}
     */
    initialize,
    
    /**
     * Create a self-healing page
     * @param {Object} page - Playwright page
     * @returns {Object} - Self-healing page
     */
    createPage: (page) => {
      return createSelfHealingPage(page, {
        selectorHealer,
        recoveryManager,
        feedbackCollector,
        config: mergedConfig
      });
    },
    
    /**
     * Start a test run
     * @param {Object} testInfo - Test information
     * @returns {Promise<void>}
     */
    startTest: async (testInfo) => {
      await initialize();
      await feedbackCollector.startTestRun(testInfo);
    },
    
    /**
     * End a test run
     * @param {Object} result - Test result
     * @returns {Promise<void>}
     */
    endTest: async (result) => {
      await feedbackCollector.endTestRun(result);
      
      // Learn from feedback data if auto-learning is enabled
      if (mergedConfig.learning.enabled && mergedConfig.learning.autoOptimize) {
        await learningEngine.learn(await feedbackCollector.exportFeedback());
      }
    },
    
    /**
     * Track performance metrics
     * @param {Object} metrics - Performance metrics
     * @returns {Promise<void>}
     */
    trackPerformance: async (metrics) => {
      await feedbackCollector.trackPerformance(metrics);
    },
    
    /**
     * Generate a report
     * @param {Object} [options] - Report options
     * @returns {Promise<string>} - Report
     */
    generateReport: async (options = {}) => {
      return await feedbackCollector.generateReport(options);
    },
    
    /**
     * Analyze feedback data
     * @returns {Promise<Object>} - Analysis results
     */
    analyzeFeedback: async () => {
      return await feedbackCollector.analyzeFeedback();
    },
    
    /**
     * Suggest improvements for a test script
     * @param {string} testPath - Path to the test script
     * @returns {Promise<Array<Object>>} - Improvement suggestions
     */
    suggestImprovements: async (testPath) => {
      return await learningEngine.suggestImprovements(testPath);
    },
    
    /**
     * Clean up resources
     * @returns {Promise<void>}
     */
    cleanup: async () => {
      await Promise.all([
        selectorHealer.cleanup(),
        recoveryManager.cleanup(),
        feedbackCollector.cleanup(),
        learningEngine.cleanup()
      ]);
    }
  };
}

module.exports = {
  createSelfHealingController,
  SelectorHealer,
  RecoveryManager,
  FeedbackCollector,
  LearningEngine,
  createSelfHealingPage,
  createSelfHealingLocator,
  createDefaultConfig,
  validateConfig,
  mergeConfigs
};