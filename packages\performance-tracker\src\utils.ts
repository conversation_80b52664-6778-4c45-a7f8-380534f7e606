/**
 * Utilities for performance tracking
 */

import { Operation, OperationStats, ResourceUsage, ResourceStats } from './types';

/**
 * Calculate operation statistics
 * @param operations Operations
 * @returns Operation statistics
 */
export function calculateOperationStats(operations: Operation[]): OperationStats {
  if (operations.length === 0) {
    return {
      count: 0,
      totalDuration: 0,
      averageDuration: 0,
      minDuration: 0,
      maxDuration: 0,
      byType: {}
    };
  }
  
  // Calculate overall statistics
  const count = operations.length;
  const totalDuration = operations.reduce((sum, operation) => sum + operation.duration, 0);
  const averageDuration = totalDuration / count;
  const minDuration = Math.min(...operations.map(operation => operation.duration));
  const maxDuration = Math.max(...operations.map(operation => operation.duration));
  
  // Calculate statistics by type
  const byType: OperationStats['byType'] = {};
  
  for (const operation of operations) {
    if (!byType[operation.type]) {
      byType[operation.type] = {
        count: 0,
        totalDuration: 0,
        averageDuration: 0,
        minDuration: Number.MAX_VALUE,
        maxDuration: 0
      };
    }
    
    byType[operation.type].count++;
    byType[operation.type].totalDuration += operation.duration;
    byType[operation.type].minDuration = Math.min(byType[operation.type].minDuration, operation.duration);
    byType[operation.type].maxDuration = Math.max(byType[operation.type].maxDuration, operation.duration);
  }
  
  // Calculate average duration by type
  for (const type in byType) {
    byType[type].averageDuration = byType[type].totalDuration / byType[type].count;
  }
  
  return {
    count,
    totalDuration,
    averageDuration,
    minDuration,
    maxDuration,
    byType
  };
}

/**
 * Calculate resource statistics
 * @param resourceUsage Resource usage
 * @returns Resource statistics
 */
export function calculateResourceStats(resourceUsage: ResourceUsage[]): ResourceStats {
  if (resourceUsage.length === 0) {
    return {
      memory: {
        maxRss: 0,
        maxHeapTotal: 0,
        maxHeapUsed: 0,
        maxExternal: 0,
        maxArrayBuffers: 0
      },
      cpu: {
        totalUser: 0,
        totalSystem: 0
      }
    };
  }
  
  // Calculate memory statistics
  const maxRss = Math.max(...resourceUsage.map(usage => usage.memory.rss));
  const maxHeapTotal = Math.max(...resourceUsage.map(usage => usage.memory.heapTotal));
  const maxHeapUsed = Math.max(...resourceUsage.map(usage => usage.memory.heapUsed));
  const maxExternal = Math.max(...resourceUsage.map(usage => usage.memory.external));
  const maxArrayBuffers = Math.max(...resourceUsage.map(usage => usage.memory.arrayBuffers));
  
  // Calculate CPU statistics
  const totalUser = resourceUsage.reduce((sum, usage) => sum + usage.cpu.user, 0);
  const totalSystem = resourceUsage.reduce((sum, usage) => sum + usage.cpu.system, 0);
  
  return {
    memory: {
      maxRss,
      maxHeapTotal,
      maxHeapUsed,
      maxExternal,
      maxArrayBuffers
    },
    cpu: {
      totalUser,
      totalSystem
    }
  };
}

/**
 * Format duration
 * @param duration Duration in milliseconds
 * @returns Formatted duration
 */
export function formatDuration(duration: number): string {
  if (duration < 1000) {
    return `${duration}ms`;
  } else if (duration < 60000) {
    return `${(duration / 1000).toFixed(2)}s`;
  } else {
    const minutes = Math.floor(duration / 60000);
    const seconds = ((duration % 60000) / 1000).toFixed(2);
    return `${minutes}m ${seconds}s`;
  }
}

/**
 * Format bytes
 * @param bytes Bytes
 * @param decimals Decimal places
 * @returns Formatted bytes
 */
export function formatBytes(bytes: number, decimals: number = 2): string {
  if (bytes === 0) {
    return '0 Bytes';
  }
  
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

/**
 * Format percentage
 * @param value Value
 * @param total Total
 * @param decimals Decimal places
 * @returns Formatted percentage
 */
export function formatPercentage(value: number, total: number, decimals: number = 2): string {
  if (total === 0) {
    return '0%';
  }
  
  return (value / total * 100).toFixed(decimals) + '%';
}

/**
 * Group operations by type
 * @param operations Operations
 * @returns Operations grouped by type
 */
export function groupOperationsByType(operations: Operation[]): { [type: string]: Operation[] } {
  const grouped: { [type: string]: Operation[] } = {};
  
  for (const operation of operations) {
    if (!grouped[operation.type]) {
      grouped[operation.type] = [];
    }
    
    grouped[operation.type].push(operation);
  }
  
  return grouped;
}

/**
 * Find slow operations
 * @param operations Operations
 * @param threshold Threshold in milliseconds
 * @returns Slow operations
 */
export function findSlowOperations(operations: Operation[], threshold: number = 1000): Operation[] {
  return operations
    .filter(operation => operation.duration > threshold)
    .sort((a, b) => b.duration - a.duration);
}

/**
 * Find operations by type
 * @param operations Operations
 * @param type Operation type
 * @returns Operations of the specified type
 */
export function findOperationsByType(operations: Operation[], type: string): Operation[] {
  return operations.filter(operation => operation.type === type);
}

/**
 * Find operations by name
 * @param operations Operations
 * @param name Operation name
 * @returns Operations with the specified name
 */
export function findOperationsByName(operations: Operation[], name: string): Operation[] {
  return operations.filter(operation => operation.name === name);
}

/**
 * Find operations by metadata
 * @param operations Operations
 * @param metadata Operation metadata
 * @returns Operations with the specified metadata
 */
export function findOperationsByMetadata(operations: Operation[], metadata: { [key: string]: any }): Operation[] {
  return operations.filter(operation => {
    if (!operation.metadata) {
      return false;
    }
    
    for (const [key, value] of Object.entries(metadata)) {
      if (operation.metadata[key] !== value) {
        return false;
      }
    }
    
    return true;
  });
}

/**
 * Calculate operation duration percentages
 * @param operations Operations
 * @returns Operation duration percentages
 */
export function calculateOperationDurationPercentages(operations: Operation[]): { [operationName: string]: number } {
  const totalDuration = operations.reduce((sum, operation) => sum + operation.duration, 0);
  
  if (totalDuration === 0) {
    return {};
  }
  
  const percentages: { [operationName: string]: number } = {};
  
  for (const operation of operations) {
    percentages[operation.name] = (operation.duration / totalDuration) * 100;
  }
  
  return percentages;
}

/**
 * Calculate operation type duration percentages
 * @param operations Operations
 * @returns Operation type duration percentages
 */
export function calculateOperationTypeDurationPercentages(operations: Operation[]): { [type: string]: number } {
  const totalDuration = operations.reduce((sum, operation) => sum + operation.duration, 0);
  
  if (totalDuration === 0) {
    return {};
  }
  
  const typeDurations: { [type: string]: number } = {};
  
  for (const operation of operations) {
    if (!typeDurations[operation.type]) {
      typeDurations[operation.type] = 0;
    }
    
    typeDurations[operation.type] += operation.duration;
  }
  
  const percentages: { [type: string]: number } = {};
  
  for (const [type, duration] of Object.entries(typeDurations)) {
    percentages[type] = (duration / totalDuration) * 100;
  }
  
  return percentages;
}
