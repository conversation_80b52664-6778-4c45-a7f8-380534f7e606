#!/usr/bin/env node

/**
 * Version Packages Script
 * 
 * This script updates version numbers across packages.
 */

const path = require('path');
const fs = require('fs');
const { execSync } = require('child_process');

// Configuration
const config = {
  packages: [
    'shared-utils',
    'mcp-optimizer',
    'test-framework'
  ]
};

/**
 * Get the current version of a package
 * @param {string} packageName - Package name
 * @returns {string} - Current version
 */
function getCurrentVersion(packageName) {
  const packagePath = path.join(process.cwd(), packageName);
  const packageJsonPath = path.join(packagePath, 'package.json');
  
  // Check if package exists
  if (!fs.existsSync(packageJsonPath)) {
    console.error(`Package ${packageName} does not exist at ${packagePath}`);
    process.exit(1);
  }
  
  // Read package.json
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  return packageJson.version;
}

/**
 * Update the version of a package
 * @param {string} packageName - Package name
 * @param {string} version - New version
 * @returns {void}
 */
function updateVersion(packageName, version) {
  const packagePath = path.join(process.cwd(), packageName);
  const packageJsonPath = path.join(packagePath, 'package.json');
  
  // Check if package exists
  if (!fs.existsSync(packageJsonPath)) {
    console.error(`Package ${packageName} does not exist at ${packagePath}`);
    process.exit(1);
  }
  
  // Read package.json
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  // Update version
  packageJson.version = version;
  
  // Update dependencies
  if (packageJson.dependencies) {
    for (const dependency in packageJson.dependencies) {
      if (dependency.startsWith('@qawolf/')) {
        const dependencyName = dependency.replace('@qawolf/', '');
        if (config.packages.includes(dependencyName)) {
          packageJson.dependencies[dependency] = `^${version}`;
        }
      }
    }
  }
  
  // Write package.json
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2), 'utf8');
  
  console.log(`Updated ${packageName} to version ${version}`);
}

/**
 * Update versions of all packages
 * @param {string} version - New version
 * @returns {void}
 */
function updateAllVersions(version) {
  // Update versions
  for (const packageName of config.packages) {
    updateVersion(packageName, version);
  }
  
  console.log(`All packages updated to version ${version}`);
  
  // Commit changes
  try {
    execSync('git add .', { stdio: 'inherit' });
    execSync(`git commit -m "chore: bump version to ${version}"`, { stdio: 'inherit' });
    console.log('Changes committed');
  } catch (error) {
    console.error('Failed to commit changes:', error.message);
  }
}

// Parse command line arguments
const args = process.argv.slice(2);
if (args.length !== 1) {
  console.error('Usage: node version-packages.js <version>');
  process.exit(1);
}

const version = args[0];

// Validate version
if (!/^\d+\.\d+\.\d+$/.test(version)) {
  console.error('Invalid version format. Please use semver (e.g., 1.0.0)');
  process.exit(1);
}

// Run the script
updateAllVersions(version);