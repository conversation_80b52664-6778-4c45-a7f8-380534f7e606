
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>QA Wolf Test Report</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 0; padding: 20px; color: #333; }
    h1, h2, h3 { color: #2c3e50; }
    .summary { background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
    .summary-item { margin-bottom: 10px; }
    .pass { color: #28a745; }
    .fail { color: #dc3545; }
    .warning { color: #ffc107; }
    .test-details { margin-top: 30px; }
    table { width: 100%; border-collapse: collapse; margin-top: 20px; }
    th, td { padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd; }
    th { background-color: #f2f2f2; }
    tr:hover { background-color: #f5f5f5; }
    .progress-container { width: 100%; background-color: #e0e0e0; border-radius: 4px; }
    .progress-bar { height: 20px; border-radius: 4px; }
    .progress-bar.good { background-color: #28a745; }
    .progress-bar.warning { background-color: #ffc107; }
    .progress-bar.poor { background-color: #dc3545; }
    .details-list { list-style-type: none; padding-left: 0; }
    .details-list li { margin-bottom: 5px; }
  </style>
</head>
<body>
  <h1>QA Wolf Test Report</h1>
  
  <div class="summary">
    <h2>Summary</h2>
    <div class="summary-item">
      <strong>Code Quality Score:</strong> 
      <span class="warning">
        74.64%
      </span>
      <div class="progress-container">
        <div class="progress-bar warning" 
             style="width: 74.64285714285714%"></div>
      </div>
    </div>
    <div class="summary-item">
      <strong>Test Execution Success:</strong> 
      <span class="fail">
        33.33%
      </span>
      <div class="progress-container">
        <div class="progress-bar poor" 
             style="width: 33.33333333333333%"></div>
      </div>
    </div>
    <div class="summary-item">
      <strong>Tests Evaluated:</strong> 28
    </div>
    <div class="summary-item">
      <strong>Tests Executed:</strong> 27
    </div>
    <div class="summary-item">
      <strong>Tests Passed:</strong> 9
    </div>
    <div class="summary-item">
      <strong>Overall Status:</strong> 
      <span class="fail">
        FAILED
      </span>
    </div>
  </div>
  
  <div class="test-details">
    <h2>Test Details</h2>
    <table>
      <thead>
        <tr>
          <th>Test File</th>
          <th>Quality Score</th>
          <th>Execution Status</th>
          <th>Details</th>
        </tr>
      </thead>
      <tbody>
        
            <tr>
              <td>action_library_example.js</td>
              <td class="fail">
                45%
              </td>
              <td class="fail">
                FAILED
              </td>
              <td>
                <details>
                  <summary>View Details</summary>
                  <ul class="details-list">
                    <li>✅ Has proper documentation</li><li>✅ No hardcoded credentials</li><li>❌ Not using centralized selectors</li><li>❌ Does not explicitly document AAA pattern</li><li>✅ Has proper Arrange step</li><li>❌ Missing proper Act step</li><li>❌ Missing proper Assert step</li><li>✅ Has proper error handling</li><li>✅ Has proper cleanup</li>
                  </ul>
                </details>
              </td>
            </tr>
          
            <tr>
              <td>airbnb_login_test.js</td>
              <td class="warning">
                70%
              </td>
              <td class="fail">
                FAILED
              </td>
              <td>
                <details>
                  <summary>View Details</summary>
                  <ul class="details-list">
                    <li>✅ Has proper documentation</li><li>❌ Contains hardcoded credentials</li><li>❌ Not using centralized selectors</li><li>✅ Explicitly documents AAA pattern</li><li>✅ Has proper Arrange step</li><li>✅ Has proper Act step</li><li>✅ Has proper Assert step</li><li>❌ Missing proper error handling</li><li>❌ Missing proper cleanup</li>
                  </ul>
                </details>
              </td>
            </tr>
          
            <tr>
              <td>chaos_test_example.js</td>
              <td class="fail">
                45%
              </td>
              <td class="fail">
                FAILED
              </td>
              <td>
                <details>
                  <summary>View Details</summary>
                  <ul class="details-list">
                    <li>✅ Has proper documentation</li><li>✅ No hardcoded credentials</li><li>❌ Not using centralized selectors</li><li>❌ Does not explicitly document AAA pattern</li><li>✅ Has proper Arrange step</li><li>❌ Missing proper Act step</li><li>❌ Missing proper Assert step</li><li>✅ Has proper error handling</li><li>✅ Has proper cleanup</li>
                  </ul>
                </details>
              </td>
            </tr>
          
            <tr>
              <td>collision_test_1.js</td>
              <td class="fail">
                50%
              </td>
              <td class="fail">
                FAILED
              </td>
              <td>
                <details>
                  <summary>View Details</summary>
                  <ul class="details-list">
                    <li>✅ Has proper documentation</li><li>✅ No hardcoded credentials</li><li>❌ Not using centralized selectors</li><li>❌ Does not explicitly document AAA pattern</li><li>✅ Has proper Arrange step</li><li>❌ Missing proper Act step</li><li>✅ Has proper Assert step</li><li>❌ Missing proper error handling</li><li>❌ Missing proper cleanup</li>
                  </ul>
                </details>
              </td>
            </tr>
          
            <tr>
              <td>collision_test_2.js</td>
              <td class="fail">
                50%
              </td>
              <td class="fail">
                FAILED
              </td>
              <td>
                <details>
                  <summary>View Details</summary>
                  <ul class="details-list">
                    <li>✅ Has proper documentation</li><li>✅ No hardcoded credentials</li><li>❌ Not using centralized selectors</li><li>❌ Does not explicitly document AAA pattern</li><li>✅ Has proper Arrange step</li><li>❌ Missing proper Act step</li><li>✅ Has proper Assert step</li><li>❌ Missing proper error handling</li><li>❌ Missing proper cleanup</li>
                  </ul>
                </details>
              </td>
            </tr>
          
            <tr>
              <td>create_delete_combined.js</td>
              <td class="fail">
                65%
              </td>
              <td class="fail">
                FAILED
              </td>
              <td>
                <details>
                  <summary>View Details</summary>
                  <ul class="details-list">
                    <li>✅ Has proper documentation</li><li>✅ No hardcoded credentials</li><li>❌ Not using centralized selectors</li><li>❌ Does not explicitly document AAA pattern</li><li>✅ Has proper Arrange step</li><li>✅ Has proper Act step</li><li>❌ Missing proper Assert step</li><li>✅ Has proper error handling</li><li>✅ Has proper cleanup</li>
                  </ul>
                </details>
              </td>
            </tr>
          
            <tr>
              <td>create_delete_final.js</td>
              <td class="fail">
                65%
              </td>
              <td class="fail">
                FAILED
              </td>
              <td>
                <details>
                  <summary>View Details</summary>
                  <ul class="details-list">
                    <li>✅ Has proper documentation</li><li>✅ No hardcoded credentials</li><li>❌ Not using centralized selectors</li><li>❌ Does not explicitly document AAA pattern</li><li>✅ Has proper Arrange step</li><li>✅ Has proper Act step</li><li>❌ Missing proper Assert step</li><li>✅ Has proper error handling</li><li>✅ Has proper cleanup</li>
                  </ul>
                </details>
              </td>
            </tr>
          
            <tr>
              <td>create_delete_local.js</td>
              <td class="warning">
                85%
              </td>
              <td class="pass">
                PASSED
              </td>
              <td>
                <details>
                  <summary>View Details</summary>
                  <ul class="details-list">
                    <li>✅ Has proper documentation</li><li>✅ No hardcoded credentials</li><li>❌ Not using centralized selectors</li><li>❌ Does not explicitly document AAA pattern</li><li>✅ Has proper Arrange step</li><li>✅ Has proper Act step</li><li>✅ Has proper Assert step</li><li>✅ Has proper error handling</li><li>✅ Has proper cleanup</li>
                  </ul>
                </details>
              </td>
            </tr>
          
            <tr>
              <td>create_delete_node20.js</td>
              <td class="warning">
                80%
              </td>
              <td class="fail">
                FAILED
              </td>
              <td>
                <details>
                  <summary>View Details</summary>
                  <ul class="details-list">
                    <li>✅ Has proper documentation</li><li>✅ No hardcoded credentials</li><li>❌ Not using centralized selectors</li><li>❌ Does not explicitly document AAA pattern</li><li>✅ Has proper Arrange step</li><li>✅ Has proper Act step</li><li>✅ Has proper Assert step</li><li>✅ Has proper error handling</li><li>❌ Missing proper cleanup</li>
                  </ul>
                </details>
              </td>
            </tr>
          
            <tr>
              <td>create_delete_node20_debug.js</td>
              <td class="warning">
                80%
              </td>
              <td class="fail">
                FAILED
              </td>
              <td>
                <details>
                  <summary>View Details</summary>
                  <ul class="details-list">
                    <li>✅ Has proper documentation</li><li>✅ No hardcoded credentials</li><li>❌ Not using centralized selectors</li><li>❌ Does not explicitly document AAA pattern</li><li>✅ Has proper Arrange step</li><li>✅ Has proper Act step</li><li>✅ Has proper Assert step</li><li>✅ Has proper error handling</li><li>❌ Missing proper cleanup</li>
                  </ul>
                </details>
              </td>
            </tr>
          
            <tr>
              <td>create_delete_operations.js</td>
              <td class="pass">
                100%
              </td>
              <td class="pass">
                PASSED
              </td>
              <td>
                <details>
                  <summary>View Details</summary>
                  <ul class="details-list">
                    <li>✅ Has proper documentation</li><li>✅ No hardcoded credentials</li><li>✅ Uses centralized selectors</li><li>✅ Explicitly documents AAA pattern</li><li>✅ Has proper Arrange step</li><li>✅ Has proper Act step</li><li>✅ Has proper Assert step</li><li>✅ Has proper error handling</li><li>✅ Has proper cleanup</li>
                  </ul>
                </details>
              </td>
            </tr>
          
            <tr>
              <td>create_delete_operations_clean.js</td>
              <td class="warning">
                85%
              </td>
              <td class="fail">
                NOT RUN
              </td>
              <td>
                <details>
                  <summary>View Details</summary>
                  <ul class="details-list">
                    <li>✅ Has proper documentation</li><li>✅ No hardcoded credentials</li><li>❌ Not using centralized selectors</li><li>❌ Does not explicitly document AAA pattern</li><li>✅ Has proper Arrange step</li><li>✅ Has proper Act step</li><li>✅ Has proper Assert step</li><li>✅ Has proper error handling</li><li>✅ Has proper cleanup</li>
                  </ul>
                </details>
              </td>
            </tr>
          
            <tr>
              <td>create_delete_operations_fixed.js</td>
              <td class="pass">
                90%
              </td>
              <td class="pass">
                PASSED
              </td>
              <td>
                <details>
                  <summary>View Details</summary>
                  <ul class="details-list">
                    <li>✅ Has proper documentation</li><li>✅ No hardcoded credentials</li><li>❌ Not using centralized selectors</li><li>✅ Explicitly documents AAA pattern</li><li>✅ Has proper Arrange step</li><li>✅ Has proper Act step</li><li>✅ Has proper Assert step</li><li>✅ Has proper error handling</li><li>✅ Has proper cleanup</li>
                  </ul>
                </details>
              </td>
            </tr>
          
            <tr>
              <td>create_delete_operations_improved.js</td>
              <td class="pass">
                100%
              </td>
              <td class="pass">
                PASSED
              </td>
              <td>
                <details>
                  <summary>View Details</summary>
                  <ul class="details-list">
                    <li>✅ Has proper documentation</li><li>✅ No hardcoded credentials</li><li>✅ Uses centralized selectors</li><li>✅ Explicitly documents AAA pattern</li><li>✅ Has proper Arrange step</li><li>✅ Has proper Act step</li><li>✅ Has proper Assert step</li><li>✅ Has proper error handling</li><li>✅ Has proper cleanup</li>
                  </ul>
                </details>
              </td>
            </tr>
          
            <tr>
              <td>create_delete_screenshot.js</td>
              <td class="warning">
                85%
              </td>
              <td class="pass">
                PASSED
              </td>
              <td>
                <details>
                  <summary>View Details</summary>
                  <ul class="details-list">
                    <li>✅ Has proper documentation</li><li>✅ No hardcoded credentials</li><li>❌ Not using centralized selectors</li><li>❌ Does not explicitly document AAA pattern</li><li>✅ Has proper Arrange step</li><li>✅ Has proper Act step</li><li>✅ Has proper Assert step</li><li>✅ Has proper error handling</li><li>✅ Has proper cleanup</li>
                  </ul>
                </details>
              </td>
            </tr>
          
            <tr>
              <td>create_file_simple.js</td>
              <td class="fail">
                65%
              </td>
              <td class="fail">
                FAILED
              </td>
              <td>
                <details>
                  <summary>View Details</summary>
                  <ul class="details-list">
                    <li>✅ Has proper documentation</li><li>✅ No hardcoded credentials</li><li>❌ Not using centralized selectors</li><li>❌ Does not explicitly document AAA pattern</li><li>✅ Has proper Arrange step</li><li>✅ Has proper Act step</li><li>❌ Missing proper Assert step</li><li>✅ Has proper error handling</li><li>✅ Has proper cleanup</li>
                  </ul>
                </details>
              </td>
            </tr>
          
            <tr>
              <td>delete_file_simple.js</td>
              <td class="fail">
                65%
              </td>
              <td class="fail">
                FAILED
              </td>
              <td>
                <details>
                  <summary>View Details</summary>
                  <ul class="details-list">
                    <li>✅ Has proper documentation</li><li>✅ No hardcoded credentials</li><li>❌ Not using centralized selectors</li><li>❌ Does not explicitly document AAA pattern</li><li>✅ Has proper Arrange step</li><li>✅ Has proper Act step</li><li>❌ Missing proper Assert step</li><li>✅ Has proper error handling</li><li>✅ Has proper cleanup</li>
                  </ul>
                </details>
              </td>
            </tr>
          
            <tr>
              <td>example_structured_screenshots_debug.js</td>
              <td class="warning">
                85%
              </td>
              <td class="pass">
                PASSED
              </td>
              <td>
                <details>
                  <summary>View Details</summary>
                  <ul class="details-list">
                    <li>✅ Has proper documentation</li><li>✅ No hardcoded credentials</li><li>❌ Not using centralized selectors</li><li>❌ Does not explicitly document AAA pattern</li><li>✅ Has proper Arrange step</li><li>✅ Has proper Act step</li><li>✅ Has proper Assert step</li><li>✅ Has proper error handling</li><li>✅ Has proper cleanup</li>
                  </ul>
                </details>
              </td>
            </tr>
          
            <tr>
              <td>invalid_credentials_node20_test.js</td>
              <td class="fail">
                65%
              </td>
              <td class="fail">
                FAILED
              </td>
              <td>
                <details>
                  <summary>View Details</summary>
                  <ul class="details-list">
                    <li>✅ Has proper documentation</li><li>✅ No hardcoded credentials</li><li>❌ Not using centralized selectors</li><li>❌ Does not explicitly document AAA pattern</li><li>✅ Has proper Arrange step</li><li>✅ Has proper Act step</li><li>❌ Missing proper Assert step</li><li>✅ Has proper error handling</li><li>✅ Has proper cleanup</li>
                  </ul>
                </details>
              </td>
            </tr>
          
            <tr>
              <td>invalid_credentials_test.js</td>
              <td class="warning">
                75%
              </td>
              <td class="fail">
                FAILED
              </td>
              <td>
                <details>
                  <summary>View Details</summary>
                  <ul class="details-list">
                    <li>✅ Has proper documentation</li><li>✅ No hardcoded credentials</li><li>❌ Not using centralized selectors</li><li>✅ Explicitly documents AAA pattern</li><li>✅ Has proper Arrange step</li><li>✅ Has proper Act step</li><li>✅ Has proper Assert step</li><li>❌ Missing proper error handling</li><li>❌ Missing proper cleanup</li>
                  </ul>
                </details>
              </td>
            </tr>
          
            <tr>
              <td>invalid_login_test.js</td>
              <td class="warning">
                70%
              </td>
              <td class="fail">
                FAILED
              </td>
              <td>
                <details>
                  <summary>View Details</summary>
                  <ul class="details-list">
                    <li>✅ Has proper documentation</li><li>✅ No hardcoded credentials</li><li>❌ Not using centralized selectors</li><li>✅ Explicitly documents AAA pattern</li><li>✅ Has proper Arrange step</li><li>✅ Has proper Act step</li><li>❌ Missing proper Assert step</li><li>✅ Has proper error handling</li><li>✅ Has proper cleanup</li>
                  </ul>
                </details>
              </td>
            </tr>
          
            <tr>
              <td>login_test.js</td>
              <td class="warning">
                70%
              </td>
              <td class="fail">
                FAILED
              </td>
              <td>
                <details>
                  <summary>View Details</summary>
                  <ul class="details-list">
                    <li>✅ Has proper documentation</li><li>❌ Contains hardcoded credentials</li><li>❌ Not using centralized selectors</li><li>✅ Explicitly documents AAA pattern</li><li>✅ Has proper Arrange step</li><li>✅ Has proper Act step</li><li>✅ Has proper Assert step</li><li>❌ Missing proper error handling</li><li>❌ Missing proper cleanup</li>
                  </ul>
                </details>
              </td>
            </tr>
          
            <tr>
              <td>log_in_invalid_credentials.js</td>
              <td class="fail">
                65%
              </td>
              <td class="fail">
                FAILED
              </td>
              <td>
                <details>
                  <summary>View Details</summary>
                  <ul class="details-list">
                    <li>✅ Has proper documentation</li><li>❌ Contains hardcoded credentials</li><li>❌ Not using centralized selectors</li><li>✅ Explicitly documents AAA pattern</li><li>✅ Has proper Arrange step</li><li>✅ Has proper Act step</li><li>❌ Missing proper Assert step</li><li>✅ Has proper error handling</li><li>✅ Has proper cleanup</li>
                  </ul>
                </details>
              </td>
            </tr>
          
            <tr>
              <td>qawolf_ci_test.js</td>
              <td class="pass">
                100%
              </td>
              <td class="pass">
                PASSED
              </td>
              <td>
                <details>
                  <summary>View Details</summary>
                  <ul class="details-list">
                    <li>✅ Has proper documentation</li><li>✅ No hardcoded credentials</li><li>✅ Uses centralized selectors</li><li>✅ Explicitly documents AAA pattern</li><li>✅ Has proper Arrange step</li><li>✅ Has proper Act step</li><li>✅ Has proper Assert step</li><li>✅ Has proper error handling</li><li>✅ Has proper cleanup</li>
                  </ul>
                </details>
              </td>
            </tr>
          
            <tr>
              <td>sandbox_login_test.js</td>
              <td class="warning">
                70%
              </td>
              <td class="fail">
                FAILED
              </td>
              <td>
                <details>
                  <summary>View Details</summary>
                  <ul class="details-list">
                    <li>✅ Has proper documentation</li><li>❌ Contains hardcoded credentials</li><li>❌ Not using centralized selectors</li><li>✅ Explicitly documents AAA pattern</li><li>✅ Has proper Arrange step</li><li>✅ Has proper Act step</li><li>✅ Has proper Assert step</li><li>❌ Missing proper error handling</li><li>❌ Missing proper cleanup</li>
                  </ul>
                </details>
              </td>
            </tr>
          
            <tr>
              <td>sort_files.js</td>
              <td class="pass">
                100%
              </td>
              <td class="pass">
                PASSED
              </td>
              <td>
                <details>
                  <summary>View Details</summary>
                  <ul class="details-list">
                    <li>✅ Has proper documentation</li><li>✅ No hardcoded credentials</li><li>✅ Uses centralized selectors</li><li>✅ Explicitly documents AAA pattern</li><li>✅ Has proper Arrange step</li><li>✅ Has proper Act step</li><li>✅ Has proper Assert step</li><li>✅ Has proper error handling</li><li>✅ Has proper cleanup</li>
                  </ul>
                </details>
              </td>
            </tr>
          
            <tr>
              <td>sort_files_local.js</td>
              <td class="warning">
                85%
              </td>
              <td class="pass">
                PASSED
              </td>
              <td>
                <details>
                  <summary>View Details</summary>
                  <ul class="details-list">
                    <li>✅ Has proper documentation</li><li>✅ No hardcoded credentials</li><li>❌ Not using centralized selectors</li><li>❌ Does not explicitly document AAA pattern</li><li>✅ Has proper Arrange step</li><li>✅ Has proper Act step</li><li>✅ Has proper Assert step</li><li>✅ Has proper error handling</li><li>✅ Has proper cleanup</li>
                  </ul>
                </details>
              </td>
            </tr>
          
            <tr>
              <td>sort_files_node20.js</td>
              <td class="warning">
                80%
              </td>
              <td class="fail">
                FAILED
              </td>
              <td>
                <details>
                  <summary>View Details</summary>
                  <ul class="details-list">
                    <li>✅ Has proper documentation</li><li>✅ No hardcoded credentials</li><li>❌ Not using centralized selectors</li><li>❌ Does not explicitly document AAA pattern</li><li>✅ Has proper Arrange step</li><li>✅ Has proper Act step</li><li>✅ Has proper Assert step</li><li>✅ Has proper error handling</li><li>❌ Missing proper cleanup</li>
                  </ul>
                </details>
              </td>
            </tr>
          
      </tbody>
    </table>
  </div>
  
  <div class="recommendations">
    <h2>Recommendations</h2>
    <ul>
      
        <li>Improve AAA pattern implementation in tests</li>
        <li>Add proper assertions to verify test outcomes</li>
        <li>Implement proper error handling with try/catch blocks</li>
        <li>Use centralized selectors for better maintainability</li>
        <li>Add proper documentation to all test files</li>
      
      
      
        <li>Fix failing tests to improve execution success rate</li>
        <li>Implement more robust selectors to handle UI changes</li>
        <li>Add retry logic for flaky tests</li>
        <li>Improve error handling in test execution</li>
      
    </ul>
  </div>
  
  <footer style="margin-top: 50px; text-align: center; color: #777;">
    <p>Generated on 5/16/2025, 6:59:38 PM</p>
  </footer>
</body>
</html>
  