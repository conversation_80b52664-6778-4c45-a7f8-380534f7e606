/**
 * Test Runner
 * 
 * This script runs all of our tests and generates a report.
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  testDir: './tests',
  reportDir: './test-reports',
  simplifiedTests: [
    'tests/simple-test.spec.js',
    'tests/simplified-integration.spec.js',
    'tests/simplified-login.spec.js',
    'tests/simplified-file-operations.spec.js',
    'tests/simplified-self-healing.spec.js',
    'tests/simplified-performance.spec.js'
  ],
  fullTests: [
    'tests/integration/shared-utils.integration.spec.js',
    'tests/integration/mcp-optimizer.integration.spec.js',
    'tests/integration/self-healing.integration.spec.js',
    'tests/integration/cross-component.integration.spec.js',
    'tests/e2e/login.e2e.spec.js',
    'tests/e2e/file-operations.e2e.spec.js',
    'tests/self-healing/selector-healing.spec.js',
    'tests/self-healing/recovery-strategies.spec.js',
    'tests/performance/execution-time.spec.js',
    'tests/performance/token-usage.spec.js'
  ]
};

// Create report directory if it doesn't exist
if (!fs.existsSync(config.reportDir)) {
  fs.mkdirSync(config.reportDir, { recursive: true });
}

// Run tests
function runTests(tests, mode) {
  console.log(`Running ${mode} tests...`);
  
  const results = {
    passed: 0,
    failed: 0,
    skipped: 0,
    total: tests.length,
    tests: []
  };
  
  for (const test of tests) {
    console.log(`Running test: ${test}`);
    
    try {
      // Run the test
      const output = execSync(`npx playwright test ${test} --reporter=list`, { encoding: 'utf8' });
      
      // Parse the output to determine if the test passed
      const passed = !output.includes('failed') && !output.includes('error');
      
      // Add the result to the results object
      results.tests.push({
        name: test,
        passed,
        output
      });
      
      if (passed) {
        results.passed++;
        console.log(`✅ Test passed: ${test}`);
      } else {
        results.failed++;
        console.log(`❌ Test failed: ${test}`);
      }
    } catch (error) {
      // If the test fails, the execSync command will throw an error
      results.tests.push({
        name: test,
        passed: false,
        output: error.stdout || error.message
      });
      
      results.failed++;
      console.log(`❌ Test failed: ${test}`);
    }
  }
  
  return results;
}

// Generate report
function generateReport(simplifiedResults, fullResults) {
  const report = {
    timestamp: new Date().toISOString(),
    simplifiedTests: simplifiedResults,
    fullTests: fullResults,
    summary: {
      simplified: {
        passed: simplifiedResults.passed,
        failed: simplifiedResults.failed,
        skipped: simplifiedResults.skipped,
        total: simplifiedResults.total,
        passRate: simplifiedResults.total > 0 ? (simplifiedResults.passed / simplifiedResults.total) * 100 : 0
      },
      full: {
        passed: fullResults.passed,
        failed: fullResults.failed,
        skipped: fullResults.skipped,
        total: fullResults.total,
        passRate: fullResults.total > 0 ? (fullResults.passed / fullResults.total) * 100 : 0
      },
      overall: {
        passed: simplifiedResults.passed + fullResults.passed,
        failed: simplifiedResults.failed + fullResults.failed,
        skipped: simplifiedResults.skipped + fullResults.skipped,
        total: simplifiedResults.total + fullResults.total,
        passRate: (simplifiedResults.total + fullResults.total) > 0 ? ((simplifiedResults.passed + fullResults.passed) / (simplifiedResults.total + fullResults.total)) * 100 : 0
      }
    }
  };
  
  // Write the report to a file
  const reportPath = path.join(config.reportDir, `test-report-${new Date().toISOString().replace(/[:.]/g, '-')}.json`);
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  console.log(`Report generated: ${reportPath}`);
  
  return report;
}

// Generate HTML report
function generateHtmlReport(report) {
  const html = `
<!DOCTYPE html>
<html>
<head>
  <title>Test Report</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
    }
    
    h1, h2, h3 {
      margin-top: 0;
    }
    
    .summary {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }
    
    .summary-card {
      background-color: #f5f5f5;
      border-radius: 5px;
      padding: 15px;
      width: 30%;
    }
    
    .summary-card h3 {
      margin-top: 0;
    }
    
    .pass-rate {
      font-size: 24px;
      font-weight: bold;
    }
    
    .test-results {
      margin-top: 20px;
    }
    
    table {
      width: 100%;
      border-collapse: collapse;
    }
    
    th, td {
      padding: 8px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    
    th {
      background-color: #f2f2f2;
    }
    
    .passed {
      color: green;
    }
    
    .failed {
      color: red;
    }
  </style>
</head>
<body>
  <h1>Test Report</h1>
  <p>Generated: ${new Date(report.timestamp).toLocaleString()}</p>
  
  <div class="summary">
    <div class="summary-card">
      <h3>Simplified Tests</h3>
      <p class="pass-rate">${report.summary.simplified.passRate.toFixed(2)}%</p>
      <p>Passed: ${report.summary.simplified.passed}</p>
      <p>Failed: ${report.summary.simplified.failed}</p>
      <p>Skipped: ${report.summary.simplified.skipped}</p>
      <p>Total: ${report.summary.simplified.total}</p>
    </div>
    
    <div class="summary-card">
      <h3>Full Tests</h3>
      <p class="pass-rate">${report.summary.full.passRate.toFixed(2)}%</p>
      <p>Passed: ${report.summary.full.passed}</p>
      <p>Failed: ${report.summary.full.failed}</p>
      <p>Skipped: ${report.summary.full.skipped}</p>
      <p>Total: ${report.summary.full.total}</p>
    </div>
    
    <div class="summary-card">
      <h3>Overall</h3>
      <p class="pass-rate">${report.summary.overall.passRate.toFixed(2)}%</p>
      <p>Passed: ${report.summary.overall.passed}</p>
      <p>Failed: ${report.summary.overall.failed}</p>
      <p>Skipped: ${report.summary.overall.skipped}</p>
      <p>Total: ${report.summary.overall.total}</p>
    </div>
  </div>
  
  <div class="test-results">
    <h2>Simplified Tests</h2>
    <table>
      <tr>
        <th>Test</th>
        <th>Result</th>
      </tr>
      ${report.simplifiedTests.tests.map(test => `
        <tr>
          <td>${test.name}</td>
          <td class="${test.passed ? 'passed' : 'failed'}">${test.passed ? 'Passed' : 'Failed'}</td>
        </tr>
      `).join('')}
    </table>
  </div>
  
  <div class="test-results">
    <h2>Full Tests</h2>
    <table>
      <tr>
        <th>Test</th>
        <th>Result</th>
      </tr>
      ${report.fullTests.tests.map(test => `
        <tr>
          <td>${test.name}</td>
          <td class="${test.passed ? 'passed' : 'failed'}">${test.passed ? 'Passed' : 'Failed'}</td>
        </tr>
      `).join('')}
    </table>
  </div>
</body>
</html>
  `;
  
  // Write the HTML report to a file
  const reportPath = path.join(config.reportDir, `test-report-${new Date().toISOString().replace(/[:.]/g, '-')}.html`);
  fs.writeFileSync(reportPath, html);
  
  console.log(`HTML report generated: ${reportPath}`);
}

// Main function
async function main() {
  // Run simplified tests
  const simplifiedResults = runTests(config.simplifiedTests, 'simplified');
  
  // Run full tests
  const fullResults = runTests(config.fullTests, 'full');
  
  // Generate report
  const report = generateReport(simplifiedResults, fullResults);
  
  // Generate HTML report
  generateHtmlReport(report);
  
  // Print summary
  console.log('\nTest Summary:');
  console.log(`Simplified Tests: ${simplifiedResults.passed}/${simplifiedResults.total} passed (${(simplifiedResults.passed / simplifiedResults.total * 100).toFixed(2)}%)`);
  console.log(`Full Tests: ${fullResults.passed}/${fullResults.total} passed (${(fullResults.passed / fullResults.total * 100).toFixed(2)}%)`);
  console.log(`Overall: ${simplifiedResults.passed + fullResults.passed}/${simplifiedResults.total + fullResults.total} passed (${((simplifiedResults.passed + fullResults.passed) / (simplifiedResults.total + fullResults.total) * 100).toFixed(2)}%)`);
}

// Run the main function
main().catch(error => {
  console.error('Error running tests:', error);
  process.exit(1);
});
