{"name": "@qawolf/shared-utils", "version": "1.0.0", "description": "Shared utilities for QA Wolf testing framework", "main": "src/index.js", "scripts": {"test": "playwright test", "lint": "eslint .", "build": "mkdir -p dist && cp -r src/* dist/"}, "publishConfig": {"registry": "https://npm.pkg.github.com/"}, "repository": {"type": "git", "url": "git+https://github.com/sneezyxl/QAWolfeesojc.git", "directory": "shared-utils"}, "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "playwright", "testing", "utilities"], "author": "", "license": "MIT", "dependencies": {"date-fns": "^4.1.0", "dotenv": "^16.5.0"}, "devDependencies": {"eslint": "^9.27.0"}}