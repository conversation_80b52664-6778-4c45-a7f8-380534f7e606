/**
 * Learning Engine
 * 
 * This module provides functionality for learning from feedback data
 * and improving test strategies.
 */

const fs = require('fs');
const path = require('path');
const { createDefaultConfig, mergeConfigs } = require('./config');

/**
 * Class for learning from feedback data
 */
class LearningEngine {
  /**
   * Create a new LearningEngine
   * @param {Object} [config] - Configuration options
   */
  constructor(config = {}) {
    this.config = mergeConfigs(createDefaultConfig().learning, config);
    this.learningData = {
      selectorPatterns: {},
      recoveryPatterns: {},
      errorPatterns: {},
      performancePatterns: {},
      improvements: []
    };
    this.testRunCount = 0;
    this.initialized = false;
    this.initializationPromise = null;
  }
  
  /**
   * Initialize the learning engine
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.initialized) {
      return;
    }
    
    if (this.initializationPromise) {
      return this.initializationPromise;
    }
    
    this.initializationPromise = (async () => {
      if (this.config.persistLearning) {
        try {
          await this.loadLearning();
        } catch (error) {
          console.warn('Failed to load learning data:', error);
        }
      }
      
      this.initialized = true;
    })();
    
    return this.initializationPromise;
  }
  
  /**
   * Load learning data from file
   * @returns {Promise<void>}
   */
  async loadLearning() {
    try {
      if (fs.existsSync(this.config.learningPath)) {
        const learningData = await fs.promises.readFile(this.config.learningPath, 'utf8');
        this.learningData = JSON.parse(learningData);
      }
    } catch (error) {
      console.error('Failed to load learning data:', error);
      this.learningData = {
        selectorPatterns: {},
        recoveryPatterns: {},
        errorPatterns: {},
        performancePatterns: {},
        improvements: []
      };
    }
  }
  
  /**
   * Save learning data to file
   * @returns {Promise<void>}
   */
  async saveLearning() {
    if (!this.config.persistLearning) {
      return;
    }
    
    try {
      const learningDir = path.dirname(this.config.learningPath);
      if (!fs.existsSync(learningDir)) {
        await fs.promises.mkdir(learningDir, { recursive: true });
      }
      
      await fs.promises.writeFile(
        this.config.learningPath,
        JSON.stringify(this.learningData, null, 2),
        'utf8'
      );
    } catch (error) {
      console.error('Failed to save learning data:', error);
    }
  }
  
  /**
   * Learn from feedback data
   * @param {Object} feedbackData - Feedback data
   * @returns {Promise<Object>} - Learning results
   */
  async learn(feedbackData) {
    await this.initialize();
    
    if (!this.config.enabled) {
      return { learned: false };
    }
    
    // Increment test run count
    this.testRunCount++;
    
    // Learn from selectors
    await this.learnFromSelectors(feedbackData.selectors);
    
    // Learn from recoveries
    await this.learnFromRecoveries(feedbackData.recoveries);
    
    // Learn from errors
    await this.learnFromErrors(feedbackData.testRuns);
    
    // Learn from performance
    await this.learnFromPerformance(feedbackData.performance);
    
    // Generate improvements
    const improvements = await this.generateImprovements(feedbackData);
    
    // Add improvements to learning data
    for (const improvement of improvements) {
      if (!this.learningData.improvements.some(imp => imp.description === improvement.description)) {
        this.learningData.improvements.push(improvement);
      }
    }
    
    // Save learning data
    await this.saveLearning();
    
    // Auto-optimize if enabled
    if (this.config.autoOptimize && this.testRunCount % this.config.optimizationInterval === 0) {
      await this.optimizeStrategies();
    }
    
    return {
      learned: true,
      improvements,
      testRunCount: this.testRunCount
    };
  }
  
  /**
   * Learn from selector data
   * @param {Object} selectors - Selector data
   * @returns {Promise<void>}
   */
  async learnFromSelectors(selectors) {
    // Analyze selector patterns
    for (const [selector, data] of Object.entries(selectors)) {
      // Skip selectors with few attempts
      if (data.attempts < 5) {
        continue;
      }
      
      // Analyze selector type
      const selectorType = this.getSelectorType(selector);
      
      // Update selector type statistics
      if (!this.learningData.selectorPatterns[selectorType]) {
        this.learningData.selectorPatterns[selectorType] = {
          type: selectorType,
          selectors: [],
          attempts: 0,
          successes: 0,
          failures: 0,
          successRate: 0
        };
      }
      
      const pattern = this.learningData.selectorPatterns[selectorType];
      
      // Add selector if not already in the list
      if (!pattern.selectors.includes(selector)) {
        pattern.selectors.push(selector);
      }
      
      // Update statistics
      pattern.attempts += data.attempts;
      pattern.successes += data.successes;
      pattern.failures += data.failures;
      pattern.successRate = pattern.successes / pattern.attempts;
    }
  }
  
  /**
   * Get the type of a selector
   * @param {string} selector - The selector
   * @returns {string} - Selector type
   */
  getSelectorType(selector) {
    if (selector.startsWith('#')) {
      return 'id';
    } else if (selector.startsWith('.')) {
      return 'class';
    } else if (selector.startsWith('[data-testid')) {
      return 'data-testid';
    } else if (selector.startsWith('[data-test-id')) {
      return 'data-test-id';
    } else if (selector.startsWith('[data-test')) {
      return 'data-test';
    } else if (selector.startsWith('[aria-')) {
      return 'aria';
    } else if (selector.startsWith(':text(')) {
      return 'text';
    } else if (selector.startsWith('//')) {
      return 'xpath';
    } else if (selector.includes('[') && selector.includes(']')) {
      return 'attribute';
    } else {
      return 'tag';
    }
  }
  
  /**
   * Learn from recovery data
   * @param {Object} recoveries - Recovery data
   * @returns {Promise<void>}
   */
  async learnFromRecoveries(recoveries) {
    // Analyze recovery patterns
    for (const [strategy, data] of Object.entries(recoveries)) {
      // Skip strategies with few attempts
      if (data.attempts < 5) {
        continue;
      }
      
      // Update recovery pattern
      if (!this.learningData.recoveryPatterns[strategy]) {
        this.learningData.recoveryPatterns[strategy] = {
          strategy,
          attempts: 0,
          successes: 0,
          failures: 0,
          successRate: 0,
          effectiveness: 0
        };
      }
      
      const pattern = this.learningData.recoveryPatterns[strategy];
      
      // Update statistics
      pattern.attempts += data.attempts;
      pattern.successes += data.successes;
      pattern.failures += data.failures;
      pattern.successRate = pattern.successes / pattern.attempts;
      
      // Calculate effectiveness (success rate weighted by number of attempts)
      pattern.effectiveness = pattern.successRate * Math.min(1, pattern.attempts / 20);
    }
  }
  
  /**
   * Learn from error data
   * @param {Array<Object>} testRuns - Test run data
   * @returns {Promise<void>}
   */
  async learnFromErrors(testRuns) {
    // Analyze error patterns
    for (const testRun of testRuns) {
      if (!testRun.error) {
        continue;
      }
      
      const errorName = testRun.error.name || 'Unknown';
      const errorMessage = testRun.error.message || '';
      
      // Create a simplified error key
      const errorKey = this.simplifyErrorMessage(errorName, errorMessage);
      
      // Update error pattern
      if (!this.learningData.errorPatterns[errorKey]) {
        this.learningData.errorPatterns[errorKey] = {
          name: errorName,
          message: errorMessage,
          key: errorKey,
          occurrences: 0,
          testRuns: []
        };
      }
      
      const pattern = this.learningData.errorPatterns[errorKey];
      
      // Update statistics
      pattern.occurrences++;
      
      // Add test run if not already in the list
      if (!pattern.testRuns.some(run => run.testId === testRun.testId)) {
        pattern.testRuns.push({
          testId: testRun.testId,
          testName: testRun.testName,
          timestamp: testRun.endTime
        });
      }
    }
  }
  
  /**
   * Simplify an error message for pattern matching
   * @param {string} errorName - Error name
   * @param {string} errorMessage - Error message
   * @returns {string} - Simplified error key
   */
  simplifyErrorMessage(errorName, errorMessage) {
    // Remove specific details like line numbers, timestamps, etc.
    let simplified = errorMessage
      .replace(/\d+/g, 'N') // Replace numbers with 'N'
      .replace(/(['"]).*?\1/g, 'STR') // Replace strings with 'STR'
      .replace(/\s+/g, ' ') // Normalize whitespace
      .trim();
    
    // Truncate if too long
    if (simplified.length > 100) {
      simplified = simplified.substring(0, 100) + '...';
    }
    
    return `${errorName}:${simplified}`;
  }
  
  /**
   * Learn from performance data
   * @param {Object} performance - Performance data
   * @returns {Promise<void>}
   */
  async learnFromPerformance(performance) {
    // Analyze performance patterns
    for (const [metric, data] of Object.entries(performance)) {
      // Skip metrics with few values
      if (data.values.length < 5) {
        continue;
      }
      
      // Update performance pattern
      if (!this.learningData.performancePatterns[metric]) {
        this.learningData.performancePatterns[metric] = {
          metric,
          values: [],
          average: 0,
          stdDev: 0,
          min: data.min,
          max: data.max,
          trend: 'stable'
        };
      }
      
      const pattern = this.learningData.performancePatterns[metric];
      
      // Update statistics
      pattern.values = data.values.slice(-20); // Keep only the most recent values
      pattern.average = data.average;
      pattern.min = data.min;
      pattern.max = data.max;
      
      // Calculate standard deviation
      const values = pattern.values.map(v => v.value);
      pattern.stdDev = Math.sqrt(values.reduce((sum, val) => sum + Math.pow(val - pattern.average, 2), 0) / values.length);
      
      // Calculate trend
      if (pattern.values.length >= 10) {
        const recentValues = pattern.values.slice(-5).map(v => v.value);
        const olderValues = pattern.values.slice(-10, -5).map(v => v.value);
        
        const recentAvg = recentValues.reduce((sum, val) => sum + val, 0) / recentValues.length;
        const olderAvg = olderValues.reduce((sum, val) => sum + val, 0) / olderValues.length;
        
        if (recentAvg < olderAvg * 0.9) {
          pattern.trend = 'improving';
        } else if (recentAvg > olderAvg * 1.1) {
          pattern.trend = 'declining';
        } else {
          pattern.trend = 'stable';
        }
      }
    }
  }
  
  /**
   * Generate improvements based on learning data
   * @param {Object} feedbackData - Feedback data
   * @returns {Promise<Array<Object>>} - Improvements
   */
  async generateImprovements(feedbackData) {
    const improvements = [];
    
    // Generate selector improvements
    const selectorImprovements = await this.generateSelectorImprovements(feedbackData.selectors);
    improvements.push(...selectorImprovements);
    
    // Generate recovery improvements
    const recoveryImprovements = await this.generateRecoveryImprovements(feedbackData.recoveries);
    improvements.push(...recoveryImprovements);
    
    // Generate error improvements
    const errorImprovements = await this.generateErrorImprovements(feedbackData.testRuns);
    improvements.push(...errorImprovements);
    
    // Generate performance improvements
    const performanceImprovements = await this.generatePerformanceImprovements(feedbackData.performance);
    improvements.push(...performanceImprovements);
    
    return improvements;
  }
  
  /**
   * Generate selector improvements
   * @param {Object} selectors - Selector data
   * @returns {Promise<Array<Object>>} - Improvements
   */
  async generateSelectorImprovements(selectors) {
    const improvements = [];
    
    // Find problematic selectors
    const problematicSelectors = Object.entries(selectors)
      .filter(([_, data]) => data.attempts >= 5 && data.successRate < 0.7)
      .sort(([_, a], [__, b]) => a.successRate - b.successRate)
      .slice(0, 3);
    
    for (const [selector, data] of problematicSelectors) {
      // Determine the best selector type based on learning data
      const bestType = Object.entries(this.learningData.selectorPatterns)
        .filter(([_, pattern]) => pattern.attempts >= 10)
        .sort(([_, a], [__, b]) => b.successRate - a.successRate)[0]?.[0] || 'data-testid';
      
      // Generate improvement suggestion
      improvements.push({
        type: 'selector',
        priority: 'high',
        description: `Improve reliability of selector: ${selector}`,
        details: `This selector has a low success rate of ${Math.round(data.successRate * 100)}% after ${data.attempts} attempts.`,
        suggestion: `Consider using a ${bestType} selector instead, as this type has shown higher reliability in your tests.`,
        timestamp: new Date().toISOString()
      });
    }
    
    return improvements;
  }
  
  /**
   * Generate recovery improvements
   * @param {Object} recoveries - Recovery data
   * @returns {Promise<Array<Object>>} - Improvements
   */
  async generateRecoveryImprovements(recoveries) {
    const improvements = [];
    
    // Find ineffective strategies
    const ineffectiveStrategies = Object.entries(recoveries)
      .filter(([_, data]) => data.attempts >= 5 && data.successRate < 0.3)
      .sort(([_, a], [__, b]) => a.successRate - b.successRate)
      .slice(0, 2);
    
    for (const [strategy, data] of ineffectiveStrategies) {
      // Generate improvement suggestion
      improvements.push({
        type: 'recovery',
        priority: 'medium',
        description: `Improve or disable recovery strategy: ${strategy}`,
        details: `This recovery strategy has a low success rate of ${Math.round(data.successRate * 100)}% after ${data.attempts} attempts.`,
        suggestion: 'Consider improving the strategy implementation or disabling it if it is not effective.',
        timestamp: new Date().toISOString()
      });
    }
    
    // Find most effective strategies
    const effectiveStrategies = Object.entries(recoveries)
      .filter(([_, data]) => data.attempts >= 5 && data.successRate > 0.7)
      .sort(([_, a], [__, b]) => b.successRate - a.successRate)
      .slice(0, 1);
    
    for (const [strategy, data] of effectiveStrategies) {
      // Generate improvement suggestion
      improvements.push({
        type: 'recovery',
        priority: 'low',
        description: `Prioritize effective recovery strategy: ${strategy}`,
        details: `This recovery strategy has a high success rate of ${Math.round(data.successRate * 100)}% after ${data.attempts} attempts.`,
        suggestion: 'Consider prioritizing this strategy in your recovery configuration.',
        timestamp: new Date().toISOString()
      });
    }
    
    return improvements;
  }
  
  /**
   * Generate error improvements
   * @param {Array<Object>} testRuns - Test run data
   * @returns {Promise<Array<Object>>} - Improvements
   */
  async generateErrorImprovements(testRuns) {
    const improvements = [];
    
    // Find common errors
    const errorCounts = {};
    
    for (const testRun of testRuns) {
      if (!testRun.error) {
        continue;
      }
      
      const errorName = testRun.error.name || 'Unknown';
      const errorMessage = testRun.error.message || '';
      const errorKey = this.simplifyErrorMessage(errorName, errorMessage);
      
      errorCounts[errorKey] = (errorCounts[errorKey] || 0) + 1;
    }
    
    // Sort errors by frequency
    const commonErrors = Object.entries(errorCounts)
      .sort(([_, a], [__, b]) => b - a)
      .slice(0, 2);
    
    for (const [errorKey, count] of commonErrors) {
      const errorPattern = this.learningData.errorPatterns[errorKey];
      
      if (!errorPattern) {
        continue;
      }
      
      // Generate improvement suggestion
      improvements.push({
        type: 'error',
        priority: 'high',
        description: `Address common error: ${errorPattern.name}`,
        details: `This error has occurred ${count} times in your tests.`,
        suggestion: 'Investigate the root cause of this error and implement a more robust handling strategy.',
        timestamp: new Date().toISOString()
      });
    }
    
    return improvements;
  }
  
  /**
   * Generate performance improvements
   * @param {Object} performance - Performance data
   * @returns {Promise<Array<Object>>} - Improvements
   */
  async generatePerformanceImprovements(performance) {
    const improvements = [];
    
    // Find slow metrics
    const slowMetrics = Object.entries(performance)
      .filter(([_, data]) => data.values.length >= 5)
      .map(([metric, data]) => ({
        metric,
        average: data.average,
        max: data.max,
        ratio: data.max / data.average
      }))
      .filter(data => data.ratio > 2)
      .sort((a, b) => b.ratio - a.ratio)
      .slice(0, 2);
    
    for (const data of slowMetrics) {
      // Generate improvement suggestion
      improvements.push({
        type: 'performance',
        priority: 'medium',
        description: `Optimize performance for metric: ${data.metric}`,
        details: `This metric has a maximum value of ${Math.round(data.max)} which is ${Math.round(data.ratio * 100) / 100}x higher than the average of ${Math.round(data.average)}.`,
        suggestion: 'Look for outliers and optimize the performance of this operation.',
        timestamp: new Date().toISOString()
      });
    }
    
    return improvements;
  }
  
  /**
   * Optimize strategies based on learning data
   * @returns {Promise<Object>} - Optimization results
   */
  async optimizeStrategies() {
    await this.initialize();
    
    if (!this.config.enabled || !this.config.autoOptimize) {
      return { optimized: false };
    }
    
    const optimizations = {
      selectors: await this.optimizeSelectorStrategies(),
      recoveries: await this.optimizeRecoveryStrategies(),
      timestamp: new Date().toISOString()
    };
    
    return {
      optimized: true,
      ...optimizations
    };
  }
  
  /**
   * Optimize selector strategies
   * @returns {Promise<Object>} - Optimization results
   */
  async optimizeSelectorStrategies() {
    // Find the best selector types
    const selectorTypes = Object.entries(this.learningData.selectorPatterns)
      .filter(([_, pattern]) => pattern.attempts >= 10)
      .sort(([_, a], [__, b]) => b.successRate - a.successRate)
      .map(([type, pattern]) => ({
        type,
        successRate: pattern.successRate,
        attempts: pattern.attempts
      }));
    
    return {
      bestTypes: selectorTypes.slice(0, 3),
      worstTypes: selectorTypes.slice(-3).reverse()
    };
  }
  
  /**
   * Optimize recovery strategies
   * @returns {Promise<Object>} - Optimization results
   */
  async optimizeRecoveryStrategies() {
    // Find the best recovery strategies
    const recoveryStrategies = Object.entries(this.learningData.recoveryPatterns)
      .filter(([_, pattern]) => pattern.attempts >= 5)
      .sort(([_, a], [__, b]) => b.effectiveness - a.effectiveness)
      .map(([strategy, pattern]) => ({
        strategy,
        successRate: pattern.successRate,
        effectiveness: pattern.effectiveness,
        attempts: pattern.attempts
      }));
    
    return {
      bestStrategies: recoveryStrategies.slice(0, 3),
      worstStrategies: recoveryStrategies.slice(-3).reverse()
    };
  }
  
  /**
   * Suggest improvements for a test script
   * @param {string} testPath - Path to the test script
   * @returns {Promise<Array<Object>>} - Improvement suggestions
   */
  async suggestImprovements(testPath) {
    await this.initialize();
    
    if (!this.config.enabled || !this.config.suggestImprovements) {
      return [];
    }
    
    // Extract test ID from path
    const testId = path.basename(testPath, path.extname(testPath));
    
    // Find improvements for this test
    const testImprovements = this.learningData.improvements
      .filter(improvement => {
        // Check if the improvement is relevant to this test
        if (improvement.testId && improvement.testId !== testId) {
          return false;
        }
        
        // Only include recent improvements (last 30 days)
        const timestamp = new Date(improvement.timestamp);
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        
        return timestamp >= thirtyDaysAgo;
      })
      .sort((a, b) => {
        // Sort by priority (high, medium, low)
        const priorityOrder = { high: 0, medium: 1, low: 2 };
        return priorityOrder[a.priority] - priorityOrder[b.priority];
      });
    
    return testImprovements;
  }
  
  /**
   * Export learning data
   * @returns {Promise<Object>} - Learning data
   */
  async exportLearning() {
    await this.initialize();
    
    return { ...this.learningData };
  }
  
  /**
   * Import learning data
   * @param {Object} data - Learning data
   * @returns {Promise<void>}
   */
  async importLearning(data) {
    await this.initialize();
    
    if (!data) {
      throw new Error('Learning data is required');
    }
    
    this.learningData = { ...data };
    
    await this.saveLearning();
  }
  
  /**
   * Clean up resources
   * @returns {Promise<void>}
   */
  async cleanup() {
    if (this.config.persistLearning) {
      await this.saveLearning();
    }
  }
}

module.exports = LearningEngine;