/**
 * QA Wolf Report Generation Script
 * 
 * This script generates a report of QA Wolf test results.
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  apiKey: process.env.QA_WOLF_API_KEY,
  teamId: process.env.QA_WOLF_TEAM_ID || 'clux0gjs50sb3ak01fnh7wvja',
  apiUrl: 'https://app.qawolf.com/api/ci',
  reportsDir: './qawolf-reports'
};

// Load deployment info
let deploymentInfo;
try {
  deploymentInfo = JSON.parse(
    fs.readFileSync(path.join(config.reportsDir, 'deployment-info.json'), 'utf8')
  );
} catch (error) {
  console.error('Error loading deployment info:', error.message);
  process.exit(1);
}

// Load greenlight result
let greenlightResult;
try {
  greenlightResult = JSON.parse(
    fs.readFileSync(path.join(config.reportsDir, 'greenlight-result.json'), 'utf8')
  );
} catch (error) {
  console.warn('Warning: Greenlight result not found. Report may be incomplete.');
  greenlightResult = null;
}

// Generate report
async function generateReport() {
  try {
    console.log('Generating QA Wolf test report...');
    
    // Fetch test results
    const response = await axios.get(
      `${config.apiUrl}/results?teamId=${config.teamId}&environment=${deploymentInfo.environment}&branch=${deploymentInfo.branch}`,
      {
        headers: {
          'Authorization': `Bearer ${config.apiKey}`
        }
      }
    );
    
    const testResults = response.data;
    
    console.log(`Fetched ${testResults.length} test results.`);
    
    // Save raw test results to file
    fs.writeFileSync(
      path.join(config.reportsDir, 'test-results.json'),
      JSON.stringify(testResults, null, 2)
    );
    
    // Calculate metrics
    const metrics = calculateMetrics(testResults);
    
    // Save metrics to file
    fs.writeFileSync(
      path.join(config.reportsDir, 'metrics.json'),
      JSON.stringify(metrics, null, 2)
    );
    
    // Generate HTML report
    const htmlReport = generateHtmlReport(testResults, metrics, greenlightResult);
    
    // Save HTML report to file
    fs.writeFileSync(
      path.join(config.reportsDir, 'report.html'),
      htmlReport
    );
    
    console.log('QA Wolf test report generated successfully!');
    console.log(`Report saved to: ${path.join(config.reportsDir, 'report.html')}`);
    
    return { testResults, metrics };
  } catch (error) {
    console.error('Error generating QA Wolf test report:', error.message);
    
    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
    }
    
    // Save error to file
    fs.writeFileSync(
      path.join(config.reportsDir, 'report-error.json'),
      JSON.stringify({
        error: error.message,
        response: error.response ? {
          data: error.response.data,
          status: error.response.status
        } : null,
        timestamp: new Date().toISOString()
      }, null, 2)
    );
    
    process.exit(1);
  }
}

// Calculate metrics from test results
function calculateMetrics(testResults) {
  const totalTests = testResults.length;
  const passedTests = testResults.filter(test => test.status === 'passed').length;
  const failedTests = testResults.filter(test => test.status === 'failed').length;
  const skippedTests = testResults.filter(test => test.status === 'skipped').length;
  
  const passRate = totalTests > 0 ? (passedTests / totalTests) * 100 : 0;
  
  // Calculate average duration
  const durations = testResults
    .filter(test => test.duration)
    .map(test => test.duration);
  
  const averageDuration = durations.length > 0
    ? durations.reduce((sum, duration) => sum + duration, 0) / durations.length
    : 0;
  
  return {
    totalTests,
    passedTests,
    failedTests,
    skippedTests,
    passRate,
    averageDuration,
    timestamp: new Date().toISOString()
  };
}

// Generate HTML report
function generateHtmlReport(testResults, metrics, greenlightResult) {
  const passRateColor = metrics.passRate >= 90 ? 'green' : metrics.passRate >= 70 ? 'orange' : 'red';
  
  const testRows = testResults.map(test => {
    const statusColor = test.status === 'passed' ? 'green' : test.status === 'skipped' ? 'orange' : 'red';
    
    return `
      <tr>
        <td>${test.name || 'Unnamed Test'}</td>
        <td style="color: ${statusColor};">${test.status}</td>
        <td>${test.duration ? `${(test.duration / 1000).toFixed(2)}s` : 'N/A'}</td>
        <td>${test.errorMessage || ''}</td>
      </tr>
    `;
  }).join('');
  
  return `
    <!DOCTYPE html>
    <html>
    <head>
      <title>QA Wolf Test Report</title>
      <style>
        body {
          font-family: Arial, sans-serif;
          margin: 0;
          padding: 20px;
          color: #333;
        }
        
        h1, h2, h3 {
          color: #2c3e50;
        }
        
        .summary {
          background-color: #f8f9fa;
          border-radius: 5px;
          padding: 15px;
          margin-bottom: 20px;
        }
        
        .metrics {
          display: flex;
          flex-wrap: wrap;
          gap: 20px;
          margin-bottom: 20px;
        }
        
        .metric {
          background-color: #fff;
          border-radius: 5px;
          padding: 15px;
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
          flex: 1;
          min-width: 200px;
        }
        
        .metric h3 {
          margin-top: 0;
          color: #2c3e50;
        }
        
        .metric .value {
          font-size: 24px;
          font-weight: bold;
          margin: 10px 0;
        }
        
        .pass-rate {
          color: ${passRateColor};
        }
        
        .greenlight {
          font-size: 18px;
          font-weight: bold;
          padding: 10px;
          border-radius: 5px;
          margin-bottom: 20px;
          text-align: center;
        }
        
        .greenlight.passed {
          background-color: #d4edda;
          color: #155724;
        }
        
        .greenlight.failed {
          background-color: #f8d7da;
          color: #721c24;
        }
        
        table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 20px;
        }
        
        th, td {
          padding: 12px 15px;
          text-align: left;
          border-bottom: 1px solid #ddd;
        }
        
        th {
          background-color: #f8f9fa;
          font-weight: bold;
        }
        
        tr:hover {
          background-color: #f8f9fa;
        }
        
        .footer {
          margin-top: 30px;
          text-align: center;
          color: #6c757d;
          font-size: 14px;
        }
      </style>
    </head>
    <body>
      <h1>QA Wolf Test Report</h1>
      
      <div class="summary">
        <p><strong>Environment:</strong> ${deploymentInfo.environment}</p>
        <p><strong>Branch:</strong> ${deploymentInfo.branch}</p>
        <p><strong>Commit:</strong> ${deploymentInfo.commit}</p>
        <p><strong>Generated:</strong> ${new Date().toLocaleString()}</p>
      </div>
      
      ${greenlightResult ? `
        <div class="greenlight ${greenlightResult.greenlight ? 'passed' : 'failed'}">
          CI Greenlight: ${greenlightResult.greenlight ? 'PASSED' : 'FAILED'}
        </div>
      ` : ''}
      
      <h2>Metrics</h2>
      
      <div class="metrics">
        <div class="metric">
          <h3>Total Tests</h3>
          <div class="value">${metrics.totalTests}</div>
        </div>
        
        <div class="metric">
          <h3>Passed Tests</h3>
          <div class="value" style="color: green;">${metrics.passedTests}</div>
        </div>
        
        <div class="metric">
          <h3>Failed Tests</h3>
          <div class="value" style="color: red;">${metrics.failedTests}</div>
        </div>
        
        <div class="metric">
          <h3>Skipped Tests</h3>
          <div class="value" style="color: orange;">${metrics.skippedTests}</div>
        </div>
        
        <div class="metric">
          <h3>Pass Rate</h3>
          <div class="value pass-rate">${metrics.passRate.toFixed(2)}%</div>
        </div>
        
        <div class="metric">
          <h3>Average Duration</h3>
          <div class="value">${(metrics.averageDuration / 1000).toFixed(2)}s</div>
        </div>
      </div>
      
      <h2>Test Results</h2>
      
      <table>
        <thead>
          <tr>
            <th>Test Name</th>
            <th>Status</th>
            <th>Duration</th>
            <th>Error Message</th>
          </tr>
        </thead>
        <tbody>
          ${testRows}
        </tbody>
      </table>
      
      <div class="footer">
        <p>Generated by QA Wolf CI Integration - ${new Date().getFullYear()}</p>
      </div>
    </body>
    </html>
  `;
}

// Run the report generation
generateReport();
