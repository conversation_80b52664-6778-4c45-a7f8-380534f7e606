/**
 * Report exporters for reporting
 */

import * as fs from 'fs';
import * as path from 'path';
import { ReportFormat, ReportExporterOptions } from './types';
import { EventBus, EventType } from '@qawolf/core';

/**
 * Report exporters
 */
export class ReportExporters {
  /**
   * Exporters
   */
  private exporters: Map<ReportFormat, ReportExporterOptions>;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   */
  constructor() {
    this.exporters = new Map<ReportFormat, ReportExporterOptions>();
    this.eventBus = EventBus.getInstance();
    
    // Register default exporters
    this.registerDefaultExporters();
  }
  
  /**
   * Register exporter
   * @param options Exporter options
   * @returns This instance for chaining
   */
  registerExporter(options: ReportExporterOptions): ReportExporters {
    this.exporters.set(options.format, options);
    
    // Emit event
    this.eventBus.emit(EventType.REPORT_EXPORTERS_REGISTER_EXPORTER, {
      exporter: options
    });
    
    return this;
  }
  
  /**
   * Get exporter
   * @param format Format
   * @returns Exporter options
   */
  getExporter(format: ReportFormat): ReportExporterOptions | undefined {
    return this.exporters.get(format);
  }
  
  /**
   * Get exporters
   * @returns Exporters
   */
  getExporters(): Map<ReportFormat, ReportExporterOptions> {
    return this.exporters;
  }
  
  /**
   * Export content
   * @param content Content
   * @param format Format
   * @param outputDir Output directory
   * @param filename Filename
   * @returns File path
   */
  async export(content: string, format: ReportFormat, outputDir: string, filename: string): Promise<string> {
    try {
      const exporter = this.exporters.get(format);
      
      if (!exporter) {
        throw new Error(`Exporter for format ${format} not found`);
      }
      
      // Create output directory if it doesn't exist
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
      }
      
      // Get file extension
      const extension = this.getFileExtension(format);
      
      // Get file path
      const filePath = path.join(outputDir, `${filename}.${extension}`);
      
      // Write content to file
      fs.writeFileSync(filePath, content);
      
      // Emit event
      this.eventBus.emit(EventType.REPORT_EXPORTERS_EXPORT, {
        content,
        format,
        outputDir,
        filename,
        filePath
      });
      
      return filePath;
    } catch (error) {
      this.handleError(error, `Failed to export content to ${format}`);
      return '';
    }
  }
  
  /**
   * Get file extension
   * @param format Format
   * @returns File extension
   */
  private getFileExtension(format: ReportFormat): string {
    switch (format) {
      case ReportFormat.HTML:
        return 'html';
      case ReportFormat.MARKDOWN:
        return 'md';
      case ReportFormat.PDF:
        return 'pdf';
      case ReportFormat.JSON:
        return 'json';
      case ReportFormat.XML:
        return 'xml';
      case ReportFormat.CSV:
        return 'csv';
      default:
        return 'txt';
    }
  }
  
  /**
   * Register default exporters
   */
  private registerDefaultExporters(): void {
    // Register HTML exporter
    this.registerExporter({
      name: 'html',
      description: 'HTML exporter',
      format: ReportFormat.HTML,
      options: {}
    });
    
    // Register Markdown exporter
    this.registerExporter({
      name: 'markdown',
      description: 'Markdown exporter',
      format: ReportFormat.MARKDOWN,
      options: {}
    });
    
    // Register PDF exporter
    this.registerExporter({
      name: 'pdf',
      description: 'PDF exporter',
      format: ReportFormat.PDF,
      options: {}
    });
    
    // Register JSON exporter
    this.registerExporter({
      name: 'json',
      description: 'JSON exporter',
      format: ReportFormat.JSON,
      options: {}
    });
    
    // Register XML exporter
    this.registerExporter({
      name: 'xml',
      description: 'XML exporter',
      format: ReportFormat.XML,
      options: {}
    });
    
    // Register CSV exporter
    this.registerExporter({
      name: 'csv',
      description: 'CSV exporter',
      format: ReportFormat.CSV,
      options: {}
    });
  }
  
  /**
   * Handle error
   * @param error Error
   * @param message Error message
   */
  private handleError(error: any, message: string): void {
    console.error(message, error);
    
    // Emit event
    this.eventBus.emit(EventType.REPORT_EXPORTERS_ERROR, {
      error,
      message
    });
  }
}

/**
 * Create report exporters
 * @returns Report exporters
 */
export function createReportExporters(): ReportExporters {
  return new ReportExporters();
}
