[{"file": "action_library_example.js", "hasArrange": true, "hasAct": true, "hasAssert": true, "usesSelectors": true, "errorHandling": true, "cleanup": true, "documentation": true, "noHardcodedCredentials": true, "usesAAA": true, "totalScore": 100, "details": ["✅ Has proper documentation", "✅ No hardcoded credentials", "✅ Uses centralized selectors", "✅ Explicitly documents AAA pattern", "✅ Has proper Arrange step", "✅ Has proper Act step", "✅ Has proper Assert step", "✅ Has proper error handling", "✅ Has proper cleanup"]}, {"file": "airbnb_login_test.js", "hasArrange": true, "hasAct": true, "hasAssert": true, "usesSelectors": false, "errorHandling": false, "cleanup": false, "documentation": true, "noHardcodedCredentials": false, "usesAAA": true, "totalScore": 70, "details": ["✅ Has proper documentation", "❌ Contains hardcoded credentials", "❌ Not using centralized selectors", "✅ Explicitly documents AAA pattern", "✅ Has proper Arrange step", "✅ Has proper Act step", "✅ Has proper Assert step", "❌ Missing proper error handling", "❌ Missing proper cleanup"]}, {"file": "chaos_test_example.js", "hasArrange": true, "hasAct": true, "hasAssert": true, "usesSelectors": true, "errorHandling": true, "cleanup": true, "documentation": true, "noHardcodedCredentials": true, "usesAAA": true, "totalScore": 100, "details": ["✅ Has proper documentation", "✅ No hardcoded credentials", "✅ Uses centralized selectors", "✅ Explicitly documents AAA pattern", "✅ Has proper Arrange step", "✅ Has proper Act step", "✅ Has proper Assert step", "✅ Has proper error handling", "✅ Has proper cleanup"]}, {"file": "collision_test_1.js", "hasArrange": true, "hasAct": true, "hasAssert": true, "usesSelectors": true, "errorHandling": true, "cleanup": true, "documentation": true, "noHardcodedCredentials": true, "usesAAA": true, "totalScore": 100, "details": ["✅ Has proper documentation", "✅ No hardcoded credentials", "✅ Uses centralized selectors", "✅ Explicitly documents AAA pattern", "✅ Has proper Arrange step", "✅ Has proper Act step", "✅ Has proper Assert step", "✅ Has proper error handling", "✅ Has proper cleanup"]}, {"file": "collision_test_2.js", "hasArrange": true, "hasAct": true, "hasAssert": true, "usesSelectors": true, "errorHandling": true, "cleanup": true, "documentation": true, "noHardcodedCredentials": true, "usesAAA": true, "totalScore": 100, "details": ["✅ Has proper documentation", "✅ No hardcoded credentials", "✅ Uses centralized selectors", "✅ Explicitly documents AAA pattern", "✅ Has proper Arrange step", "✅ Has proper Act step", "✅ Has proper Assert step", "✅ Has proper error handling", "✅ Has proper cleanup"]}, {"file": "create_delete_combined.js", "hasArrange": true, "hasAct": true, "hasAssert": true, "usesSelectors": true, "errorHandling": true, "cleanup": true, "documentation": true, "noHardcodedCredentials": true, "usesAAA": true, "totalScore": 100, "details": ["✅ Has proper documentation", "✅ No hardcoded credentials", "✅ Uses centralized selectors", "✅ Explicitly documents AAA pattern", "✅ Has proper Arrange step", "✅ Has proper Act step", "✅ Has proper Assert step", "✅ Has proper error handling", "✅ Has proper cleanup"]}, {"file": "create_delete_final.js", "hasArrange": true, "hasAct": true, "hasAssert": true, "usesSelectors": true, "errorHandling": true, "cleanup": true, "documentation": true, "noHardcodedCredentials": true, "usesAAA": true, "totalScore": 100, "details": ["✅ Has proper documentation", "✅ No hardcoded credentials", "✅ Uses centralized selectors", "✅ Explicitly documents AAA pattern", "✅ Has proper Arrange step", "✅ Has proper Act step", "✅ Has proper Assert step", "✅ Has proper error handling", "✅ Has proper cleanup"]}, {"file": "create_delete_local.js", "hasArrange": true, "hasAct": true, "hasAssert": true, "usesSelectors": false, "errorHandling": true, "cleanup": true, "documentation": true, "noHardcodedCredentials": true, "usesAAA": false, "totalScore": 85, "details": ["✅ Has proper documentation", "✅ No hardcoded credentials", "❌ Not using centralized selectors", "❌ Does not explicitly document AAA pattern", "✅ Has proper Arrange step", "✅ Has proper Act step", "✅ Has proper Assert step", "✅ Has proper error handling", "✅ Has proper cleanup"]}, {"file": "create_delete_node20.js", "hasArrange": true, "hasAct": true, "hasAssert": true, "usesSelectors": true, "errorHandling": true, "cleanup": true, "documentation": true, "noHardcodedCredentials": true, "usesAAA": true, "totalScore": 100, "details": ["✅ Has proper documentation", "✅ No hardcoded credentials", "✅ Uses centralized selectors", "✅ Explicitly documents AAA pattern", "✅ Has proper Arrange step", "✅ Has proper Act step", "✅ Has proper Assert step", "✅ Has proper error handling", "✅ Has proper cleanup"]}, {"file": "create_delete_node20_debug.js", "hasArrange": true, "hasAct": true, "hasAssert": true, "usesSelectors": false, "errorHandling": true, "cleanup": false, "documentation": true, "noHardcodedCredentials": true, "usesAAA": false, "totalScore": 80, "details": ["✅ Has proper documentation", "✅ No hardcoded credentials", "❌ Not using centralized selectors", "❌ Does not explicitly document AAA pattern", "✅ Has proper Arrange step", "✅ Has proper Act step", "✅ Has proper Assert step", "✅ Has proper error handling", "❌ Missing proper cleanup"]}, {"file": "create_delete_operations.js", "hasArrange": true, "hasAct": true, "hasAssert": true, "usesSelectors": true, "errorHandling": true, "cleanup": true, "documentation": true, "noHardcodedCredentials": true, "usesAAA": true, "totalScore": 100, "details": ["✅ Has proper documentation", "✅ No hardcoded credentials", "✅ Uses centralized selectors", "✅ Explicitly documents AAA pattern", "✅ Has proper Arrange step", "✅ Has proper Act step", "✅ Has proper Assert step", "✅ Has proper error handling", "✅ Has proper cleanup"]}, {"file": "create_delete_operations_clean.js", "hasArrange": true, "hasAct": true, "hasAssert": true, "usesSelectors": false, "errorHandling": true, "cleanup": true, "documentation": true, "noHardcodedCredentials": true, "usesAAA": false, "totalScore": 85, "details": ["✅ Has proper documentation", "✅ No hardcoded credentials", "❌ Not using centralized selectors", "❌ Does not explicitly document AAA pattern", "✅ Has proper Arrange step", "✅ Has proper Act step", "✅ Has proper Assert step", "✅ Has proper error handling", "✅ Has proper cleanup"]}, {"file": "create_delete_operations_fixed.js", "hasArrange": true, "hasAct": true, "hasAssert": true, "usesSelectors": false, "errorHandling": true, "cleanup": true, "documentation": true, "noHardcodedCredentials": true, "usesAAA": true, "totalScore": 90, "details": ["✅ Has proper documentation", "✅ No hardcoded credentials", "❌ Not using centralized selectors", "✅ Explicitly documents AAA pattern", "✅ Has proper Arrange step", "✅ Has proper Act step", "✅ Has proper Assert step", "✅ Has proper error handling", "✅ Has proper cleanup"]}, {"file": "create_delete_operations_improved.js", "hasArrange": true, "hasAct": true, "hasAssert": true, "usesSelectors": true, "errorHandling": true, "cleanup": true, "documentation": true, "noHardcodedCredentials": true, "usesAAA": true, "totalScore": 100, "details": ["✅ Has proper documentation", "✅ No hardcoded credentials", "✅ Uses centralized selectors", "✅ Explicitly documents AAA pattern", "✅ Has proper Arrange step", "✅ Has proper Act step", "✅ Has proper Assert step", "✅ Has proper error handling", "✅ Has proper cleanup"]}, {"file": "create_delete_screenshot.js", "hasArrange": true, "hasAct": true, "hasAssert": true, "usesSelectors": false, "errorHandling": true, "cleanup": true, "documentation": true, "noHardcodedCredentials": true, "usesAAA": false, "totalScore": 85, "details": ["✅ Has proper documentation", "✅ No hardcoded credentials", "❌ Not using centralized selectors", "❌ Does not explicitly document AAA pattern", "✅ Has proper Arrange step", "✅ Has proper Act step", "✅ Has proper Assert step", "✅ Has proper error handling", "✅ Has proper cleanup"]}, {"file": "create_file_simple.js", "hasArrange": true, "hasAct": true, "hasAssert": true, "usesSelectors": true, "errorHandling": true, "cleanup": true, "documentation": true, "noHardcodedCredentials": true, "usesAAA": true, "totalScore": 100, "details": ["✅ Has proper documentation", "✅ No hardcoded credentials", "✅ Uses centralized selectors", "✅ Explicitly documents AAA pattern", "✅ Has proper Arrange step", "✅ Has proper Act step", "✅ Has proper Assert step", "✅ Has proper error handling", "✅ Has proper cleanup"]}, {"file": "delete_file_simple.js", "hasArrange": true, "hasAct": true, "hasAssert": true, "usesSelectors": true, "errorHandling": true, "cleanup": true, "documentation": true, "noHardcodedCredentials": true, "usesAAA": true, "totalScore": 100, "details": ["✅ Has proper documentation", "✅ No hardcoded credentials", "✅ Uses centralized selectors", "✅ Explicitly documents AAA pattern", "✅ Has proper Arrange step", "✅ Has proper Act step", "✅ Has proper Assert step", "✅ Has proper error handling", "✅ Has proper cleanup"]}, {"file": "example_structured_screenshots_debug.js", "hasArrange": true, "hasAct": true, "hasAssert": true, "usesSelectors": false, "errorHandling": true, "cleanup": true, "documentation": true, "noHardcodedCredentials": true, "usesAAA": false, "totalScore": 85, "details": ["✅ Has proper documentation", "✅ No hardcoded credentials", "❌ Not using centralized selectors", "❌ Does not explicitly document AAA pattern", "✅ Has proper Arrange step", "✅ Has proper Act step", "✅ Has proper Assert step", "✅ Has proper error handling", "✅ Has proper cleanup"]}, {"file": "invalid_credentials_node20_test.js", "hasArrange": true, "hasAct": true, "hasAssert": true, "usesSelectors": true, "errorHandling": true, "cleanup": true, "documentation": true, "noHardcodedCredentials": true, "usesAAA": true, "totalScore": 100, "details": ["✅ Has proper documentation", "✅ No hardcoded credentials", "✅ Uses centralized selectors", "✅ Explicitly documents AAA pattern", "✅ Has proper Arrange step", "✅ Has proper Act step", "✅ Has proper Assert step", "✅ Has proper error handling", "✅ Has proper cleanup"]}, {"file": "invalid_credentials_test.js", "hasArrange": true, "hasAct": true, "hasAssert": true, "usesSelectors": true, "errorHandling": true, "cleanup": true, "documentation": true, "noHardcodedCredentials": true, "usesAAA": true, "totalScore": 100, "details": ["✅ Has proper documentation", "✅ No hardcoded credentials", "✅ Uses centralized selectors", "✅ Explicitly documents AAA pattern", "✅ Has proper Arrange step", "✅ Has proper Act step", "✅ Has proper Assert step", "✅ Has proper error handling", "✅ Has proper cleanup"]}, {"file": "invalid_login_test.js", "hasArrange": true, "hasAct": true, "hasAssert": true, "usesSelectors": true, "errorHandling": true, "cleanup": true, "documentation": true, "noHardcodedCredentials": true, "usesAAA": true, "totalScore": 100, "details": ["✅ Has proper documentation", "✅ No hardcoded credentials", "✅ Uses centralized selectors", "✅ Explicitly documents AAA pattern", "✅ Has proper Arrange step", "✅ Has proper Act step", "✅ Has proper Assert step", "✅ Has proper error handling", "✅ Has proper cleanup"]}, {"file": "login_test.js", "hasArrange": true, "hasAct": true, "hasAssert": true, "usesSelectors": true, "errorHandling": true, "cleanup": true, "documentation": true, "noHardcodedCredentials": false, "usesAAA": true, "totalScore": 95, "details": ["✅ Has proper documentation", "❌ Contains hardcoded credentials", "✅ Uses centralized selectors", "✅ Explicitly documents AAA pattern", "✅ Has proper Arrange step", "✅ Has proper Act step", "✅ Has proper Assert step", "✅ Has proper error handling", "✅ Has proper cleanup"]}, {"file": "log_in_invalid_credentials.js", "hasArrange": true, "hasAct": true, "hasAssert": true, "usesSelectors": true, "errorHandling": true, "cleanup": true, "documentation": true, "noHardcodedCredentials": false, "usesAAA": true, "totalScore": 95, "details": ["✅ Has proper documentation", "❌ Contains hardcoded credentials", "✅ Uses centralized selectors", "✅ Explicitly documents AAA pattern", "✅ Has proper Arrange step", "✅ Has proper Act step", "✅ Has proper Assert step", "✅ Has proper error handling", "✅ Has proper cleanup"]}, {"file": "qawolf_ci_test.js", "hasArrange": true, "hasAct": true, "hasAssert": true, "usesSelectors": true, "errorHandling": true, "cleanup": true, "documentation": true, "noHardcodedCredentials": true, "usesAAA": true, "totalScore": 100, "details": ["✅ Has proper documentation", "✅ No hardcoded credentials", "✅ Uses centralized selectors", "✅ Explicitly documents AAA pattern", "✅ Has proper Arrange step", "✅ Has proper Act step", "✅ Has proper Assert step", "✅ Has proper error handling", "✅ Has proper cleanup"]}, {"file": "sandbox_login_test.js", "hasArrange": true, "hasAct": true, "hasAssert": true, "usesSelectors": false, "errorHandling": false, "cleanup": false, "documentation": true, "noHardcodedCredentials": false, "usesAAA": true, "totalScore": 70, "details": ["✅ Has proper documentation", "❌ Contains hardcoded credentials", "❌ Not using centralized selectors", "✅ Explicitly documents AAA pattern", "✅ Has proper Arrange step", "✅ Has proper Act step", "✅ Has proper Assert step", "❌ Missing proper error handling", "❌ Missing proper cleanup"]}, {"file": "sort_files.js", "hasArrange": true, "hasAct": true, "hasAssert": true, "usesSelectors": true, "errorHandling": true, "cleanup": true, "documentation": true, "noHardcodedCredentials": true, "usesAAA": true, "totalScore": 100, "details": ["✅ Has proper documentation", "✅ No hardcoded credentials", "✅ Uses centralized selectors", "✅ Explicitly documents AAA pattern", "✅ Has proper Arrange step", "✅ Has proper Act step", "✅ Has proper Assert step", "✅ Has proper error handling", "✅ Has proper cleanup"]}, {"file": "sort_files_local.js", "hasArrange": true, "hasAct": true, "hasAssert": true, "usesSelectors": false, "errorHandling": true, "cleanup": true, "documentation": true, "noHardcodedCredentials": true, "usesAAA": false, "totalScore": 85, "details": ["✅ Has proper documentation", "✅ No hardcoded credentials", "❌ Not using centralized selectors", "❌ Does not explicitly document AAA pattern", "✅ Has proper Arrange step", "✅ Has proper Act step", "✅ Has proper Assert step", "✅ Has proper error handling", "✅ Has proper cleanup"]}, {"file": "sort_files_node20.js", "hasArrange": true, "hasAct": true, "hasAssert": true, "usesSelectors": false, "errorHandling": true, "cleanup": false, "documentation": true, "noHardcodedCredentials": true, "usesAAA": false, "totalScore": 80, "details": ["✅ Has proper documentation", "✅ No hardcoded credentials", "❌ Not using centralized selectors", "❌ Does not explicitly document AAA pattern", "✅ Has proper Arrange step", "✅ Has proper Act step", "✅ Has proper Assert step", "✅ Has proper error handling", "❌ Missing proper cleanup"]}]