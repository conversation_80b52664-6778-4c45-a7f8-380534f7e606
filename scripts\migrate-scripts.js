/**
 * <PERSON><PERSON>t to migrate existing scripts to the new package structure
 */

const fs = require('fs');
const path = require('path');

// Define the migration mappings
const migrations = [
  {
    source: 'scripts/qawolf-ci-greenlight.js',
    target: 'packages/qawolf-integration/bin/ci-greenlight.js',
    content: `#!/usr/bin/env node

/**
 * QA Wolf CI Greenlight CLI
 * 
 * This script polls the QA Wolf API for CI greenlight status.
 */

const { createCIGreenlight } = require('../dist/ci-greenlight');

// Parse command line arguments
const args = process.argv.slice(2);
const options = {};

for (let i = 0; i < args.length; i++) {
  const arg = args[i];
  
  if (arg === '--api-key' && i + 1 < args.length) {
    options.apiKey = args[++i];
  } else if (arg === '--team-id' && i + 1 < args.length) {
    options.teamId = args[++i];
  } else if (arg === '--api-url' && i + 1 < args.length) {
    options.apiUrl = args[++i];
  } else if (arg === '--reports-dir' && i + 1 < args.length) {
    options.reportsDir = args[++i];
  } else if (arg === '--max-polls' && i + 1 < args.length) {
    options.maxPolls = parseInt(args[++i], 10);
  } else if (arg === '--poll-interval' && i + 1 < args.length) {
    options.pollInterval = parseInt(args[++i], 10);
  } else if (arg === '--environment' && i + 1 < args.length) {
    options.environment = args[++i];
  } else if (arg === '--branch' && i + 1 < args.length) {
    options.branch = args[++i];
  }
}

// Create CI greenlight instance
const ciGreenlight = createCIGreenlight(options);

// Poll for greenlight
ciGreenlight.pollForGreenlight()
  .then(greenlight => {
    if (greenlight) {
      console.log('QA Wolf CI greenlight: PASSED');
      process.exit(0);
    } else {
      console.error('QA Wolf CI greenlight: FAILED');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('Error polling for QA Wolf CI greenlight:', error.message);
    process.exit(1);
  });
`
  },
  {
    source: 'scripts/qawolf-notify-deployment.js',
    target: 'packages/qawolf-integration/bin/notify-deployment.js',
    content: `#!/usr/bin/env node

/**
 * QA Wolf Deployment Notification CLI
 * 
 * This script notifies QA Wolf of a new deployment.
 */

const { createDeploymentNotification } = require('../dist/deployment-notification');

// Parse command line arguments
const args = process.argv.slice(2);
const options = {};

for (let i = 0; i < args.length; i++) {
  const arg = args[i];
  
  if (arg === '--api-key' && i + 1 < args.length) {
    options.apiKey = args[++i];
  } else if (arg === '--team-id' && i + 1 < args.length) {
    options.teamId = args[++i];
  } else if (arg === '--api-url' && i + 1 < args.length) {
    options.apiUrl = args[++i];
  } else if (arg === '--reports-dir' && i + 1 < args.length) {
    options.reportsDir = args[++i];
  } else if (arg === '--environment' && i + 1 < args.length) {
    options.environment = args[++i];
  } else if (arg === '--branch' && i + 1 < args.length) {
    options.branch = args[++i];
  } else if (arg === '--commit' && i + 1 < args.length) {
    options.commit = args[++i];
  } else if (arg === '--build-url' && i + 1 < args.length) {
    options.buildUrl = args[++i];
  }
}

// Create deployment notification instance
const deploymentNotification = createDeploymentNotification(options);

// Notify deployment
deploymentNotification.notifyDeployment()
  .then(response => {
    console.log('QA Wolf deployment notification successful!');
    process.exit(0);
  })
  .catch(error => {
    console.error('Error notifying QA Wolf of deployment:', error.message);
    process.exit(1);
  });
`
  },
  {
    source: 'scripts/qawolf-generate-report.js',
    target: 'packages/qawolf-integration/bin/generate-report.js',
    content: `#!/usr/bin/env node

/**
 * QA Wolf Report Generator CLI
 * 
 * This script generates a report from QA Wolf test results.
 */

const { createQAWolfReportGenerator } = require('../dist/report-generator');

// Parse command line arguments
const args = process.argv.slice(2);
const options = {};

for (let i = 0; i < args.length; i++) {
  const arg = args[i];
  
  if (arg === '--api-key' && i + 1 < args.length) {
    options.apiKey = args[++i];
  } else if (arg === '--team-id' && i + 1 < args.length) {
    options.teamId = args[++i];
  } else if (arg === '--api-url' && i + 1 < args.length) {
    options.apiUrl = args[++i];
  } else if (arg === '--reports-dir' && i + 1 < args.length) {
    options.reportsDir = args[++i];
  } else if (arg === '--environment' && i + 1 < args.length) {
    options.environment = args[++i];
  } else if (arg === '--branch' && i + 1 < args.length) {
    options.branch = args[++i];
  } else if (arg === '--title' && i + 1 < args.length) {
    options.title = args[++i];
  } else if (arg === '--description' && i + 1 < args.length) {
    options.description = args[++i];
  } else if (arg === '--format' && i + 1 < args.length) {
    options.format = args[++i];
  } else if (arg === '--template' && i + 1 < args.length) {
    options.template = args[++i];
  }
}

// Create report generator instance
const reportGenerator = createQAWolfReportGenerator(options);

// Generate report
reportGenerator.generateReport()
  .then(filePath => {
    console.log('QA Wolf test report generated successfully!');
    console.log(\`Report saved to: \${filePath}\`);
    process.exit(0);
  })
  .catch(error => {
    console.error('Error generating QA Wolf test report:', error.message);
    process.exit(1);
  });
`
  },
  {
    source: 'scripts/testmo-integration.js',
    target: 'packages/testmo-integration/bin/testmo-integration.js',
    content: `#!/usr/bin/env node

/**
 * Testmo Integration CLI
 * 
 * This script integrates with Testmo for test case management.
 */

const { createTestmoReporter } = require('../dist/testmo-reporter');

// Parse command line arguments
const args = process.argv.slice(2);
const command = args[0];
const options = {};

for (let i = 1; i < args.length; i++) {
  const arg = args[i];
  
  if (arg === '--api-key' && i + 1 < args.length) {
    options.apiKey = args[++i];
  } else if (arg === '--host' && i + 1 < args.length) {
    options.host = args[++i];
  } else if (arg === '--project-id' && i + 1 < args.length) {
    options.projectId = parseInt(args[++i], 10);
  } else if (arg === '--source-id' && i + 1 < args.length) {
    options.sourceId = args[++i];
  } else if (arg === '--reports-dir' && i + 1 < args.length) {
    options.reportsDir = args[++i];
  } else if (arg === '--run-name' && i + 1 < args.length) {
    options.runName = args[++i];
  } else if (arg === '--results-file' && i + 1 < args.length) {
    options.resultsFile = args[++i];
  }
}

// Create Testmo reporter instance
const testmoReporter = createTestmoReporter(options);

// Execute command
if (command === 'create-run') {
  testmoReporter.createRun()
    .then(run => {
      console.log('Testmo test run created successfully!');
      console.log(\`Run ID: \${run.id}\`);
      process.exit(0);
    })
    .catch(error => {
      console.error('Error creating Testmo test run:', error.message);
      process.exit(1);
    });
} else if (command === 'submit-results') {
  if (!options.resultsFile) {
    console.error('Results file is required for submit-results command');
    process.exit(1);
  }
  
  testmoReporter.submitResults(options.resultsFile)
    .then(submission => {
      console.log('Testmo test results submitted successfully!');
      console.log(\`Submission ID: \${submission.id}\`);
      process.exit(0);
    })
    .catch(error => {
      console.error('Error submitting Testmo test results:', error.message);
      process.exit(1);
    });
} else if (command === 'complete-run') {
  if (!options.runId) {
    console.error('Run ID is required for complete-run command');
    process.exit(1);
  }
  
  testmoReporter.completeRun(options.runId)
    .then(run => {
      console.log('Testmo test run completed successfully!');
      process.exit(0);
    })
    .catch(error => {
      console.error('Error completing Testmo test run:', error.message);
      process.exit(1);
    });
} else {
  console.error(\`Unknown command: \${command}\`);
  console.error('Available commands: create-run, submit-results, complete-run');
  process.exit(1);
}
`
  }
];

// Create directories if they don't exist
migrations.forEach(migration => {
  const targetDir = path.dirname(migration.target);
  if (!fs.existsSync(targetDir)) {
    fs.mkdirSync(targetDir, { recursive: true });
  }
});

// Migrate scripts
migrations.forEach(migration => {
  console.log(`Migrating ${migration.source} to ${migration.target}...`);
  
  // Write the new file
  fs.writeFileSync(migration.target, migration.content);
  
  // Make the file executable
  try {
    fs.chmodSync(migration.target, '755');
  } catch (error) {
    console.warn(`Warning: Could not make ${migration.target} executable: ${error.message}`);
  }
  
  console.log(`Migrated ${migration.source} to ${migration.target}`);
});

console.log('Migration complete!');
