/**
 * Test reporter for QA Wolf Test Framework
 */

import { Test, TestReporter, TestResult, TestStep, TestStatus } from './types';
import { writeJsonToFile, writeStringToFile, ensureDirectoryExists } from '@qawolf/shared-utils';
import { getConfig } from '@qawolf/core';

// Get configuration
const config = getConfig();

/**
 * Console reporter
 */
export class ConsoleReporter implements TestReporter {
  /**
   * On test start
   * @param test Test
   */
  onTestStart(test: Test): void {
    console.log(`\n[${new Date().toISOString()}] Starting test: ${test.name}`);
  }
  
  /**
   * On test end
   * @param result Test result
   */
  onTestEnd(result: TestResult): void {
    const duration = result.duration / 1000;
    const status = result.status === TestStatus.PASSED ? '✅ PASSED' : result.status === TestStatus.FAILED ? '❌ FAILED' : '⏭️ SKIPPED';
    
    console.log(`[${new Date().toISOString()}] Test ${result.name} ${status} in ${duration.toFixed(2)}s`);
    
    if (result.error) {
      console.error(`Error: ${result.error.message}`);
      console.error(result.error.stack);
    }
  }
  
  /**
   * On step start
   * @param test Test
   * @param step Test step
   */
  onStepStart(test: Test, step: TestStep): void {
    console.log(`  [${new Date().toISOString()}] Starting step: ${step.name} (${step.type})`);
  }
  
  /**
   * On step end
   * @param test Test
   * @param step Test step
   */
  onStepEnd(test: Test, step: TestStep): void {
    const duration = step.duration / 1000;
    const status = step.status === 'passed' ? '✅' : step.status === 'failed' ? '❌' : '⏭️';
    
    console.log(`  [${new Date().toISOString()}] Step ${step.name} ${status} in ${duration.toFixed(2)}s`);
    
    if (step.error) {
      console.error(`  Error: ${step.error.message}`);
      console.error(step.error.stack);
    }
  }
  
  /**
   * On run start
   * @param tests Tests
   */
  onRunStart(tests: Test[]): void {
    console.log(`\n[${new Date().toISOString()}] Starting test run with ${tests.length} tests`);
  }
  
  /**
   * On run end
   * @param results Test results
   */
  onRunEnd(results: TestResult[]): void {
    const passed = results.filter(r => r.status === TestStatus.PASSED).length;
    const failed = results.filter(r => r.status === TestStatus.FAILED).length;
    const skipped = results.filter(r => r.status === TestStatus.SKIPPED).length;
    const total = results.length;
    const duration = results.reduce((sum, r) => sum + r.duration, 0) / 1000;
    
    console.log(`\n[${new Date().toISOString()}] Test run completed in ${duration.toFixed(2)}s`);
    console.log(`Total: ${total}, Passed: ${passed}, Failed: ${failed}, Skipped: ${skipped}`);
    
    if (failed > 0) {
      console.log('\nFailed tests:');
      
      results
        .filter(r => r.status === TestStatus.FAILED)
        .forEach(result => {
          console.log(`- ${result.name}`);
          
          if (result.error) {
            console.error(`  Error: ${result.error.message}`);
          }
        });
    }
  }
}

/**
 * JSON reporter
 */
export class JsonReporter implements TestReporter {
  private outputDir: string;
  private results: TestResult[] = [];
  
  /**
   * Constructor
   * @param outputDir Output directory
   */
  constructor(outputDir: string = 'test-results') {
    this.outputDir = outputDir;
    ensureDirectoryExists(outputDir);
  }
  
  /**
   * On test start
   * @param test Test
   */
  onTestStart(test: Test): void {
    // Nothing to do
  }
  
  /**
   * On test end
   * @param result Test result
   */
  onTestEnd(result: TestResult): void {
    this.results.push(result);
    
    // Write individual test result
    writeJsonToFile(`${this.outputDir}/test-${result.id}.json`, result);
  }
  
  /**
   * On step start
   * @param test Test
   * @param step Test step
   */
  onStepStart(test: Test, step: TestStep): void {
    // Nothing to do
  }
  
  /**
   * On step end
   * @param test Test
   * @param step Test step
   */
  onStepEnd(test: Test, step: TestStep): void {
    // Nothing to do
  }
  
  /**
   * On run start
   * @param tests Tests
   */
  onRunStart(tests: Test[]): void {
    this.results = [];
  }
  
  /**
   * On run end
   * @param results Test results
   */
  onRunEnd(results: TestResult[]): void {
    // Write all test results
    writeJsonToFile(`${this.outputDir}/results.json`, results);
    
    // Write summary
    const passed = results.filter(r => r.status === TestStatus.PASSED).length;
    const failed = results.filter(r => r.status === TestStatus.FAILED).length;
    const skipped = results.filter(r => r.status === TestStatus.SKIPPED).length;
    const total = results.length;
    const duration = results.reduce((sum, r) => sum + r.duration, 0);
    
    const summary = {
      total,
      passed,
      failed,
      skipped,
      duration,
      timestamp: new Date().toISOString()
    };
    
    writeJsonToFile(`${this.outputDir}/summary.json`, summary);
  }
}

/**
 * HTML reporter
 */
export class HtmlReporter implements TestReporter {
  private outputDir: string;
  private results: TestResult[] = [];
  
  /**
   * Constructor
   * @param outputDir Output directory
   */
  constructor(outputDir: string = 'test-results') {
    this.outputDir = outputDir;
    ensureDirectoryExists(outputDir);
  }
  
  /**
   * On test start
   * @param test Test
   */
  onTestStart(test: Test): void {
    // Nothing to do
  }
  
  /**
   * On test end
   * @param result Test result
   */
  onTestEnd(result: TestResult): void {
    this.results.push(result);
  }
  
  /**
   * On step start
   * @param test Test
   * @param step Test step
   */
  onStepStart(test: Test, step: TestStep): void {
    // Nothing to do
  }
  
  /**
   * On step end
   * @param test Test
   * @param step Test step
   */
  onStepEnd(test: Test, step: TestStep): void {
    // Nothing to do
  }
  
  /**
   * On run start
   * @param tests Tests
   */
  onRunStart(tests: Test[]): void {
    this.results = [];
  }
  
  /**
   * On run end
   * @param results Test results
   */
  onRunEnd(results: TestResult[]): void {
    // Generate HTML report
    const html = this.generateHtml(results);
    
    // Write HTML report
    writeStringToFile(`${this.outputDir}/report.html`, html);
  }
  
  /**
   * Generate HTML report
   * @param results Test results
   * @returns HTML report
   */
  private generateHtml(results: TestResult[]): string {
    const passed = results.filter(r => r.status === TestStatus.PASSED).length;
    const failed = results.filter(r => r.status === TestStatus.FAILED).length;
    const skipped = results.filter(r => r.status === TestStatus.SKIPPED).length;
    const total = results.length;
    const duration = results.reduce((sum, r) => sum + r.duration, 0) / 1000;
    
    const passRate = total > 0 ? (passed / total) * 100 : 0;
    
    return `
<!DOCTYPE html>
<html>
<head>
  <title>Test Report</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      color: #333;
    }
    
    h1, h2, h3 {
      color: #2c3e50;
    }
    
    .summary {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }
    
    .summary-item {
      text-align: center;
      padding: 10px;
      border-radius: 5px;
      flex: 1;
      margin: 0 5px;
    }
    
    .summary-item.total {
      background-color: #f8f9fa;
    }
    
    .summary-item.passed {
      background-color: #d4edda;
      color: #155724;
    }
    
    .summary-item.failed {
      background-color: #f8d7da;
      color: #721c24;
    }
    
    .summary-item.skipped {
      background-color: #fff3cd;
      color: #856404;
    }
    
    .summary-item h2 {
      margin: 0;
      font-size: 24px;
    }
    
    .summary-item p {
      margin: 5px 0 0;
    }
    
    .test {
      margin-bottom: 20px;
      padding: 15px;
      border-radius: 5px;
    }
    
    .test.passed {
      background-color: #d4edda;
      border-left: 5px solid #28a745;
    }
    
    .test.failed {
      background-color: #f8d7da;
      border-left: 5px solid #dc3545;
    }
    
    .test.skipped {
      background-color: #fff3cd;
      border-left: 5px solid #ffc107;
    }
    
    .test h3 {
      margin-top: 0;
    }
    
    .test-info {
      display: flex;
      justify-content: space-between;
      margin-bottom: 10px;
      font-size: 14px;
      color: #6c757d;
    }
    
    .steps {
      margin-top: 10px;
    }
    
    .step {
      padding: 10px;
      margin-bottom: 5px;
      border-radius: 3px;
    }
    
    .step.passed {
      background-color: #eafaef;
    }
    
    .step.failed {
      background-color: #fbeaec;
    }
    
    .step.skipped {
      background-color: #fff9e6;
    }
    
    .step-info {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      color: #6c757d;
    }
    
    .error {
      margin-top: 10px;
      padding: 10px;
      background-color: #f8d7da;
      border-radius: 3px;
      font-family: monospace;
      white-space: pre-wrap;
    }
    
    .screenshots {
      margin-top: 10px;
      display: flex;
      flex-wrap: wrap;
    }
    
    .screenshot {
      margin: 5px;
      border: 1px solid #ddd;
      border-radius: 3px;
      overflow: hidden;
    }
    
    .screenshot img {
      max-width: 200px;
      max-height: 200px;
    }
  </style>
</head>
<body>
  <h1>Test Report</h1>
  
  <div class="summary">
    <div class="summary-item total">
      <h2>${total}</h2>
      <p>Total Tests</p>
    </div>
    <div class="summary-item passed">
      <h2>${passed}</h2>
      <p>Passed</p>
    </div>
    <div class="summary-item failed">
      <h2>${failed}</h2>
      <p>Failed</p>
    </div>
    <div class="summary-item skipped">
      <h2>${skipped}</h2>
      <p>Skipped</p>
    </div>
  </div>
  
  <p>Pass Rate: ${passRate.toFixed(2)}% | Duration: ${duration.toFixed(2)}s | Timestamp: ${new Date().toISOString()}</p>
  
  <h2>Tests</h2>
  
  ${results.map(result => `
    <div class="test ${result.status}">
      <h3>${result.name}</h3>
      <div class="test-info">
        <span>Status: ${result.status}</span>
        <span>Duration: ${(result.duration / 1000).toFixed(2)}s</span>
      </div>
      
      ${result.error ? `
        <div class="error">
          ${result.error.message}
          ${result.error.stack}
        </div>
      ` : ''}
      
      <div class="steps">
        <h4>Steps</h4>
        ${result.steps.map(step => `
          <div class="step ${step.status}">
            <div>${step.name} (${step.type})</div>
            <div class="step-info">
              <span>Status: ${step.status}</span>
              <span>Duration: ${(step.duration / 1000).toFixed(2)}s</span>
            </div>
            
            ${step.error ? `
              <div class="error">
                ${step.error.message}
                ${step.error.stack}
              </div>
            ` : ''}
          </div>
        `).join('')}
      </div>
      
      ${result.screenshots.length > 0 ? `
        <div class="screenshots">
          <h4>Screenshots</h4>
          ${result.screenshots.map(screenshot => `
            <div class="screenshot">
              <img src="${screenshot}" alt="Screenshot" />
            </div>
          `).join('')}
        </div>
      ` : ''}
    </div>
  `).join('')}
</body>
</html>
    `;
  }
}

/**
 * Create console reporter
 * @returns Console reporter
 */
export function createConsoleReporter(): TestReporter {
  return new ConsoleReporter();
}

/**
 * Create JSON reporter
 * @param outputDir Output directory
 * @returns JSON reporter
 */
export function createJsonReporter(outputDir: string = 'test-results'): TestReporter {
  return new JsonReporter(outputDir);
}

/**
 * Create HTML reporter
 * @param outputDir Output directory
 * @returns HTML reporter
 */
export function createHtmlReporter(outputDir: string = 'test-results'): TestReporter {
  return new HtmlReporter(outputDir);
}

/**
 * Create reporters
 * @param reporters Reporter names
 * @param outputDir Output directory
 * @returns Reporters
 */
export function createReporters(reporters: string[] = ['console'], outputDir: string = 'test-results'): TestReporter[] {
  const reporterInstances: TestReporter[] = [];
  
  for (const reporter of reporters) {
    switch (reporter) {
      case 'console':
        reporterInstances.push(createConsoleReporter());
        break;
      case 'json':
        reporterInstances.push(createJsonReporter(outputDir));
        break;
      case 'html':
        reporterInstances.push(createHtmlReporter(outputDir));
        break;
      default:
        console.warn(`Unknown reporter: ${reporter}`);
    }
  }
  
  return reporterInstances;
}
