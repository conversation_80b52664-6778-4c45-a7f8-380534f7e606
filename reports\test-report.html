
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>QA Wolf Test Report</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 0; padding: 20px; color: #333; }
    h1, h2, h3 { color: #2c3e50; }
    .summary { background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
    .summary-item { margin-bottom: 10px; }
    .pass { color: #28a745; }
    .fail { color: #dc3545; }
    .warning { color: #ffc107; }
    .test-details { margin-top: 30px; }
    table { width: 100%; border-collapse: collapse; margin-top: 20px; }
    th, td { padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd; }
    th { background-color: #f2f2f2; }
    tr:hover { background-color: #f5f5f5; }
    .progress-container { width: 100%; background-color: #e0e0e0; border-radius: 4px; }
    .progress-bar { height: 20px; border-radius: 4px; }
    .progress-bar.good { background-color: #28a745; }
    .progress-bar.warning { background-color: #ffc107; }
    .progress-bar.poor { background-color: #dc3545; }
    .details-list { list-style-type: none; padding-left: 0; }
    .details-list li { margin-bottom: 5px; }
  </style>
</head>
<body>
  <h1>QA Wolf Test Report</h1>

  <div class="summary">
    <h2>Summary</h2>
    <div class="summary-item">
      <strong>Generated on:</strong> 5/19/2025, 1:23:38 PM
    </div>
    <div class="summary-item">
      <strong>Tests Run:</strong> 4
    </div>
    <div class="summary-item">
      <strong>Tests Passed:</strong> 2
    </div>
    <div class="summary-item">
      <strong>Tests Failed:</strong> 2
    </div>
    <div class="summary-item">
      <strong>Pass Rate:</strong>
      <span class="fail">
        50.00%
      </span>
      <div class="progress-container">
        <div class="progress-bar poor"
             style="width: 50%"></div>
      </div>
    </div>
  </div>

  <div class="test-details">
    <h2>Test Details</h2>
    <table>
      <thead>
        <tr>
          <th>Test File</th>
          <th>Status</th>
          <th>Pass Rate</th>
          <th>Duration</th>
        </tr>
      </thead>
      <tbody>
        
          <tr>
            <td>login_test.js</td>
            <td class="pass">PASS</td>
            <td>100.0%</td>
            <td>1.5 seconds</td>
          </tr>
        
          <tr>
            <td>inject_test_failures.js</td>
            <td class="fail">FAIL</td>
            <td>33.33%</td>
            <td>1.5 seconds</td>
          </tr>
        
          <tr>
            <td>collision_test_1.js</td>
            <td class="pass">PASS</td>
            <td>100.0%</td>
            <td>1.5 seconds</td>
          </tr>
        
          <tr>
            <td>collision_test_2.js</td>
            <td class="fail">FAIL</td>
            <td>33.33%</td>
            <td>1.5 seconds</td>
          </tr>
        
      </tbody>
    </table>
  </div>

  <footer style="margin-top: 50px; text-align: center; color: #777;">
    <p>Generated on 5/19/2025, 1:23:38 PM</p>
  </footer>
</body>
</html>
  