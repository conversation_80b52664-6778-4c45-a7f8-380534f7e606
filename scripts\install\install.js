#!/usr/bin/env node

/**
 * Install Script
 * 
 * This script installs the QA Wolf testing framework.
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');
const readline = require('readline');

// Create readline interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Configuration
const config = {
  packages: [
    '@qawolf/shared-utils',
    '@qawolf/mcp-optimizer',
    '@qawolf/test-framework'
  ],
  devDependencies: [
    '@playwright/test',
    'eslint',
    'prettier'
  ]
};

/**
 * Ask a question
 * @param {string} question - Question to ask
 * @returns {Promise<string>} - User's answer
 */
function ask(question) {
  return new Promise((resolve) => {
    rl.question(question, (answer) => {
      resolve(answer);
    });
  });
}

/**
 * Install packages
 * @param {Array<string>} packages - Packages to install
 * @param {boolean} dev - Whether to install as dev dependencies
 * @returns {void}
 */
function installPackages(packages, dev = false) {
  const command = `npm install ${dev ? '--save-dev' : '--save'} ${packages.join(' ')}`;
  console.log(`Running: ${command}`);
  execSync(command, { stdio: 'inherit' });
}

/**
 * Create a package.json file if it doesn't exist
 * @returns {void}
 */
function createPackageJson() {
  if (!fs.existsSync('package.json')) {
    console.log('Creating package.json...');
    execSync('npm init -y', { stdio: 'inherit' });
  }
}

/**
 * Create a playwright.config.js file
 * @returns {void}
 */
function createPlaywrightConfig() {
  const configPath = path.join(process.cwd(), 'playwright.config.js');
  
  if (fs.existsSync(configPath)) {
    console.log('playwright.config.js already exists, skipping');
    return;
  }
  
  console.log('Creating playwright.config.js...');
  
  const configContent = `
const { defineConfig } = require('@playwright/test');

module.exports = defineConfig({
  testDir: './tests',
  timeout: 30000,
  expect: {
    timeout: 5000
  },
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: [
    ['html'],
    ['json', { outputFile: 'reports/test-results.json' }]
  ],
  use: {
    actionTimeout: 0,
    trace: 'on-first-retry',
    screenshot: 'only-on-failure'
  },
  projects: [
    {
      name: 'chromium',
      use: {
        browserName: 'chromium'
      }
    }
  ]
});
`;
  
  fs.writeFileSync(configPath, configContent, 'utf8');
}

/**
 * Create a .env file
 * @param {string} apiKey - QA Wolf API key
 * @returns {void}
 */
function createEnvFile(apiKey) {
  const envPath = path.join(process.cwd(), '.env');
  
  if (fs.existsSync(envPath)) {
    console.log('.env file already exists, updating');
    let envContent = fs.readFileSync(envPath, 'utf8');
    
    if (!envContent.includes('QAWOLF_API_KEY')) {
      envContent += `\nQAWOLF_API_KEY=${apiKey}\n`;
      fs.writeFileSync(envPath, envContent, 'utf8');
    } else {
      console.log('QAWOLF_API_KEY already exists in .env file, skipping');
    }
    
    return;
  }
  
  console.log('Creating .env file...');
  fs.writeFileSync(envPath, `QAWOLF_API_KEY=${apiKey}\n`, 'utf8');
}

/**
 * Create a tests directory
 * @returns {void}
 */
function createTestsDirectory() {
  const testsDir = path.join(process.cwd(), 'tests');
  
  if (!fs.existsSync(testsDir)) {
    console.log('Creating tests directory...');
    fs.mkdirSync(testsDir, { recursive: true });
  }
  
  // Create example test
  const exampleTestPath = path.join(testsDir, 'example.spec.js');
  
  if (!fs.existsSync(exampleTestPath)) {
    console.log('Creating example test...');
    
    const exampleTestContent = `
const { test, expect } = require('@playwright/test');
const { createMcpController, createSelfHealingController } = require('@qawolf/test-framework');

test('example test', async ({ page }) => {
  // Create an MCP controller
  const mcpController = createMcpController();
  
  // Create a self-healing controller
  const selfHealingController = createSelfHealingController();
  
  try {
    // Start the test run
    await selfHealingController.startTest({
      testId: 'example-test',
      testName: 'Example Test'
    });
    
    // Create a self-healing page
    const selfHealingPage = selfHealingController.createPage(page);
    
    // Navigate to a website
    await selfHealingPage.goto('https://example.com');
    
    // Check the title
    const title = await selfHealingPage.title();
    expect(title).toBe('Example Domain');
    
    // End the test run
    await selfHealingController.endTest({
      success: true
    });
  } catch (error) {
    // End the test run with failure
    await selfHealingController.endTest({
      success: false,
      error
    });
    
    throw error;
  } finally {
    // Clean up resources
    await mcpController.cleanup();
    await selfHealingController.cleanup();
  }
});
`;
    
    fs.writeFileSync(exampleTestPath, exampleTestContent, 'utf8');
  }
}

/**
 * Update package.json scripts
 * @returns {void}
 */
function updatePackageJsonScripts() {
  const packageJsonPath = path.join(process.cwd(), 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  // Add scripts
  packageJson.scripts = {
    ...packageJson.scripts,
    test: 'playwright test',
    'test:ui': 'playwright test --ui',
    'test:debug': 'playwright test --debug',
    'test:report': 'playwright show-report'
  };
  
  // Write package.json
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2), 'utf8');
}

/**
 * Install Playwright browsers
 * @returns {void}
 */
function installPlaywrightBrowsers() {
  console.log('Installing Playwright browsers...');
  execSync('npx playwright install --with-deps', { stdio: 'inherit' });
}

/**
 * Run the installation
 * @returns {Promise<void>}
 */
async function runInstallation() {
  try {
    console.log('Installing QA Wolf testing framework...');
    
    // Create package.json if it doesn't exist
    createPackageJson();
    
    // Install packages
    console.log('Installing QA Wolf packages...');
    installPackages(config.packages);
    
    console.log('Installing dev dependencies...');
    installPackages(config.devDependencies, true);
    
    // Create configuration files
    createPlaywrightConfig();
    
    // Ask for QA Wolf API key
    const apiKey = await ask('Enter your QA Wolf API key (Team ID): ');
    createEnvFile(apiKey);
    
    // Create tests directory
    createTestsDirectory();
    
    // Update package.json scripts
    updatePackageJsonScripts();
    
    // Install Playwright browsers
    installPlaywrightBrowsers();
    
    console.log('Installation complete!');
    console.log('Run `npm test` to run the example test.');
    
    rl.close();
  } catch (error) {
    console.error('Installation failed:', error.message);
    process.exit(1);
  }
}

// Run the script
runInstallation();