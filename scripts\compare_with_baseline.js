/**
 * Compare Screenshots with Baseline
 *
 * This script compares screenshots from a test run with the baseline screenshots.
 * It uses the pixelmatch library to compare images pixel by pixel.
 *
 * Usage:
 * node scripts/compare_with_baseline.js --test=test_name --run=run_id [--date=YYYY-MM-DD] [--threshold=0.1]
 *
 * Examples:
 * node scripts/compare_with_baseline.js --test=login_test --run=run-12345
 * node scripts/compare_with_baseline.js --test=login_test --run=run-12345 --date=2025-05-18 --threshold=0.05
 *
 * @module compare_with_baseline
 * <AUTHOR> Wolf Team
 * @date 2025-01-01
 */

const fs = require('fs');
const path = require('path');
const { PNG } = require('pngjs');
const { format } = require('date-fns');
const { SCREENSHOTS_BASE_DIR } = require('../src/utils/screenshot-utils');

// Import pixelmatch dynamically
let pixelmatch;
try {
  pixelmatch = require('pixelmatch');
} catch (error) {
  console.error('Error: Failed to import pixelmatch. Make sure the pixelmatch package is installed correctly.');
  console.log('Try running: npm install pixelmatch --save-dev');
  process.exit(1);
}

// Verify pixelmatch is a function
if (typeof pixelmatch !== 'function') {
  console.error('Error: pixelmatch is not a function. Make sure the pixelmatch package is installed correctly.');
  console.log('Try running: npm install pixelmatch --save-dev');
  process.exit(1);
}

// Parse command line arguments
const args = process.argv.slice(2);
let testName = null;
let runId = null;
let date = null;
let threshold = 0.1; // Default threshold for pixel matching (0-1)

args.forEach(arg => {
  if (arg.startsWith('--test=')) {
    testName = arg.substring('--test='.length);
  } else if (arg.startsWith('--run=')) {
    runId = arg.substring('--run='.length);
  } else if (arg.startsWith('--date=')) {
    date = arg.substring('--date='.length);
  } else if (arg.startsWith('--threshold=')) {
    threshold = parseFloat(arg.substring('--threshold='.length));
  }
});

// Validate required parameters
if (!testName || !runId) {
  console.error('Error: Missing required parameters');
  console.log('Usage: node scripts/compare_with_baseline.js --test=test_name --run=run_id [--date=YYYY-MM-DD] [--threshold=0.1]');
  process.exit(1);
}

// If no date is specified, use today's date
if (!date) {
  date = format(new Date(), 'yyyy-MM-dd');
}

// Validate threshold
if (isNaN(threshold) || threshold < 0 || threshold > 1) {
  console.error('Error: Threshold must be a number between 0 and 1');
  process.exit(1);
}

/**
 * Compare screenshots with baseline
 *
 * @param {Object} options - Comparison options
 * @param {string} options.testName - Test name
 * @param {string} options.runId - Run ID
 * @param {string} options.date - Date in YYYY-MM-DD format
 * @param {number} options.threshold - Threshold for pixel matching (0-1)
 * @returns {Promise<Object>} - Comparison results
 */
async function compareWithBaseline(options) {
  const { testName, runId, date, threshold } = options;

  console.log(`Comparing screenshots for test "${testName}" on ${date} with baseline`);
  console.log(`Run: ${runId}`);
  console.log(`Threshold: ${threshold}`);

  // Get baseline screenshots
  const baselineDir = path.join(SCREENSHOTS_BASE_DIR, 'baseline', testName);
  if (!fs.existsSync(baselineDir)) {
    console.error(`Error: Baseline directory not found: ${baselineDir}`);
    process.exit(1);
  }

  // Check if baseline metadata exists
  const metadataPath = path.join(baselineDir, 'baseline.json');
  let metadata = null;
  if (fs.existsSync(metadataPath)) {
    try {
      metadata = JSON.parse(fs.readFileSync(metadataPath, 'utf8'));
      console.log(`Baseline created on ${metadata.date} using run ${metadata.runId}`);
    } catch (error) {
      console.error(`Error reading baseline metadata: ${error.message}`);
    }
  }

  const baselineScreenshots = fs.readdirSync(baselineDir)
    .filter(file => file.endsWith('.png'))
    .map(file => ({
      path: path.join(baselineDir, file),
      filename: file
    }));

  console.log(`Found ${baselineScreenshots.length} baseline screenshots`);

  // Get compare screenshots
  const compareDir = path.join(SCREENSHOTS_BASE_DIR, date, runId, testName);
  if (!fs.existsSync(compareDir)) {
    console.error(`Error: Compare directory not found: ${compareDir}`);
    process.exit(1);
  }

  const compareScreenshots = fs.readdirSync(compareDir)
    .filter(file => file.endsWith('.png'))
    .map(file => ({
      path: path.join(compareDir, file),
      filename: file
    }));

  console.log(`Found ${compareScreenshots.length} compare screenshots`);

  // Create reports directory if it doesn't exist
  const reportsDir = path.join(process.cwd(), 'reports', 'visual-regression');
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir, { recursive: true });
  }

  // Create a directory for the diff images
  const diffDir = path.join(reportsDir, `${date}_${testName}_baseline_vs_${runId}`);
  if (!fs.existsSync(diffDir)) {
    fs.mkdirSync(diffDir, { recursive: true });
  }

  // Compare screenshots
  const results = [];

  for (const baselineScreenshot of baselineScreenshots) {
    // Find matching screenshot in compare run
    const matchingScreenshot = compareScreenshots.find(s => s.filename === baselineScreenshot.filename);

    if (!matchingScreenshot) {
      console.log(`No matching screenshot found for ${baselineScreenshot.filename}`);
      results.push({
        filename: baselineScreenshot.filename,
        status: 'missing',
        diffPercentage: 100,
        baselinePath: baselineScreenshot.path,
        comparePath: null,
        diffPath: null
      });
      continue;
    }

    // Read baseline image
    const baselineImage = PNG.sync.read(fs.readFileSync(baselineScreenshot.path));

    // Read compare image
    const compareImage = PNG.sync.read(fs.readFileSync(matchingScreenshot.path));

    // Check if images have the same dimensions
    if (baselineImage.width !== compareImage.width || baselineImage.height !== compareImage.height) {
      console.log(`Image dimensions don't match for ${baselineScreenshot.filename}`);
      results.push({
        filename: baselineScreenshot.filename,
        status: 'dimension_mismatch',
        diffPercentage: 100,
        baselinePath: baselineScreenshot.path,
        comparePath: matchingScreenshot.path,
        diffPath: null,
        dimensions: {
          baseline: { width: baselineImage.width, height: baselineImage.height },
          compare: { width: compareImage.width, height: compareImage.height }
        }
      });
      continue;
    }

    // Create diff image
    const { width, height } = baselineImage;
    const diffImage = new PNG({ width, height });

    // Compare images
    const numDiffPixels = pixelmatch(
      baselineImage.data,
      compareImage.data,
      diffImage.data,
      width,
      height,
      { threshold }
    );

    // Calculate diff percentage
    const diffPercentage = (numDiffPixels / (width * height)) * 100;

    // Save diff image
    const diffPath = path.join(diffDir, `diff_${baselineScreenshot.filename}`);
    fs.writeFileSync(diffPath, PNG.sync.write(diffImage));

    // Add result
    results.push({
      filename: baselineScreenshot.filename,
      status: diffPercentage > 0 ? 'different' : 'identical',
      diffPercentage,
      numDiffPixels,
      baselinePath: baselineScreenshot.path,
      comparePath: matchingScreenshot.path,
      diffPath,
      dimensions: { width, height }
    });

    console.log(`Compared ${baselineScreenshot.filename}: ${diffPercentage.toFixed(2)}% different`);
  }

  // Check for screenshots in compare run that are not in baseline
  for (const compareScreenshot of compareScreenshots) {
    const matchingScreenshot = baselineScreenshots.find(s => s.filename === compareScreenshot.filename);

    if (!matchingScreenshot) {
      console.log(`New screenshot found in compare run: ${compareScreenshot.filename}`);
      results.push({
        filename: compareScreenshot.filename,
        status: 'new',
        diffPercentage: 100,
        baselinePath: null,
        comparePath: compareScreenshot.path,
        diffPath: null
      });
    }
  }

  // Generate HTML report
  generateHtmlReport({
    testName,
    runId,
    date,
    threshold,
    results,
    diffDir,
    reportsDir,
    metadata
  });

  return {
    testName,
    runId,
    date,
    threshold,
    results,
    metadata
  };
}

/**
 * Generate HTML report for visual regression test
 *
 * @param {Object} options - Report options
 * @param {string} options.testName - Test name
 * @param {string} options.runId - Run ID
 * @param {string} options.date - Date in YYYY-MM-DD format
 * @param {number} options.threshold - Threshold for pixel matching (0-1)
 * @param {Array<Object>} options.results - Comparison results
 * @param {string} options.diffDir - Directory for diff images
 * @param {string} options.reportsDir - Directory for reports
 * @param {Object} options.metadata - Baseline metadata
 */
function generateHtmlReport(options) {
  const { testName, runId, date, threshold, results, diffDir, reportsDir, metadata } = options;

  // Copy baseline and compare images to the diff directory
  for (const result of results) {
    if (result.baselinePath) {
      const baselineCopyPath = path.join(diffDir, `baseline_${result.filename}`);
      fs.copyFileSync(result.baselinePath, baselineCopyPath);
    }

    if (result.comparePath) {
      const compareCopyPath = path.join(diffDir, `compare_${result.filename}`);
      fs.copyFileSync(result.comparePath, compareCopyPath);
    }
  }

  // Calculate summary statistics
  const totalScreenshots = results.length;
  const identicalScreenshots = results.filter(r => r.status === 'identical').length;
  const differentScreenshots = results.filter(r => r.status === 'different').length;
  const missingScreenshots = results.filter(r => r.status === 'missing').length;
  const newScreenshots = results.filter(r => r.status === 'new').length;
  const dimensionMismatchScreenshots = results.filter(r => r.status === 'dimension_mismatch').length;

  // Calculate overall pass/fail status
  const passThreshold = 1; // 1% difference is acceptable
  const failedScreenshots = results.filter(r =>
    r.status === 'different' && r.diffPercentage > passThreshold ||
    r.status === 'missing' ||
    r.status === 'dimension_mismatch'
  ).length;

  const passed = failedScreenshots === 0;

  // Sort results by diff percentage (descending)
  results.sort((a, b) => b.diffPercentage - a.diffPercentage);

  // Generate HTML report
  const reportPath = path.join(reportsDir, `${date}_${testName}_baseline_vs_${runId}.html`);

  const html = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Visual Regression Report: ${testName}</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      margin: 0;
      padding: 20px;
      color: #333;
    }
    h1, h2, h3 {
      color: #2c3e50;
    }
    .summary {
      background-color: #f8f9fa;
      padding: 20px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    .summary-item {
      margin-bottom: 10px;
    }
    .status-identical {
      color: #27ae60;
    }
    .status-different {
      color: #e67e22;
    }
    .status-missing, .status-dimension_mismatch {
      color: #e74c3c;
    }
    .status-new {
      color: #3498db;
    }
    .comparison {
      margin-bottom: 40px;
      border: 1px solid #ddd;
      border-radius: 5px;
      overflow: hidden;
    }
    .comparison-header {
      background-color: #f8f9fa;
      padding: 10px 20px;
      border-bottom: 1px solid #ddd;
    }
    .comparison-content {
      display: flex;
      flex-wrap: wrap;
      padding: 20px;
    }
    .comparison-image {
      flex: 1;
      min-width: 300px;
      margin: 10px;
      text-align: center;
    }
    .comparison-image img {
      max-width: 100%;
      border: 1px solid #ddd;
    }
    .comparison-image-label {
      margin-top: 10px;
      font-weight: bold;
    }
    .diff-percentage {
      font-size: 1.2em;
      font-weight: bold;
    }
    .diff-percentage.high {
      color: #e74c3c;
    }
    .diff-percentage.medium {
      color: #e67e22;
    }
    .diff-percentage.low {
      color: #f1c40f;
    }
    .diff-percentage.none {
      color: #27ae60;
    }
    .overall-status {
      font-size: 1.5em;
      font-weight: bold;
      padding: 10px 20px;
      border-radius: 5px;
      display: inline-block;
      margin-bottom: 20px;
    }
    .overall-status.passed {
      background-color: #27ae60;
      color: white;
    }
    .overall-status.failed {
      background-color: #e74c3c;
      color: white;
    }
  </style>
</head>
<body>
  <h1>Visual Regression Report: ${testName}</h1>

  <div class="overall-status ${passed ? 'passed' : 'failed'}">
    ${passed ? 'PASSED' : 'FAILED'}
  </div>

  <div class="summary">
    <h2>Summary</h2>
    <div class="summary-item"><strong>Date:</strong> ${date}</div>
    <div class="summary-item"><strong>Test:</strong> ${testName}</div>
    <div class="summary-item"><strong>Run:</strong> ${runId}</div>
    <div class="summary-item"><strong>Threshold:</strong> ${threshold}</div>
    ${metadata ? `<div class="summary-item"><strong>Baseline:</strong> Created on ${metadata.date} using run ${metadata.runId}</div>` : ''}
    <div class="summary-item"><strong>Total Screenshots:</strong> ${totalScreenshots}</div>
    <div class="summary-item"><strong>Identical:</strong> <span class="status-identical">${identicalScreenshots}</span></div>
    <div class="summary-item"><strong>Different:</strong> <span class="status-different">${differentScreenshots}</span></div>
    <div class="summary-item"><strong>Missing:</strong> <span class="status-missing">${missingScreenshots}</span></div>
    <div class="summary-item"><strong>New:</strong> <span class="status-new">${newScreenshots}</span></div>
    <div class="summary-item"><strong>Dimension Mismatch:</strong> <span class="status-dimension_mismatch">${dimensionMismatchScreenshots}</span></div>
    <div class="summary-item"><strong>Failed Screenshots:</strong> ${failedScreenshots}</div>
  </div>

  <h2>Comparisons</h2>

  ${results.map(result => {
    const diffPercentageClass =
      result.status === 'identical' ? 'none' :
      result.diffPercentage < 1 ? 'low' :
      result.diffPercentage < 5 ? 'medium' : 'high';

    return `
    <div class="comparison">
      <div class="comparison-header">
        <h3>${result.filename}</h3>
        <div><strong>Status:</strong> <span class="status-${result.status}">${result.status}</span></div>
        <div><strong>Diff Percentage:</strong> <span class="diff-percentage ${diffPercentageClass}">${result.diffPercentage.toFixed(2)}%</span></div>
        ${result.dimensions ? `<div><strong>Dimensions:</strong> ${result.dimensions.width}x${result.dimensions.height}</div>` : ''}
      </div>
      <div class="comparison-content">
        ${result.baselinePath ? `
        <div class="comparison-image">
          <img src="${path.relative(reportsDir, path.join(diffDir, `baseline_${result.filename}`)).replace(/\\/g, '/')}" alt="Baseline">
          <div class="comparison-image-label">Baseline</div>
        </div>
        ` : ''}

        ${result.comparePath ? `
        <div class="comparison-image">
          <img src="${path.relative(reportsDir, path.join(diffDir, `compare_${result.filename}`)).replace(/\\/g, '/')}" alt="Compare">
          <div class="comparison-image-label">Compare</div>
        </div>
        ` : ''}

        ${result.diffPath ? `
        <div class="comparison-image">
          <img src="${path.relative(reportsDir, path.join(diffDir, `diff_${result.filename}`)).replace(/\\/g, '/')}" alt="Diff">
          <div class="comparison-image-label">Diff</div>
        </div>
        ` : ''}
      </div>
    </div>
    `;
  }).join('')}
</body>
</html>
  `;

  fs.writeFileSync(reportPath, html);

  console.log(`HTML report generated: ${reportPath}`);
}

// Run the comparison
compareWithBaseline({
  testName,
  runId,
  date,
  threshold
}).catch(error => {
  console.error('Error comparing screenshots:', error);
  process.exit(1);
});
