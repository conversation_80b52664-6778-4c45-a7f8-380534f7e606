/**
 * Test fixtures for QA Wolf Test Framework
 */

import { <PERSON><PERSON><PERSON>, BrowserContext, Page, chromium, firefox, webkit } from 'playwright';
import { TestFixture, TestFixtures } from './types';
import { getConfig } from '@qawolf/core';

// Get configuration
const config = getConfig();

/**
 * Browser fixture
 */
export const browserFixture: TestFixture<Browser> = {
  setup: async () => {
    const browser = await chromium.launch({
      headless: process.env.HEADLESS !== 'false'
    });
    
    return browser;
  },
  teardown: async (browser) => {
    await browser.close();
  }
};

/**
 * Browser context fixture
 */
export const browserContextFixture: TestFixture<BrowserContext> = {
  setup: async () => {
    const browser = await browserFixture.setup();
    const context = await browser.newContext();
    
    return context;
  },
  teardown: async (context) => {
    await context.close();
    await browserFixture.teardown(await context.browser());
  }
};

/**
 * Page fixture
 */
export const pageFixture: TestFixture<Page> = {
  setup: async () => {
    const context = await browserContextFixture.setup();
    const page = await context.newPage();
    
    return page;
  },
  teardown: async (page) => {
    const context = page.context();
    await page.close();
    await browserContextFixture.teardown(context);
  }
};

/**
 * Authenticated page fixture
 */
export const authenticatedPageFixture: TestFixture<Page> = {
  setup: async () => {
    const page = await pageFixture.setup();
    
    // Navigate to login page
    await page.goto(process.env.URL || 'https://app.lidostaging.com');
    
    // Fill in login credentials
    await page.fill('[data-test-id="SignInEmail"]', process.env.EMAIL || '<EMAIL>');
    await page.fill('[data-test-id="SignInPassword"]', process.env.PASSWORD || 'vhc!tGK289IS&');
    
    // Click login button
    await page.click(':text("Log in with email")');
    
    // Wait for navigation to complete
    await page.waitForNavigation();
    
    return page;
  },
  teardown: async (page) => {
    await pageFixture.teardown(page);
  }
};

/**
 * Storage state fixture
 */
export const storageStateFixture: TestFixture<string> = {
  setup: async () => {
    const page = await authenticatedPageFixture.setup();
    const storageStatePath = './storage-state.json';
    
    // Save storage state
    await page.context().storageState({ path: storageStatePath });
    
    // Close page
    await authenticatedPageFixture.teardown(page);
    
    return storageStatePath;
  },
  teardown: async (storageStatePath) => {
    // Nothing to do
  }
};

/**
 * Authenticated context fixture
 */
export const authenticatedContextFixture: TestFixture<BrowserContext> = {
  setup: async () => {
    const storageStatePath = await storageStateFixture.setup();
    const browser = await browserFixture.setup();
    
    // Create context with storage state
    const context = await browser.newContext({
      storageState: storageStatePath
    });
    
    return context;
  },
  teardown: async (context) => {
    await browserContextFixture.teardown(context);
  }
};

/**
 * Default fixtures
 */
export const defaultFixtures: TestFixtures = {
  browser: browserFixture,
  context: browserContextFixture,
  page: pageFixture,
  authenticatedPage: authenticatedPageFixture,
  storageState: storageStateFixture,
  authenticatedContext: authenticatedContextFixture
};

/**
 * Create fixture
 * @param name Fixture name
 * @param fixture Fixture
 */
export function createFixture<T>(name: string, fixture: TestFixture<T>): void {
  defaultFixtures[name] = fixture;
}

/**
 * Get fixture
 * @param name Fixture name
 * @returns Fixture
 */
export function getFixture<T>(name: string): TestFixture<T> {
  return defaultFixtures[name] as TestFixture<T>;
}

/**
 * Use fixture
 * @param name Fixture name
 * @returns Fixture value
 */
export async function useFixture<T>(name: string): Promise<T> {
  const fixture = getFixture<T>(name);
  return fixture.setup();
}

/**
 * Release fixture
 * @param name Fixture name
 * @param value Fixture value
 */
export async function releaseFixture<T>(name: string, value: T): Promise<void> {
  const fixture = getFixture<T>(name);
  await fixture.teardown(value);
}

/**
 * With fixture
 * @param name Fixture name
 * @param fn Function to run with fixture
 * @returns Function result
 */
export async function withFixture<T, R>(name: string, fn: (value: T) => Promise<R>): Promise<R> {
  const value = await useFixture<T>(name);
  
  try {
    return await fn(value);
  } finally {
    await releaseFixture<T>(name, value);
  }
}
