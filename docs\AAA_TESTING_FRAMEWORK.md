# AAA Testing Framework

This framework evaluates and improves test code to ensure it follows the Arrange-Act-Assert (AAA) pattern and meets quality standards.

## Overview

The AAA (Arrange-Act-Assert) pattern is a best practice for structuring tests:

1. **Arrange**: Set up the test environment and prerequisites
2. **Act**: Perform the actions being tested
3. **Assert**: Verify the expected outcomes

This framework provides tools to:
- Evaluate test files for AAA compliance
- Run tests and collect execution results
- Generate comprehensive reports
- Suggest and apply improvements to tests that don't meet standards

## Quality Criteria

Tests are evaluated based on the following criteria:

| Criterion | Points | Description |
|-----------|--------|-------------|
| Arrange Step | 20 | Proper test setup (browser launch, navigation, login) |
| Act Step | 20 | Clear action steps (clicking, typing, etc.) |
| Assert Step | 20 | Proper assertions to verify outcomes |
| Selectors | 10 | Using centralized selectors |
| Error Handling | 10 | Proper error handling with try/catch |
| Cleanup | 5 | Proper cleanup of resources |
| Documentation | 5 | Good documentation with JSDoc |
| No Hardcoded Credentials | 5 | Using environment variables for credentials |
| AAA Pattern | 5 | Explicitly following AAA pattern in comments |

A test must score at least 90% to meet the required standard.

## Scripts and Tools

### 1. AAA Validator

Our main validator script that analyzes test files for AAA compliance and generates detailed reports.

```bash
node validate-aaa.js
```

This script:
- Scans all test files in the `tests` directory
- Analyzes each file for AAA pattern compliance
- Generates both JSON and HTML reports in the `aaa-reports` directory
- Provides an overall compliance score and detailed recommendations

### 2. Test Evaluator

Analyzes individual test files for AAA compliance and generates a score.

```bash
node scripts/test_evaluator.js [file_path]
```

### 3. Test Runner

Runs tests and collects execution results.

```bash
node scripts/test_runner.js [test_pattern]
```

Optional `test_pattern` parameter to filter tests by name.

### 4. Report Generator

Generates a comprehensive HTML report combining evaluation and execution results.

```bash
node scripts/report_generator.js
```

### 5. AAA Validation Runner

Runs the entire validation process: evaluation, execution, and report generation.

```bash
node scripts/run_aaa_validation.js
```

### 6. Test Improver

Analyzes tests that don't meet standards and suggests improvements.

```bash
# Analyze a specific test file
node scripts/test_improver.js file.js

# Improve a specific test file
node scripts/test_improver.js --improve file.js

# Improve all test files below threshold
node scripts/test_improver.js --improve-all
```

## GitHub Actions Integration

We've integrated AAA validation into our CI/CD pipeline using GitHub Actions. The workflow file is located at `.github/workflows/aaa-validation.yml`.

The workflow:
1. Runs on every push to the main branch and pull requests
2. Checks out the code
3. Sets up Node.js
4. Installs dependencies
5. Runs the AAA validator
6. Uploads the AAA validation report as an artifact

This ensures that all code changes are automatically checked for AAA compliance before being merged.

Example workflow configuration:

```yaml
name: AAA Validation

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

jobs:
  validate-aaa:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run AAA validation
        run: node validate-aaa.js

      - name: Upload AAA validation report
        uses: actions/upload-artifact@v3
        with:
          name: aaa-validation-report
          path: aaa-reports/
          retention-days: 14
```

## Best Practices for AAA Pattern

### Arrange

- Set up the browser and page
- Navigate to the application
- Log in if required
- Set up test data or state

Example:
```javascript
// ARRANGE: Set up the test environment
const { browser, context } = await launch();
const page = await context.newPage();
await page.goto(process.env.URL || 'https://app.lidostaging.com');
await login(page);
```

### Act

- Perform the actions being tested
- Click buttons, fill forms, etc.
- Trigger the functionality under test

Example:
```javascript
// ACT: Perform the actions being tested
await page.click(selectors.newFileButton);
await page.fill(selectors.fileNameInput, 'Test File');
await page.click(selectors.createButton);
```

### Assert

- Verify the expected outcomes
- Use Playwright assertions
- Check that the UI reflects the expected state

Example:
```javascript
// ASSERT: Verify the expected outcomes
const fileTitle = page.locator(selectors.fileTitle).filter({ hasText: 'Test File' });
await expect(fileTitle).toBeVisible();
await expect(page.locator(selectors.fileViewer)).toBeVisible();
```

## Additional Best Practices

1. **Use centralized selectors**
   - Store selectors in a separate file
   - Import and use them in tests

2. **Handle errors properly**
   - Use try/catch blocks
   - Take screenshots on failure
   - Clean up resources in finally block

3. **Use environment variables for credentials**
   - Never hardcode credentials
   - Use process.env for sensitive data

4. **Add proper documentation**
   - Use JSDoc comments
   - Describe what the test is verifying
   - Mention that it follows the AAA pattern

5. **Clean up resources**
   - Close browser and context after tests
   - Use finally block to ensure cleanup happens

## Example of a Well-Structured Test

```javascript
/**
 * Create and Delete Operations Test
 *
 * This test verifies both creation and deletion functionality,
 * including handling the deletion confirmation dialog.
 *
 * It follows the AAA (Arrange-Act-Assert) pattern.
 */

// Import Playwright and selectors
const { chromium, expect } = require('@playwright/test');
const selectors = require('./selectors');

async function testCreateAndDelete() {
  let browser, context, page;

  try {
    // ARRANGE: Set up the test environment
    browser = await chromium.launch({ headless: false });
    context = await browser.newContext();
    page = await context.newPage();

    await page.goto(process.env.URL || 'https://app.lidostaging.com');
    await page.fill(selectors.emailInput, process.env.EMAIL);
    await page.fill(selectors.passwordInput, process.env.PASSWORD);
    await page.click(selectors.loginButton);

    // ACT: Create a new file
    await page.click(selectors.newFileButton);
    const fileName = 'Test File';
    await page.fill(selectors.fileNameInput, fileName);
    await page.click(selectors.createButton);

    // ASSERT: Verify file was created
    const fileTitle = page.locator(selectors.fileTitle).filter({ hasText: fileName });
    await expect(fileTitle).toBeVisible();

    // ACT: Delete the file
    await fileTitle.click();
    await page.click(selectors.moreButton);
    await page.click(selectors.deleteOption);
    await page.click(selectors.confirmDeleteButton);

    // ASSERT: Verify file was deleted
    await expect(fileTitle).toHaveCount(0);

  } catch (error) {
    console.error('Test failed:', error);
    await page.screenshot({ path: 'error-screenshot.png' });
    throw error;
  } finally {
    // Clean up resources
    if (browser) {
      await browser.close();
    }
  }
}
```

## Report Interpretation

The generated HTML report provides:
- Overall code quality score
- Test execution success rate
- Detailed breakdown of each test file
- Specific recommendations for improvement

A score of 90% or higher indicates that the tests meet the required standard for AAA compliance.

## QA Wolf CI Integration

We've integrated our testing framework with QA Wolf's CI system to automate test execution and reporting. This integration consists of three main components:

### 1. Deployment Notification

The `scripts/qawolf-notify-deployment.js` script notifies QA Wolf of a new deployment, which triggers the test runs.

```bash
node scripts/qawolf-notify-deployment.js
```

This script:
- Sends deployment information to QA Wolf's API
- Includes environment, branch, and commit details
- Saves the deployment response for reference

### 2. CI Greenlight Polling

The `scripts/qawolf-ci-greenlight.js` script polls the QA Wolf API to check if the tests have passed (greenlight).

```bash
node scripts/qawolf-ci-greenlight.js
```

This script:
- Polls the QA Wolf API at regular intervals
- Checks if tests have completed and passed
- Returns a success or failure status based on the greenlight

### 3. Report Generation

The `scripts/qawolf-generate-report.js` script generates a comprehensive report of QA Wolf test results.

```bash
node scripts/qawolf-generate-report.js
```

This script:
- Fetches test results from QA Wolf's API
- Calculates metrics like pass rate and average duration
- Generates an HTML report with detailed test information

### GitHub Actions Integration

We've integrated QA Wolf CI into our GitHub Actions workflow. The workflow file is located at `.github/workflows/qawolf-ci.yml`.

The workflow:
1. Runs on every push to the main branch and pull requests
2. Notifies QA Wolf of the deployment
3. Polls for CI greenlight
4. Generates a QA Wolf metrics report
5. Uploads the report as an artifact

Example workflow configuration:

```yaml
name: QA Wolf CI Integration

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

jobs:
  qawolf-ci:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Install QA Wolf CI SDK
        run: npm install @qawolf/ci-sdk

      - name: Notify QA Wolf of deployment
        run: node scripts/qawolf-notify-deployment.js
        env:
          QA_WOLF_API_KEY: ${{ secrets.QA_WOLF_API_KEY }}
          QA_WOLF_TEAM_ID: ${{ secrets.QA_WOLF_TEAM_ID }}

      - name: Run tests and wait for CI greenlight
        run: node scripts/qawolf-ci-greenlight.js
        env:
          QA_WOLF_API_KEY: ${{ secrets.QA_WOLF_API_KEY }}
          QA_WOLF_TEAM_ID: ${{ secrets.QA_WOLF_TEAM_ID }}

      - name: Generate QA Wolf metrics report
        run: node scripts/qawolf-generate-report.js
        env:
          QA_WOLF_API_KEY: ${{ secrets.QA_WOLF_API_KEY }}
          QA_WOLF_TEAM_ID: ${{ secrets.QA_WOLF_TEAM_ID }}

      - name: Upload QA Wolf report
        uses: actions/upload-artifact@v3
        with:
          name: qawolf-report
          path: qawolf-reports/
          retention-days: 30
```
