/**
 * AAA Validation Runner
 * 
 * This script runs the entire AAA validation process:
 * 1. Evaluates test files for AAA compliance
 * 2. Runs tests to collect execution results
 * 3. Generates a comprehensive report
 */

const path = require('path');
const fs = require('fs');
const { main: evaluateTests } = require('./test_evaluator');
const { main: runTests } = require('./test_runner');
const { main: generateReport } = require('./report_generator');

async function main() {
  console.log('=== AAA VALIDATION PROCESS ===\n');
  
  // Create reports directory if it doesn't exist
  const reportsDir = path.join(__dirname, '..', 'reports');
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir, { recursive: true });
  }
  
  // Step 1: Evaluate test files for AAA compliance
  console.log('Step 1: Evaluating test files for AAA compliance...');
  const evaluationResult = await evaluateTests();
  console.log(`Evaluation complete. Average score: ${evaluationResult.averageScore.toFixed(2)}%\n`);
  
  // Step 2: Run tests to collect execution results
  console.log('Step 2: Running tests to collect execution results...');
  const executionResult = await runTests();
  console.log(`Test execution complete. Success rate: ${executionResult.successRate.toFixed(2)}%\n`);
  
  // Step 3: Generate comprehensive report
  console.log('Step 3: Generating comprehensive report...');
  await generateReport();
  console.log('Report generation complete.\n');
  
  // Final result
  const passed = evaluationResult.averageScore >= 90 && executionResult.successRate >= 90;
  console.log('=== FINAL RESULT ===');
  console.log(`AAA Compliance Score: ${evaluationResult.averageScore.toFixed(2)}%`);
  console.log(`Test Execution Success: ${executionResult.successRate.toFixed(2)}%`);
  console.log(`Overall Status: ${passed ? 'PASSED' : 'FAILED'}`);
  
  if (!passed) {
    console.log('\nRecommendations:');
    if (evaluationResult.averageScore < 90) {
      console.log('- Improve AAA pattern implementation in tests');
      console.log('- Add proper assertions to verify test outcomes');
      console.log('- Implement proper error handling with try/catch blocks');
      console.log('- Use centralized selectors for better maintainability');
      console.log('- Add proper documentation to all test files');
    }
    
    if (executionResult.successRate < 90) {
      console.log('- Fix failing tests to improve execution success rate');
      console.log('- Implement more robust selectors to handle UI changes');
      console.log('- Add retry logic for flaky tests');
      console.log('- Improve error handling in test execution');
    }
  }
  
  console.log('\nDetailed report available at: reports/aaa-compliance-report.html');
}

// Run the script
if (require.main === module) {
  main().catch(console.error);
}
