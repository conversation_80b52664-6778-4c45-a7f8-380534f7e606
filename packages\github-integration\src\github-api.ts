/**
 * GitHub API for GitHub integration
 */

import { Octokit } from '@octokit/rest';
import { GitHubAPIOptions, GitHubRepository, GitHubBranch, GitHubCommit, GitHubPullRequest, GitHubIssue, GitHubPullRequestFilter, GitHubIssueFilter, GitHubPullRequestMergeMethod } from './types';
import { EventBus, EventType } from '@qawolf/core';

/**
 * GitHub API
 */
export class GitHubAPI {
  /**
   * API token
   */
  private token: string;
  
  /**
   * Owner
   */
  private owner: string;
  
  /**
   * Repository
   */
  private repo: string;
  
  /**
   * Base URL
   */
  private baseUrl: string;
  
  /**
   * Timeout in milliseconds
   */
  private timeout: number;
  
  /**
   * Octokit instance
   */
  private octokit: Octokit;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options GitHub API options
   */
  constructor(options: GitHubAPIOptions = {}) {
    this.token = options.token || '';
    this.owner = options.owner || '';
    this.repo = options.repo || '';
    this.baseUrl = options.baseUrl || 'https://api.github.com';
    this.timeout = options.timeout || 30000;
    
    this.octokit = new Octokit({
      auth: this.token,
      baseUrl: this.baseUrl,
      request: {
        timeout: this.timeout
      }
    });
    
    this.eventBus = EventBus.getInstance();
  }
  
  /**
   * Get repository
   * @param owner Owner
   * @param repo Repository
   * @returns Repository
   */
  async getRepository(owner?: string, repo?: string): Promise<GitHubRepository | null> {
    try {
      const response = await this.octokit.repos.get({
        owner: owner || this.owner,
        repo: repo || this.repo
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_API_GET_REPOSITORY, {
        repository: response.data
      });
      
      return response.data;
    } catch (error) {
      this.handleError(error, `Failed to get repository ${owner || this.owner}/${repo || this.repo}`);
      return null;
    }
  }
  
  /**
   * Get branch
   * @param branch Branch
   * @param owner Owner
   * @param repo Repository
   * @returns Branch
   */
  async getBranch(branch: string, owner?: string, repo?: string): Promise<GitHubBranch | null> {
    try {
      const response = await this.octokit.repos.getBranch({
        owner: owner || this.owner,
        repo: repo || this.repo,
        branch
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_API_GET_BRANCH, {
        branch: response.data
      });
      
      return response.data;
    } catch (error) {
      this.handleError(error, `Failed to get branch ${branch} for repository ${owner || this.owner}/${repo || this.repo}`);
      return null;
    }
  }
  
  /**
   * Get branches
   * @param owner Owner
   * @param repo Repository
   * @returns Branches
   */
  async getBranches(owner?: string, repo?: string): Promise<GitHubBranch[]> {
    try {
      const response = await this.octokit.repos.listBranches({
        owner: owner || this.owner,
        repo: repo || this.repo
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_API_GET_BRANCHES, {
        branches: response.data
      });
      
      return response.data as GitHubBranch[];
    } catch (error) {
      this.handleError(error, `Failed to get branches for repository ${owner || this.owner}/${repo || this.repo}`);
      return [];
    }
  }
  
  /**
   * Get commit
   * @param ref Commit reference
   * @param owner Owner
   * @param repo Repository
   * @returns Commit
   */
  async getCommit(ref: string, owner?: string, repo?: string): Promise<GitHubCommit | null> {
    try {
      const response = await this.octokit.repos.getCommit({
        owner: owner || this.owner,
        repo: repo || this.repo,
        ref
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_API_GET_COMMIT, {
        commit: response.data
      });
      
      return response.data;
    } catch (error) {
      this.handleError(error, `Failed to get commit ${ref} for repository ${owner || this.owner}/${repo || this.repo}`);
      return null;
    }
  }
  
  /**
   * Get commits
   * @param sha SHA
   * @param path Path
   * @param author Author
   * @param since Since
   * @param until Until
   * @param owner Owner
   * @param repo Repository
   * @returns Commits
   */
  async getCommits(sha?: string, path?: string, author?: string, since?: string, until?: string, owner?: string, repo?: string): Promise<GitHubCommit[]> {
    try {
      const response = await this.octokit.repos.listCommits({
        owner: owner || this.owner,
        repo: repo || this.repo,
        sha,
        path,
        author,
        since,
        until
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_API_GET_COMMITS, {
        commits: response.data
      });
      
      return response.data as GitHubCommit[];
    } catch (error) {
      this.handleError(error, `Failed to get commits for repository ${owner || this.owner}/${repo || this.repo}`);
      return [];
    }
  }
  
  /**
   * Get pull request
   * @param pullNumber Pull request number
   * @param owner Owner
   * @param repo Repository
   * @returns Pull request
   */
  async getPullRequest(pullNumber: number, owner?: string, repo?: string): Promise<GitHubPullRequest | null> {
    try {
      const response = await this.octokit.pulls.get({
        owner: owner || this.owner,
        repo: repo || this.repo,
        pull_number: pullNumber
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_API_GET_PULL_REQUEST, {
        pullRequest: response.data
      });
      
      return response.data;
    } catch (error) {
      this.handleError(error, `Failed to get pull request ${pullNumber} for repository ${owner || this.owner}/${repo || this.repo}`);
      return null;
    }
  }
  
  /**
   * Get pull requests
   * @param filter Pull request filter
   * @param owner Owner
   * @param repo Repository
   * @returns Pull requests
   */
  async getPullRequests(filter?: GitHubPullRequestFilter, owner?: string, repo?: string): Promise<GitHubPullRequest[]> {
    try {
      const response = await this.octokit.pulls.list({
        owner: owner || this.owner,
        repo: repo || this.repo,
        state: filter?.state,
        head: filter?.head,
        base: filter?.base,
        sort: filter?.sort,
        direction: filter?.direction
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_API_GET_PULL_REQUESTS, {
        pullRequests: response.data
      });
      
      return response.data;
    } catch (error) {
      this.handleError(error, `Failed to get pull requests for repository ${owner || this.owner}/${repo || this.repo}`);
      return [];
    }
  }
  
  /**
   * Create pull request
   * @param title Title
   * @param head Head
   * @param base Base
   * @param body Body
   * @param draft Draft
   * @param owner Owner
   * @param repo Repository
   * @returns Pull request
   */
  async createPullRequest(title: string, head: string, base: string, body?: string, draft?: boolean, owner?: string, repo?: string): Promise<GitHubPullRequest | null> {
    try {
      const response = await this.octokit.pulls.create({
        owner: owner || this.owner,
        repo: repo || this.repo,
        title,
        head,
        base,
        body,
        draft
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_API_CREATE_PULL_REQUEST, {
        pullRequest: response.data
      });
      
      return response.data;
    } catch (error) {
      this.handleError(error, `Failed to create pull request for repository ${owner || this.owner}/${repo || this.repo}`);
      return null;
    }
  }
  
  /**
   * Update pull request
   * @param pullNumber Pull request number
   * @param title Title
   * @param body Body
   * @param state State
   * @param base Base
   * @param owner Owner
   * @param repo Repository
   * @returns Pull request
   */
  async updatePullRequest(pullNumber: number, title?: string, body?: string, state?: 'open' | 'closed', base?: string, owner?: string, repo?: string): Promise<GitHubPullRequest | null> {
    try {
      const response = await this.octokit.pulls.update({
        owner: owner || this.owner,
        repo: repo || this.repo,
        pull_number: pullNumber,
        title,
        body,
        state,
        base
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_API_UPDATE_PULL_REQUEST, {
        pullRequest: response.data
      });
      
      return response.data;
    } catch (error) {
      this.handleError(error, `Failed to update pull request ${pullNumber} for repository ${owner || this.owner}/${repo || this.repo}`);
      return null;
    }
  }
  
  /**
   * Merge pull request
   * @param pullNumber Pull request number
   * @param commitTitle Commit title
   * @param commitMessage Commit message
   * @param mergeMethod Merge method
   * @param owner Owner
   * @param repo Repository
   * @returns True if merged, false otherwise
   */
  async mergePullRequest(pullNumber: number, commitTitle?: string, commitMessage?: string, mergeMethod?: GitHubPullRequestMergeMethod, owner?: string, repo?: string): Promise<boolean> {
    try {
      await this.octokit.pulls.merge({
        owner: owner || this.owner,
        repo: repo || this.repo,
        pull_number: pullNumber,
        commit_title: commitTitle,
        commit_message: commitMessage,
        merge_method: mergeMethod
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_API_MERGE_PULL_REQUEST, {
        pullNumber
      });
      
      return true;
    } catch (error) {
      this.handleError(error, `Failed to merge pull request ${pullNumber} for repository ${owner || this.owner}/${repo || this.repo}`);
      return false;
    }
  }
  
  /**
   * Get issue
   * @param issueNumber Issue number
   * @param owner Owner
   * @param repo Repository
   * @returns Issue
   */
  async getIssue(issueNumber: number, owner?: string, repo?: string): Promise<GitHubIssue | null> {
    try {
      const response = await this.octokit.issues.get({
        owner: owner || this.owner,
        repo: repo || this.repo,
        issue_number: issueNumber
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_API_GET_ISSUE, {
        issue: response.data
      });
      
      return response.data;
    } catch (error) {
      this.handleError(error, `Failed to get issue ${issueNumber} for repository ${owner || this.owner}/${repo || this.repo}`);
      return null;
    }
  }
  
  /**
   * Get issues
   * @param filter Issue filter
   * @param owner Owner
   * @param repo Repository
   * @returns Issues
   */
  async getIssues(filter?: GitHubIssueFilter, owner?: string, repo?: string): Promise<GitHubIssue[]> {
    try {
      const response = await this.octokit.issues.listForRepo({
        owner: owner || this.owner,
        repo: repo || this.repo,
        state: filter?.state,
        labels: filter?.labels?.join(','),
        assignee: filter?.assignee,
        creator: filter?.creator,
        mentioned: filter?.mentioned,
        since: filter?.since,
        sort: filter?.sort,
        direction: filter?.direction
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_API_GET_ISSUES, {
        issues: response.data
      });
      
      return response.data;
    } catch (error) {
      this.handleError(error, `Failed to get issues for repository ${owner || this.owner}/${repo || this.repo}`);
      return [];
    }
  }
  
  /**
   * Create issue
   * @param title Title
   * @param body Body
   * @param labels Labels
   * @param assignees Assignees
   * @param owner Owner
   * @param repo Repository
   * @returns Issue
   */
  async createIssue(title: string, body?: string, labels?: string[], assignees?: string[], owner?: string, repo?: string): Promise<GitHubIssue | null> {
    try {
      const response = await this.octokit.issues.create({
        owner: owner || this.owner,
        repo: repo || this.repo,
        title,
        body,
        labels,
        assignees
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_API_CREATE_ISSUE, {
        issue: response.data
      });
      
      return response.data;
    } catch (error) {
      this.handleError(error, `Failed to create issue for repository ${owner || this.owner}/${repo || this.repo}`);
      return null;
    }
  }
  
  /**
   * Update issue
   * @param issueNumber Issue number
   * @param title Title
   * @param body Body
   * @param state State
   * @param labels Labels
   * @param assignees Assignees
   * @param owner Owner
   * @param repo Repository
   * @returns Issue
   */
  async updateIssue(issueNumber: number, title?: string, body?: string, state?: 'open' | 'closed', labels?: string[], assignees?: string[], owner?: string, repo?: string): Promise<GitHubIssue | null> {
    try {
      const response = await this.octokit.issues.update({
        owner: owner || this.owner,
        repo: repo || this.repo,
        issue_number: issueNumber,
        title,
        body,
        state,
        labels,
        assignees
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_API_UPDATE_ISSUE, {
        issue: response.data
      });
      
      return response.data;
    } catch (error) {
      this.handleError(error, `Failed to update issue ${issueNumber} for repository ${owner || this.owner}/${repo || this.repo}`);
      return null;
    }
  }
  
  /**
   * Add comment to issue
   * @param issueNumber Issue number
   * @param body Body
   * @param owner Owner
   * @param repo Repository
   * @returns Comment
   */
  async addCommentToIssue(issueNumber: number, body: string, owner?: string, repo?: string): Promise<any | null> {
    try {
      const response = await this.octokit.issues.createComment({
        owner: owner || this.owner,
        repo: repo || this.repo,
        issue_number: issueNumber,
        body
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_API_ADD_COMMENT_TO_ISSUE, {
        comment: response.data
      });
      
      return response.data;
    } catch (error) {
      this.handleError(error, `Failed to add comment to issue ${issueNumber} for repository ${owner || this.owner}/${repo || this.repo}`);
      return null;
    }
  }
  
  /**
   * Handle error
   * @param error Error
   * @param message Error message
   */
  private handleError(error: any, message: string): void {
    console.error(message, error);
    
    // Emit event
    this.eventBus.emit(EventType.GITHUB_API_ERROR, {
      error,
      message
    });
  }
  
  /**
   * Get token
   * @returns API token
   */
  getToken(): string {
    return this.token;
  }
  
  /**
   * Get owner
   * @returns Owner
   */
  getOwner(): string {
    return this.owner;
  }
  
  /**
   * Get repository
   * @returns Repository
   */
  getRepo(): string {
    return this.repo;
  }
  
  /**
   * Get base URL
   * @returns Base URL
   */
  getBaseUrl(): string {
    return this.baseUrl;
  }
  
  /**
   * Get timeout
   * @returns Timeout in milliseconds
   */
  getTimeout(): number {
    return this.timeout;
  }
  
  /**
   * Set token
   * @param token API token
   * @returns This instance for chaining
   */
  setToken(token: string): GitHubAPI {
    this.token = token;
    
    this.octokit = new Octokit({
      auth: this.token,
      baseUrl: this.baseUrl,
      request: {
        timeout: this.timeout
      }
    });
    
    return this;
  }
  
  /**
   * Set owner
   * @param owner Owner
   * @returns This instance for chaining
   */
  setOwner(owner: string): GitHubAPI {
    this.owner = owner;
    return this;
  }
  
  /**
   * Set repository
   * @param repo Repository
   * @returns This instance for chaining
   */
  setRepo(repo: string): GitHubAPI {
    this.repo = repo;
    return this;
  }
  
  /**
   * Set base URL
   * @param baseUrl Base URL
   * @returns This instance for chaining
   */
  setBaseUrl(baseUrl: string): GitHubAPI {
    this.baseUrl = baseUrl;
    
    this.octokit = new Octokit({
      auth: this.token,
      baseUrl: this.baseUrl,
      request: {
        timeout: this.timeout
      }
    });
    
    return this;
  }
  
  /**
   * Set timeout
   * @param timeout Timeout in milliseconds
   * @returns This instance for chaining
   */
  setTimeout(timeout: number): GitHubAPI {
    this.timeout = timeout;
    
    this.octokit = new Octokit({
      auth: this.token,
      baseUrl: this.baseUrl,
      request: {
        timeout: this.timeout
      }
    });
    
    return this;
  }
}

/**
 * Create GitHub API
 * @param options GitHub API options
 * @returns GitHub API
 */
export function createGitHubAPI(options: GitHubAPIOptions = {}): GitHubAPI {
  return new GitHubAPI(options);
}
