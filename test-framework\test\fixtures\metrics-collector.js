/**
 * Test Metrics Collector
 * 
 * This module provides utilities for collecting test metrics.
 */

/**
 * Create a metrics collector
 * @returns {Object} - Metrics collector
 */
function createMetricsCollector() {
  const metrics = {
    executionTime: {
      total: 0,
      tests: {}
    },
    successRate: {
      total: 0,
      success: 0,
      failure: 0,
      tests: {}
    },
    selfHealing: {
      total: 0,
      success: 0,
      failure: 0,
      tests: {}
    },
    tokenUsage: {
      total: 0,
      tests: {}
    },
    resourceUsage: {
      cpu: {
        average: 0,
        peak: 0,
        tests: {}
      },
      memory: {
        average: 0,
        peak: 0,
        tests: {}
      }
    }
  };
  
  return {
    /**
     * Record test execution time
     * @param {string} testName - Test name
     * @param {number} duration - Duration in milliseconds
     */
    recordExecutionTime(testName, duration) {
      metrics.executionTime.tests[testName] = duration;
      metrics.executionTime.total += duration;
    },
    
    /**
     * Record test result
     * @param {string} testName - Test name
     * @param {boolean} success - Whether the test was successful
     */
    recordTestResult(testName, success) {
      metrics.successRate.tests[testName] = success;
      metrics.successRate.total++;
      
      if (success) {
        metrics.successRate.success++;
      } else {
        metrics.successRate.failure++;
      }
    },
    
    /**
     * Record self-healing result
     * @param {string} testName - Test name
     * @param {boolean} success - Whether the self-healing was successful
     */
    recordSelfHealingResult(testName, success) {
      if (!metrics.selfHealing.tests[testName]) {
        metrics.selfHealing.tests[testName] = {
          total: 0,
          success: 0,
          failure: 0
        };
      }
      
      metrics.selfHealing.tests[testName].total++;
      metrics.selfHealing.total++;
      
      if (success) {
        metrics.selfHealing.tests[testName].success++;
        metrics.selfHealing.success++;
      } else {
        metrics.selfHealing.tests[testName].failure++;
        metrics.selfHealing.failure++;
      }
    },
    
    /**
     * Record token usage
     * @param {string} testName - Test name
     * @param {number} tokens - Number of tokens used
     */
    recordTokenUsage(testName, tokens) {
      metrics.tokenUsage.tests[testName] = tokens;
      metrics.tokenUsage.total += tokens;
    },
    
    /**
     * Record resource usage
     * @param {string} testName - Test name
     * @param {Object} usage - Resource usage
     * @param {number} usage.cpu - CPU usage percentage
     * @param {number} usage.memory - Memory usage in MB
     */
    recordResourceUsage(testName, usage) {
      metrics.resourceUsage.cpu.tests[testName] = usage.cpu;
      metrics.resourceUsage.memory.tests[testName] = usage.memory;
      
      // Update peak values
      metrics.resourceUsage.cpu.peak = Math.max(metrics.resourceUsage.cpu.peak, usage.cpu);
      metrics.resourceUsage.memory.peak = Math.max(metrics.resourceUsage.memory.peak, usage.memory);
      
      // Update average values
      const cpuTests = Object.values(metrics.resourceUsage.cpu.tests);
      metrics.resourceUsage.cpu.average = cpuTests.reduce((a, b) => a + b, 0) / cpuTests.length;
      
      const memoryTests = Object.values(metrics.resourceUsage.memory.tests);
      metrics.resourceUsage.memory.average = memoryTests.reduce((a, b) => a + b, 0) / memoryTests.length;
    },
    
    /**
     * Get all metrics
     * @returns {Object} - All metrics
     */
    getMetrics() {
      return { ...metrics };
    },
    
    /**
     * Get success rate
     * @returns {number} - Success rate (0-1)
     */
    getSuccessRate() {
      return metrics.successRate.success / metrics.successRate.total;
    },
    
    /**
     * Get self-healing success rate
     * @returns {number} - Self-healing success rate (0-1)
     */
    getSelfHealingSuccessRate() {
      return metrics.selfHealing.success / metrics.selfHealing.total;
    },
    
    /**
     * Get average execution time
     * @returns {number} - Average execution time in milliseconds
     */
    getAverageExecutionTime() {
      const times = Object.values(metrics.executionTime.tests);
      return times.reduce((a, b) => a + b, 0) / times.length;
    },
    
    /**
     * Get average token usage
     * @returns {number} - Average token usage
     */
    getAverageTokenUsage() {
      const usages = Object.values(metrics.tokenUsage.tests);
      return usages.reduce((a, b) => a + b, 0) / usages.length;
    },
    
    /**
     * Reset metrics
     */
    resetMetrics() {
      metrics.executionTime = {
        total: 0,
        tests: {}
      };
      
      metrics.successRate = {
        total: 0,
        success: 0,
        failure: 0,
        tests: {}
      };
      
      metrics.selfHealing = {
        total: 0,
        success: 0,
        failure: 0,
        tests: {}
      };
      
      metrics.tokenUsage = {
        total: 0,
        tests: {}
      };
      
      metrics.resourceUsage = {
        cpu: {
          average: 0,
          peak: 0,
          tests: {}
        },
        memory: {
          average: 0,
          peak: 0,
          tests: {}
        }
      };
    }
  };
}

module.exports = {
  createMetricsCollector
};