/**
 * MCP Controller
 * 
 * This module provides a controller for interacting with the MCP optimizer.
 * It acts as the main interface between the test framework and the MCP optimizer.
 */

const mcpOptimizer = require('@qawolf/mcp-optimizer');
const { adaptSelectors, adaptTask, adaptResults } = require('./adapters');
const { createDefaultConfig, validateConfig, mergeConfigs } = require('./config');

/**
 * Controller for MCP operations
 */
class McpController {
  /**
   * Create a new McpController
   * @param {Object} [config] - Configuration options
   */
  constructor(config = {}) {
    this.config = mergeConfigs(createDefaultConfig(), config);
    validateConfig(this.config);
    
    // Initialize MCP tools if needed
    this.initialized = false;
    this.initializationPromise = null;
  }
  
  /**
   * Initialize MCP tools
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.initialized) {
      return;
    }
    
    if (this.initializationPromise) {
      return this.initializationPromise;
    }
    
    this.initializationPromise = (async () => {
      // Initialize Playwright MCP if needed
      if (this.config.autoStartPlaywrightMcp) {
        try {
          const isRunning = await mcpOptimizer.tools.playwrightMcp.isPlaywrightMCPRunning(this.config.playwrightMcpPort);
          
          if (!isRunning) {
            console.log('Starting Playwright MCP...');
            this.playwrightMcpServer = await mcpOptimizer.tools.playwrightMcp.startPlaywrightMCP({
              port: this.config.playwrightMcpPort,
              headless: this.config.headless,
              configPath: this.config.playwrightMcpConfigPath
            });
            console.log(`Playwright MCP started on port ${this.playwrightMcpServer.port}`);
          } else {
            console.log(`Playwright MCP is already running on port ${this.config.playwrightMcpPort}`);
          }
        } catch (error) {
          console.error('Failed to start Playwright MCP:', error);
          throw error;
        }
      }
      
      this.initialized = true;
    })();
    
    return this.initializationPromise;
  }
  
  /**
   * Optimize selectors for better reliability
   * @param {string|string[]} selectors - Selector(s) to optimize
   * @param {Object} [options] - Optimization options
   * @returns {Promise<Object>} - Optimization result
   */
  async optimizeSelectors(selectors, options = {}) {
    await this.initialize();
    
    const adaptedSelectors = adaptSelectors(selectors);
    
    const result = await mcpOptimizer.optimizeSelectors({
      selectors: adaptedSelectors,
      options: {
        generateFallbacks: options.generateFallbacks ?? this.config.generateFallbacks,
        prioritizeTestIds: options.prioritizeTestIds ?? this.config.prioritizeTestIds,
        ...options
      }
    });
    
    return adaptResults(result);
  }
  
  /**
   * Select the appropriate MCP tool for a task
   * @param {Object} task - Task to select a tool for
   * @param {Object} [options] - Selection options
   * @returns {Promise<Object>} - Selected tool and configuration
   */
  async selectMcpTool(task, options = {}) {
    await this.initialize();
    
    const adaptedTask = adaptTask(task);
    
    const result = await mcpOptimizer.selectMcpTool(adaptedTask, {
      tokenOptimized: options.tokenOptimized ?? this.config.tokenOptimized,
      performanceOptimized: options.performanceOptimized ?? this.config.performanceOptimized,
      ...options
    });
    
    return adaptResults(result);
  }
  
  /**
   * Analyze a screenshot to identify UI elements
   * @param {Buffer|string} screenshot - Screenshot data or path
   * @param {Object} [options] - Analysis options
   * @returns {Promise<Object>} - Analysis result
   */
  async analyzeScreenshot(screenshot, options = {}) {
    await this.initialize();
    
    const result = await mcpOptimizer.analyzeScreenshot({
      screenshot,
      ...options
    });
    
    return adaptResults(result);
  }
  
  /**
   * Generate a report on MCP usage
   * @param {Object} results - Results to include in the report
   * @param {Object} [options] - Report options
   * @returns {Promise<Object>} - Generated report
   */
  async generateReport(results, options = {}) {
    await this.initialize();
    
    const result = await mcpOptimizer.generateReport(results, {
      format: options.format ?? this.config.reportFormat,
      includeTimestamps: options.includeTimestamps ?? this.config.includeTimestamps,
      includeTokenUsage: options.includeTokenUsage ?? this.config.includeTokenUsage,
      ...options
    });
    
    return adaptResults(result);
  }
  
  /**
   * Clean up resources
   * @returns {Promise<void>}
   */
  async cleanup() {
    if (this.playwrightMcpServer) {
      console.log('Stopping Playwright MCP...');
      await this.playwrightMcpServer.stop();
      this.playwrightMcpServer = null;
    }
    
    this.initialized = false;
    this.initializationPromise = null;
  }
}

module.exports = McpController;