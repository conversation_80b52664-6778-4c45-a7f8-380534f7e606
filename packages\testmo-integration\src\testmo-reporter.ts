/**
 * Testmo reporter for Testmo integration
 */

import axios, { AxiosInstance } from 'axios';
import { TestmoReporterOptions, TestmoAPIResponse, TestmoTestRun, TestmoTestResult } from './types';
import { TestmoAPI } from './testmo-api';
import { EventBus, EventType } from '@qawolf/core';

/**
 * Testmo reporter
 */
export class TestmoReporter {
  /**
   * API key
   */
  private apiKey: string;
  
  /**
   * Host URL
   */
  private host: string;
  
  /**
   * Project ID
   */
  private projectId: number;
  
  /**
   * Test run ID
   */
  private testRunId?: number;
  
  /**
   * Test run name
   */
  private testRunName: string;
  
  /**
   * Timeout in milliseconds
   */
  private timeout: number;
  
  /**
   * Testmo API
   */
  private api: TestmoAPI;
  
  /**
   * Axios instance
   */
  private axios: AxiosInstance;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options Testmo reporter options
   */
  constructor(options: TestmoReporterOptions = {}) {
    this.apiKey = options.apiKey || '';
    this.host = options.host || 'https://api.testmo.io';
    this.projectId = options.projectId || 0;
    this.testRunId = options.testRunId;
    this.testRunName = options.testRunName || 'QA Wolf Test Run';
    this.timeout = options.timeout || 30000;
    
    this.api = new TestmoAPI({
      apiKey: this.apiKey,
      host: this.host,
      projectId: this.projectId,
      timeout: this.timeout
    });
    
    this.axios = axios.create({
      baseURL: this.host,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      }
    });
    
    this.eventBus = EventBus.getInstance();
  }
  
  /**
   * Create test run
   * @param name Test run name
   * @param description Test run description
   * @param milestoneId Test run milestone ID
   * @param assignedtoId Test run assignedto ID
   * @param configIds Test run config IDs
   * @param customFields Test run custom fields
   * @returns Test run
   */
  async createTestRun(
    name?: string,
    description?: string,
    milestoneId?: number,
    assignedtoId?: number,
    configIds?: number[],
    customFields?: Record<string, any>
  ): Promise<TestmoTestRun | null> {
    try {
      const testRun = await this.api.createTestRun(
        name || this.testRunName,
        description,
        milestoneId,
        assignedtoId,
        configIds,
        customFields,
        this.projectId
      );
      
      if (testRun) {
        this.testRunId = testRun.id;
        this.testRunName = testRun.name;
      }
      
      // Emit event
      this.eventBus.emit(EventType.TESTMO_REPORTER_CREATE_TEST_RUN, {
        testRun
      });
      
      return testRun;
    } catch (error) {
      this.handleError(error, `Failed to create test run with name ${name || this.testRunName}`);
      return null;
    }
  }
  
  /**
   * Get current test run
   * @returns Test run
   */
  async getCurrentTestRun(): Promise<TestmoTestRun | null> {
    if (!this.testRunId) {
      return null;
    }
    
    try {
      const testRun = await this.api.getTestRunById(this.testRunId, this.projectId);
      
      // Emit event
      this.eventBus.emit(EventType.TESTMO_REPORTER_GET_CURRENT_TEST_RUN, {
        testRun
      });
      
      return testRun;
    } catch (error) {
      this.handleError(error, `Failed to get current test run with ID ${this.testRunId}`);
      return null;
    }
  }
  
  /**
   * Complete test run
   * @returns Test run
   */
  async completeTestRun(): Promise<TestmoTestRun | null> {
    if (!this.testRunId) {
      return null;
    }
    
    try {
      const testRun = await this.api.updateTestRun(
        this.testRunId,
        undefined,
        undefined,
        undefined,
        undefined,
        true,
        undefined,
        this.projectId
      );
      
      // Emit event
      this.eventBus.emit(EventType.TESTMO_REPORTER_COMPLETE_TEST_RUN, {
        testRun
      });
      
      return testRun;
    } catch (error) {
      this.handleError(error, `Failed to complete test run with ID ${this.testRunId}`);
      return null;
    }
  }
  
  /**
   * Add test result
   * @param testId Test ID
   * @param statusId Status ID
   * @param comment Comment
   * @param version Version
   * @param elapsed Elapsed
   * @param defects Defects
   * @param assignedtoId Assignedto ID
   * @returns Test result
   */
  async addTestResult(
    testId: number,
    statusId: number,
    comment?: string,
    version?: string,
    elapsed?: string,
    defects?: string,
    assignedtoId?: number
  ): Promise<TestmoTestResult | null> {
    if (!this.testRunId) {
      const testRun = await this.createTestRun();
      
      if (!testRun) {
        return null;
      }
    }
    
    try {
      const testResult = await this.api.addTestResult(
        this.testRunId!,
        testId,
        statusId,
        comment,
        version,
        elapsed,
        defects,
        assignedtoId,
        this.projectId
      );
      
      // Emit event
      this.eventBus.emit(EventType.TESTMO_REPORTER_ADD_TEST_RESULT, {
        testResult
      });
      
      return testResult;
    } catch (error) {
      this.handleError(error, `Failed to add test result for test with ID ${testId}`);
      return null;
    }
  }
  
  /**
   * Add test results
   * @param results Test results
   * @returns Test results
   */
  async addTestResults(results: {
    testId: number;
    statusId: number;
    comment?: string;
    version?: string;
    elapsed?: string;
    defects?: string;
    assignedtoId?: number;
  }[]): Promise<TestmoTestResult[]> {
    if (!this.testRunId) {
      const testRun = await this.createTestRun();
      
      if (!testRun) {
        return [];
      }
    }
    
    const testResults: TestmoTestResult[] = [];
    
    for (const result of results) {
      try {
        const testResult = await this.api.addTestResult(
          this.testRunId!,
          result.testId,
          result.statusId,
          result.comment,
          result.version,
          result.elapsed,
          result.defects,
          result.assignedtoId,
          this.projectId
        );
        
        if (testResult) {
          testResults.push(testResult);
        }
      } catch (error) {
        this.handleError(error, `Failed to add test result for test with ID ${result.testId}`);
      }
    }
    
    // Emit event
    this.eventBus.emit(EventType.TESTMO_REPORTER_ADD_TEST_RESULTS, {
      testResults
    });
    
    return testResults;
  }
  
  /**
   * Get test results
   * @returns Test results
   */
  async getTestResults(): Promise<TestmoTestResult[]> {
    if (!this.testRunId) {
      return [];
    }
    
    try {
      const testResults = await this.api.getTestResults(this.testRunId, this.projectId);
      
      // Emit event
      this.eventBus.emit(EventType.TESTMO_REPORTER_GET_TEST_RESULTS, {
        testResults
      });
      
      return testResults;
    } catch (error) {
      this.handleError(error, `Failed to get test results for test run with ID ${this.testRunId}`);
      return [];
    }
  }
  
  /**
   * Handle error
   * @param error Error
   * @param message Error message
   */
  private handleError(error: any, message: string): void {
    console.error(message, error);
    
    // Emit event
    this.eventBus.emit(EventType.TESTMO_REPORTER_ERROR, {
      error,
      message
    });
  }
  
  /**
   * Get API key
   * @returns API key
   */
  getAPIKey(): string {
    return this.apiKey;
  }
  
  /**
   * Get host URL
   * @returns Host URL
   */
  getHost(): string {
    return this.host;
  }
  
  /**
   * Get project ID
   * @returns Project ID
   */
  getProjectId(): number {
    return this.projectId;
  }
  
  /**
   * Get test run ID
   * @returns Test run ID
   */
  getTestRunId(): number | undefined {
    return this.testRunId;
  }
  
  /**
   * Get test run name
   * @returns Test run name
   */
  getTestRunName(): string {
    return this.testRunName;
  }
  
  /**
   * Get timeout
   * @returns Timeout in milliseconds
   */
  getTimeout(): number {
    return this.timeout;
  }
  
  /**
   * Set API key
   * @param apiKey API key
   * @returns This instance for chaining
   */
  setAPIKey(apiKey: string): TestmoReporter {
    this.apiKey = apiKey;
    
    this.api.setAPIKey(apiKey);
    
    this.axios = axios.create({
      baseURL: this.host,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      }
    });
    
    return this;
  }
  
  /**
   * Set host URL
   * @param host Host URL
   * @returns This instance for chaining
   */
  setHost(host: string): TestmoReporter {
    this.host = host;
    
    this.api.setHost(host);
    
    this.axios = axios.create({
      baseURL: this.host,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      }
    });
    
    return this;
  }
  
  /**
   * Set project ID
   * @param projectId Project ID
   * @returns This instance for chaining
   */
  setProjectId(projectId: number): TestmoReporter {
    this.projectId = projectId;
    
    this.api.setProjectId(projectId);
    
    return this;
  }
  
  /**
   * Set test run ID
   * @param testRunId Test run ID
   * @returns This instance for chaining
   */
  setTestRunId(testRunId: number): TestmoReporter {
    this.testRunId = testRunId;
    return this;
  }
  
  /**
   * Set test run name
   * @param testRunName Test run name
   * @returns This instance for chaining
   */
  setTestRunName(testRunName: string): TestmoReporter {
    this.testRunName = testRunName;
    return this;
  }
  
  /**
   * Set timeout
   * @param timeout Timeout in milliseconds
   * @returns This instance for chaining
   */
  setTimeout(timeout: number): TestmoReporter {
    this.timeout = timeout;
    
    this.api.setTimeout(timeout);
    
    this.axios = axios.create({
      baseURL: this.host,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`
      }
    });
    
    return this;
  }
}

/**
 * Create Testmo reporter
 * @param options Testmo reporter options
 * @returns Testmo reporter
 */
export function createTestmoReporter(options: TestmoReporterOptions = {}): TestmoReporter {
  return new TestmoReporter(options);
}
