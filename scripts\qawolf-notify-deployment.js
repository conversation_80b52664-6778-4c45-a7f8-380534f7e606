/**
 * QA Wolf Deployment Notification Script
 * 
 * This script notifies QA <PERSON> of a new deployment, which triggers the test runs.
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  apiKey: process.env.QA_WOLF_API_KEY,
  teamId: process.env.QA_WOLF_TEAM_ID || 'clux0gjs50sb3ak01fnh7wvja',
  apiUrl: 'https://app.qawolf.com/api/ci',
  reportsDir: './qawolf-reports'
};

// Create reports directory if it doesn't exist
if (!fs.existsSync(config.reportsDir)) {
  fs.mkdirSync(config.reportsDir, { recursive: true });
}

// Get deployment information
const deploymentInfo = {
  teamId: config.teamId,
  environment: process.env.ENVIRONMENT || 'staging',
  branch: process.env.GITHUB_REF_NAME || 'main',
  commit: process.env.GITHUB_SHA || 'latest',
  buildUrl: process.env.GITHUB_SERVER_URL ? `${process.env.GITHUB_SERVER_URL}/${process.env.GITHUB_REPOSITORY}/actions/runs/${process.env.GITHUB_RUN_ID}` : null,
  timestamp: new Date().toISOString()
};

// Save deployment info to file
fs.writeFileSync(
  path.join(config.reportsDir, 'deployment-info.json'),
  JSON.stringify(deploymentInfo, null, 2)
);

// Notify QA Wolf of deployment
async function notifyDeployment() {
  try {
    console.log('Notifying QA Wolf of deployment...');
    console.log('Deployment info:', deploymentInfo);
    
    const response = await axios.post(
      `${config.apiUrl}/deployments`,
      deploymentInfo,
      {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${config.apiKey}`
        }
      }
    );
    
    console.log('QA Wolf deployment notification successful!');
    console.log('Response:', response.data);
    
    // Save deployment response to file
    fs.writeFileSync(
      path.join(config.reportsDir, 'deployment-response.json'),
      JSON.stringify(response.data, null, 2)
    );
    
    return response.data;
  } catch (error) {
    console.error('Error notifying QA Wolf of deployment:', error.message);
    
    if (error.response) {
      console.error('Response data:', error.response.data);
      console.error('Response status:', error.response.status);
    }
    
    // Save error to file
    fs.writeFileSync(
      path.join(config.reportsDir, 'deployment-error.json'),
      JSON.stringify({
        error: error.message,
        response: error.response ? {
          data: error.response.data,
          status: error.response.status
        } : null,
        timestamp: new Date().toISOString()
      }, null, 2)
    );
    
    process.exit(1);
  }
}

// Run the notification
notifyDeployment();
