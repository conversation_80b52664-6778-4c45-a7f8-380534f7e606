/**
 * Test Improver for AAA Pattern Compliance
 * 
 * This script analyzes test files that don't meet the AAA standard
 * and suggests improvements.
 */

const fs = require('fs');
const path = require('path');
const { evaluateTestFile } = require('./test_evaluator');

// Function to suggest improvements for a test file
function suggestImprovements(filePath) {
  const content = fs.readFileSync(filePath, 'utf8');
  const evaluation = evaluateTestFile(filePath);
  const suggestions = [];
  
  // Check if file needs improvement
  if (evaluation.totalScore >= 90) {
    return {
      file: path.basename(filePath),
      score: evaluation.totalScore,
      needsImprovement: false,
      message: 'This file already meets the AAA standard (score >= 90%).'
    };
  }
  
  // Suggest improvements based on evaluation
  if (!evaluation.documentation) {
    suggestions.push({
      type: 'documentation',
      message: 'Add proper JSDoc documentation to the file',
      example: `/**
 * Test Name
 * 
 * This test verifies [functionality].
 * It follows the AAA (Arrange-Act-Assert) pattern.
 */`
    });
  }
  
  if (!evaluation.usesAAA) {
    suggestions.push({
      type: 'aaaPattern',
      message: 'Add explicit AAA pattern comments to structure the test',
      example: `// ARRANGE: Set up the test environment
// ...

// ACT: Perform the actions being tested
// ...

// ASSERT: Verify the expected outcomes
// ...`
    });
  }
  
  if (!evaluation.hasArrange) {
    suggestions.push({
      type: 'arrange',
      message: 'Add proper Arrange step to set up the test environment',
      example: `// ARRANGE: Set up the test environment
const { browser, context } = await launch();
const page = await context.newPage();
await page.goto(process.env.URL || 'https://app.lidostaging.com');
await login(page);`
    });
  }
  
  if (!evaluation.hasAct) {
    suggestions.push({
      type: 'act',
      message: 'Add proper Act step to perform the actions being tested',
      example: `// ACT: Perform the actions being tested
await page.click(selectors.newFileButton);
await page.fill(selectors.fileNameInput, 'Test File');
await page.click(selectors.createButton);`
    });
  }
  
  if (!evaluation.hasAssert) {
    suggestions.push({
      type: 'assert',
      message: 'Add proper Assert step with Playwright assertions',
      example: `// ASSERT: Verify the expected outcomes
const fileTitle = page.locator(selectors.fileTitle).filter({ hasText: 'Test File' });
await expect(fileTitle).toBeVisible();
await expect(page.locator(selectors.fileViewer)).toBeVisible();`
    });
  }
  
  if (!evaluation.usesSelectors) {
    suggestions.push({
      type: 'selectors',
      message: 'Use centralized selectors for better maintainability',
      example: `// Import selectors
const selectors = require('./selectors');

// Use selectors in the test
await page.click(selectors.newFileButton);`
    });
  }
  
  if (!evaluation.errorHandling) {
    suggestions.push({
      type: 'errorHandling',
      message: 'Add proper error handling with try/catch blocks',
      example: `try {
  // Test code here
} catch (error) {
  console.error('Test failed:', error);
  // Take screenshot on failure
  await page.screenshot({ path: 'error-screenshot.png' });
  throw error;
} finally {
  // Cleanup code here
}`
    });
  }
  
  if (!evaluation.cleanup) {
    suggestions.push({
      type: 'cleanup',
      message: 'Add proper cleanup to close browser resources',
      example: `finally {
  // Make sure to close the browser even if there's an error
  if (browser) {
    await browser.close();
  }
}`
    });
  }
  
  if (!evaluation.noHardcodedCredentials) {
    suggestions.push({
      type: 'credentials',
      message: 'Use environment variables for credentials instead of hardcoding them',
      example: `// Use environment variables for credentials
await page.fill(selectors.emailInput, process.env.EMAIL);
await page.fill(selectors.passwordInput, process.env.PASSWORD);`
    });
  }
  
  return {
    file: path.basename(filePath),
    score: evaluation.totalScore,
    needsImprovement: true,
    suggestions
  };
}

// Function to improve a test file based on suggestions
function improveTestFile(filePath, createBackup = true) {
  // Create backup of original file
  if (createBackup) {
    const backupPath = `${filePath}.backup`;
    fs.copyFileSync(filePath, backupPath);
    console.log(`Backup created: ${backupPath}`);
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  const { needsImprovement, suggestions } = suggestImprovements(filePath);
  
  if (!needsImprovement) {
    console.log(`No improvements needed for ${path.basename(filePath)}`);
    return false;
  }
  
  let improvedContent = content;
  
  // Apply improvements based on suggestions
  for (const suggestion of suggestions) {
    switch (suggestion.type) {
      case 'documentation':
        if (!improvedContent.includes('/**')) {
          improvedContent = `${suggestion.example}\n\n${improvedContent}`;
        }
        break;
        
      case 'aaaPattern':
        // This is more complex and would require parsing the code structure
        // For now, just log the suggestion
        console.log(`Manual improvement needed: ${suggestion.message}`);
        console.log(`Example:\n${suggestion.example}`);
        break;
        
      case 'selectors':
        if (!improvedContent.includes('require(\'./selectors\')') && 
            !improvedContent.includes('require("./selectors")')) {
          // Add selectors import at the top of the file
          improvedContent = improvedContent.replace(
            /const \{ chromium[^}]*\} = require\([^)]*\);/,
            `$&\n\n// Import selectors\nconst selectors = require('./selectors');`
          );
        }
        break;
        
      case 'credentials':
        // Replace hardcoded credentials with environment variables
        improvedContent = improvedContent
          .replace(/['"]test@example\.com['"]/, 'process.env.EMAIL')
          .replace(/['"]password['"]/, 'process.env.PASSWORD');
        break;
        
      default:
        // Other improvements require more complex code analysis
        console.log(`Manual improvement needed: ${suggestion.message}`);
        console.log(`Example:\n${suggestion.example}`);
    }
  }
  
  // Write improved content back to file
  fs.writeFileSync(filePath, improvedContent);
  console.log(`Improvements applied to ${path.basename(filePath)}`);
  
  return true;
}

// Function to find and improve all test files below threshold
function improveAllTests(dirPath, threshold = 90) {
  const files = fs.readdirSync(dirPath);
  const improved = [];
  
  for (const file of files) {
    if (file.endsWith('.js') && !file.includes('selectors') && !file.endsWith('.config.js')) {
      const filePath = path.join(dirPath, file);
      const evaluation = evaluateTestFile(filePath);
      
      if (evaluation.totalScore < threshold) {
        console.log(`Improving ${file} (current score: ${evaluation.totalScore}%)...`);
        const wasImproved = improveTestFile(filePath);
        if (wasImproved) {
          improved.push(file);
        }
      }
    }
  }
  
  return improved;
}

// Main function
function main() {
  const testDir = path.join(__dirname, '..', 'tests', 'qawolf');
  
  // Check if a specific file was provided
  const specificFile = process.argv[2];
  
  if (specificFile) {
    const filePath = path.join(testDir, specificFile);
    if (fs.existsSync(filePath)) {
      console.log(`Analyzing ${specificFile}...`);
      const result = suggestImprovements(filePath);
      
      console.log(`\nFile: ${result.file}`);
      console.log(`Current Score: ${result.score}%`);
      
      if (result.needsImprovement) {
        console.log('\nSuggested Improvements:');
        result.suggestions.forEach((suggestion, index) => {
          console.log(`\n${index + 1}. ${suggestion.message}`);
          console.log('Example:');
          console.log(suggestion.example);
        });
        
        // Ask if user wants to apply improvements
        console.log('\nWould you like to apply these improvements? (y/n)');
        // In a real script, you would wait for user input here
        // For this example, we'll just log the message
        console.log('To apply improvements, run: node scripts/test_improver.js --improve ' + specificFile);
      } else {
        console.log('\n✅ This file already meets the AAA standard.');
      }
    } else {
      console.error(`File not found: ${specificFile}`);
    }
  } else if (process.argv.includes('--improve-all')) {
    // Improve all tests below threshold
    console.log('Improving all tests below threshold...');
    const improved = improveAllTests(testDir);
    console.log(`\nImproved ${improved.length} test files.`);
  } else if (process.argv.includes('--improve') && process.argv[3]) {
    // Improve specific file
    const fileToImprove = process.argv[3];
    const filePath = path.join(testDir, fileToImprove);
    
    if (fs.existsSync(filePath)) {
      console.log(`Improving ${fileToImprove}...`);
      improveTestFile(filePath);
    } else {
      console.error(`File not found: ${fileToImprove}`);
    }
  } else {
    console.log('Usage:');
    console.log('  node scripts/test_improver.js [file.js]         - Analyze a specific test file');
    console.log('  node scripts/test_improver.js --improve file.js - Improve a specific test file');
    console.log('  node scripts/test_improver.js --improve-all     - Improve all test files below threshold');
  }
}

// Export functions for use in other scripts
module.exports = {
  suggestImprovements,
  improveTestFile,
  improveAllTests
};

// Run the script if executed directly
if (require.main === module) {
  main();
}
