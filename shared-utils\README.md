# @qawolf/shared-utils

Shared utilities for QA Wolf testing framework.

## Installation

```bash
npm install @qawolf/shared-utils
```

## Usage

```javascript
// Import all utilities
const utils = require('@qawolf/shared-utils');

// Or import specific modules
const { config, screenshot, test } = require('@qawolf/shared-utils');

// Use configuration utilities
const appUrl = config.getConfig('appUrl', 'https://app.lidostaging.com');
const isCI = config.isCI();

// Use screenshot utilities
await screenshot.takeScreenshot(page, {
  testName: 'login_test',
  action: 'before-login',
  fullPage: false
});

// Use test utilities
const { browser, context, page } = await test.login({
  testName: 'login_test',
  useValidCredentials: true
});

// Use performance tracking
const performanceTracker = new test.PerformanceTracker();
performanceTracker.startOperation('login');
// ... perform login
performanceTracker.endOperation();
console.log(performanceTracker.generateReport());
```

## Available Utilities

### Configuration Utilities

- `config.loadConfigFromFile(configPath)` - Load configuration from a file
- `config.getConfig(key, defaultValue)` - Get configuration value
- `config.getAllConfig()` - Get all configuration values
- `config.setConfig(key, value)` - Set configuration value
- `config.getEnv(name, defaultValue)` - Get environment variable
- `config.getEnvBool(name, defaultValue)` - Get environment variable as boolean
- `config.getEnvNumber(name, defaultValue)` - Get environment variable as number
- `config.getEnvArray(name, separator, defaultValue)` - Get environment variable as array
- `config.isCI()` - Check if running in CI environment
- `config.isProduction()` - Check if running in production environment
- `config.isDevelopment()` - Check if running in development environment
- `config.isTest()` - Check if running in test environment

### Screenshot Utilities

- `screenshot.takeScreenshot(page, options)` - Take a screenshot
- `screenshot.takeErrorScreenshot(page, options)` - Take an error screenshot
- `screenshot.getTestScreenshots(testName, ticketId, date, runId)` - Get all screenshots for a test
- `screenshot.cleanupScreenshots(olderThanDays)` - Clean up old screenshots
- `screenshot.verifyScreenshotStructure()` - Verify screenshot structure
- `screenshot.moveStrayScreenshots(testName)` - Move stray screenshots

### Test Utilities

- `test.launchBrowser(options)` - Launch a browser for testing
- `test.login(options)` - Log in to the application
- `test.createFile(page, options)` - Create a new file in the application
- `test.calculateAAAComplianceScore(testSummary)` - Calculate AAA compliance score
- `test.printTestSummary(result, testId, testName, startTime)` - Print test summary
- `test.PerformanceTracker` - Performance tracking utility class

## License

MIT