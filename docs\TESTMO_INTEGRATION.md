# Testmo Integration

This document describes our integration with Testmo for test management.

## Overview

Testmo is a test management platform that helps teams organize, track, and report on their testing efforts. Our integration with Testmo allows us to:

1. **Automatically submit test results** from QA Wolf and Playwright tests to Testmo
2. **Track test execution history** over time
3. **Generate comprehensive reports** of test results
4. **Manage test cases** in a centralized location
5. **Link tests to requirements** and issues

## Integration Components

Our Testmo integration consists of several components:

### 1. Testmo API Integration

The `scripts/testmo-integration.js` script provides functions for interacting with the Testmo API:

- `createTestRun`: Creates a new test run in Testmo
- `submitTestResults`: Submits test results to Testmo
- `completeTestRun`: Completes a test run in Testmo
- `processTestResults`: Processes QA Wolf test results and submits them to Testmo

### 2. Playwright Results Converter

The `scripts/convert-playwright-results.js` script converts Playwright test results to QA Wolf format:

- `convertPlaywrightResults`: Converts Playwright test results to QA Wolf format
- `findLatestPlaywrightReport`: Finds the latest Playwright report file
- `convertAndSave`: Converts Playwright test results to QA Wolf format and saves to file

### 3. Testmo Integration Runner

The `scripts/run-testmo-integration.js` script runs the entire Testmo integration process:

- `runPlaywrightTests`: Runs Playwright tests
- `runTestmoIntegration`: Runs the entire Testmo integration process

### 4. GitHub Actions Workflow

The `.github/workflows/testmo-integration.yml` workflow automates the Testmo integration process:

- Runs on push to main branch, pull requests, and manual triggers
- Runs Playwright tests
- Converts test results to QA Wolf format
- Submits test results to Testmo
- Uploads reports as artifacts

## Configuration

The Testmo integration requires the following environment variables:

| Variable | Description |
|----------|-------------|
| `TESTMO_API_KEY` | API key for authenticating with Testmo's API |
| `TESTMO_URL` | URL of your Testmo instance (e.g., `https://your-instance.testmo.net`) |
| `TESTMO_PROJECT_ID` | Testmo project ID |
| `TESTMO_SOURCE_ID` | Source ID for test results (default: `qawolf`) |

These variables should be set as secrets in your GitHub repository.

## Usage

### Running the Testmo Integration

To run the Testmo integration manually:

```bash
node scripts/run-testmo-integration.js [test_pattern] [run_name] [run_description] [source] [branch] [commit] [environment]
```

Example:

```bash
node scripts/run-testmo-integration.js "tests/simplified-*.spec.js" "QA Wolf Test Run" "Automated test run" "qawolf" "main" "latest" "staging"
```

### Triggering the GitHub Actions Workflow

You can trigger the Testmo integration workflow manually from the GitHub Actions tab:

1. Go to the "Actions" tab in your GitHub repository
2. Select the "Testmo Integration" workflow
3. Click "Run workflow"
4. (Optional) Specify a test pattern and environment
5. Click "Run workflow"

## Test Result Format

Test results are submitted to Testmo in the following format:

```json
{
  "name": "Test Name",
  "status": "passed",
  "duration": 1000,
  "automation_content": "test-id",
  "comment": "Test comment",
  "steps": [
    {
      "description": "Step Description",
      "expected": "Expected Result",
      "actual": "Actual Result",
      "status": "passed"
    }
  ],
  "attachments": [
    {
      "name": "Screenshot",
      "content_type": "image/png",
      "data": "base64-encoded-data"
    }
  ]
}
```

## Testmo Project Structure

Our Testmo project is structured as follows:

### Test Cases

Test cases are organized into folders based on functionality:

- **Login**: Tests for login functionality
- **File Operations**: Tests for file operations
- **Self-Healing**: Tests for self-healing functionality
- **Performance**: Tests for performance tracking

### Test Runs

Test runs are created automatically by the Testmo integration process. Each test run includes:

- **Name**: Name of the test run (e.g., "QA Wolf Test Run - 2025-05-19")
- **Description**: Description of the test run
- **Source**: Source of the test run (e.g., "qawolf")
- **Branch**: Branch name
- **Commit**: Commit hash
- **Environment**: Environment name (e.g., "staging")

### Reports

Testmo provides several types of reports:

- **Test Run Reports**: Reports of individual test runs
- **Test Case Reports**: Reports of test case execution history
- **Milestone Reports**: Reports of test execution across milestones
- **Custom Reports**: Custom reports based on specific criteria

## Benefits

Our Testmo integration provides several benefits:

1. **Centralized Test Management**: All test cases and results are stored in a centralized location
2. **Historical Test Data**: Test execution history is tracked over time
3. **Comprehensive Reporting**: Detailed reports of test results
4. **Traceability**: Tests can be linked to requirements and issues
5. **Collaboration**: Team members can collaborate on test cases and results

## Troubleshooting

### Test Results Not Appearing in Testmo

If test results are not appearing in Testmo, check the following:

1. Verify that the Testmo API key is correct
2. Verify that the Testmo URL is correct
3. Verify that the Testmo project ID is correct
4. Check the logs for error messages
5. Check the Testmo reports directory for error files

### Playwright Tests Failing

If Playwright tests are failing, check the following:

1. Verify that Playwright browsers are installed
2. Verify that the test pattern is correct
3. Check the Playwright report for error messages
4. Try running the tests locally to debug

### GitHub Actions Workflow Failing

If the GitHub Actions workflow is failing, check the following:

1. Verify that all required secrets are set
2. Check the workflow logs for error messages
3. Try running the workflow with a simpler test pattern
4. Check if the tests pass locally

## Future Improvements

We plan to improve our Testmo integration in the following ways:

1. **Two-Way Synchronization**: Synchronize test cases between code and Testmo
2. **Test Case Management**: Create and update test cases in Testmo from code
3. **Requirements Integration**: Link tests to requirements in Testmo
4. **Issue Tracker Integration**: Link tests to issues in Linear or GitHub
5. **Custom Fields**: Add custom fields to test cases and results
6. **Automated Test Case Generation**: Generate test cases from code comments
