/**
 * Screenshot Cleanup Script
 * 
 * This script cleans up old screenshots to prevent disk space issues.
 * It deletes screenshot directories older than the specified number of days.
 * 
 * Usage:
 * node cleanup-screenshots.js [days]
 * 
 * Examples:
 * node cleanup-screenshots.js        # Use default retention period (7 days)
 * node cleanup-screenshots.js 14     # Keep screenshots for 14 days
 * 
 * @module cleanup-screenshots
 * <AUTHOR> Wolf Team
 * @date 2025-01-01
 */

const { cleanupScreenshots } = require('../src/utils/screenshot-utils');

async function main() {
  try {
    // Get the retention period from command line arguments
    const args = process.argv.slice(2);
    const days = args[0] ? parseInt(args[0], 10) : undefined;
    
    if (days !== undefined && (isNaN(days) || days < 1)) {
      console.error('Error: Days must be a positive number');
      process.exit(1);
    }
    
    console.log(`Cleaning up screenshots older than ${days || 7} days...`);
    await cleanupScreenshots(days);
    console.log('Screenshot cleanup complete');
  } catch (error) {
    console.error('Screenshot cleanup failed:', error);
    process.exit(1);
  }
}

// Run the script
main();
