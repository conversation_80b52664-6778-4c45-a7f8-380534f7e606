/**
 * Utilities for Testmo integration
 */

import * as dotenv from 'dotenv';
import { TestmoProject, TestmoTestCase, TestmoTestRun, TestmoTestResult, TestmoAutomation as TestmoAutomationType, TestmoAutomationResult } from './types';

// Load environment variables
dotenv.config();

/**
 * Get API key from environment
 * @returns API key
 */
export function getAPIKeyFromEnv(): string {
  return process.env.TESTMO_API_KEY || '';
}

/**
 * Get host URL from environment
 * @returns Host URL
 */
export function getHostFromEnv(): string {
  return process.env.TESTMO_HOST || 'https://api.testmo.io';
}

/**
 * Get project ID from environment
 * @returns Project ID
 */
export function getProjectIdFromEnv(): number {
  return Number(process.env.TESTMO_PROJECT_ID) || 0;
}

/**
 * Format duration
 * @param duration Duration in milliseconds
 * @returns Formatted duration
 */
export function formatDuration(duration: number): string {
  if (duration < 1000) {
    return `${duration}ms`;
  } else if (duration < 60000) {
    return `${(duration / 1000).toFixed(2)}s`;
  } else {
    const minutes = Math.floor(duration / 60000);
    const seconds = ((duration % 60000) / 1000).toFixed(2);
    return `${minutes}m ${seconds}s`;
  }
}

/**
 * Format date
 * @param date Date
 * @returns Formatted date
 */
export function formatDate(date: string | Date): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toISOString();
}

/**
 * Get test case status
 * @param testCase Test case
 * @returns Test case status
 */
export function getTestCaseStatus(testCase: TestmoTestCase): string {
  return testCase.type_id ? `Type ${testCase.type_id}` : 'Unknown';
}

/**
 * Get test run status
 * @param testRun Test run
 * @returns Test run status
 */
export function getTestRunStatus(testRun: TestmoTestRun): string {
  return testRun.is_completed ? 'Completed' : 'In Progress';
}

/**
 * Get test result status
 * @param testResult Test result
 * @returns Test result status
 */
export function getTestResultStatus(testResult: TestmoTestResult): string {
  switch (testResult.status_id) {
    case 1:
      return 'Passed';
    case 2:
      return 'Blocked';
    case 3:
      return 'Untested';
    case 4:
      return 'Retest';
    case 5:
      return 'Failed';
    default:
      return 'Custom';
  }
}

/**
 * Get automation status
 * @param automation Automation
 * @returns Automation status
 */
export function getAutomationStatus(automation: TestmoAutomationType): string {
  return automation.case_id ? 'Linked' : 'Unlinked';
}

/**
 * Get automation result status
 * @param automationResult Automation result
 * @returns Automation result status
 */
export function getAutomationResultStatus(automationResult: TestmoAutomationResult): string {
  switch (automationResult.status_id) {
    case 1:
      return 'Passed';
    case 2:
      return 'Blocked';
    case 3:
      return 'Untested';
    case 4:
      return 'Retest';
    case 5:
      return 'Failed';
    default:
      return 'Custom';
  }
}

/**
 * Get test run URL
 * @param testRun Test run
 * @returns Test run URL
 */
export function getTestRunURL(testRun: TestmoTestRun): string {
  return testRun.url;
}

/**
 * Get test run pass rate
 * @param testRun Test run
 * @returns Test run pass rate
 */
export function getTestRunPassRate(testRun: TestmoTestRun): number {
  const total = testRun.passed_count + testRun.blocked_count + testRun.untested_count + testRun.retest_count + testRun.failed_count + testRun.custom_status_count;
  
  if (total === 0) {
    return 0;
  }
  
  return testRun.passed_count / total;
}

/**
 * Get test run completion rate
 * @param testRun Test run
 * @returns Test run completion rate
 */
export function getTestRunCompletionRate(testRun: TestmoTestRun): number {
  const total = testRun.passed_count + testRun.blocked_count + testRun.untested_count + testRun.retest_count + testRun.failed_count + testRun.custom_status_count;
  
  if (total === 0) {
    return 0;
  }
  
  return (testRun.passed_count + testRun.blocked_count + testRun.failed_count + testRun.custom_status_count) / total;
}

/**
 * Get test run failure rate
 * @param testRun Test run
 * @returns Test run failure rate
 */
export function getTestRunFailureRate(testRun: TestmoTestRun): number {
  const total = testRun.passed_count + testRun.blocked_count + testRun.untested_count + testRun.retest_count + testRun.failed_count + testRun.custom_status_count;
  
  if (total === 0) {
    return 0;
  }
  
  return testRun.failed_count / total;
}

/**
 * Get test run block rate
 * @param testRun Test run
 * @returns Test run block rate
 */
export function getTestRunBlockRate(testRun: TestmoTestRun): number {
  const total = testRun.passed_count + testRun.blocked_count + testRun.untested_count + testRun.retest_count + testRun.failed_count + testRun.custom_status_count;
  
  if (total === 0) {
    return 0;
  }
  
  return testRun.blocked_count / total;
}

/**
 * Get test run summary
 * @param testRun Test run
 * @returns Test run summary
 */
export function getTestRunSummary(testRun: TestmoTestRun): string {
  const total = testRun.passed_count + testRun.blocked_count + testRun.untested_count + testRun.retest_count + testRun.failed_count + testRun.custom_status_count;
  
  return `${testRun.name}: ${testRun.passed_count} passed, ${testRun.failed_count} failed, ${testRun.blocked_count} blocked, ${testRun.untested_count} untested, ${testRun.retest_count} retest, ${testRun.custom_status_count} custom (${total} total)`;
}

/**
 * Get test case steps as string
 * @param testCase Test case
 * @returns Test case steps as string
 */
export function getTestCaseStepsAsString(testCase: TestmoTestCase): string {
  if (!testCase.steps || testCase.steps.length === 0) {
    return '';
  }
  
  return testCase.steps.map((step, index) => {
    let stepString = `${index + 1}. ${step.content}`;
    
    if (step.expected) {
      stepString += `\n   Expected: ${step.expected}`;
    }
    
    if (step.refs) {
      stepString += `\n   References: ${step.refs}`;
    }
    
    return stepString;
  }).join('\n\n');
}

/**
 * Get test case as string
 * @param testCase Test case
 * @returns Test case as string
 */
export function getTestCaseAsString(testCase: TestmoTestCase): string {
  let testCaseString = `# ${testCase.title}\n\n`;
  
  if (testCase.preconditions) {
    testCaseString += `## Preconditions\n\n${testCase.preconditions}\n\n`;
  }
  
  if (testCase.steps && testCase.steps.length > 0) {
    testCaseString += `## Steps\n\n${getTestCaseStepsAsString(testCase)}\n\n`;
  }
  
  if (testCase.expected) {
    testCaseString += `## Expected Result\n\n${testCase.expected}\n\n`;
  }
  
  if (testCase.refs) {
    testCaseString += `## References\n\n${testCase.refs}\n\n`;
  }
  
  return testCaseString;
}

/**
 * Get test result as string
 * @param testResult Test result
 * @returns Test result as string
 */
export function getTestResultAsString(testResult: TestmoTestResult): string {
  let testResultString = `# Test Result ${testResult.id}\n\n`;
  
  testResultString += `Status: ${getTestResultStatus(testResult)}\n\n`;
  
  if (testResult.comment) {
    testResultString += `## Comment\n\n${testResult.comment}\n\n`;
  }
  
  if (testResult.version) {
    testResultString += `Version: ${testResult.version}\n\n`;
  }
  
  if (testResult.elapsed) {
    testResultString += `Elapsed: ${testResult.elapsed}\n\n`;
  }
  
  if (testResult.defects) {
    testResultString += `Defects: ${testResult.defects}\n\n`;
  }
  
  return testResultString;
}

/**
 * Get automation as string
 * @param automation Automation
 * @returns Automation as string
 */
export function getAutomationAsString(automation: TestmoAutomationType): string {
  let automationString = `# ${automation.name}\n\n`;
  
  automationString += `Source: ${automation.source}\n\n`;
  automationString += `Source ID: ${automation.source_id}\n\n`;
  
  if (automation.case_id) {
    automationString += `Case ID: ${automation.case_id}\n\n`;
  }
  
  return automationString;
}

/**
 * Get automation result as string
 * @param automationResult Automation result
 * @returns Automation result as string
 */
export function getAutomationResultAsString(automationResult: TestmoAutomationResult): string {
  let automationResultString = `# Automation Result ${automationResult.id}\n\n`;
  
  automationResultString += `Status: ${getAutomationResultStatus(automationResult)}\n\n`;
  
  if (automationResult.comment) {
    automationResultString += `## Comment\n\n${automationResult.comment}\n\n`;
  }
  
  if (automationResult.version) {
    automationResultString += `Version: ${automationResult.version}\n\n`;
  }
  
  if (automationResult.elapsed) {
    automationResultString += `Elapsed: ${automationResult.elapsed}\n\n`;
  }
  
  if (automationResult.defects) {
    automationResultString += `Defects: ${automationResult.defects}\n\n`;
  }
  
  return automationResultString;
}
