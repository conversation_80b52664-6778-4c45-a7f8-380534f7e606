/**
 * QA Wolf API for QA Wolf integration
 */

import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { QAWolfAPIOptions, QAWolfAPIResponse, QAWolfTest, QAWolfRun, QAWolfArtifact, QAWolfDeployment } from './types';
import { EventBus, EventType } from '@qawolf/core';

/**
 * QA Wolf API
 */
export class QAWolfAPI {
  /**
   * API key
   */
  private apiKey: string;
  
  /**
   * Team ID
   */
  private teamId: string;
  
  /**
   * API URL
   */
  private apiUrl: string;
  
  /**
   * Timeout in milliseconds
   */
  private timeout: number;
  
  /**
   * Axios instance
   */
  private axios: AxiosInstance;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options QA Wolf API options
   */
  constructor(options: QAWolfAPIOptions = {}) {
    this.apiKey = options.apiKey || '';
    this.teamId = options.teamId || '';
    this.apiUrl = options.apiUrl || 'https://app.qawolf.com/api';
    this.timeout = options.timeout || 30000;
    
    this.axios = axios.create({
      baseURL: this.apiUrl,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        'X-Team-ID': this.teamId
      }
    });
    
    this.eventBus = EventBus.getInstance();
  }
  
  /**
   * Get tests
   * @returns Tests
   */
  async getTests(): Promise<QAWolfTest[]> {
    try {
      const response = await this.axios.get<QAWolfAPIResponse<QAWolfTest[]>>('/tests');
      
      // Emit event
      this.eventBus.emit(EventType.QAWOLF_API_GET_TESTS, {
        tests: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, 'Failed to get tests');
      return [];
    }
  }
  
  /**
   * Get test by ID
   * @param testId Test ID
   * @returns Test
   */
  async getTestById(testId: string): Promise<QAWolfTest | null> {
    try {
      const response = await this.axios.get<QAWolfAPIResponse<QAWolfTest>>(`/tests/${testId}`);
      
      // Emit event
      this.eventBus.emit(EventType.QAWOLF_API_GET_TEST_BY_ID, {
        test: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, `Failed to get test with ID ${testId}`);
      return null;
    }
  }
  
  /**
   * Get test by name
   * @param testName Test name
   * @returns Test
   */
  async getTestByName(testName: string): Promise<QAWolfTest | null> {
    try {
      const tests = await this.getTests();
      const test = tests.find(test => test.name === testName);
      
      if (test) {
        // Emit event
        this.eventBus.emit(EventType.QAWOLF_API_GET_TEST_BY_NAME, {
          test
        });
      }
      
      return test || null;
    } catch (error) {
      this.handleError(error, `Failed to get test with name ${testName}`);
      return null;
    }
  }
  
  /**
   * Create test
   * @param name Test name
   * @param code Test code
   * @param tags Test tags
   * @param metadata Test metadata
   * @returns Created test
   */
  async createTest(name: string, code: string, tags?: string[], metadata?: Record<string, any>): Promise<QAWolfTest | null> {
    try {
      const response = await this.axios.post<QAWolfAPIResponse<QAWolfTest>>('/tests', {
        name,
        code,
        tags,
        metadata
      });
      
      // Emit event
      this.eventBus.emit(EventType.QAWOLF_API_CREATE_TEST, {
        test: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, `Failed to create test with name ${name}`);
      return null;
    }
  }
  
  /**
   * Update test
   * @param testId Test ID
   * @param name Test name
   * @param code Test code
   * @param tags Test tags
   * @param metadata Test metadata
   * @returns Updated test
   */
  async updateTest(testId: string, name?: string, code?: string, tags?: string[], metadata?: Record<string, any>): Promise<QAWolfTest | null> {
    try {
      const response = await this.axios.put<QAWolfAPIResponse<QAWolfTest>>(`/tests/${testId}`, {
        name,
        code,
        tags,
        metadata
      });
      
      // Emit event
      this.eventBus.emit(EventType.QAWOLF_API_UPDATE_TEST, {
        test: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, `Failed to update test with ID ${testId}`);
      return null;
    }
  }
  
  /**
   * Delete test
   * @param testId Test ID
   * @returns True if deleted, false otherwise
   */
  async deleteTest(testId: string): Promise<boolean> {
    try {
      await this.axios.delete<QAWolfAPIResponse<void>>(`/tests/${testId}`);
      
      // Emit event
      this.eventBus.emit(EventType.QAWOLF_API_DELETE_TEST, {
        testId
      });
      
      return true;
    } catch (error) {
      this.handleError(error, `Failed to delete test with ID ${testId}`);
      return false;
    }
  }
  
  /**
   * Run test
   * @param testId Test ID
   * @param environment Environment
   * @param browser Browser
   * @param device Device
   * @param metadata Metadata
   * @returns Run
   */
  async runTest(testId: string, environment?: string, browser?: string, device?: string, metadata?: Record<string, any>): Promise<QAWolfRun | null> {
    try {
      const response = await this.axios.post<QAWolfAPIResponse<QAWolfRun>>(`/tests/${testId}/runs`, {
        environment,
        browser,
        device,
        metadata
      });
      
      // Emit event
      this.eventBus.emit(EventType.QAWOLF_API_RUN_TEST, {
        run: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, `Failed to run test with ID ${testId}`);
      return null;
    }
  }
  
  /**
   * Get runs
   * @param testId Test ID
   * @returns Runs
   */
  async getRuns(testId: string): Promise<QAWolfRun[]> {
    try {
      const response = await this.axios.get<QAWolfAPIResponse<QAWolfRun[]>>(`/tests/${testId}/runs`);
      
      // Emit event
      this.eventBus.emit(EventType.QAWOLF_API_GET_RUNS, {
        runs: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, `Failed to get runs for test with ID ${testId}`);
      return [];
    }
  }
  
  /**
   * Get run by ID
   * @param runId Run ID
   * @returns Run
   */
  async getRunById(runId: string): Promise<QAWolfRun | null> {
    try {
      const response = await this.axios.get<QAWolfAPIResponse<QAWolfRun>>(`/runs/${runId}`);
      
      // Emit event
      this.eventBus.emit(EventType.QAWOLF_API_GET_RUN_BY_ID, {
        run: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, `Failed to get run with ID ${runId}`);
      return null;
    }
  }
  
  /**
   * Cancel run
   * @param runId Run ID
   * @returns True if cancelled, false otherwise
   */
  async cancelRun(runId: string): Promise<boolean> {
    try {
      await this.axios.post<QAWolfAPIResponse<void>>(`/runs/${runId}/cancel`);
      
      // Emit event
      this.eventBus.emit(EventType.QAWOLF_API_CANCEL_RUN, {
        runId
      });
      
      return true;
    } catch (error) {
      this.handleError(error, `Failed to cancel run with ID ${runId}`);
      return false;
    }
  }
  
  /**
   * Get artifacts
   * @param runId Run ID
   * @returns Artifacts
   */
  async getArtifacts(runId: string): Promise<QAWolfArtifact[]> {
    try {
      const response = await this.axios.get<QAWolfAPIResponse<QAWolfArtifact[]>>(`/runs/${runId}/artifacts`);
      
      // Emit event
      this.eventBus.emit(EventType.QAWOLF_API_GET_ARTIFACTS, {
        artifacts: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, `Failed to get artifacts for run with ID ${runId}`);
      return [];
    }
  }
  
  /**
   * Upload artifact
   * @param runId Run ID
   * @param type Artifact type
   * @param name Artifact name
   * @param content Artifact content
   * @param metadata Artifact metadata
   * @returns Artifact
   */
  async uploadArtifact(runId: string, type: string, name: string, content: Buffer, metadata?: Record<string, any>): Promise<QAWolfArtifact | null> {
    try {
      const formData = new FormData();
      formData.append('type', type);
      formData.append('name', name);
      formData.append('file', new Blob([content]));
      
      if (metadata) {
        formData.append('metadata', JSON.stringify(metadata));
      }
      
      const config: AxiosRequestConfig = {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      };
      
      const response = await this.axios.post<QAWolfAPIResponse<QAWolfArtifact>>(`/runs/${runId}/artifacts`, formData, config);
      
      // Emit event
      this.eventBus.emit(EventType.QAWOLF_API_UPLOAD_ARTIFACT, {
        artifact: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, `Failed to upload artifact for run with ID ${runId}`);
      return null;
    }
  }
  
  /**
   * Get deployments
   * @returns Deployments
   */
  async getDeployments(): Promise<QAWolfDeployment[]> {
    try {
      const response = await this.axios.get<QAWolfAPIResponse<QAWolfDeployment[]>>('/deployments');
      
      // Emit event
      this.eventBus.emit(EventType.QAWOLF_API_GET_DEPLOYMENTS, {
        deployments: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, 'Failed to get deployments');
      return [];
    }
  }
  
  /**
   * Create deployment
   * @param environment Environment
   * @param url URL
   * @param metadata Metadata
   * @returns Deployment
   */
  async createDeployment(environment: string, url: string, metadata?: Record<string, any>): Promise<QAWolfDeployment | null> {
    try {
      const response = await this.axios.post<QAWolfAPIResponse<QAWolfDeployment>>('/deployments', {
        environment,
        url,
        metadata
      });
      
      // Emit event
      this.eventBus.emit(EventType.QAWOLF_API_CREATE_DEPLOYMENT, {
        deployment: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, `Failed to create deployment for environment ${environment}`);
      return null;
    }
  }
  
  /**
   * Handle error
   * @param error Error
   * @param message Error message
   */
  private handleError(error: any, message: string): void {
    console.error(message, error);
    
    // Emit event
    this.eventBus.emit(EventType.QAWOLF_API_ERROR, {
      error,
      message
    });
  }
  
  /**
   * Get API key
   * @returns API key
   */
  getAPIKey(): string {
    return this.apiKey;
  }
  
  /**
   * Get team ID
   * @returns Team ID
   */
  getTeamId(): string {
    return this.teamId;
  }
  
  /**
   * Get API URL
   * @returns API URL
   */
  getAPIUrl(): string {
    return this.apiUrl;
  }
  
  /**
   * Get timeout
   * @returns Timeout in milliseconds
   */
  getTimeout(): number {
    return this.timeout;
  }
  
  /**
   * Set API key
   * @param apiKey API key
   * @returns This instance for chaining
   */
  setAPIKey(apiKey: string): QAWolfAPI {
    this.apiKey = apiKey;
    
    this.axios = axios.create({
      baseURL: this.apiUrl,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        'X-Team-ID': this.teamId
      }
    });
    
    return this;
  }
  
  /**
   * Set team ID
   * @param teamId Team ID
   * @returns This instance for chaining
   */
  setTeamId(teamId: string): QAWolfAPI {
    this.teamId = teamId;
    
    this.axios = axios.create({
      baseURL: this.apiUrl,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        'X-Team-ID': this.teamId
      }
    });
    
    return this;
  }
  
  /**
   * Set API URL
   * @param apiUrl API URL
   * @returns This instance for chaining
   */
  setAPIUrl(apiUrl: string): QAWolfAPI {
    this.apiUrl = apiUrl;
    
    this.axios = axios.create({
      baseURL: this.apiUrl,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        'X-Team-ID': this.teamId
      }
    });
    
    return this;
  }
  
  /**
   * Set timeout
   * @param timeout Timeout in milliseconds
   * @returns This instance for chaining
   */
  setTimeout(timeout: number): QAWolfAPI {
    this.timeout = timeout;
    
    this.axios = axios.create({
      baseURL: this.apiUrl,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        'X-Team-ID': this.teamId
      }
    });
    
    return this;
  }
}

/**
 * Create QA Wolf API
 * @param options QA Wolf API options
 * @returns QA Wolf API
 */
export function createQAWolfAPI(options: QAWolfAPIOptions = {}): QAWolfAPI {
  return new QAWolfAPI(options);
}
