/**
 * AAA Compliance Configuration
 * 
 * This module provides configuration options for AAA compliance validation.
 */

/**
 * Default AAA compliance configuration
 */
const DEFAULT_CONFIG = {
  // General
  enabled: true,
  minimumScore: 0.9,
  generateRecommendations: true,
  
  // Scoring weights
  scoringWeights: {
    arrangementClarity: 0.3,
    actionFocus: 0.3,
    assertionCompleteness: 0.3,
    documentation: 0.1
  },
  
  // Validation rules
  rules: {
    // Arrangement rules
    arrangement: {
      requireArrangeComment: true,
      requireClearSeparation: true,
      requireProperSetup: true
    },
    
    // Action rules
    action: {
      requireActComment: true,
      requireSingleFocusedAction: true,
      requireErrorHandling: true
    },
    
    // Assertion rules
    assertion: {
      requireAssertComment: true,
      requireMultipleAssertions: true,
      requireExplicitMessages: true
    },
    
    // Documentation rules
    documentation: {
      requirePurposeStatement: true,
      requireInputOutputDocumentation: true,
      requireEdgeCaseDocumentation: false
    }
  },
  
  // Reporting
  reporting: {
    includeScoreBreakdown: true,
    includeRecommendations: true,
    includeCodeSnippets: true
  }
};

/**
 * Get AAA compliance configuration
 * 
 * @param {Object} [overrides] - Configuration overrides
 * @returns {Object} - AAA compliance configuration
 */
function getConfig(overrides = {}) {
  return {
    ...DEFAULT_CONFIG,
    ...overrides,
    // Merge nested objects
    scoringWeights: {
      ...DEFAULT_CONFIG.scoringWeights,
      ...overrides.scoringWeights
    },
    rules: {
      ...DEFAULT_CONFIG.rules,
      ...overrides.rules,
      // Merge nested rule objects
      arrangement: {
        ...DEFAULT_CONFIG.rules.arrangement,
        ...overrides.rules?.arrangement
      },
      action: {
        ...DEFAULT_CONFIG.rules.action,
        ...overrides.rules?.action
      },
      assertion: {
        ...DEFAULT_CONFIG.rules.assertion,
        ...overrides.rules?.assertion
      },
      documentation: {
        ...DEFAULT_CONFIG.rules.documentation,
        ...overrides.rules?.documentation
      }
    },
    reporting: {
      ...DEFAULT_CONFIG.reporting,
      ...overrides.reporting
    }
  };
}

module.exports = {
  DEFAULT_CONFIG,
  getConfig
};
