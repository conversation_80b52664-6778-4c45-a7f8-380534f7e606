#!/usr/bin/env node

/**
 * Create Release Script
 * 
 * This script creates a GitHub release.
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');
const readline = require('readline');

// Create readline interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

/**
 * Ask a question
 * @param {string} question - Question to ask
 * @param {string} [defaultValue] - Default value
 * @returns {Promise<string>} - User's answer
 */
function ask(question, defaultValue) {
  const defaultText = defaultValue ? ` (${defaultValue})` : '';
  return new Promise((resolve) => {
    rl.question(`${question}${defaultText}: `, (answer) => {
      resolve(answer || defaultValue);
    });
  });
}

/**
 * Ask a yes/no question
 * @param {string} question - Question to ask
 * @param {boolean} [defaultValue=true] - Default value
 * @returns {Promise<boolean>} - User's answer
 */
async function askYesNo(question, defaultValue = true) {
  const defaultText = defaultValue ? 'Y/n' : 'y/N';
  const answer = await ask(`${question} [${defaultText}]`);
  
  if (!answer) {
    return defaultValue;
  }
  
  return answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes';
}

/**
 * Get the current version
 * @returns {string} - Current version
 */
function getCurrentVersion() {
  const packageJsonPath = path.join(process.cwd(), 'test-framework/package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  return packageJson.version;
}

/**
 * Get the next version
 * @param {string} currentVersion - Current version
 * @param {string} releaseType - Release type (major, minor, patch)
 * @returns {string} - Next version
 */
function getNextVersion(currentVersion, releaseType) {
  const [major, minor, patch] = currentVersion.split('.').map(Number);
  
  switch (releaseType) {
    case 'major':
      return `${major + 1}.0.0`;
    case 'minor':
      return `${major}.${minor + 1}.0`;
    case 'patch':
      return `${major}.${minor}.${patch + 1}`;
    default:
      return currentVersion;
  }
}

/**
 * Update version in package.json files
 * @param {string} version - New version
 * @returns {void}
 */
function updateVersions(version) {
  console.log(`Updating versions to ${version}...`);
  execSync(`node scripts/deploy/version-packages.js ${version}`, { stdio: 'inherit' });
}

/**
 * Generate a changelog
 * @param {string} version - New version
 * @param {string} previousVersion - Previous version
 * @returns {string} - Changelog
 */
function generateChangelog(version, previousVersion) {
  console.log('Generating changelog...');
  
  // Get commits since previous version
  const commits = execSync(`git log --pretty=format:"%h %s" ${previousVersion}..HEAD`, { encoding: 'utf8' }).trim().split('\n');
  
  // Group commits by type
  const commitsByType = {
    feat: [],
    fix: [],
    docs: [],
    style: [],
    refactor: [],
    perf: [],
    test: [],
    build: [],
    ci: [],
    chore: [],
    revert: [],
    other: []
  };
  
  for (const commit of commits) {
    const match = commit.match(/^([a-f0-9]+) (feat|fix|docs|style|refactor|perf|test|build|ci|chore|revert)(\(.+\))?:\s(.+)$/);
    
    if (match) {
      const [, hash, type, scope, message] = match;
      commitsByType[type].push({ hash, scope, message });
    } else {
      const [hash, message] = commit.split(' ');
      commitsByType.other.push({ hash, message });
    }
  }
  
  // Generate changelog
  let changelog = `# ${version} (${new Date().toISOString().split('T')[0]})\n\n`;
  
  // Features
  if (commitsByType.feat.length > 0) {
    changelog += '## Features\n\n';
    
    for (const commit of commitsByType.feat) {
      const scope = commit.scope ? ` ${commit.scope}` : '';
      changelog += `- ${commit.message} ([${commit.hash}](https://github.com/sneezyxl/QAWolfeesojc/commit/${commit.hash}))\n`;
    }
    
    changelog += '\n';
  }
  
  // Bug Fixes
  if (commitsByType.fix.length > 0) {
    changelog += '## Bug Fixes\n\n';
    
    for (const commit of commitsByType.fix) {
      const scope = commit.scope ? ` ${commit.scope}` : '';
      changelog += `- ${commit.message} ([${commit.hash}](https://github.com/sneezyxl/QAWolfeesojc/commit/${commit.hash}))\n`;
    }
    
    changelog += '\n';
  }
  
  // Documentation
  if (commitsByType.docs.length > 0) {
    changelog += '## Documentation\n\n';
    
    for (const commit of commitsByType.docs) {
      const scope = commit.scope ? ` ${commit.scope}` : '';
      changelog += `- ${commit.message} ([${commit.hash}](https://github.com/sneezyxl/QAWolfeesojc/commit/${commit.hash}))\n`;
    }
    
    changelog += '\n';
  }
  
  // Performance
  if (commitsByType.perf.length > 0) {
    changelog += '## Performance\n\n';
    
    for (const commit of commitsByType.perf) {
      const scope = commit.scope ? ` ${commit.scope}` : '';
      changelog += `- ${commit.message} ([${commit.hash}](https://github.com/sneezyxl/QAWolfeesojc/commit/${commit.hash}))\n`;
    }
    
    changelog += '\n';
  }
  
  // Other Changes
  const otherTypes = ['style', 'refactor', 'test', 'build', 'ci', 'chore', 'revert', 'other'];
  const otherCommits = otherTypes.flatMap(type => commitsByType[type]);
  
  if (otherCommits.length > 0) {
    changelog += '## Other Changes\n\n';
    
    for (const commit of otherCommits) {
      if (commit.message) {
        changelog += `- ${commit.message} ([${commit.hash}](https://github.com/sneezyxl/QAWolfeesojc/commit/${commit.hash}))\n`;
      } else {
        changelog += `- ${commit.hash}\n`;
      }
    }
    
    changelog += '\n';
  }
  
  return changelog;
}

/**
 * Create a GitHub release
 * @param {string} version - Release version
 * @param {string} changelog - Release changelog
 * @returns {void}
 */
function createGitHubRelease(version, changelog) {
  console.log('Creating GitHub release...');
  
  // Create a temporary file with the changelog
  const changelogPath = path.join(process.cwd(), 'CHANGELOG.md');
  fs.writeFileSync(changelogPath, changelog, 'utf8');
  
  // Create a tag
  execSync(`git tag -a v${version} -m "Release v${version}"`, { stdio: 'inherit' });
  
  // Push the tag
  execSync('git push --tags', { stdio: 'inherit' });
  
  // Create a release
  execSync(`gh release create v${version} --title "v${version}" --notes-file CHANGELOG.md`, { stdio: 'inherit' });
  
  // Remove the temporary file
  fs.unlinkSync(changelogPath);
}

/**
 * Run the release creation
 * @returns {Promise<void>}
 */
async function runCreateRelease() {
  try {
    console.log('Creating a new release...');
    
    // Get current version
    const currentVersion = getCurrentVersion();
    console.log(`Current version: ${currentVersion}`);
    
    // Ask for release type
    const releaseType = await ask('Enter release type (major, minor, patch)', 'patch');
    
    // Get next version
    const nextVersion = getNextVersion(currentVersion, releaseType);
    console.log(`Next version: ${nextVersion}`);
    
    // Confirm
    const confirm = await askYesNo(`Create release ${nextVersion}?`);
    
    if (!confirm) {
      console.log('Release creation cancelled');
      rl.close();
      return;
    }
    
    // Update versions
    updateVersions(nextVersion);
    
    // Generate changelog
    const changelog = generateChangelog(nextVersion, `v${currentVersion}`);
    
    // Show changelog
    console.log('Changelog:');
    console.log(changelog);
    
    // Confirm changelog
    const confirmChangelog = await askYesNo('Is the changelog correct?');
    
    if (!confirmChangelog) {
      console.log('Release creation cancelled');
      rl.close();
      return;
    }
    
    // Create GitHub release
    createGitHubRelease(nextVersion, changelog);
    
    console.log(`Release ${nextVersion} created successfully!`);
    
    rl.close();
  } catch (error) {
    console.error('Release creation failed:', error.message);
    process.exit(1);
  }
}

// Run the script
runCreateRelease();