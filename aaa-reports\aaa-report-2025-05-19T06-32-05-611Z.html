
<!DOCTYPE html>
<html>
<head>
  <title>AAA Compliance Report</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
    }
    
    h1, h2, h3 {
      margin-top: 0;
    }
    
    .summary {
      background-color: #f5f5f5;
      border-radius: 5px;
      padding: 15px;
      margin-bottom: 20px;
    }
    
    .overall-score {
      font-size: 24px;
      font-weight: bold;
    }
    
    .test-results {
      margin-top: 20px;
    }
    
    table {
      width: 100%;
      border-collapse: collapse;
    }
    
    th, td {
      padding: 8px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    
    th {
      background-color: #f2f2f2;
    }
    
    .passed {
      color: green;
    }
    
    .failed {
      color: red;
    }
    
    .error {
      color: orange;
    }
    
    .recommendations {
      margin-top: 10px;
      padding: 10px;
      background-color: #f9f9f9;
      border-left: 3px solid #ccc;
    }
    
    .file-details {
      margin-top: 20px;
      padding: 10px;
      background-color: #f9f9f9;
      border-radius: 5px;
    }
    
    .file-details h3 {
      margin-top: 0;
    }
    
    .aspect-score {
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
    }
    
    .aspect-score .score-bar {
      flex-grow: 1;
      margin: 0 10px;
      height: 10px;
      background-color: #eee;
      border-radius: 5px;
      overflow: hidden;
    }
    
    .aspect-score .score-bar .score-fill {
      height: 100%;
      background-color: #4CAF50;
    }
  </style>
</head>
<body>
  <h1>AAA Compliance Report</h1>
  <p>Generated: 5/19/2025, 1:32:05 AM</p>
  
  <div class="summary">
    <h2>Summary</h2>
    <p class="overall-score">Overall Score: 10.57%</p>
    <p>Total Files: 23</p>
    <p>Passed Files: 0</p>
    <p>Failed Files: 23</p>
    <p>Error Files: 0</p>
  </div>
  
  <div class="test-results">
    <h2>Test Results</h2>
    <table>
      <tr>
        <th>File</th>
        <th>Score</th>
        <th>Status</th>
      </tr>
      
          <tr>
            <td>tests\e2e\file-operations.e2e.spec.js</td>
            <td>0.00%</td>
            <td class="failed">FAILED</td>
          </tr>
        
          <tr>
            <td>tests\e2e\login.e2e.spec.js</td>
            <td>0.00%</td>
            <td class="failed">FAILED</td>
          </tr>
        
          <tr>
            <td>tests\integration\cross-component.integration.spec.js</td>
            <td>0.00%</td>
            <td class="failed">FAILED</td>
          </tr>
        
          <tr>
            <td>tests\integration\mcp-optimizer.integration.spec.js</td>
            <td>0.00%</td>
            <td class="failed">FAILED</td>
          </tr>
        
          <tr>
            <td>tests\integration\self-healing.integration.spec.js</td>
            <td>0.00%</td>
            <td class="failed">FAILED</td>
          </tr>
        
          <tr>
            <td>tests\integration\shared-utils.integration.spec.js</td>
            <td>0.00%</td>
            <td class="failed">FAILED</td>
          </tr>
        
          <tr>
            <td>tests\performance\execution-time.spec.js</td>
            <td>0.00%</td>
            <td class="failed">FAILED</td>
          </tr>
        
          <tr>
            <td>tests\performance\token-usage.spec.js</td>
            <td>0.00%</td>
            <td class="failed">FAILED</td>
          </tr>
        
          <tr>
            <td>tests\playwright-mcp\examples\create_delete.spec.js</td>
            <td>18.00%</td>
            <td class="failed">FAILED</td>
          </tr>
        
          <tr>
            <td>tests\playwright-mcp\examples\login.spec.js</td>
            <td>27.00%</td>
            <td class="failed">FAILED</td>
          </tr>
        
          <tr>
            <td>tests\playwright-mcp\generated\login_create_delete.spec.js</td>
            <td>36.00%</td>
            <td class="failed">FAILED</td>
          </tr>
        
          <tr>
            <td>tests\playwright-mcp\generated\login_create_delete_debug.spec.js</td>
            <td>36.00%</td>
            <td class="failed">FAILED</td>
          </tr>
        
          <tr>
            <td>tests\playwright-mcp\generated\login_create_delete_enhanced.spec.js</td>
            <td>36.00%</td>
            <td class="failed">FAILED</td>
          </tr>
        
          <tr>
            <td>tests\playwright-mcp\generated\login_create_delete_enhanced_prod.spec.js</td>
            <td>36.00%</td>
            <td class="failed">FAILED</td>
          </tr>
        
          <tr>
            <td>tests\playwright-mcp\generated\login_create_file_production.spec.js</td>
            <td>31.50%</td>
            <td class="failed">FAILED</td>
          </tr>
        
          <tr>
            <td>tests\self-healing\recovery-strategies.spec.js</td>
            <td>0.00%</td>
            <td class="failed">FAILED</td>
          </tr>
        
          <tr>
            <td>tests\self-healing\selector-healing.spec.js</td>
            <td>0.00%</td>
            <td class="failed">FAILED</td>
          </tr>
        
          <tr>
            <td>tests\simple-test.spec.js</td>
            <td>13.50%</td>
            <td class="failed">FAILED</td>
          </tr>
        
          <tr>
            <td>tests\simplified-file-operations.spec.js</td>
            <td>0.00%</td>
            <td class="failed">FAILED</td>
          </tr>
        
          <tr>
            <td>tests\simplified-integration.spec.js</td>
            <td>9.00%</td>
            <td class="failed">FAILED</td>
          </tr>
        
          <tr>
            <td>tests\simplified-login.spec.js</td>
            <td>0.00%</td>
            <td class="failed">FAILED</td>
          </tr>
        
          <tr>
            <td>tests\simplified-performance.spec.js</td>
            <td>0.00%</td>
            <td class="failed">FAILED</td>
          </tr>
        
          <tr>
            <td>tests\simplified-self-healing.spec.js</td>
            <td>0.00%</td>
            <td class="failed">FAILED</td>
          </tr>
        
    </table>
  </div>
  
  <div class="file-details">
    <h2>File Details</h2>
    
        <div class="file-details">
          <h3>tests\e2e\file-operations.e2e.spec.js</h3>
          <p>Overall Score: <strong>0.00%</strong></p>
          
          <h4>Aspect Scores</h4>
          
            <div class="aspect-score">
              <span>arrangementClarity: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>actionFocus: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>assertionCompleteness: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>documentation: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
          
          
            <h4>Recommendations</h4>
            <div class="recommendations">
              <ul>
                
                  <li>
                    <strong>should create a new file</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
                  <li>
                    <strong>should delete a file</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
                  <li>
                    <strong>should create and delete multiple files</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
              </ul>
            </div>
          
        </div>
      
        <div class="file-details">
          <h3>tests\e2e\login.e2e.spec.js</h3>
          <p>Overall Score: <strong>0.00%</strong></p>
          
          <h4>Aspect Scores</h4>
          
            <div class="aspect-score">
              <span>arrangementClarity: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>actionFocus: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>assertionCompleteness: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>documentation: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
          
          
            <h4>Recommendations</h4>
            <div class="recommendations">
              <ul>
                
                  <li>
                    <strong>should login with valid credentials</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
                  <li>
                    <strong>should show error with invalid credentials</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
                  <li>
                    <strong>should show error with empty credentials</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
                  <li>
                    <strong>should login and logout successfully</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
              </ul>
            </div>
          
        </div>
      
        <div class="file-details">
          <h3>tests\integration\cross-component.integration.spec.js</h3>
          <p>Overall Score: <strong>0.00%</strong></p>
          
          <h4>Aspect Scores</h4>
          
            <div class="aspect-score">
              <span>arrangementClarity: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>actionFocus: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>assertionCompleteness: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>documentation: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
          
          
            <h4>Recommendations</h4>
            <div class="recommendations">
              <ul>
                
                  <li>
                    <strong>should integrate all components in an end-to-end scenario</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
                  <li>
                    <strong>should integrate MCP and self-healing controllers</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
              </ul>
            </div>
          
        </div>
      
        <div class="file-details">
          <h3>tests\integration\mcp-optimizer.integration.spec.js</h3>
          <p>Overall Score: <strong>0.00%</strong></p>
          
          <h4>Aspect Scores</h4>
          
            <div class="aspect-score">
              <span>arrangementClarity: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>actionFocus: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>assertionCompleteness: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>documentation: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
          
          
            <h4>Recommendations</h4>
            <div class="recommendations">
              <ul>
                
                  <li>
                    <strong>should create and initialize MCP controller</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
                  <li>
                    <strong>should optimize selectors</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
                  <li>
                    <strong>should select appropriate MCP tool for a task</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
                  <li>
                    <strong>should analyze screenshots</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
                  <li>
                    <strong>should generate reports</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
              </ul>
            </div>
          
        </div>
      
        <div class="file-details">
          <h3>tests\integration\self-healing.integration.spec.js</h3>
          <p>Overall Score: <strong>0.00%</strong></p>
          
          <h4>Aspect Scores</h4>
          
            <div class="aspect-score">
              <span>arrangementClarity: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>actionFocus: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>assertionCompleteness: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>documentation: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
          
          
            <h4>Recommendations</h4>
            <div class="recommendations">
              <ul>
                
                  <li>
                    <strong>should create and initialize self-healing controller</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
                  <li>
                    <strong>should create a self-healing page</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
                  <li>
                    <strong>should track test runs</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
                  <li>
                    <strong>should track performance metrics</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
                  <li>
                    <strong>should generate reports</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
              </ul>
            </div>
          
        </div>
      
        <div class="file-details">
          <h3>tests\integration\shared-utils.integration.spec.js</h3>
          <p>Overall Score: <strong>0.00%</strong></p>
          
          <h4>Aspect Scores</h4>
          
            <div class="aspect-score">
              <span>arrangementClarity: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>actionFocus: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>assertionCompleteness: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>documentation: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
          
          
            <h4>Recommendations</h4>
            <div class="recommendations">
              <ul>
                
                  <li>
                    <strong>should integrate screenshot utilities correctly</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
                  <li>
                    <strong>should integrate error screenshot utilities correctly</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
                  <li>
                    <strong>should integrate configuration utilities correctly</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
                  <li>
                    <strong>should integrate self-healing with shared-utils correctly</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
              </ul>
            </div>
          
        </div>
      
        <div class="file-details">
          <h3>tests\performance\execution-time.spec.js</h3>
          <p>Overall Score: <strong>0.00%</strong></p>
          
          <h4>Aspect Scores</h4>
          
            <div class="aspect-score">
              <span>arrangementClarity: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>actionFocus: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>assertionCompleteness: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>documentation: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
          
          
            <h4>Recommendations</h4>
            <div class="recommendations">
              <ul>
                
                  <li>
                    <strong>should measure login performance</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
                  <li>
                    <strong>should measure file creation performance with and without MCP optimization</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
                  <li>
                    <strong>should measure self-healing performance</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
              </ul>
            </div>
          
        </div>
      
        <div class="file-details">
          <h3>tests\performance\token-usage.spec.js</h3>
          <p>Overall Score: <strong>0.00%</strong></p>
          
          <h4>Aspect Scores</h4>
          
            <div class="aspect-score">
              <span>arrangementClarity: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>actionFocus: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>assertionCompleteness: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>documentation: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
          
          
            <h4>Recommendations</h4>
            <div class="recommendations">
              <ul>
                
                  <li>
                    <strong>should measure selector optimization token usage</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
                  <li>
                    <strong>should measure screenshot analysis token usage</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
                  <li>
                    <strong>should measure tool selection token usage</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
              </ul>
            </div>
          
        </div>
      
        <div class="file-details">
          <h3>tests\playwright-mcp\examples\create_delete.spec.js</h3>
          <p>Overall Score: <strong>18.00%</strong></p>
          
          <h4>Aspect Scores</h4>
          
            <div class="aspect-score">
              <span>arrangementClarity: 30.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 30%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>actionFocus: 30.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 30%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>assertionCompleteness: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>documentation: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
          
          
            <h4>Recommendations</h4>
            <div class="recommendations">
              <ul>
                
                  <li>
                    <strong>should create and delete a file</strong> (Score: 18.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
              </ul>
            </div>
          
        </div>
      
        <div class="file-details">
          <h3>tests\playwright-mcp\examples\login.spec.js</h3>
          <p>Overall Score: <strong>27.00%</strong></p>
          
          <h4>Aspect Scores</h4>
          
            <div class="aspect-score">
              <span>arrangementClarity: 30.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 30%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>actionFocus: 30.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 30%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>assertionCompleteness: 60.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 60%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>documentation: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
          
          
            <h4>Recommendations</h4>
            <div class="recommendations">
              <ul>
                
                  <li>
                    <strong>should login with valid credentials</strong> (Score: 36.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
                  <li>
                    <strong>should show error with invalid credentials</strong> (Score: 18.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
              </ul>
            </div>
          
        </div>
      
        <div class="file-details">
          <h3>tests\playwright-mcp\generated\login_create_delete.spec.js</h3>
          <p>Overall Score: <strong>36.00%</strong></p>
          
          <h4>Aspect Scores</h4>
          
            <div class="aspect-score">
              <span>arrangementClarity: 30.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 30%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>actionFocus: 30.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 30%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>assertionCompleteness: 60.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 60%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>documentation: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
          
          
            <h4>Recommendations</h4>
            <div class="recommendations">
              <ul>
                
                  <li>
                    <strong>should login, create a file, and delete it</strong> (Score: 36.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
              </ul>
            </div>
          
        </div>
      
        <div class="file-details">
          <h3>tests\playwright-mcp\generated\login_create_delete_debug.spec.js</h3>
          <p>Overall Score: <strong>36.00%</strong></p>
          
          <h4>Aspect Scores</h4>
          
            <div class="aspect-score">
              <span>arrangementClarity: 30.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 30%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>actionFocus: 30.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 30%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>assertionCompleteness: 60.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 60%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>documentation: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
          
          
            <h4>Recommendations</h4>
            <div class="recommendations">
              <ul>
                
                  <li>
                    <strong>should login, create a file, and delete it</strong> (Score: 36.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
              </ul>
            </div>
          
        </div>
      
        <div class="file-details">
          <h3>tests\playwright-mcp\generated\login_create_delete_enhanced.spec.js</h3>
          <p>Overall Score: <strong>36.00%</strong></p>
          
          <h4>Aspect Scores</h4>
          
            <div class="aspect-score">
              <span>arrangementClarity: 30.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 30%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>actionFocus: 30.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 30%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>assertionCompleteness: 60.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 60%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>documentation: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
          
          
            <h4>Recommendations</h4>
            <div class="recommendations">
              <ul>
                
                  <li>
                    <strong>should login, create a file, and delete it</strong> (Score: 36.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
              </ul>
            </div>
          
        </div>
      
        <div class="file-details">
          <h3>tests\playwright-mcp\generated\login_create_delete_enhanced_prod.spec.js</h3>
          <p>Overall Score: <strong>36.00%</strong></p>
          
          <h4>Aspect Scores</h4>
          
            <div class="aspect-score">
              <span>arrangementClarity: 30.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 30%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>actionFocus: 30.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 30%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>assertionCompleteness: 60.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 60%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>documentation: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
          
          
            <h4>Recommendations</h4>
            <div class="recommendations">
              <ul>
                
                  <li>
                    <strong>should login, create a file, and delete it</strong> (Score: 36.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
              </ul>
            </div>
          
        </div>
      
        <div class="file-details">
          <h3>tests\playwright-mcp\generated\login_create_file_production.spec.js</h3>
          <p>Overall Score: <strong>31.50%</strong></p>
          
          <h4>Aspect Scores</h4>
          
            <div class="aspect-score">
              <span>arrangementClarity: 30.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 30%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>actionFocus: 30.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 30%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>assertionCompleteness: 45.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 44.99999999999999%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>documentation: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
          
          
            <h4>Recommendations</h4>
            <div class="recommendations">
              <ul>
                
                  <li>
                    <strong>should login, create a file, and delete it</strong> (Score: 31.50%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
              </ul>
            </div>
          
        </div>
      
        <div class="file-details">
          <h3>tests\self-healing\recovery-strategies.spec.js</h3>
          <p>Overall Score: <strong>0.00%</strong></p>
          
          <h4>Aspect Scores</h4>
          
            <div class="aspect-score">
              <span>arrangementClarity: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>actionFocus: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>assertionCompleteness: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>documentation: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
          
          
            <h4>Recommendations</h4>
            <div class="recommendations">
              <ul>
                
                  <li>
                    <strong>should recover using retry strategy</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
                  <li>
                    <strong>should recover using wait strategy</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
                  <li>
                    <strong>should recover using selector strategy</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
              </ul>
            </div>
          
        </div>
      
        <div class="file-details">
          <h3>tests\self-healing\selector-healing.spec.js</h3>
          <p>Overall Score: <strong>0.00%</strong></p>
          
          <h4>Aspect Scores</h4>
          
            <div class="aspect-score">
              <span>arrangementClarity: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>actionFocus: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>assertionCompleteness: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>documentation: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
          
          
            <h4>Recommendations</h4>
            <div class="recommendations">
              <ul>
                
                  <li>
                    <strong>should heal CSS selectors</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
                  <li>
                    <strong>should heal XPath selectors</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
                  <li>
                    <strong>should heal text-based selectors</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
              </ul>
            </div>
          
        </div>
      
        <div class="file-details">
          <h3>tests\simple-test.spec.js</h3>
          <p>Overall Score: <strong>13.50%</strong></p>
          
          <h4>Aspect Scores</h4>
          
            <div class="aspect-score">
              <span>arrangementClarity: 30.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 30%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>actionFocus: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>assertionCompleteness: 15.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 15%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>documentation: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
          
          
            <h4>Recommendations</h4>
            <div class="recommendations">
              <ul>
                
                  <li>
                    <strong>should pass</strong> (Score: 13.50%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add proper error handling with try/catch blocks.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
              </ul>
            </div>
          
        </div>
      
        <div class="file-details">
          <h3>tests\simplified-file-operations.spec.js</h3>
          <p>Overall Score: <strong>0.00%</strong></p>
          
          <h4>Aspect Scores</h4>
          
            <div class="aspect-score">
              <span>arrangementClarity: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>actionFocus: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>assertionCompleteness: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>documentation: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
          
          
            <h4>Recommendations</h4>
            <div class="recommendations">
              <ul>
                
                  <li>
                    <strong>should create a new file</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li>
                    </ul>
                  </li>
                
              </ul>
            </div>
          
        </div>
      
        <div class="file-details">
          <h3>tests\simplified-integration.spec.js</h3>
          <p>Overall Score: <strong>9.00%</strong></p>
          
          <h4>Aspect Scores</h4>
          
            <div class="aspect-score">
              <span>arrangementClarity: 30.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 30%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>actionFocus: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>assertionCompleteness: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>documentation: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
          
          
            <h4>Recommendations</h4>
            <div class="recommendations">
              <ul>
                
                  <li>
                    <strong>should take screenshots correctly</strong> (Score: 9.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li>
                    </ul>
                  </li>
                
                  <li>
                    <strong>should take error screenshots correctly</strong> (Score: 9.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li><li>Add screenshot capture for better debugging and documentation.</li>
                    </ul>
                  </li>
                
              </ul>
            </div>
          
        </div>
      
        <div class="file-details">
          <h3>tests\simplified-login.spec.js</h3>
          <p>Overall Score: <strong>0.00%</strong></p>
          
          <h4>Aspect Scores</h4>
          
            <div class="aspect-score">
              <span>arrangementClarity: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>actionFocus: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>assertionCompleteness: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>documentation: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
          
          
            <h4>Recommendations</h4>
            <div class="recommendations">
              <ul>
                
                  <li>
                    <strong>should login with valid credentials</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li>
                    </ul>
                  </li>
                
                  <li>
                    <strong>should show error with invalid credentials</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li>
                    </ul>
                  </li>
                
              </ul>
            </div>
          
        </div>
      
        <div class="file-details">
          <h3>tests\simplified-performance.spec.js</h3>
          <p>Overall Score: <strong>0.00%</strong></p>
          
          <h4>Aspect Scores</h4>
          
            <div class="aspect-score">
              <span>arrangementClarity: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>actionFocus: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>assertionCompleteness: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>documentation: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
          
          
            <h4>Recommendations</h4>
            <div class="recommendations">
              <ul>
                
                  <li>
                    <strong>should measure login performance</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li>
                    </ul>
                  </li>
                
                  <li>
                    <strong>should measure selector optimization performance</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li>
                    </ul>
                  </li>
                
              </ul>
            </div>
          
        </div>
      
        <div class="file-details">
          <h3>tests\simplified-self-healing.spec.js</h3>
          <p>Overall Score: <strong>0.00%</strong></p>
          
          <h4>Aspect Scores</h4>
          
            <div class="aspect-score">
              <span>arrangementClarity: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>actionFocus: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>assertionCompleteness: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
            <div class="aspect-score">
              <span>documentation: 0.00%</span>
              <div class="score-bar">
                <div class="score-fill" style="width: 0%"></div>
              </div>
            </div>
          
          
          
            <h4>Recommendations</h4>
            <div class="recommendations">
              <ul>
                
                  <li>
                    <strong>should heal CSS selectors</strong> (Score: 0.00%)
                    <ul>
                      <li>Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.</li><li>Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.</li><li>Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.</li><li>Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.</li>
                    </ul>
                  </li>
                
              </ul>
            </div>
          
        </div>
      
  </div>
</body>
</html>
  