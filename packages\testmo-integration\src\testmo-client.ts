/**
 * Testmo client for Testmo integration
 */

import * as dotenv from 'dotenv';
import { TestmoClientOptions } from './types';
import { TestmoAPI } from './testmo-api';
import { TestmoReporter } from './testmo-reporter';
import { TestmoAutomation } from './testmo-automation';
import { EventBus, EventType } from '@qawolf/core';

// Load environment variables
dotenv.config();

/**
 * Testmo client
 */
export class TestmoClient {
  /**
   * API key
   */
  private apiKey: string;
  
  /**
   * Host URL
   */
  private host: string;
  
  /**
   * Project ID
   */
  private projectId: number;
  
  /**
   * Timeout in milliseconds
   */
  private timeout: number;
  
  /**
   * Testmo API
   */
  private api: TestmoAPI;
  
  /**
   * Testmo reporter
   */
  private reporter: TestmoReporter;
  
  /**
   * Testmo automation
   */
  private automation: TestmoAutomation;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options Testmo client options
   */
  constructor(options: TestmoClientOptions = {}) {
    this.apiKey = options.apiKey || process.env.TESTMO_API_KEY || '';
    this.host = options.host || process.env.TESTMO_HOST || 'https://api.testmo.io';
    this.projectId = options.projectId || Number(process.env.TESTMO_PROJECT_ID) || 0;
    this.timeout = options.timeout || 30000;
    
    if (!this.apiKey) {
      throw new Error('Testmo API key is required');
    }
    
    if (!this.host) {
      throw new Error('Testmo host URL is required');
    }
    
    if (!this.projectId) {
      throw new Error('Testmo project ID is required');
    }
    
    this.api = new TestmoAPI({
      apiKey: this.apiKey,
      host: this.host,
      projectId: this.projectId,
      timeout: this.timeout
    });
    
    this.reporter = new TestmoReporter({
      apiKey: this.apiKey,
      host: this.host,
      projectId: this.projectId,
      timeout: this.timeout
    });
    
    this.automation = new TestmoAutomation({
      apiKey: this.apiKey,
      host: this.host,
      projectId: this.projectId,
      timeout: this.timeout
    });
    
    this.eventBus = EventBus.getInstance();
    
    // Emit event
    this.eventBus.emit(EventType.TESTMO_CLIENT_INITIALIZED, {
      apiKey: this.apiKey,
      host: this.host,
      projectId: this.projectId
    });
  }
  
  /**
   * Get API
   * @returns Testmo API
   */
  getAPI(): TestmoAPI {
    return this.api;
  }
  
  /**
   * Get reporter
   * @returns Testmo reporter
   */
  getReporter(): TestmoReporter {
    return this.reporter;
  }
  
  /**
   * Get automation
   * @returns Testmo automation
   */
  getAutomation(): TestmoAutomation {
    return this.automation;
  }
  
  /**
   * Get API key
   * @returns API key
   */
  getAPIKey(): string {
    return this.apiKey;
  }
  
  /**
   * Get host URL
   * @returns Host URL
   */
  getHost(): string {
    return this.host;
  }
  
  /**
   * Get project ID
   * @returns Project ID
   */
  getProjectId(): number {
    return this.projectId;
  }
  
  /**
   * Get timeout
   * @returns Timeout in milliseconds
   */
  getTimeout(): number {
    return this.timeout;
  }
  
  /**
   * Set API key
   * @param apiKey API key
   * @returns This instance for chaining
   */
  setAPIKey(apiKey: string): TestmoClient {
    this.apiKey = apiKey;
    
    this.api.setAPIKey(apiKey);
    this.reporter.setAPIKey(apiKey);
    this.automation.setAPIKey(apiKey);
    
    return this;
  }
  
  /**
   * Set host URL
   * @param host Host URL
   * @returns This instance for chaining
   */
  setHost(host: string): TestmoClient {
    this.host = host;
    
    this.api.setHost(host);
    this.reporter.setHost(host);
    this.automation.setHost(host);
    
    return this;
  }
  
  /**
   * Set project ID
   * @param projectId Project ID
   * @returns This instance for chaining
   */
  setProjectId(projectId: number): TestmoClient {
    this.projectId = projectId;
    
    this.api.setProjectId(projectId);
    this.reporter.setProjectId(projectId);
    this.automation.setProjectId(projectId);
    
    return this;
  }
  
  /**
   * Set timeout
   * @param timeout Timeout in milliseconds
   * @returns This instance for chaining
   */
  setTimeout(timeout: number): TestmoClient {
    this.timeout = timeout;
    
    this.api.setTimeout(timeout);
    this.reporter.setTimeout(timeout);
    this.automation.setTimeout(timeout);
    
    return this;
  }
}

/**
 * Create Testmo client
 * @param options Testmo client options
 * @returns Testmo client
 */
export function createTestmoClient(options: TestmoClientOptions = {}): TestmoClient {
  return new TestmoClient(options);
}
