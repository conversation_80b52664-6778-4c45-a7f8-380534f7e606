name: Testmo Integration

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:
    inputs:
      test_pattern:
        description: 'Test pattern to run'
        required: false
        default: 'tests/simplified-*.spec.js'
      environment:
        description: 'Environment to run tests against'
        required: false
        default: 'staging'

jobs:
  testmo-integration:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
      
      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
          cache: 'npm'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Install Playwright browsers
        run: npx playwright install --with-deps
      
      - name: Run Testmo integration
        run: node scripts/run-testmo-integration.js "${{ github.event.inputs.test_pattern || 'tests/simplified-*.spec.js' }}" "QA Wolf Test Run - ${{ github.run_id }}" "Automated test run from GitHub Actions" "qawolf" "${{ github.ref_name }}" "${{ github.sha }}" "${{ github.event.inputs.environment || 'staging' }}"
        env:
          TESTMO_API_KEY: ${{ secrets.TESTMO_API_KEY }}
          TESTMO_URL: ${{ secrets.TESTMO_URL }}
          TESTMO_PROJECT_ID: ${{ secrets.TESTMO_PROJECT_ID }}
          EMAIL: ${{ secrets.QA_WOLF_EMAIL }}
          PASSWORD: ${{ secrets.QA_WOLF_PASSWORD }}
          URL: ${{ secrets.QA_WOLF_URL }}
      
      - name: Upload Playwright report
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 30
      
      - name: Upload QA Wolf results
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: qawolf-results
          path: qawolf-reports/
          retention-days: 30
      
      - name: Upload Testmo reports
        if: always()
        uses: actions/upload-artifact@v3
        with:
          name: testmo-reports
          path: testmo-reports/
          retention-days: 30
