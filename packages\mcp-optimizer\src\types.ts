/**
 * Types for MCP optimizer
 */

/**
 * MCP tool type
 */
export enum MCPToolType {
  /**
   * Desktop Commander MCP
   */
  DESKTOP_COMMANDER = 'desktop-commander',
  
  /**
   * <PERSON>rowser Tools MCP
   */
  BROWSER_TOOLS = 'browser-tools',
  
  /**
   * Playwright MCP
   */
  PLAYWRIGHT = 'playwright',
  
  /**
   * Sequential Thinking MCP
   */
  SEQUENTIAL_THINKING = 'sequential-thinking',
  
  /**
   * Quillopy MCP
   */
  QUILLOPY = 'quillopy',
  
  /**
   * Other MCP
   */
  OTHER = 'other'
}

/**
 * MCP operation type
 */
export enum MCPOperationType {
  /**
   * File operation
   */
  FILE_OPERATION = 'file-operation',
  
  /**
   * Browser operation
   */
  BROWSER_OPERATION = 'browser-operation',
  
  /**
   * Terminal operation
   */
  TERMINAL_OPERATION = 'terminal-operation',
  
  /**
   * Thinking operation
   */
  THINKING_OPERATION = 'thinking-operation',
  
  /**
   * Documentation operation
   */
  DOCUMENTATION_OPERATION = 'documentation-operation',
  
  /**
   * Other operation
   */
  OTHER_OPERATION = 'other-operation'
}

/**
 * MCP operation
 */
export interface MCPOperation {
  /**
   * Operation ID
   */
  id: string;
  
  /**
   * Operation name
   */
  name: string;
  
  /**
   * Operation type
   */
  type: MCPOperationType;
  
  /**
   * MCP tool type
   */
  toolType: MCPToolType;
  
  /**
   * MCP tool name
   */
  toolName: string;
  
  /**
   * Operation parameters
   */
  parameters: Record<string, any>;
  
  /**
   * Operation result
   */
  result?: any;
  
  /**
   * Operation duration in milliseconds
   */
  duration: number;
  
  /**
   * Operation timestamp
   */
  timestamp: number;
  
  /**
   * Operation cost
   */
  cost?: number;
  
  /**
   * Operation token count
   */
  tokenCount?: number;
  
  /**
   * Operation metadata
   */
  metadata?: Record<string, any>;
}

/**
 * MCP operation pattern
 */
export interface MCPOperationPattern {
  /**
   * Pattern ID
   */
  id: string;
  
  /**
   * Pattern name
   */
  name: string;
  
  /**
   * Pattern description
   */
  description: string;
  
  /**
   * Pattern operations
   */
  operations: MCPOperation[];
  
  /**
   * Pattern frequency
   */
  frequency: number;
  
  /**
   * Pattern cost
   */
  cost: number;
  
  /**
   * Pattern token count
   */
  tokenCount: number;
  
  /**
   * Pattern metadata
   */
  metadata?: Record<string, any>;
}

/**
 * MCP optimization suggestion
 */
export interface MCPOptimizationSuggestion {
  /**
   * Suggestion ID
   */
  id: string;
  
  /**
   * Suggestion name
   */
  name: string;
  
  /**
   * Suggestion description
   */
  description: string;
  
  /**
   * Suggestion type
   */
  type: 'replace' | 'remove' | 'reorder' | 'combine' | 'split' | 'other';
  
  /**
   * Original operations
   */
  originalOperations: MCPOperation[];
  
  /**
   * Optimized operations
   */
  optimizedOperations: MCPOperation[];
  
  /**
   * Cost savings
   */
  costSavings: number;
  
  /**
   * Token savings
   */
  tokenSavings: number;
  
  /**
   * Suggestion priority
   */
  priority: 'high' | 'medium' | 'low';
  
  /**
   * Suggestion code example
   */
  codeExample?: string;
  
  /**
   * Suggestion metadata
   */
  metadata?: Record<string, any>;
}

/**
 * MCP optimizer options
 */
export interface MCPOptimizerOptions {
  /**
   * Whether to analyze patterns
   */
  analyzePatterns?: boolean;
  
  /**
   * Whether to calculate costs
   */
  calculateCosts?: boolean;
  
  /**
   * Whether to generate suggestions
   */
  generateSuggestions?: boolean;
  
  /**
   * Minimum pattern frequency
   */
  minPatternFrequency?: number;
  
  /**
   * Minimum cost savings
   */
  minCostSavings?: number;
  
  /**
   * Minimum token savings
   */
  minTokenSavings?: number;
  
  /**
   * Maximum operations to analyze
   */
  maxOperations?: number;
  
  /**
   * Whether to include low priority suggestions
   */
  includeLowPrioritySuggestions?: boolean;
}

/**
 * MCP analyzer options
 */
export interface MCPAnalyzerOptions {
  /**
   * Whether to analyze patterns
   */
  analyzePatterns?: boolean;
  
  /**
   * Minimum pattern frequency
   */
  minPatternFrequency?: number;
  
  /**
   * Maximum operations to analyze
   */
  maxOperations?: number;
}

/**
 * MCP transformer options
 */
export interface MCPTransformerOptions {
  /**
   * Whether to generate suggestions
   */
  generateSuggestions?: boolean;
  
  /**
   * Minimum cost savings
   */
  minCostSavings?: number;
  
  /**
   * Minimum token savings
   */
  minTokenSavings?: number;
  
  /**
   * Whether to include low priority suggestions
   */
  includeLowPrioritySuggestions?: boolean;
}

/**
 * MCP cost calculator options
 */
export interface MCPCostCalculatorOptions {
  /**
   * Cost per token
   */
  costPerToken?: number;
  
  /**
   * Cost per operation
   */
  costPerOperation?: Record<string, number>;
  
  /**
   * Cost per tool
   */
  costPerTool?: Record<string, number>;
}

/**
 * MCP analysis result
 */
export interface MCPAnalysisResult {
  /**
   * Total operations
   */
  totalOperations: number;
  
  /**
   * Operations by type
   */
  operationsByType: Record<string, number>;
  
  /**
   * Operations by tool
   */
  operationsByTool: Record<string, number>;
  
  /**
   * Operation patterns
   */
  patterns: MCPOperationPattern[];
  
  /**
   * Total cost
   */
  totalCost: number;
  
  /**
   * Total tokens
   */
  totalTokens: number;
  
  /**
   * Cost by type
   */
  costByType: Record<string, number>;
  
  /**
   * Cost by tool
   */
  costByTool: Record<string, number>;
  
  /**
   * Tokens by type
   */
  tokensByType: Record<string, number>;
  
  /**
   * Tokens by tool
   */
  tokensByTool: Record<string, number>;
  
  /**
   * Optimization suggestions
   */
  suggestions: MCPOptimizationSuggestion[];
}

/**
 * MCP operation log
 */
export interface MCPOperationLog {
  /**
   * Operations
   */
  operations: MCPOperation[];
  
  /**
   * Log timestamp
   */
  timestamp: number;
  
  /**
   * Log metadata
   */
  metadata?: Record<string, any>;
}
