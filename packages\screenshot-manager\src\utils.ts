/**
 * Utilities for screenshot manager
 */

import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';
import * as imageSize from 'image-size';
import { <PERSON>rows<PERSON> } from 'playwright';
import { ScreenshotInfo, ScreenshotType } from './types';

/**
 * Get screenshot info
 * @param screenshotPath Screenshot path
 * @returns Screenshot info
 */
export async function getScreenshotInfo(screenshotPath: string): Promise<ScreenshotInfo> {
  // Get file stats
  const stats = fs.statSync(screenshotPath);
  
  // Get file size
  const size = stats.size;
  
  // Get file modification time
  const timestamp = stats.mtimeMs;
  
  // Get file extension
  const extension = path.extname(screenshotPath).toLowerCase();
  
  // Get file name
  const fileName = path.basename(screenshotPath);
  
  // Get file name without extension
  const fileNameWithoutExtension = path.basename(screenshotPath, extension);
  
  // Get screenshot ID
  const idMatch = fileNameWithoutExtension.match(/_([a-f0-9-]+)_\d+$/);
  const id = idMatch ? idMatch[1] : fileNameWithoutExtension;
  
  // Get screenshot name
  const nameMatch = fileNameWithoutExtension.match(/^(.+)_[a-f0-9-]+_\d+$/);
  const name = nameMatch ? nameMatch[1] : fileNameWithoutExtension;
  
  // Get screenshot format
  const format = extension === '.jpg' || extension === '.jpeg' ? 'jpeg' : 'png';
  
  // Get screenshot dimensions
  let width: number | undefined;
  let height: number | undefined;
  
  try {
    const dimensions = imageSize(screenshotPath);
    width = dimensions.width;
    height = dimensions.height;
  } catch (error) {
    console.error(`Error getting image dimensions for ${screenshotPath}:`, error);
  }
  
  // Determine screenshot type
  let type: ScreenshotType = ScreenshotType.VIEWPORT;
  
  if (fileName.includes('full-page')) {
    type = ScreenshotType.FULL_PAGE;
  } else if (fileName.includes('element')) {
    type = ScreenshotType.ELEMENT;
  } else if (fileName.includes('custom')) {
    type = ScreenshotType.CUSTOM;
  }
  
  return {
    id,
    name,
    path: screenshotPath,
    type,
    format,
    width,
    height,
    size,
    timestamp
  };
}

/**
 * Get browser info
 * @param browser Browser
 * @returns Browser info
 */
export async function getBrowserInfo(browser: Browser): Promise<{ name: string; version: string }> {
  try {
    const context = await browser.newContext();
    const page = await context.newPage();
    
    const userAgent = await page.evaluate(() => navigator.userAgent);
    await page.close();
    await context.close();
    
    // Parse user agent
    const browserMatch = userAgent.match(/(Chrome|Firefox|Safari)\/(\d+\.\d+)/);
    
    if (browserMatch) {
      return {
        name: browserMatch[1],
        version: browserMatch[2]
      };
    }
    
    return {
      name: 'Unknown',
      version: 'Unknown'
    };
  } catch (error) {
    console.error('Error getting browser info:', error);
    
    return {
      name: 'Unknown',
      version: 'Unknown'
    };
  }
}

/**
 * Get OS info
 * @returns OS info
 */
export function getOsInfo(): { name: string; version: string } {
  const platform = os.platform();
  const release = os.release();
  
  let name = 'Unknown';
  let version = release;
  
  switch (platform) {
    case 'win32':
      name = 'Windows';
      break;
    case 'darwin':
      name = 'macOS';
      break;
    case 'linux':
      name = 'Linux';
      break;
    default:
      name = platform;
  }
  
  return {
    name,
    version
  };
}

/**
 * Format bytes
 * @param bytes Bytes
 * @param decimals Decimal places
 * @returns Formatted bytes
 */
export function formatBytes(bytes: number, decimals: number = 2): string {
  if (bytes === 0) {
    return '0 Bytes';
  }
  
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
  
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

/**
 * Format date
 * @param date Date
 * @param format Format
 * @returns Formatted date
 */
export function formatDate(date: Date, format: string = 'YYYY-MM-DD'): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  
  return format
    .replace('YYYY', String(year))
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
}

/**
 * Generate screenshot name
 * @param baseName Base name
 * @param type Screenshot type
 * @returns Screenshot name
 */
export function generateScreenshotName(baseName: string, type: ScreenshotType = ScreenshotType.VIEWPORT): string {
  const timestamp = Date.now();
  const sanitizedBaseName = baseName.replace(/[^a-zA-Z0-9_-]/g, '_');
  
  return `${sanitizedBaseName}_${type}_${timestamp}`;
}

/**
 * Get screenshot directory
 * @param baseDir Base directory
 * @param testName Test name
 * @param testId Test ID
 * @param browserName Browser name
 * @param organizeByDate Whether to organize by date
 * @param organizeByTestName Whether to organize by test name
 * @param organizeByTestId Whether to organize by test ID
 * @param organizeByBrowser Whether to organize by browser
 * @returns Screenshot directory
 */
export function getScreenshotDirectory(
  baseDir: string,
  testName?: string,
  testId?: string,
  browserName?: string,
  organizeByDate: boolean = true,
  organizeByTestName: boolean = true,
  organizeByTestId: boolean = true,
  organizeByBrowser: boolean = true
): string {
  const pathParts: string[] = [baseDir];
  
  // Add date
  if (organizeByDate) {
    const date = new Date();
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    
    pathParts.push(`${year}-${month}-${day}`);
  }
  
  // Add test name
  if (organizeByTestName && testName) {
    pathParts.push(testName.replace(/[^a-zA-Z0-9_-]/g, '_'));
  }
  
  // Add test ID
  if (organizeByTestId && testId) {
    pathParts.push(testId);
  }
  
  // Add browser
  if (organizeByBrowser && browserName) {
    pathParts.push(browserName.toLowerCase());
  }
  
  return path.join(...pathParts);
}

/**
 * Get screenshot file name
 * @param name Screenshot name
 * @param id Screenshot ID
 * @param format Screenshot format
 * @returns Screenshot file name
 */
export function getScreenshotFileName(name: string, id: string, format: 'png' | 'jpeg'): string {
  const timestamp = Date.now();
  const extension = format === 'jpeg' ? 'jpg' : 'png';
  
  return `${name.replace(/[^a-zA-Z0-9_-]/g, '_')}_${id}_${timestamp}.${extension}`;
}

/**
 * Get screenshot path
 * @param directory Screenshot directory
 * @param name Screenshot name
 * @param id Screenshot ID
 * @param format Screenshot format
 * @returns Screenshot path
 */
export function getScreenshotPath(directory: string, name: string, id: string, format: 'png' | 'jpeg'): string {
  const fileName = getScreenshotFileName(name, id, format);
  return path.join(directory, fileName);
}
