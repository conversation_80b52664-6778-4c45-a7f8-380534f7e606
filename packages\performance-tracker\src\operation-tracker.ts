/**
 * Operation tracker for performance tracking
 */

import { v4 as uuidv4 } from 'uuid';
import { Operation, OperationType, OperationMetadata, OperationTrackerOptions } from './types';
import { EventBus, EventType } from '@qawolf/core';

/**
 * Operation tracker
 */
export class OperationTracker {
  /**
   * Operations
   */
  private operations: Operation[] = [];
  
  /**
   * Active operations
   */
  private activeOperations: Map<string, { name: string; type: OperationType | string; startTime: number; metadata?: OperationMetadata }> = new Map();
  
  /**
   * Whether to track operations
   */
  private trackOperations: boolean;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options Operation tracker options
   */
  constructor(options: OperationTrackerOptions = {}) {
    this.trackOperations = options.trackOperations !== false;
    this.eventBus = EventBus.getInstance();
  }
  
  /**
   * Track operation
   * @param operation Operation to track
   * @returns This instance for chaining
   */
  trackOperation(operation: Operation): OperationTracker {
    if (!this.trackOperations) {
      return this;
    }
    
    this.operations.push(operation);
    
    // Emit event
    this.eventBus.emit(EventType.OPERATION_TRACKED, {
      operation
    });
    
    return this;
  }
  
  /**
   * Start operation
   * @param name Operation name
   * @param type Operation type
   * @param metadata Operation metadata
   * @returns Operation ID
   */
  startOperation(name: string, type: OperationType | string, metadata?: OperationMetadata): string {
    if (!this.trackOperations) {
      return '';
    }
    
    const operationId = uuidv4();
    
    this.activeOperations.set(operationId, {
      name,
      type,
      startTime: Date.now(),
      metadata
    });
    
    // Emit event
    this.eventBus.emit(EventType.OPERATION_STARTED, {
      operationId,
      name,
      type,
      startTime: Date.now(),
      metadata
    });
    
    return operationId;
  }
  
  /**
   * End operation
   * @param operationId Operation ID
   * @returns Operation
   */
  endOperation(operationId: string): Operation {
    if (!this.trackOperations || !this.activeOperations.has(operationId)) {
      throw new Error(`Operation with ID ${operationId} not found`);
    }
    
    const activeOperation = this.activeOperations.get(operationId)!;
    const endTime = Date.now();
    const duration = endTime - activeOperation.startTime;
    
    const operation: Operation = {
      name: activeOperation.name,
      type: activeOperation.type,
      startTime: activeOperation.startTime,
      endTime,
      duration,
      metadata: activeOperation.metadata
    };
    
    this.operations.push(operation);
    this.activeOperations.delete(operationId);
    
    // Emit event
    this.eventBus.emit(EventType.OPERATION_ENDED, {
      operationId,
      operation
    });
    
    return operation;
  }
  
  /**
   * Get operations
   * @returns Operations
   */
  getOperations(): Operation[] {
    return [...this.operations];
  }
  
  /**
   * Get active operations
   * @returns Active operations
   */
  getActiveOperations(): Map<string, { name: string; type: OperationType | string; startTime: number; metadata?: OperationMetadata }> {
    return new Map(this.activeOperations);
  }
  
  /**
   * Clear operations
   * @returns This instance for chaining
   */
  clearOperations(): OperationTracker {
    this.operations = [];
    return this;
  }
  
  /**
   * Enable operation tracking
   * @returns This instance for chaining
   */
  enable(): OperationTracker {
    this.trackOperations = true;
    return this;
  }
  
  /**
   * Disable operation tracking
   * @returns This instance for chaining
   */
  disable(): OperationTracker {
    this.trackOperations = false;
    return this;
  }
  
  /**
   * Check if operation tracking is enabled
   * @returns True if operation tracking is enabled, false otherwise
   */
  isEnabled(): boolean {
    return this.trackOperations;
  }
}

/**
 * Create operation tracker
 * @param options Operation tracker options
 * @returns Operation tracker
 */
export function createOperationTracker(options: OperationTrackerOptions = {}): OperationTracker {
  return new OperationTracker(options);
}
