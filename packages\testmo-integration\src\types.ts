/**
 * Types for Testmo integration
 */

/**
 * Testmo client options
 */
export interface TestmoClientOptions {
  /**
   * API key
   */
  apiKey?: string;
  
  /**
   * Host URL
   */
  host?: string;
  
  /**
   * Project ID
   */
  projectId?: number;
  
  /**
   * Timeout in milliseconds
   */
  timeout?: number;
}

/**
 * Testmo project
 */
export interface TestmoProject {
  /**
   * Project ID
   */
  id: number;
  
  /**
   * Project name
   */
  name: string;
  
  /**
   * Project slug
   */
  slug: string;
  
  /**
   * Project description
   */
  description?: string;
  
  /**
   * Project created at
   */
  created_on: string;
  
  /**
   * Project created by
   */
  created_by: number;
  
  /**
   * Project is completed
   */
  is_completed: boolean;
  
  /**
   * Project completed on
   */
  completed_on?: string;
  
  /**
   * Project completed by
   */
  completed_by?: number;
}

/**
 * Testmo test case
 */
export interface TestmoTestCase {
  /**
   * Test case ID
   */
  id: number;
  
  /**
   * Project ID
   */
  project_id: number;
  
  /**
   * Test case title
   */
  title: string;
  
  /**
   * Test case section ID
   */
  section_id?: number;
  
  /**
   * Test case type ID
   */
  type_id?: number;
  
  /**
   * Test case priority ID
   */
  priority_id?: number;
  
  /**
   * Test case milestone ID
   */
  milestone_id?: number;
  
  /**
   * Test case created by
   */
  created_by: number;
  
  /**
   * Test case created on
   */
  created_on: string;
  
  /**
   * Test case updated by
   */
  updated_by?: number;
  
  /**
   * Test case updated on
   */
  updated_on?: string;
  
  /**
   * Test case estimate
   */
  estimate?: string;
  
  /**
   * Test case estimate forecast
   */
  estimate_forecast?: string;
  
  /**
   * Test case suite ID
   */
  suite_id?: number;
  
  /**
   * Test case template ID
   */
  template_id?: number;
  
  /**
   * Test case custom fields
   */
  custom_fields?: Record<string, any>;
  
  /**
   * Test case steps
   */
  steps?: TestmoTestStep[];
  
  /**
   * Test case preconditions
   */
  preconditions?: string;
  
  /**
   * Test case expected result
   */
  expected?: string;
  
  /**
   * Test case references
   */
  refs?: string;
}

/**
 * Testmo test step
 */
export interface TestmoTestStep {
  /**
   * Test step content
   */
  content: string;
  
  /**
   * Test step expected result
   */
  expected?: string;
  
  /**
   * Test step references
   */
  refs?: string;
}

/**
 * Testmo test run
 */
export interface TestmoTestRun {
  /**
   * Test run ID
   */
  id: number;
  
  /**
   * Project ID
   */
  project_id: number;
  
  /**
   * Test run name
   */
  name: string;
  
  /**
   * Test run description
   */
  description?: string;
  
  /**
   * Test run milestone ID
   */
  milestone_id?: number;
  
  /**
   * Test run assignedto ID
   */
  assignedto_id?: number;
  
  /**
   * Test run is completed
   */
  is_completed: boolean;
  
  /**
   * Test run completed on
   */
  completed_on?: string;
  
  /**
   * Test run created by
   */
  created_by: number;
  
  /**
   * Test run created on
   */
  created_on: string;
  
  /**
   * Test run config
   */
  config?: string;
  
  /**
   * Test run config ids
   */
  config_ids?: number[];
  
  /**
   * Test run passed count
   */
  passed_count: number;
  
  /**
   * Test run blocked count
   */
  blocked_count: number;
  
  /**
   * Test run untested count
   */
  untested_count: number;
  
  /**
   * Test run retest count
   */
  retest_count: number;
  
  /**
   * Test run failed count
   */
  failed_count: number;
  
  /**
   * Test run custom status count
   */
  custom_status_count: number;
  
  /**
   * Test run custom fields
   */
  custom_fields?: Record<string, any>;
  
  /**
   * Test run url
   */
  url: string;
}

/**
 * Testmo test result
 */
export interface TestmoTestResult {
  /**
   * Test result ID
   */
  id: number;
  
  /**
   * Test run ID
   */
  test_id: number;
  
  /**
   * Status ID
   */
  status_id: number;
  
  /**
   * Assignedto ID
   */
  assignedto_id?: number;
  
  /**
   * Test result comment
   */
  comment?: string;
  
  /**
   * Test result version
   */
  version?: string;
  
  /**
   * Test result elapsed
   */
  elapsed?: string;
  
  /**
   * Test result defects
   */
  defects?: string;
  
  /**
   * Test result created by
   */
  created_by: number;
  
  /**
   * Test result created on
   */
  created_on: string;
}

/**
 * Testmo automation
 */
export interface TestmoAutomation {
  /**
   * Automation ID
   */
  id: number;
  
  /**
   * Project ID
   */
  project_id: number;
  
  /**
   * Automation name
   */
  name: string;
  
  /**
   * Automation source
   */
  source: string;
  
  /**
   * Automation source ID
   */
  source_id: string;
  
  /**
   * Automation test case ID
   */
  case_id?: number;
  
  /**
   * Automation created by
   */
  created_by: number;
  
  /**
   * Automation created on
   */
  created_on: string;
  
  /**
   * Automation updated by
   */
  updated_by?: number;
  
  /**
   * Automation updated on
   */
  updated_on?: string;
}

/**
 * Testmo automation result
 */
export interface TestmoAutomationResult {
  /**
   * Automation result ID
   */
  id: number;
  
  /**
   * Automation ID
   */
  automation_id: number;
  
  /**
   * Status ID
   */
  status_id: number;
  
  /**
   * Automation result comment
   */
  comment?: string;
  
  /**
   * Automation result version
   */
  version?: string;
  
  /**
   * Automation result elapsed
   */
  elapsed?: string;
  
  /**
   * Automation result defects
   */
  defects?: string;
  
  /**
   * Automation result created by
   */
  created_by: number;
  
  /**
   * Automation result created on
   */
  created_on: string;
}

/**
 * Testmo API options
 */
export interface TestmoAPIOptions {
  /**
   * API key
   */
  apiKey?: string;
  
  /**
   * Host URL
   */
  host?: string;
  
  /**
   * Project ID
   */
  projectId?: number;
  
  /**
   * Timeout in milliseconds
   */
  timeout?: number;
}

/**
 * Testmo reporter options
 */
export interface TestmoReporterOptions {
  /**
   * API key
   */
  apiKey?: string;
  
  /**
   * Host URL
   */
  host?: string;
  
  /**
   * Project ID
   */
  projectId?: number;
  
  /**
   * Test run ID
   */
  testRunId?: number;
  
  /**
   * Test run name
   */
  testRunName?: string;
  
  /**
   * Timeout in milliseconds
   */
  timeout?: number;
}

/**
 * Testmo automation options
 */
export interface TestmoAutomationOptions {
  /**
   * API key
   */
  apiKey?: string;
  
  /**
   * Host URL
   */
  host?: string;
  
  /**
   * Project ID
   */
  projectId?: number;
  
  /**
   * Source
   */
  source?: string;
  
  /**
   * Timeout in milliseconds
   */
  timeout?: number;
}

/**
 * Testmo API response
 */
export interface TestmoAPIResponse<T> {
  /**
   * Response data
   */
  data: T;
  
  /**
   * Response status
   */
  status: number;
  
  /**
   * Response message
   */
  message: string;
}

/**
 * Testmo API error
 */
export interface TestmoAPIError {
  /**
   * Error code
   */
  code: string;
  
  /**
   * Error message
   */
  message: string;
  
  /**
   * Error details
   */
  details?: Record<string, any>;
}
