/**
 * Feedback Collector
 * 
 * This module provides functionality for collecting feedback from test runs.
 */

const fs = require('fs');
const path = require('path');
const { createDefaultConfig, mergeConfigs } = require('./config');

/**
 * Class for collecting feedback from test runs
 */
class FeedbackCollector {
  /**
   * Create a new FeedbackCollector
   * @param {Object} [config] - Configuration options
   */
  constructor(config = {}) {
    this.config = mergeConfigs(createDefaultConfig().feedbackCollection, config);
    this.feedbackData = {
      testRuns: [],
      events: [],
      selectors: {},
      recoveries: {},
      performance: {}
    };
    this.currentTestRun = null;
    this.initialized = false;
    this.initializationPromise = null;
  }
  
  /**
   * Initialize the feedback collector
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.initialized) {
      return;
    }
    
    if (this.initializationPromise) {
      return this.initializationPromise;
    }
    
    this.initializationPromise = (async () => {
      if (this.config.persistFeedback) {
        try {
          await this.loadFeedback();
        } catch (error) {
          console.warn('Failed to load feedback data:', error);
        }
      }
      
      if (this.config.collectScreenshots) {
        try {
          await this.ensureScreenshotDir();
        } catch (error) {
          console.warn('Failed to create screenshot directory:', error);
        }
      }
      
      this.initialized = true;
    })();
    
    return this.initializationPromise;
  }
  
  /**
   * Load feedback data from file
   * @returns {Promise<void>}
   */
  async loadFeedback() {
    try {
      if (fs.existsSync(this.config.feedbackPath)) {
        const feedbackData = await fs.promises.readFile(this.config.feedbackPath, 'utf8');
        this.feedbackData = JSON.parse(feedbackData);
      }
    } catch (error) {
      console.error('Failed to load feedback data:', error);
      this.feedbackData = {
        testRuns: [],
        events: [],
        selectors: {},
        recoveries: {},
        performance: {}
      };
    }
  }
  
  /**
   * Save feedback data to file
   * @returns {Promise<void>}
   */
  async saveFeedback() {
    if (!this.config.persistFeedback) {
      return;
    }
    
    try {
      const feedbackDir = path.dirname(this.config.feedbackPath);
      if (!fs.existsSync(feedbackDir)) {
        await fs.promises.mkdir(feedbackDir, { recursive: true });
      }
      
      await fs.promises.writeFile(
        this.config.feedbackPath,
        JSON.stringify(this.feedbackData, null, 2),
        'utf8'
      );
    } catch (error) {
      console.error('Failed to save feedback data:', error);
    }
  }
  
  /**
   * Ensure the screenshot directory exists
   * @returns {Promise<void>}
   */
  async ensureScreenshotDir() {
    if (!this.config.collectScreenshots) {
      return;
    }
    
    try {
      if (!fs.existsSync(this.config.screenshotDir)) {
        await fs.promises.mkdir(this.config.screenshotDir, { recursive: true });
      }
    } catch (error) {
      console.error('Failed to create screenshot directory:', error);
    }
  }
  
  /**
   * Start collecting feedback for a test run
   * @param {Object} testInfo - Test information
   * @returns {Promise<void>}
   */
  async startTestRun(testInfo) {
    await this.initialize();
    
    if (!this.config.enabled) {
      return;
    }
    
    // End any current test run
    if (this.currentTestRun) {
      await this.endTestRun({ success: false, error: new Error('Test run was not properly ended') });
    }
    
    // Create a new test run
    this.currentTestRun = {
      testId: testInfo.testId || `test-${Date.now()}`,
      testName: testInfo.testName || 'Unknown Test',
      startTime: new Date(),
      endTime: null,
      success: null,
      duration: null,
      events: [],
      selectors: {},
      recoveries: [],
      performance: {},
      error: null
    };
    
    // Collect the start event
    await this.collectEvent('test:start', {
      testId: this.currentTestRun.testId,
      testName: this.currentTestRun.testName,
      timestamp: this.currentTestRun.startTime
    });
  }
  
  /**
   * End collecting feedback for a test run
   * @param {Object} result - Test result
   * @returns {Promise<void>}
   */
  async endTestRun(result) {
    await this.initialize();
    
    if (!this.config.enabled || !this.currentTestRun) {
      return;
    }
    
    // Update the test run
    this.currentTestRun.endTime = new Date();
    this.currentTestRun.success = result.success;
    this.currentTestRun.duration = this.currentTestRun.endTime - this.currentTestRun.startTime;
    this.currentTestRun.error = result.error;
    
    // Collect the end event
    await this.collectEvent('test:end', {
      testId: this.currentTestRun.testId,
      testName: this.currentTestRun.testName,
      timestamp: this.currentTestRun.endTime,
      success: this.currentTestRun.success,
      duration: this.currentTestRun.duration,
      error: this.currentTestRun.error ? {
        name: this.currentTestRun.error.name,
        message: this.currentTestRun.error.message,
        stack: this.currentTestRun.error.stack
      } : null
    });
    
    // Add the test run to the feedback data
    this.feedbackData.testRuns.push(this.currentTestRun);
    
    // Update selector statistics
    for (const [selector, selectorData] of Object.entries(this.currentTestRun.selectors)) {
      if (!this.feedbackData.selectors[selector]) {
        this.feedbackData.selectors[selector] = {
          selector,
          attempts: 0,
          successes: 0,
          failures: 0,
          successRate: 0,
          testRuns: []
        };
      }
      
      this.feedbackData.selectors[selector].attempts += selectorData.attempts;
      this.feedbackData.selectors[selector].successes += selectorData.successes;
      this.feedbackData.selectors[selector].failures += selectorData.failures;
      this.feedbackData.selectors[selector].successRate = 
        this.feedbackData.selectors[selector].successes / this.feedbackData.selectors[selector].attempts;
      
      this.feedbackData.selectors[selector].testRuns.push({
        testId: this.currentTestRun.testId,
        timestamp: this.currentTestRun.endTime
      });
    }
    
    // Update recovery statistics
    for (const recovery of this.currentTestRun.recoveries) {
      const strategyName = recovery.strategy || 'unknown';
      
      if (!this.feedbackData.recoveries[strategyName]) {
        this.feedbackData.recoveries[strategyName] = {
          strategy: strategyName,
          attempts: 0,
          successes: 0,
          failures: 0,
          successRate: 0,
          testRuns: []
        };
      }
      
      this.feedbackData.recoveries[strategyName].attempts++;
      if (recovery.success) {
        this.feedbackData.recoveries[strategyName].successes++;
      } else {
        this.feedbackData.recoveries[strategyName].failures++;
      }
      this.feedbackData.recoveries[strategyName].successRate = 
        this.feedbackData.recoveries[strategyName].successes / this.feedbackData.recoveries[strategyName].attempts;
      
      this.feedbackData.recoveries[strategyName].testRuns.push({
        testId: this.currentTestRun.testId,
        timestamp: this.currentTestRun.endTime
      });
    }
    
    // Update performance statistics
    for (const [metric, value] of Object.entries(this.currentTestRun.performance)) {
      if (!this.feedbackData.performance[metric]) {
        this.feedbackData.performance[metric] = {
          metric,
          values: [],
          average: 0,
          min: value,
          max: value
        };
      }
      
      this.feedbackData.performance[metric].values.push({
        testId: this.currentTestRun.testId,
        timestamp: this.currentTestRun.endTime,
        value
      });
      
      // Update statistics
      const values = this.feedbackData.performance[metric].values.map(v => v.value);
      this.feedbackData.performance[metric].average = values.reduce((a, b) => a + b, 0) / values.length;
      this.feedbackData.performance[metric].min = Math.min(...values);
      this.feedbackData.performance[metric].max = Math.max(...values);
    }
    
    // Save feedback data
    await this.saveFeedback();
    
    // Clear current test run
    this.currentTestRun = null;
  }
  
  /**
   * Collect an event
   * @param {string} type - Event type
   * @param {Object} data - Event data
   * @returns {Promise<void>}
   */
  async collectEvent(type, data) {
    await this.initialize();
    
    if (!this.config.enabled) {
      return;
    }
    
    // Create the event
    const event = {
      type,
      timestamp: data.timestamp || new Date(),
      data: { ...data }
    };
    
    // Add the event to the feedback data
    this.feedbackData.events.push(event);
    
    // If we're collecting for a test run, add it to the test run
    if (this.currentTestRun) {
      this.currentTestRun.events.push(event);
    }
    
    // Take a screenshot if needed
    if (this.config.collectScreenshots && (type.startsWith('error:') || type === 'test:end' && !data.success)) {
      await this.takeScreenshot(data.page, {
        testId: data.testId || (this.currentTestRun ? this.currentTestRun.testId : 'unknown'),
        eventType: type,
        timestamp: event.timestamp
      });
    }
  }
  
  /**
   * Take a screenshot
   * @param {Object} page - Playwright page
   * @param {Object} options - Screenshot options
   * @returns {Promise<string|null>} - Screenshot path
   */
  async takeScreenshot(page, options = {}) {
    if (!this.config.collectScreenshots || !page) {
      return null;
    }
    
    try {
      await this.ensureScreenshotDir();
      
      const timestamp = options.timestamp || new Date();
      const formattedTimestamp = timestamp.toISOString().replace(/:/g, '-');
      const filename = `${options.testId || 'unknown'}_${options.eventType || 'event'}_${formattedTimestamp}.png`;
      const screenshotPath = path.join(this.config.screenshotDir, filename);
      
      await page.screenshot({ path: screenshotPath, fullPage: true });
      
      return screenshotPath;
    } catch (error) {
      console.error('Failed to take screenshot:', error);
      return null;
    }
  }
  
  /**
   * Track a selector result
   * @param {string} selector - The selector
   * @param {boolean} success - Whether the selector was successful
   * @param {Object} context - Context information
   * @returns {Promise<void>}
   */
  async trackSelectorResult(selector, success, context = {}) {
    await this.initialize();
    
    if (!this.config.enabled || !this.currentTestRun) {
      return;
    }
    
    // Get or create selector data
    if (!this.currentTestRun.selectors[selector]) {
      this.currentTestRun.selectors[selector] = {
        selector,
        attempts: 0,
        successes: 0,
        failures: 0,
        successRate: 0,
        events: []
      };
    }
    
    // Update selector data
    this.currentTestRun.selectors[selector].attempts++;
    if (success) {
      this.currentTestRun.selectors[selector].successes++;
    } else {
      this.currentTestRun.selectors[selector].failures++;
    }
    this.currentTestRun.selectors[selector].successRate = 
      this.currentTestRun.selectors[selector].successes / this.currentTestRun.selectors[selector].attempts;
    
    // Add event
    const event = {
      timestamp: new Date(),
      success,
      context: { ...context }
    };
    this.currentTestRun.selectors[selector].events.push(event);
    
    // Collect event
    await this.collectEvent(success ? 'selector:success' : 'selector:failure', {
      testId: this.currentTestRun.testId,
      testName: this.currentTestRun.testName,
      selector,
      success,
      context: { ...context }
    });
  }
  
  /**
   * Track a recovery result
   * @param {Object} recoveryResult - Recovery result
   * @param {Object} context - Context information
   * @returns {Promise<void>}
   */
  async trackRecoveryResult(recoveryResult, context = {}) {
    await this.initialize();
    
    if (!this.config.enabled || !this.currentTestRun) {
      return;
    }
    
    // Add recovery to test run
    this.currentTestRun.recoveries.push({
      ...recoveryResult,
      timestamp: new Date(),
      context: { ...context }
    });
    
    // Collect event
    await this.collectEvent(recoveryResult.success ? 'recovery:success' : 'recovery:failure', {
      testId: this.currentTestRun.testId,
      testName: this.currentTestRun.testName,
      recovery: recoveryResult,
      context: { ...context }
    });
  }
  
  /**
   * Track performance metrics
   * @param {Object} metrics - Performance metrics
   * @returns {Promise<void>}
   */
  async trackPerformance(metrics) {
    await this.initialize();
    
    if (!this.config.enabled || !this.currentTestRun) {
      return;
    }
    
    // Update test run performance
    this.currentTestRun.performance = {
      ...this.currentTestRun.performance,
      ...metrics
    };
    
    // Collect event
    await this.collectEvent('performance', {
      testId: this.currentTestRun.testId,
      testName: this.currentTestRun.testName,
      metrics
    });
  }
  
  /**
   * Generate a feedback report
   * @param {Object} [options] - Report options
   * @returns {Promise<string>} - Report
   */
  async generateReport(options = {}) {
    await this.initialize();
    
    const format = options.format || 'markdown';
    
    if (format === 'markdown') {
      return this.generateMarkdownReport(options);
    } else if (format === 'json') {
      return JSON.stringify(this.feedbackData, null, 2);
    } else {
      throw new Error(`Unsupported report format: ${format}`);
    }
  }
  
  /**
   * Generate a markdown report
   * @param {Object} [options] - Report options
   * @returns {Promise<string>} - Markdown report
   */
  async generateMarkdownReport(options = {}) {
    let report = '# Self-Healing Automation Feedback Report\n\n';
    report += `Generated: ${new Date().toISOString()}\n\n`;
    
    // Test run summary
    report += '## Test Run Summary\n\n';
    report += `Total Test Runs: ${this.feedbackData.testRuns.length}\n`;
    
    const successfulRuns = this.feedbackData.testRuns.filter(run => run.success).length;
    const failedRuns = this.feedbackData.testRuns.length - successfulRuns;
    
    report += `Successful Runs: ${successfulRuns} (${Math.round(successfulRuns / this.feedbackData.testRuns.length * 100)}%)\n`;
    report += `Failed Runs: ${failedRuns} (${Math.round(failedRuns / this.feedbackData.testRuns.length * 100)}%)\n\n`;
    
    // Selector statistics
    report += '## Selector Statistics\n\n';
    report += '| Selector | Attempts | Success Rate |\n';
    report += '|----------|----------|-------------|\n';
    
    const sortedSelectors = Object.values(this.feedbackData.selectors)
      .sort((a, b) => b.attempts - a.attempts)
      .slice(0, 10);
    
    for (const selector of sortedSelectors) {
      report += `| \`${selector.selector}\` | ${selector.attempts} | ${Math.round(selector.successRate * 100)}% |\n`;
    }
    
    report += '\n';
    
    // Recovery statistics
    report += '## Recovery Statistics\n\n';
    report += '| Strategy | Attempts | Success Rate |\n';
    report += '|----------|----------|-------------|\n';
    
    const sortedRecoveries = Object.values(this.feedbackData.recoveries)
      .sort((a, b) => b.attempts - a.attempts);
    
    for (const recovery of sortedRecoveries) {
      report += `| ${recovery.strategy} | ${recovery.attempts} | ${Math.round(recovery.successRate * 100)}% |\n`;
    }
    
    report += '\n';
    
    // Performance statistics
    report += '## Performance Statistics\n\n';
    report += '| Metric | Average | Min | Max |\n';
    report += '|--------|---------|-----|-----|\n';
    
    const sortedPerformance = Object.values(this.feedbackData.performance)
      .sort((a, b) => a.metric.localeCompare(b.metric));
    
    for (const performance of sortedPerformance) {
      report += `| ${performance.metric} | ${Math.round(performance.average)} | ${performance.min} | ${performance.max} |\n`;
    }
    
    report += '\n';
    
    // Recent test runs
    report += '## Recent Test Runs\n\n';
    report += '| Test ID | Test Name | Result | Duration |\n';
    report += '|---------|-----------|--------|----------|\n';
    
    const recentRuns = [...this.feedbackData.testRuns]
      .sort((a, b) => new Date(b.endTime) - new Date(a.endTime))
      .slice(0, 10);
    
    for (const run of recentRuns) {
      report += `| ${run.testId} | ${run.testName} | ${run.success ? '✅' : '❌'} | ${Math.round(run.duration / 1000)}s |\n`;
    }
    
    return report;
  }
  
  /**
   * Analyze feedback data
   * @returns {Promise<Object>} - Analysis results
   */
  async analyzeFeedback() {
    await this.initialize();
    
    const analysis = {
      testRunTrends: this.analyzeTestRunTrends(),
      selectorTrends: this.analyzeSelectorTrends(),
      recoveryTrends: this.analyzeRecoveryTrends(),
      performanceTrends: this.analyzePerformanceTrends(),
      recommendations: this.generateRecommendations()
    };
    
    return analysis;
  }
  
  /**
   * Analyze test run trends
   * @returns {Object} - Test run trends
   */
  analyzeTestRunTrends() {
    const trends = {
      successRate: {
        overall: 0,
        trend: 'stable'
      },
      duration: {
        average: 0,
        trend: 'stable'
      },
      frequency: {
        perDay: 0,
        trend: 'stable'
      }
    };
    
    if (this.feedbackData.testRuns.length === 0) {
      return trends;
    }
    
    // Calculate success rate
    const successfulRuns = this.feedbackData.testRuns.filter(run => run.success).length;
    trends.successRate.overall = successfulRuns / this.feedbackData.testRuns.length;
    
    // Calculate average duration
    const totalDuration = this.feedbackData.testRuns.reduce((sum, run) => sum + run.duration, 0);
    trends.duration.average = totalDuration / this.feedbackData.testRuns.length;
    
    // Calculate frequency
    const dates = this.feedbackData.testRuns.map(run => new Date(run.startTime).toDateString());
    const uniqueDates = [...new Set(dates)];
    trends.frequency.perDay = this.feedbackData.testRuns.length / Math.max(1, uniqueDates.length);
    
    // Calculate trends
    if (this.feedbackData.testRuns.length >= 10) {
      const recentRuns = this.feedbackData.testRuns.slice(-10);
      const olderRuns = this.feedbackData.testRuns.slice(-20, -10);
      
      if (recentRuns.length > 0 && olderRuns.length > 0) {
        // Success rate trend
        const recentSuccessRate = recentRuns.filter(run => run.success).length / recentRuns.length;
        const olderSuccessRate = olderRuns.filter(run => run.success).length / olderRuns.length;
        
        if (recentSuccessRate > olderSuccessRate * 1.1) {
          trends.successRate.trend = 'improving';
        } else if (recentSuccessRate < olderSuccessRate * 0.9) {
          trends.successRate.trend = 'declining';
        }
        
        // Duration trend
        const recentDuration = recentRuns.reduce((sum, run) => sum + run.duration, 0) / recentRuns.length;
        const olderDuration = olderRuns.reduce((sum, run) => sum + run.duration, 0) / olderRuns.length;
        
        if (recentDuration < olderDuration * 0.9) {
          trends.duration.trend = 'improving';
        } else if (recentDuration > olderDuration * 1.1) {
          trends.duration.trend = 'declining';
        }
      }
    }
    
    return trends;
  }
  
  /**
   * Analyze selector trends
   * @returns {Object} - Selector trends
   */
  analyzeSelectorTrends() {
    const trends = {
      topProblematicSelectors: [],
      improvingSelectors: [],
      decliningSelectors: []
    };
    
    // Find problematic selectors
    const problematicSelectors = Object.values(this.feedbackData.selectors)
      .filter(selector => selector.attempts >= 5 && selector.successRate < 0.8)
      .sort((a, b) => a.successRate - b.successRate)
      .slice(0, 5);
    
    trends.topProblematicSelectors = problematicSelectors.map(selector => ({
      selector: selector.selector,
      attempts: selector.attempts,
      successRate: selector.successRate
    }));
    
    // TODO: Implement trend analysis for selectors
    
    return trends;
  }
  
  /**
   * Analyze recovery trends
   * @returns {Object} - Recovery trends
   */
  analyzeRecoveryTrends() {
    const trends = {
      mostEffectiveStrategies: [],
      leastEffectiveStrategies: []
    };
    
    // Find most effective strategies
    const effectiveStrategies = Object.values(this.feedbackData.recoveries)
      .filter(recovery => recovery.attempts >= 5)
      .sort((a, b) => b.successRate - a.successRate)
      .slice(0, 3);
    
    trends.mostEffectiveStrategies = effectiveStrategies.map(recovery => ({
      strategy: recovery.strategy,
      attempts: recovery.attempts,
      successRate: recovery.successRate
    }));
    
    // Find least effective strategies
    const ineffectiveStrategies = Object.values(this.feedbackData.recoveries)
      .filter(recovery => recovery.attempts >= 5)
      .sort((a, b) => a.successRate - b.successRate)
      .slice(0, 3);
    
    trends.leastEffectiveStrategies = ineffectiveStrategies.map(recovery => ({
      strategy: recovery.strategy,
      attempts: recovery.attempts,
      successRate: recovery.successRate
    }));
    
    return trends;
  }
  
  /**
   * Analyze performance trends
   * @returns {Object} - Performance trends
   */
  analyzePerformanceTrends() {
    const trends = {
      metrics: {}
    };
    
    // Analyze each performance metric
    for (const [metric, data] of Object.entries(this.feedbackData.performance)) {
      if (data.values.length < 5) {
        continue;
      }
      
      const values = data.values.map(v => v.value);
      const average = data.average;
      const stdDev = Math.sqrt(values.reduce((sum, val) => sum + Math.pow(val - average, 2), 0) / values.length);
      
      trends.metrics[metric] = {
        average,
        stdDev,
        min: data.min,
        max: data.max,
        trend: 'stable'
      };
      
      // Calculate trend
      if (data.values.length >= 10) {
        const recentValues = data.values.slice(-5).map(v => v.value);
        const olderValues = data.values.slice(-10, -5).map(v => v.value);
        
        const recentAvg = recentValues.reduce((sum, val) => sum + val, 0) / recentValues.length;
        const olderAvg = olderValues.reduce((sum, val) => sum + val, 0) / olderValues.length;
        
        if (recentAvg < olderAvg * 0.9) {
          trends.metrics[metric].trend = 'improving';
        } else if (recentAvg > olderAvg * 1.1) {
          trends.metrics[metric].trend = 'declining';
        }
      }
    }
    
    return trends;
  }
  
  /**
   * Generate recommendations based on feedback data
   * @returns {Array<Object>} - Recommendations
   */
  generateRecommendations() {
    const recommendations = [];
    
    // Recommend fixing problematic selectors
    const problematicSelectors = Object.values(this.feedbackData.selectors)
      .filter(selector => selector.attempts >= 5 && selector.successRate < 0.7)
      .sort((a, b) => a.successRate - b.successRate)
      .slice(0, 3);
    
    for (const selector of problematicSelectors) {
      recommendations.push({
        type: 'selector',
        priority: 'high',
        description: `Fix problematic selector: ${selector.selector}`,
        details: `This selector has a low success rate of ${Math.round(selector.successRate * 100)}% after ${selector.attempts} attempts.`,
        suggestion: 'Consider using a more reliable selector or adding fallback selectors.'
      });
    }
    
    // Recommend recovery strategy adjustments
    const ineffectiveStrategies = Object.values(this.feedbackData.recoveries)
      .filter(recovery => recovery.attempts >= 5 && recovery.successRate < 0.3)
      .sort((a, b) => a.successRate - b.successRate)
      .slice(0, 2);
    
    for (const strategy of ineffectiveStrategies) {
      recommendations.push({
        type: 'recovery',
        priority: 'medium',
        description: `Improve or disable recovery strategy: ${strategy.strategy}`,
        details: `This recovery strategy has a low success rate of ${Math.round(strategy.successRate * 100)}% after ${strategy.attempts} attempts.`,
        suggestion: 'Consider improving the strategy or disabling it if it is not effective.'
      });
    }
    
    // Recommend performance improvements
    const slowMetrics = Object.entries(this.feedbackData.performance)
      .filter(([_, data]) => data.values.length >= 5)
      .map(([metric, data]) => ({
        metric,
        average: data.average,
        max: data.max
      }))
      .filter(data => data.max > data.average * 2)
      .sort((a, b) => b.max - b.average - (a.max - a.average))
      .slice(0, 2);
    
    for (const metric of slowMetrics) {
      recommendations.push({
        type: 'performance',
        priority: 'low',
        description: `Optimize performance for metric: ${metric.metric}`,
        details: `This metric has a maximum value of ${metric.max} which is significantly higher than the average of ${Math.round(metric.average)}.`,
        suggestion: 'Look for outliers and optimize the performance of this operation.'
      });
    }
    
    return recommendations;
  }
  
  /**
   * Clean up resources
   * @returns {Promise<void>}
   */
  async cleanup() {
    if (this.currentTestRun) {
      await this.endTestRun({ success: false, error: new Error('Test run was not properly ended') });
    }
    
    if (this.config.persistFeedback) {
      await this.saveFeedback();
    }
  }
}

module.exports = FeedbackCollector;