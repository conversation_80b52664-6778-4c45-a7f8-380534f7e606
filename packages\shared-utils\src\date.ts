/**
 * Date utilities for QA Wolf Metrics Framework
 */

import { format, parse, addDays, subDays, addMonths, subMonths, addYears, subYears, differenceInDays, differenceInMonths, differenceInYears, isAfter, isBefore, isEqual, isValid } from 'date-fns';

/**
 * Format date
 * @param date Date to format
 * @param formatString Format string
 * @returns Formatted date
 */
export function formatDate(date: Date | number, formatString: string = 'yyyy-MM-dd'): string {
  return format(date, formatString);
}

/**
 * Parse date
 * @param dateString Date string
 * @param formatString Format string
 * @returns Parsed date
 */
export function parseDate(dateString: string, formatString: string = 'yyyy-MM-dd'): Date {
  return parse(dateString, formatString, new Date());
}

/**
 * Get current date
 * @returns Current date
 */
export function getCurrentDate(): Date {
  return new Date();
}

/**
 * Get current date string
 * @param formatString Format string
 * @returns Current date string
 */
export function getCurrentDateString(formatString: string = 'yyyy-MM-dd'): string {
  return formatDate(getCurrentDate(), formatString);
}

/**
 * Add days to date
 * @param date Date
 * @param days Days to add
 * @returns Date with days added
 */
export function addDaysToDate(date: Date | number, days: number): Date {
  return addDays(date, days);
}

/**
 * Subtract days from date
 * @param date Date
 * @param days Days to subtract
 * @returns Date with days subtracted
 */
export function subtractDaysFromDate(date: Date | number, days: number): Date {
  return subDays(date, days);
}

/**
 * Add months to date
 * @param date Date
 * @param months Months to add
 * @returns Date with months added
 */
export function addMonthsToDate(date: Date | number, months: number): Date {
  return addMonths(date, months);
}

/**
 * Subtract months from date
 * @param date Date
 * @param months Months to subtract
 * @returns Date with months subtracted
 */
export function subtractMonthsFromDate(date: Date | number, months: number): Date {
  return subMonths(date, months);
}

/**
 * Add years to date
 * @param date Date
 * @param years Years to add
 * @returns Date with years added
 */
export function addYearsToDate(date: Date | number, years: number): Date {
  return addYears(date, years);
}

/**
 * Subtract years from date
 * @param date Date
 * @param years Years to subtract
 * @returns Date with years subtracted
 */
export function subtractYearsFromDate(date: Date | number, years: number): Date {
  return subYears(date, years);
}

/**
 * Get difference in days between dates
 * @param dateLeft Left date
 * @param dateRight Right date
 * @returns Difference in days
 */
export function getDifferenceInDays(dateLeft: Date | number, dateRight: Date | number): number {
  return differenceInDays(dateLeft, dateRight);
}

/**
 * Get difference in months between dates
 * @param dateLeft Left date
 * @param dateRight Right date
 * @returns Difference in months
 */
export function getDifferenceInMonths(dateLeft: Date | number, dateRight: Date | number): number {
  return differenceInMonths(dateLeft, dateRight);
}

/**
 * Get difference in years between dates
 * @param dateLeft Left date
 * @param dateRight Right date
 * @returns Difference in years
 */
export function getDifferenceInYears(dateLeft: Date | number, dateRight: Date | number): number {
  return differenceInYears(dateLeft, dateRight);
}

/**
 * Check if date is after another date
 * @param date Date
 * @param dateToCompare Date to compare
 * @returns True if date is after dateToCompare, false otherwise
 */
export function isDateAfter(date: Date | number, dateToCompare: Date | number): boolean {
  return isAfter(date, dateToCompare);
}

/**
 * Check if date is before another date
 * @param date Date
 * @param dateToCompare Date to compare
 * @returns True if date is before dateToCompare, false otherwise
 */
export function isDateBefore(date: Date | number, dateToCompare: Date | number): boolean {
  return isBefore(date, dateToCompare);
}

/**
 * Check if date is equal to another date
 * @param date Date
 * @param dateToCompare Date to compare
 * @returns True if date is equal to dateToCompare, false otherwise
 */
export function isDateEqual(date: Date | number, dateToCompare: Date | number): boolean {
  return isEqual(date, dateToCompare);
}

/**
 * Check if date is valid
 * @param date Date
 * @returns True if date is valid, false otherwise
 */
export function isDateValid(date: Date): boolean {
  return isValid(date);
}

/**
 * Get date from timestamp
 * @param timestamp Timestamp
 * @returns Date
 */
export function getDateFromTimestamp(timestamp: number): Date {
  return new Date(timestamp);
}

/**
 * Get timestamp from date
 * @param date Date
 * @returns Timestamp
 */
export function getTimestampFromDate(date: Date): number {
  return date.getTime();
}

/**
 * Get start of day
 * @param date Date
 * @returns Start of day
 */
export function getStartOfDay(date: Date): Date {
  const startOfDay = new Date(date);
  startOfDay.setHours(0, 0, 0, 0);
  return startOfDay;
}

/**
 * Get end of day
 * @param date Date
 * @returns End of day
 */
export function getEndOfDay(date: Date): Date {
  const endOfDay = new Date(date);
  endOfDay.setHours(23, 59, 59, 999);
  return endOfDay;
}

/**
 * Get start of month
 * @param date Date
 * @returns Start of month
 */
export function getStartOfMonth(date: Date): Date {
  const startOfMonth = new Date(date);
  startOfMonth.setDate(1);
  startOfMonth.setHours(0, 0, 0, 0);
  return startOfMonth;
}

/**
 * Get end of month
 * @param date Date
 * @returns End of month
 */
export function getEndOfMonth(date: Date): Date {
  const endOfMonth = new Date(date);
  endOfMonth.setMonth(endOfMonth.getMonth() + 1);
  endOfMonth.setDate(0);
  endOfMonth.setHours(23, 59, 59, 999);
  return endOfMonth;
}

/**
 * Get start of year
 * @param date Date
 * @returns Start of year
 */
export function getStartOfYear(date: Date): Date {
  const startOfYear = new Date(date);
  startOfYear.setMonth(0);
  startOfYear.setDate(1);
  startOfYear.setHours(0, 0, 0, 0);
  return startOfYear;
}

/**
 * Get end of year
 * @param date Date
 * @returns End of year
 */
export function getEndOfYear(date: Date): Date {
  const endOfYear = new Date(date);
  endOfYear.setMonth(11);
  endOfYear.setDate(31);
  endOfYear.setHours(23, 59, 59, 999);
  return endOfYear;
}
