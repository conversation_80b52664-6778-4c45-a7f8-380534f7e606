/**
 * Linear issues for Linear integration
 */

import { LinearClient, Issue, IssueConnection } from '@linear/sdk';
import { LinearIssuesOptions, LinearIssueFilter, LinearIssueCreateInput, LinearIssueUpdateInput } from './types';
import { EventBus, EventType } from '@qawolf/core';

/**
 * Linear issues
 */
export class LinearIssues {
  /**
   * Linear client
   */
  private client: LinearClient;
  
  /**
   * Team ID
   */
  private teamId: string;
  
  /**
   * Project ID
   */
  private projectId: string;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options Linear issues options
   */
  constructor(options: LinearIssuesOptions = {}) {
    if (!options.client && !options.apiKey) {
      throw new Error('Linear client or API key is required');
    }
    
    if (options.client) {
      this.client = options.client;
    } else {
      this.client = new LinearClient({
        apiKey: options.apiKey
      });
    }
    
    this.teamId = options.teamId || '';
    this.projectId = options.projectId || '';
    this.eventBus = EventBus.getInstance();
  }
  
  /**
   * Get issues
   * @param filter Issue filter
   * @returns Issues
   */
  async getIssues(filter?: LinearIssueFilter): Promise<Issue[]> {
    try {
      // Build filter
      const queryFilter: Record<string, any> = {};
      
      if (filter?.id) {
        queryFilter.id = { eq: filter.id };
      }
      
      if (filter?.number) {
        queryFilter.number = { eq: filter.number };
      }
      
      if (filter?.title) {
        queryFilter.title = { contains: filter.title };
      }
      
      if (filter?.description) {
        queryFilter.description = { contains: filter.description };
      }
      
      if (filter?.state) {
        queryFilter.state = { name: { eq: filter.state } };
      }
      
      if (filter?.priority) {
        queryFilter.priority = { eq: filter.priority };
      }
      
      if (filter?.labels && filter.labels.length > 0) {
        queryFilter.labels = { name: { in: filter.labels } };
      }
      
      if (filter?.assignee) {
        queryFilter.assignee = { id: { eq: filter.assignee } };
      }
      
      if (filter?.creator) {
        queryFilter.creator = { id: { eq: filter.creator } };
      }
      
      if (filter?.team) {
        queryFilter.team = { id: { eq: filter.team } };
      } else if (this.teamId) {
        queryFilter.team = { id: { eq: this.teamId } };
      }
      
      if (filter?.project) {
        queryFilter.project = { id: { eq: filter.project } };
      } else if (this.projectId) {
        queryFilter.project = { id: { eq: this.projectId } };
      }
      
      if (filter?.cycle) {
        queryFilter.cycle = { id: { eq: filter.cycle } };
      }
      
      if (filter?.createdAfter) {
        queryFilter.createdAt = { ...queryFilter.createdAt, gt: filter.createdAfter };
      }
      
      if (filter?.createdBefore) {
        queryFilter.createdAt = { ...queryFilter.createdAt, lt: filter.createdBefore };
      }
      
      if (filter?.updatedAfter) {
        queryFilter.updatedAt = { ...queryFilter.updatedAt, gt: filter.updatedAfter };
      }
      
      if (filter?.updatedBefore) {
        queryFilter.updatedAt = { ...queryFilter.updatedAt, lt: filter.updatedBefore };
      }
      
      if (filter?.dueDate) {
        queryFilter.dueDate = { eq: filter.dueDate };
      }
      
      if (filter?.dueDateAfter) {
        queryFilter.dueDate = { ...queryFilter.dueDate, gt: filter.dueDateAfter };
      }
      
      if (filter?.dueDateBefore) {
        queryFilter.dueDate = { ...queryFilter.dueDate, lt: filter.dueDateBefore };
      }
      
      // Build order by
      const orderBy = filter?.sortBy ? `${filter.sortBy}_${filter.sortOrder || 'ASC'}` : undefined;
      
      // Get issues
      const issues = await this.client.issues({
        filter: queryFilter,
        orderBy
      });
      
      // Get all issues
      const allIssues = await this.getAllIssues(issues);
      
      // Emit event
      this.eventBus.emit(EventType.LINEAR_ISSUES_GET_ISSUES, {
        issues: allIssues
      });
      
      return allIssues;
    } catch (error) {
      this.handleError(error, 'Failed to get issues');
      return [];
    }
  }
  
  /**
   * Get issue by ID
   * @param id Issue ID
   * @returns Issue
   */
  async getIssueById(id: string): Promise<Issue | null> {
    try {
      const issue = await this.client.issue(id);
      
      // Emit event
      this.eventBus.emit(EventType.LINEAR_ISSUES_GET_ISSUE_BY_ID, {
        issue
      });
      
      return issue;
    } catch (error) {
      this.handleError(error, `Failed to get issue with ID ${id}`);
      return null;
    }
  }
  
  /**
   * Get issue by number
   * @param number Issue number
   * @param teamId Team ID
   * @returns Issue
   */
  async getIssueByNumber(number: number, teamId?: string): Promise<Issue | null> {
    try {
      const id = teamId || this.teamId;
      
      if (!id) {
        throw new Error('Team ID is required');
      }
      
      const issue = await this.client.issueByNumber(number, id);
      
      // Emit event
      this.eventBus.emit(EventType.LINEAR_ISSUES_GET_ISSUE_BY_NUMBER, {
        issue
      });
      
      return issue;
    } catch (error) {
      this.handleError(error, `Failed to get issue with number ${number}`);
      return null;
    }
  }
  
  /**
   * Create issue
   * @param input Issue create input
   * @returns Issue
   */
  async createIssue(input: LinearIssueCreateInput): Promise<Issue | null> {
    try {
      const teamId = input.teamId || this.teamId;
      
      if (!teamId) {
        throw new Error('Team ID is required');
      }
      
      const issue = await this.client.issueCreate({
        title: input.title,
        description: input.description,
        stateId: input.stateId,
        priority: input.priority,
        labelIds: input.labelIds,
        assigneeId: input.assigneeId,
        teamId,
        projectId: input.projectId || this.projectId,
        cycleId: input.cycleId,
        parentId: input.parentId,
        dueDate: input.dueDate ? input.dueDate.toISOString() : undefined,
        estimate: input.estimate
      });
      
      // Emit event
      this.eventBus.emit(EventType.LINEAR_ISSUES_CREATE_ISSUE, {
        issue: issue.issue
      });
      
      return issue.issue;
    } catch (error) {
      this.handleError(error, 'Failed to create issue');
      return null;
    }
  }
  
  /**
   * Update issue
   * @param id Issue ID
   * @param input Issue update input
   * @returns Issue
   */
  async updateIssue(id: string, input: LinearIssueUpdateInput): Promise<Issue | null> {
    try {
      const issue = await this.client.issueUpdate(id, {
        title: input.title,
        description: input.description,
        stateId: input.stateId,
        priority: input.priority,
        labelIds: input.labelIds,
        assigneeId: input.assigneeId,
        teamId: input.teamId,
        projectId: input.projectId,
        cycleId: input.cycleId,
        parentId: input.parentId,
        dueDate: input.dueDate ? input.dueDate.toISOString() : undefined,
        estimate: input.estimate
      });
      
      // Emit event
      this.eventBus.emit(EventType.LINEAR_ISSUES_UPDATE_ISSUE, {
        issue: issue.issue
      });
      
      return issue.issue;
    } catch (error) {
      this.handleError(error, `Failed to update issue with ID ${id}`);
      return null;
    }
  }
  
  /**
   * Delete issue
   * @param id Issue ID
   * @returns True if deleted, false otherwise
   */
  async deleteIssue(id: string): Promise<boolean> {
    try {
      const result = await this.client.issueDelete(id);
      
      // Emit event
      this.eventBus.emit(EventType.LINEAR_ISSUES_DELETE_ISSUE, {
        id,
        success: result.success
      });
      
      return result.success;
    } catch (error) {
      this.handleError(error, `Failed to delete issue with ID ${id}`);
      return false;
    }
  }
  
  /**
   * Get all issues
   * @param issues Issue connection
   * @returns Issues
   */
  private async getAllIssues(issues: IssueConnection): Promise<Issue[]> {
    const allIssues: Issue[] = [];
    let currentPage = issues;
    
    while (true) {
      const nodes = await currentPage.nodes;
      allIssues.push(...nodes);
      
      if (!(await currentPage.hasNextPage)) {
        break;
      }
      
      currentPage = await currentPage.next();
    }
    
    return allIssues;
  }
  
  /**
   * Handle error
   * @param error Error
   * @param message Error message
   */
  private handleError(error: any, message: string): void {
    console.error(message, error);
    
    // Emit event
    this.eventBus.emit(EventType.LINEAR_ISSUES_ERROR, {
      error,
      message
    });
  }
  
  /**
   * Get team ID
   * @returns Team ID
   */
  getTeamId(): string {
    return this.teamId;
  }
  
  /**
   * Get project ID
   * @returns Project ID
   */
  getProjectId(): string {
    return this.projectId;
  }
  
  /**
   * Set team ID
   * @param teamId Team ID
   * @returns This instance for chaining
   */
  setTeamId(teamId: string): LinearIssues {
    this.teamId = teamId;
    return this;
  }
  
  /**
   * Set project ID
   * @param projectId Project ID
   * @returns This instance for chaining
   */
  setProjectId(projectId: string): LinearIssues {
    this.projectId = projectId;
    return this;
  }
}

/**
 * Create Linear issues
 * @param options Linear issues options
 * @returns Linear issues
 */
export function createLinearIssues(options: LinearIssuesOptions = {}): LinearIssues {
  return new LinearIssues(options);
}
