/**
 * MCP Integration Tests
 * 
 * This file contains integration tests for the MCP integration.
 */

const { test, expect } = require('@playwright/test');
const { McpController } = require('../../src/mcp');
const mcpOptimizer = require('@qawolf/mcp-optimizer');

// Mock the MCP optimizer
jest.mock('@qawolf/mcp-optimizer', () => ({
  optimizeSelectors: jest.fn().mockResolvedValue({
    selectors: [
      {
        original: 'button.login-button',
        optimized: '[data-testid="login-button"]',
        fallbacks: ['button.login-button'],
        reliability: 0.9,
        performance: 0.95
      }
    ],
    timestamp: '2023-01-01T00:00:00.000Z'
  }),
  selectMcpTool: jest.fn().mockResolvedValue({
    tool: 'playwright_mcp',
    category: 'browser_interaction',
    estimatedTokenUsage: 300,
    configuration: {},
    timestamp: '2023-01-01T00:00:00.000Z'
  }),
  analyzeScreenshot: jest.fn().mockResolvedValue({
    elements: [
      {
        type: 'button',
        text: 'Login',
        selector: 'button.login-button',
        confidence: 0.95
      }
    ],
    timestamp: '2023-01-01T00:00:00.000Z'
  }),
  generateReport: jest.fn().mockResolvedValue({
    report: '# MCP Usage Report',
    format: 'markdown',
    timestamp: '2023-01-01T00:00:00.000Z'
  }),
  tools: {
    playwrightMcp: {
      isPlaywrightMCPRunning: jest.fn().mockResolvedValue(false),
      startPlaywrightMCP: jest.fn().mockResolvedValue({
        port: 8932,
        stop: jest.fn()
      })
    }
  }
}));

test.describe('MCP Controller', () => {
  let controller;
  
  test.beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Create a new controller for each test
    controller = new McpController({
      autoStartPlaywrightMcp: false // Disable auto-start for tests
    });
  });
  
  test('should optimize selectors', async () => {
    const result = await controller.optimizeSelectors(['button.login-button']);
    
    expect(mcpOptimizer.optimizeSelectors).toHaveBeenCalledWith({
      selectors: ['button.login-button'],
      options: {
        generateFallbacks: true,
        prioritizeTestIds: true
      }
    });
    
    expect(result).toEqual({
      selectors: [
        {
          original: 'button.login-button',
          optimized: '[data-testid="login-button"]',
          fallbacks: ['button.login-button'],
          reliability: 0.9,
          performance: 0.95
        }
      ],
      timestamp: '2023-01-01T00:00:00.000Z'
    });
  });
  
  test('should select MCP tool', async () => {
    const result = await controller.selectMcpTool({
      type: 'browser',
      subtype: 'click'
    });
    
    expect(mcpOptimizer.selectMcpTool).toHaveBeenCalledWith({
      type: 'browser_interaction',
      subtype: 'click',
      context: {}
    }, {
      tokenOptimized: true,
      performanceOptimized: false
    });
    
    expect(result).toEqual({
      tool: 'playwright_mcp',
      category: 'browser_interaction',
      estimatedTokenUsage: 300,
      configuration: {},
      timestamp: '2023-01-01T00:00:00.000Z'
    });
  });
  
  test('should analyze screenshot', async () => {
    const screenshot = Buffer.from('fake-screenshot');
    const result = await controller.analyzeScreenshot(screenshot);
    
    expect(mcpOptimizer.analyzeScreenshot).toHaveBeenCalledWith({
      screenshot
    });
    
    expect(result).toEqual({
      elements: [
        {
          type: 'button',
          text: 'Login',
          selector: 'button.login-button',
          confidence: 0.95
        }
      ],
      timestamp: '2023-01-01T00:00:00.000Z'
    });
  });
  
  test('should generate report', async () => {
    const results = { key: 'value' };
    const result = await controller.generateReport(results);
    
    expect(mcpOptimizer.generateReport).toHaveBeenCalledWith(results, {
      format: 'markdown',
      includeTimestamps: true,
      includeTokenUsage: true
    });
    
    expect(result).toEqual({
      report: '# MCP Usage Report',
      format: 'markdown',
      timestamp: '2023-01-01T00:00:00.000Z'
    });
  });
  
  test('should initialize Playwright MCP if autoStartPlaywrightMcp is true', async () => {
    controller = new McpController({
      autoStartPlaywrightMcp: true
    });
    
    await controller.initialize();
    
    expect(mcpOptimizer.tools.playwrightMcp.isPlaywrightMCPRunning).toHaveBeenCalledWith(8932);
    expect(mcpOptimizer.tools.playwrightMcp.startPlaywrightMCP).toHaveBeenCalledWith({
      port: 8932,
      headless: false,
      configPath: null
    });
  });
  
  test('should not initialize Playwright MCP if autoStartPlaywrightMcp is false', async () => {
    await controller.initialize();
    
    expect(mcpOptimizer.tools.playwrightMcp.isPlaywrightMCPRunning).not.toHaveBeenCalled();
    expect(mcpOptimizer.tools.playwrightMcp.startPlaywrightMCP).not.toHaveBeenCalled();
  });
  
  test('should cleanup resources', async () => {
    controller = new McpController({
      autoStartPlaywrightMcp: true
    });
    
    await controller.initialize();
    await controller.cleanup();
    
    expect(controller.initialized).toBe(false);
    expect(controller.playwrightMcpServer).toBe(null);
  });
});

test.describe('MCP Adapters', () => {
  test('should adapt selectors', () => {
    const { adaptSelectors } = require('../../src/mcp/adapters');
    
    // Single selector
    expect(adaptSelectors('button.login-button')).toEqual(['button.login-button']);
    
    // Array of selectors
    expect(adaptSelectors(['button.login-button', 'input[name="username"]']))
      .toEqual(['button.login-button', 'input[name="username"]']);
    
    // Invalid selectors
    expect(() => adaptSelectors(null)).toThrow('Selectors are required');
    expect(() => adaptSelectors(123)).toThrow('Selectors must be a string or an array of strings');
    expect(() => adaptSelectors([123])).toThrow('Invalid selector: 123');
  });
  
  test('should adapt task', () => {
    const { adaptTask } = require('../../src/mcp/adapters');
    
    // Valid task
    expect(adaptTask({ type: 'browser', subtype: 'click' }))
      .toEqual({ type: 'browser_interaction', subtype: 'click', context: {} });
    
    // Task with normalized type
    expect(adaptTask({ type: 'browser_interaction', subtype: 'click' }))
      .toEqual({ type: 'browser_interaction', subtype: 'click', context: {} });
    
    // Task with context
    expect(adaptTask({ type: 'browser', subtype: 'click', context: { key: 'value' } }))
      .toEqual({ type: 'browser_interaction', subtype: 'click', context: { key: 'value' } });
    
    // Invalid tasks
    expect(() => adaptTask(null)).toThrow('Task is required');
    expect(() => adaptTask('task')).toThrow('Task must be an object');
    expect(() => adaptTask({})).toThrow('Task must have a type');
  });
});

test.describe('MCP Configuration', () => {
  test('should create default config', () => {
    const { createDefaultConfig } = require('../../src/mcp/config');
    
    const config = createDefaultConfig();
    
    expect(config).toEqual({
      enabled: true,
      autoStartPlaywrightMcp: true,
      playwrightMcpPort: 8932,
      playwrightMcpConfigPath: null,
      headless: false,
      generateFallbacks: true,
      prioritizeTestIds: true,
      tokenOptimized: true,
      performanceOptimized: false,
      reportFormat: 'markdown',
      includeTimestamps: true,
      includeTokenUsage: true,
      logLevel: 'info',
      retryCount: 3,
      retryDelay: 1000,
      timeout: 30000
    });
  });
  
  test('should validate config', () => {
    const { validateConfig } = require('../../src/mcp/config');
    
    // Valid config
    expect(() => validateConfig({ enabled: true })).not.toThrow();
    
    // Invalid configs
    expect(() => validateConfig(null)).toThrow('Configuration is required');
    expect(() => validateConfig('config')).toThrow('Configuration must be an object');
    expect(() => validateConfig({ enabled: 'true' })).toThrow('config.enabled must be a boolean');
    expect(() => validateConfig({ autoStartPlaywrightMcp: 'true' })).toThrow('config.autoStartPlaywrightMcp must be a boolean');
    expect(() => validateConfig({ playwrightMcpPort: '8932' })).toThrow('config.playwrightMcpPort must be a number');
    expect(() => validateConfig({ headless: 'false' })).toThrow('config.headless must be a boolean');
    expect(() => validateConfig({ generateFallbacks: 'true' })).toThrow('config.generateFallbacks must be a boolean');
    expect(() => validateConfig({ prioritizeTestIds: 'true' })).toThrow('config.prioritizeTestIds must be a boolean');
    expect(() => validateConfig({ tokenOptimized: 'true' })).toThrow('config.tokenOptimized must be a boolean');
    expect(() => validateConfig({ performanceOptimized: 'false' })).toThrow('config.performanceOptimized must be a boolean');
    expect(() => validateConfig({ reportFormat: 123 })).toThrow('config.reportFormat must be a string');
    expect(() => validateConfig({ includeTimestamps: 'true' })).toThrow('config.includeTimestamps must be a boolean');
    expect(() => validateConfig({ includeTokenUsage: 'true' })).toThrow('config.includeTokenUsage must be a boolean');
    expect(() => validateConfig({ logLevel: 123 })).toThrow('config.logLevel must be a string');
    expect(() => validateConfig({ retryCount: '3' })).toThrow('config.retryCount must be a number');
    expect(() => validateConfig({ retryDelay: '1000' })).toThrow('config.retryDelay must be a number');
    expect(() => validateConfig({ timeout: '30000' })).toThrow('config.timeout must be a number');
  });
  
  test('should merge configs', () => {
    const { mergeConfigs } = require('../../src/mcp/config');
    
    const baseConfig = { key1: 'value1', key2: 'value2' };
    const overrides = { key2: 'new-value2', key3: 'value3' };
    
    expect(mergeConfigs(baseConfig, overrides)).toEqual({
      key1: 'value1',
      key2: 'new-value2',
      key3: 'value3'
    });
    
    expect(mergeConfigs(baseConfig, null)).toEqual(baseConfig);
    expect(() => mergeConfigs(null, overrides)).toThrow('Base configuration is required');
  });
});