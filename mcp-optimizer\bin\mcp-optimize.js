#!/usr/bin/env node

/**
 * MCP Optimizer CLI
 * 
 * This is the entry point for the MCP optimizer CLI.
 */

const { runCommand, explainCommand } = require('../src/cli/commands');

// Parse command line arguments
const args = process.argv.slice(2);
const command = args[0];
const options = parseOptions(args.slice(1));

// Execute the appropriate command
async function main() {
  try {
    if (command === 'run') {
      await runCommand(options);
    } else if (command === 'explain') {
      await explainCommand(options);
    } else if (command === 'help' || command === '--help' || command === '-h') {
      showHelp();
    } else if (command === 'version' || command === '--version' || command === '-v') {
      showVersion();
    } else {
      console.error(`Unknown command: ${command}`);
      showHelp();
      process.exit(1);
    }
  } catch (error) {
    console.error(`Error: ${error.message}`);
    process.exit(1);
  }
}

// Parse command line options
function parseOptions(args) {
  const options = {};
  
  for (let i = 0; i < args.length; i++) {
    const arg = args[i];
    
    if (arg.startsWith('--')) {
      const key = arg.slice(2);
      
      if (i + 1 < args.length && !args[i + 1].startsWith('--')) {
        options[key] = args[i + 1];
        i++;
      } else {
        options[key] = true;
      }
    }
  }
  
  return options;
}

// Show help message
function showHelp() {
  console.log(`
MCP Optimizer CLI

Usage:
  mcp-optimize run [options]
  mcp-optimize explain [options]
  mcp-optimize help
  mcp-optimize version

Commands:
  run                Run the optimizer on a test or set of tests
  explain            Explain the optimization decisions made
  help               Show this help message
  version            Show version information

Run Options:
  --test <path>      Path to the test file
  --dir <path>       Path to the directory containing test files
  --selectors <list> Comma-separated list of selectors to optimize
  --fallbacks        Generate fallback selectors (default: true)
  --testIds          Prioritize test IDs (default: true)
  --report           Generate a report (default: false)
  --format <format>  Report format (markdown, json, html)
  --timestamps       Include timestamps in the report (default: true)
  --tokens           Include token usage in the report (default: true)
  --startMcp         Start Playwright MCP (default: false)
  --port <port>      Port for Playwright MCP (default: 8932)
  --headless         Run Playwright MCP in headless mode (default: false)

Explain Options:
  --task <type>      Task type (e.g., browser_interaction, file_operation)
  --subtype <type>   Task subtype (e.g., click, type, read)
  --tokenOptimized   Optimize for token usage (default: true)
  --performanceOptimized Optimize for performance (default: false)
`);
}

// Show version information
function showVersion() {
  const packageJson = require('../package.json');
  console.log(`MCP Optimizer v${packageJson.version}`);
}

// Run the CLI
main().catch(error => {
  console.error(`Fatal error: ${error.message}`);
  process.exit(1);
});