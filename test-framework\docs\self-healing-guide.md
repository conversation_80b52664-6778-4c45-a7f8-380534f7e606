# Self-Healing Automation Guide

This guide explains how to use the self-healing automation capabilities in the QA Wolf testing framework.

## What is Self-Healing Automation?

Self-healing automation is a set of techniques that allow tests to automatically recover from failures and improve over time. It includes:

- **Self-Healing Selectors**: Automatically try alternative selectors when the primary selector fails
- **Automatic Recovery**: Attempt recovery strategies when a test step fails
- **Feedback Collection**: Collect data on failures and successful recoveries
- **Learning Engine**: Use collected data to improve future test runs

## Getting Started

### Basic Usage

```javascript
const { test } = require('@playwright/test');
const { createSelfHealingController } = require('@qawolf/test-framework');

test('My test with self-healing', async ({ page }) => {
  // Create a self-healing controller
  const selfHealingController = createSelfHealingController();
  
  try {
    // Start the test run
    await selfHealingController.startTest({
      testId: 'my-test',
      testName: 'My test with self-healing'
    });
    
    // Create a self-healing page
    const selfHealingPage = selfHealingController.createPage(page);
    
    // Use the self-healing page like a normal Playwright page
    await selfHealingPage.goto('https://example.com');
    await selfHealingPage.fill('#username', 'testuser');
    await selfHealingPage.fill('#password', 'password');
    await selfHealingPage.click('#login-button');
    
    // End the test run
    await selfHealingController.endTest({
      success: true
    });
  } catch (error) {
    // End the test run with failure
    await selfHealingController.endTest({
      success: false,
      error
    });
    
    throw error;
  } finally {
    // Clean up resources
    await selfHealingController.cleanup();
  }
});
```

### Configuration

You can configure the self-healing controller with various options:

```javascript
const selfHealingController = createSelfHealingController({
  // General
  enabled: true,
  
  // Selector Healing
  selectorHealing: {
    enabled: true,
    maxAttempts: 3,
    strategies: ['css-relaxation', 'attribute-based', 'text-based', 'xpath'],
    persistHistory: true,
    historyPath: './selector-history.json',
    optimizationThreshold: 5,
    confidenceThreshold: 0.7
  },
  
  // Recovery
  recovery: {
    enabled: true,
    maxAttempts: 3,
    strategies: ['retry', 'wait', 'selector', 'refresh', 'screenshot'],
    timeout: 30000,
    waitTime: 1000,
    screenshotOnFailure: true
  },
  
  // Feedback Collection
  feedbackCollection: {
    enabled: true,
    collectScreenshots: true,
    screenshotDir: './screenshots/feedback',
    persistFeedback: true,
    feedbackPath: './feedback-data.json',
    anonymizeData: false
  },
  
  // Learning
  learning: {
    enabled: true,
    autoOptimize: true,
    optimizationInterval: 10,
    persistLearning: true,
    learningPath: './learning-data.json',
    suggestImprovements: true
  }
});
```

## Self-Healing Selectors

Self-healing selectors automatically try alternative selectors when the primary selector fails. The system learns from successful alternatives and updates its selector strategy.

### Selector Healing Strategies

- **CSS Relaxation**: Relaxes CSS selectors by removing specific classes, IDs, or attributes
- **Attribute-Based**: Generates selectors based on element attributes
- **Text-Based**: Generates selectors based on text content
- **XPath**: Converts CSS selectors to XPath

### Example

```javascript
// This selector will be automatically healed if it fails
await selfHealingPage.click('.login-button');

// You can also use locators with self-healing
const loginButton = selfHealingPage.locator('.login-button');
await loginButton.click();
```

## Automatic Recovery

Automatic recovery attempts to recover from test failures using various strategies.

### Recovery Strategies

- **Retry**: Retry the action
- **Wait**: Wait longer for elements to appear
- **Selector**: Try alternative selectors
- **Refresh**: Refresh the page and retry
- **Screenshot**: Take a screenshot for debugging

### Example

```javascript
// If this action fails, recovery strategies will be attempted
await selfHealingPage.click('.login-button');
```

## Feedback Collection

Feedback collection gathers data on test runs, including selector usage, recovery attempts, and performance metrics.

### Example

```javascript
// Track performance metrics
await selfHealingController.trackPerformance({
  loginTime: 1500,
  pageLoadTime: 2000
});

// Generate a report
const report = await selfHealingController.generateReport();
console.log(report);

// Analyze feedback
const analysis = await selfHealingController.analyzeFeedback();
console.log(analysis);
```

## Learning Engine

The learning engine uses collected feedback data to improve test strategies over time.

### Example

```javascript
// Suggest improvements for a test script
const improvements = await selfHealingController.suggestImprovements('/path/to/test.js');
console.log(improvements);
```

## API Reference

### SelfHealingController

- `createPage(page)`: Create a self-healing page wrapper
- `startTest(testInfo)`: Start a test run
- `endTest(result)`: End a test run
- `trackPerformance(metrics)`: Track performance metrics
- `generateReport(options)`: Generate a report
- `analyzeFeedback()`: Analyze feedback data
- `suggestImprovements(testPath)`: Suggest improvements for a test script
- `cleanup()`: Clean up resources

### SelectorHealer

- `healSelector(selector, context)`: Try to heal a failed selector
- `generateAlternatives(selector, context)`: Generate alternative selectors
- `trackSelectorResult(selector, success, context)`: Track selector success/failure
- `optimizeSelector(selector, context)`: Optimize a selector based on history

### RecoveryManager

- `recoverFromFailure(error, context)`: Attempt to recover from a failure
- `registerStrategy(name, execute, priority)`: Register a recovery strategy
- `executeStrategy(name, context)`: Execute a specific strategy

### FeedbackCollector

- `startTestRun(testInfo)`: Start collecting feedback for a test run
- `endTestRun(result)`: End collecting feedback for a test run
- `collectEvent(type, data)`: Collect an event
- `trackSelectorResult(selector, success, context)`: Track a selector result
- `trackRecoveryResult(recoveryResult, context)`: Track a recovery result
- `trackPerformance(metrics)`: Track performance metrics
- `generateReport(options)`: Generate a feedback report
- `analyzeFeedback()`: Analyze feedback data

### LearningEngine

- `learn(feedbackData)`: Learn from feedback data
- `optimizeStrategies()`: Optimize strategies based on learning
- `suggestImprovements(testPath)`: Suggest improvements for a test script
- `exportLearning()`: Export learning data
- `importLearning(data)`: Import learning data