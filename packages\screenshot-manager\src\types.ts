/**
 * Types for screenshot manager
 */

import { Page, Locator } from 'playwright';

/**
 * Screenshot type
 */
export enum ScreenshotType {
  /**
   * Full page screenshot
   */
  FULL_PAGE = 'full-page',
  
  /**
   * Viewport screenshot
   */
  VIEWPORT = 'viewport',
  
  /**
   * Element screenshot
   */
  ELEMENT = 'element',
  
  /**
   * Custom screenshot
   */
  CUSTOM = 'custom'
}

/**
 * Screenshot format
 */
export enum ScreenshotFormat {
  /**
   * PNG format
   */
  PNG = 'png',
  
  /**
   * JPEG format
   */
  JPEG = 'jpeg'
}

/**
 * Screenshot quality
 */
export type ScreenshotQuality = number;

/**
 * Screenshot options
 */
export interface ScreenshotOptions {
  /**
   * Screenshot type
   */
  type?: ScreenshotType;
  
  /**
   * Screenshot format
   */
  format?: ScreenshotFormat;
  
  /**
   * Screenshot quality (0-100, only for JPEG)
   */
  quality?: ScreenshotQuality;
  
  /**
   * Whether to omit background
   */
  omitBackground?: boolean;
  
  /**
   * Clip area
   */
  clip?: {
    x: number;
    y: number;
    width: number;
    height: number;
  };
  
  /**
   * Timeout in milliseconds
   */
  timeout?: number;
  
  /**
   * Whether to mask sensitive data
   */
  maskSensitiveData?: boolean;
  
  /**
   * Mask areas
   */
  maskAreas?: {
    x: number;
    y: number;
    width: number;
    height: number;
  }[];
  
  /**
   * Mask color
   */
  maskColor?: string;
  
  /**
   * Whether to add timestamp
   */
  addTimestamp?: boolean;
  
  /**
   * Whether to add test name
   */
  addTestName?: boolean;
  
  /**
   * Whether to add browser info
   */
  addBrowserInfo?: boolean;
  
  /**
   * Whether to add metadata
   */
  addMetadata?: boolean;
  
  /**
   * Custom metadata
   */
  metadata?: Record<string, any>;
}

/**
 * Screenshot info
 */
export interface ScreenshotInfo {
  /**
   * Screenshot ID
   */
  id: string;
  
  /**
   * Screenshot name
   */
  name: string;
  
  /**
   * Screenshot path
   */
  path: string;
  
  /**
   * Screenshot type
   */
  type: ScreenshotType;
  
  /**
   * Screenshot format
   */
  format: ScreenshotFormat;
  
  /**
   * Screenshot quality
   */
  quality?: ScreenshotQuality;
  
  /**
   * Screenshot width
   */
  width?: number;
  
  /**
   * Screenshot height
   */
  height?: number;
  
  /**
   * Screenshot size in bytes
   */
  size?: number;
  
  /**
   * Screenshot timestamp
   */
  timestamp: number;
  
  /**
   * Test name
   */
  testName?: string;
  
  /**
   * Test ID
   */
  testId?: string;
  
  /**
   * Browser name
   */
  browserName?: string;
  
  /**
   * Browser version
   */
  browserVersion?: string;
  
  /**
   * Operating system
   */
  os?: string;
  
  /**
   * Operating system version
   */
  osVersion?: string;
  
  /**
   * Viewport width
   */
  viewportWidth?: number;
  
  /**
   * Viewport height
   */
  viewportHeight?: number;
  
  /**
   * URL
   */
  url?: string;
  
  /**
   * Selector
   */
  selector?: string;
  
  /**
   * Tags
   */
  tags?: string[];
  
  /**
   * Metadata
   */
  metadata?: Record<string, any>;
}

/**
 * Screenshot manager options
 */
export interface ScreenshotManagerOptions {
  /**
   * Base directory for screenshots
   */
  baseDir?: string;
  
  /**
   * Default screenshot options
   */
  defaultOptions?: ScreenshotOptions;
  
  /**
   * Whether to organize screenshots by date
   */
  organizeByDate?: boolean;
  
  /**
   * Whether to organize screenshots by test name
   */
  organizeByTestName?: boolean;
  
  /**
   * Whether to organize screenshots by test ID
   */
  organizeByTestId?: boolean;
  
  /**
   * Whether to organize screenshots by browser
   */
  organizeByBrowser?: boolean;
  
  /**
   * Maximum age of screenshots in days
   */
  maxAge?: number;
  
  /**
   * Maximum number of screenshots to keep
   */
  maxCount?: number;
  
  /**
   * Whether to compress screenshots
   */
  compressScreenshots?: boolean;
  
  /**
   * Whether to add metadata to screenshots
   */
  addMetadata?: boolean;
  
  /**
   * Whether to verify screenshots
   */
  verifyScreenshots?: boolean;
}

/**
 * Screenshot storage options
 */
export interface ScreenshotStorageOptions {
  /**
   * Base directory for screenshots
   */
  baseDir?: string;
  
  /**
   * Whether to organize screenshots by date
   */
  organizeByDate?: boolean;
  
  /**
   * Whether to organize screenshots by test name
   */
  organizeByTestName?: boolean;
  
  /**
   * Whether to organize screenshots by test ID
   */
  organizeByTestId?: boolean;
  
  /**
   * Whether to organize screenshots by browser
   */
  organizeByBrowser?: boolean;
  
  /**
   * Maximum age of screenshots in days
   */
  maxAge?: number;
  
  /**
   * Maximum number of screenshots to keep
   */
  maxCount?: number;
}

/**
 * Screenshot processor options
 */
export interface ScreenshotProcessorOptions {
  /**
   * Whether to compress screenshots
   */
  compressScreenshots?: boolean;
  
  /**
   * Compression quality (0-100, only for JPEG)
   */
  compressionQuality?: number;
  
  /**
   * Whether to resize screenshots
   */
  resizeScreenshots?: boolean;
  
  /**
   * Maximum width of screenshots
   */
  maxWidth?: number;
  
  /**
   * Maximum height of screenshots
   */
  maxHeight?: number;
  
  /**
   * Whether to add watermark
   */
  addWatermark?: boolean;
  
  /**
   * Watermark text
   */
  watermarkText?: string;
  
  /**
   * Watermark position
   */
  watermarkPosition?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  
  /**
   * Whether to add metadata
   */
  addMetadata?: boolean;
}

/**
 * Screenshot reporter options
 */
export interface ScreenshotReporterOptions {
  /**
   * Output directory
   */
  outputDir?: string;
  
  /**
   * Report formats
   */
  formats?: ('json' | 'html' | 'csv')[];
  
  /**
   * Whether to include thumbnails in HTML report
   */
  includeThumbnails?: boolean;
  
  /**
   * Whether to include metadata in report
   */
  includeMetadata?: boolean;
}

/**
 * Screenshot comparison options
 */
export interface ScreenshotComparisonOptions {
  /**
   * Threshold for pixel difference (0-1)
   */
  threshold?: number;
  
  /**
   * Whether to ignore anti-aliasing
   */
  ignoreAntialiasing?: boolean;
  
  /**
   * Whether to ignore colors
   */
  ignoreColors?: boolean;
  
  /**
   * Whether to ignore alpha
   */
  ignoreAlpha?: boolean;
  
  /**
   * Whether to ignore less
   */
  ignoreLess?: boolean;
  
  /**
   * Whether to ignore nothing
   */
  ignoreNothing?: boolean;
}

/**
 * Screenshot comparison result
 */
export interface ScreenshotComparisonResult {
  /**
   * Whether the screenshots match
   */
  match: boolean;
  
  /**
   * Difference percentage (0-100)
   */
  diffPercentage: number;
  
  /**
   * Number of different pixels
   */
  diffPixels: number;
  
  /**
   * Total number of pixels
   */
  totalPixels: number;
  
  /**
   * Difference image path
   */
  diffImagePath?: string;
}
