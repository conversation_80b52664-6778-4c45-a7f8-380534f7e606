#!/usr/bin/env node

/**
 * Version Bump Script
 * 
 * This script bumps the version of packages in the monorepo according to semver.
 * It supports:
 * - Bumping all packages in lockstep
 * - Bumping individual packages
 * - Updating internal dependencies
 * 
 * Usage:
 *   node scripts/version-bump.js <version> [package]
 * 
 * Arguments:
 *   version: The version to bump to, or 'major', 'minor', 'patch'
 *   package: (Optional) The package to bump. If not specified, all packages are bumped.
 * 
 * Examples:
 *   node scripts/version-bump.js patch
 *   node scripts/version-bump.js minor test-framework
 *   node scripts/version-bump.js 1.2.0 mcp-optimizer
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Get command line arguments
const args = process.argv.slice(2);
const versionArg = args[0];
const packageArg = args[1];

// Validate arguments
if (!versionArg) {
  console.error('Error: Version argument is required.');
  console.error('Usage: node scripts/version-bump.js <version> [package]');
  process.exit(1);
}

// Define packages
const packages = ['test-framework', 'mcp-optimizer', 'shared-utils', 'docs'];

// Determine which packages to bump
const packagesToUpdate = packageArg ? [packageArg] : packages;

// Validate package argument
if (packageArg && !packages.includes(packageArg)) {
  console.error(`Error: Package "${packageArg}" not found.`);
  console.error(`Available packages: ${packages.join(', ')}`);
  process.exit(1);
}

// Function to read package.json
function readPackageJson(packagePath) {
  const packageJsonPath = path.join(packagePath, 'package.json');
  return JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
}

// Function to write package.json
function writePackageJson(packagePath, packageJson) {
  const packageJsonPath = path.join(packagePath, 'package.json');
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n');
}

// Function to calculate new version based on semver
function calculateNewVersion(currentVersion, versionArg) {
  if (versionArg === 'major' || versionArg === 'minor' || versionArg === 'patch') {
    const [major, minor, patch] = currentVersion.split('.').map(Number);
    
    if (versionArg === 'major') {
      return `${major + 1}.0.0`;
    } else if (versionArg === 'minor') {
      return `${major}.${minor + 1}.0`;
    } else if (versionArg === 'patch') {
      return `${major}.${minor}.${patch + 1}`;
    }
  }
  
  return versionArg;
}

// Get current versions
const currentVersions = {};
for (const pkg of packages) {
  const packageJson = readPackageJson(pkg);
  currentVersions[pkg] = packageJson.version;
}

// Calculate new versions
const newVersions = {};
for (const pkg of packagesToUpdate) {
  newVersions[pkg] = calculateNewVersion(currentVersions[pkg], versionArg);
}

// Update package.json files
for (const pkg of packagesToUpdate) {
  console.log(`Updating ${pkg} from ${currentVersions[pkg]} to ${newVersions[pkg]}`);
  
  const packageJson = readPackageJson(pkg);
  packageJson.version = newVersions[pkg];
  
  // Update internal dependencies
  if (packageJson.dependencies) {
    for (const dep in packageJson.dependencies) {
      if (dep.startsWith('@qawolf/') && newVersions[dep.replace('@qawolf/', '')]) {
        packageJson.dependencies[dep] = newVersions[dep.replace('@qawolf/', '')];
      }
    }
  }
  
  writePackageJson(pkg, packageJson);
}

// Update root package.json
const rootPackageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
if (packagesToUpdate.length === packages.length) {
  rootPackageJson.version = newVersions[packages[0]];
}
fs.writeFileSync('package.json', JSON.stringify(rootPackageJson, null, 2) + '\n');

// Create git commit and tag
if (packagesToUpdate.length === packages.length) {
  // If all packages are updated, create a single commit and tag
  const version = newVersions[packages[0]];
  execSync(`git add .`);
  execSync(`git commit -m "chore: bump version to ${version}"`);
  execSync(`git tag -a v${version} -m "v${version}"`);
  
  console.log(`\nCreated commit and tag for v${version}`);
  console.log(`\nTo push the changes and tag, run:`);
  console.log(`  git push && git push --tags`);
} else {
  // If only one package is updated, create a commit and tag for that package
  const pkg = packagesToUpdate[0];
  const version = newVersions[pkg];
  execSync(`git add .`);
  execSync(`git commit -m "chore: bump ${pkg} version to ${version}"`);
  execSync(`git tag -a ${pkg}-v${version} -m "${pkg} v${version}"`);
  
  console.log(`\nCreated commit and tag for ${pkg} v${version}`);
  console.log(`\nTo push the changes and tag, run:`);
  console.log(`  git push && git push --tags`);
}