#!/usr/bin/env node

/**
 * Testmo Integration CLI
 * 
 * This script integrates with Testmo for test case management.
 */

const { createTestmoReporter } = require('../dist/testmo-reporter');

// Parse command line arguments
const args = process.argv.slice(2);
const command = args[0];
const options = {};

for (let i = 1; i < args.length; i++) {
  const arg = args[i];
  
  if (arg === '--api-key' && i + 1 < args.length) {
    options.apiKey = args[++i];
  } else if (arg === '--host' && i + 1 < args.length) {
    options.host = args[++i];
  } else if (arg === '--project-id' && i + 1 < args.length) {
    options.projectId = parseInt(args[++i], 10);
  } else if (arg === '--source-id' && i + 1 < args.length) {
    options.sourceId = args[++i];
  } else if (arg === '--reports-dir' && i + 1 < args.length) {
    options.reportsDir = args[++i];
  } else if (arg === '--run-name' && i + 1 < args.length) {
    options.runName = args[++i];
  } else if (arg === '--results-file' && i + 1 < args.length) {
    options.resultsFile = args[++i];
  }
}

// Create Testmo reporter instance
const testmoReporter = createTestmoReporter(options);

// Execute command
if (command === 'create-run') {
  testmoReporter.createRun()
    .then(run => {
      console.log('Testmo test run created successfully!');
      console.log(`Run ID: ${run.id}`);
      process.exit(0);
    })
    .catch(error => {
      console.error('Error creating Testmo test run:', error.message);
      process.exit(1);
    });
} else if (command === 'submit-results') {
  if (!options.resultsFile) {
    console.error('Results file is required for submit-results command');
    process.exit(1);
  }
  
  testmoReporter.submitResults(options.resultsFile)
    .then(submission => {
      console.log('Testmo test results submitted successfully!');
      console.log(`Submission ID: ${submission.id}`);
      process.exit(0);
    })
    .catch(error => {
      console.error('Error submitting Testmo test results:', error.message);
      process.exit(1);
    });
} else if (command === 'complete-run') {
  if (!options.runId) {
    console.error('Run ID is required for complete-run command');
    process.exit(1);
  }
  
  testmoReporter.completeRun(options.runId)
    .then(run => {
      console.log('Testmo test run completed successfully!');
      process.exit(0);
    })
    .catch(error => {
      console.error('Error completing Testmo test run:', error.message);
      process.exit(1);
    });
} else {
  console.error(`Unknown command: ${command}`);
  console.error('Available commands: create-run, submit-results, complete-run');
  process.exit(1);
}
