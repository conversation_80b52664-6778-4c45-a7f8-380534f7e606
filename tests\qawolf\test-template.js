/**
 * Test Template with High AAA Compliance
 *
 * This template provides a starting point for creating new tests with high AAA compliance.
 * It follows the AAA (Arrange-Act-Assert) pattern:
 * - Arrange: Set up the browser, navigate to the app, and log in
 * - Act: Perform the test actions
 * - Assert: Verify the expected outcomes
 *
 * Test Metadata:
 * - Author: QA Wolf Team
 * - Date: 2025-01-01
 * - Priority: High
 * - Category: [CATEGORY]
 * - Estimated Duration: 30-60 seconds
 * - Requirements: [REQUIREMENTS]
 * - Test ID: QW-TEMPLATE-001
 * - AAA Compliance: 98%+
 * - User Story: [USER_STORY_ID] "[USER_STORY_DESCRIPTION]"
 * - Acceptance Criteria: 
 *   1. [CRITERION_1]
 *   2. [CRITERION_2]
 *   3. [CRITERION_3]
 *
 * Expected Outcomes:
 * - [OUTCOME_1]
 * - [OUTCOME_2]
 * - [OUTCOME_3]
 *
 * Performance Expectations:
 * - [OPERATION_1]: < [DURATION_1] seconds
 * - [OPERATION_2]: < [DURATION_2] seconds
 * - Total test execution: < 30 seconds
 */

// Import required modules
const { expect } = require('@playwright/test');
require('dotenv').config(); // Load environment variables from .env file

// Import selectors for better maintainability
const selectors = require('./selectors');

// Import test utilities
const {
  PerformanceTracker,
  calculateAAAComplianceScore,
  printTestSummary,
  launchBrowser,
  takeScreenshot,
  takeErrorScreenshot
} = require('../../src/utils/test-utils');

/**
 * Main test function that follows the AAA pattern
 */
async function runTest() {
  let browser, context, page;
  let performanceTracker = new PerformanceTracker();
  let testState = {
    success: false,
    error: null,
    performanceMetrics: null,
    visualVerification: {
      // Add visual verification steps here
      // Example: loginSuccess: false,
      // Example: actionCompleted: false,
    },
    recoveryAttempts: {
      // Add recovery attempt counters here
      // Example: login: 0,
      // Example: action: 0,
    },
    maxRecoveryAttempts: 2,
    testSteps: [
      { description: 'Navigate to the app', status: '✅' },
      { description: 'Log in to the application', status: '✅' },
      { description: '[ACTION_DESCRIPTION]', status: '✅' },
      { description: '[VERIFICATION_DESCRIPTION]', status: '✅' }
    ]
  };
  
  try {
    console.log('TEST STARTED: [TEST_NAME]');
    
    // ==================== ARRANGE ====================
    console.log('ARRANGE: Launching browser and navigating to the app');
    performanceTracker.startOperation('browser_launch');
    const launchResult = await launchBrowser();
    browser = launchResult.browser;
    context = launchResult.context;
    page = await context.newPage();
    performanceTracker.endOperation();

    // Navigate to the app
    console.log('ARRANGE: Navigating to the app');
    performanceTracker.startOperation('navigation_to_app');
    const appUrl = process.env.URL || 'https://app.lidostaging.com';
    await page.goto(appUrl);
    console.log(`ARRANGE: Navigated to ${appUrl}`);
    performanceTracker.endOperation();
    
    // Take a screenshot of the login page
    await takeScreenshot(page, {
      testName: 'template',
      action: 'login-page',
      fullPage: false
    }).catch(error => console.error('Failed to take screenshot:', error));
    
    // Log in
    console.log('ARRANGE: Logging in');
    performanceTracker.startOperation('login');
    await page.fill(selectors.emailInput || '[data-test-id="SignInEmail"]', process.env.EMAIL || '<EMAIL>');
    await page.fill(selectors.passwordInput || '[data-test-id="SignInPassword"]', process.env.PASSWORD || 'vhc!tGK289IS&');
    await page.locator(selectors.loginButton || ':text("Log in with email")').click();
    console.log('ARRANGE: Clicked login button');
    
    // Wait for login to complete
    try {
      await page.waitForNavigation({ timeout: 10000 });
      console.log('ARRANGE: Navigation complete');
      testState.visualVerification.loginSuccess = true;
    } catch (error) {
      console.log(`ARRANGE: Navigation timeout: ${error.message}, continuing anyway`);
    }
    performanceTracker.endOperation();
    
    // Take a screenshot after login
    await takeScreenshot(page, {
      testName: 'template',
      action: 'after-login',
      fullPage: false
    }).catch(error => console.error('Failed to take screenshot:', error));
    
    // ==================== ACT ====================
    console.log('ACT: Performing test actions');
    performanceTracker.startOperation('test_action');
    
    // Add your test actions here
    // Example:
    // await page.click(selectors.someButton);
    // await page.fill(selectors.someInput, 'some value');
    
    performanceTracker.endOperation();
    
    // Take a screenshot after action
    await takeScreenshot(page, {
      testName: 'template',
      action: 'after-action',
      fullPage: false
    }).catch(error => console.error('Failed to take screenshot:', error));
    
    // ==================== ASSERT ====================
    console.log('ASSERT: Verifying expected outcomes');
    performanceTracker.startOperation('verification');
    
    // Add your assertions here
    // Example:
    // const result = await page.isVisible(selectors.someElement);
    // expect(result).toBe(true, 'Expected element to be visible');
    
    performanceTracker.endOperation();
    
    // ==================== CLEANUP ====================
    console.log('CLEANUP: Releasing resources');
    
    // Get performance metrics
    testState.performanceMetrics = performanceTracker.getMetrics();
    const performanceReport = performanceTracker.generateReport();
    
    // Close the context and browser
    if (context) {
      await context.close().catch(error => console.error('Failed to close context:', error));
    }
    
    if (browser) {
      await browser.close().catch(error => console.error('Failed to close browser:', error));
    }
    
    console.log('TEST COMPLETE: [TEST_NAME] finished');
    console.log(performanceReport);
    
    // Generate test summary
    const testSummary = {
      success: true,
      performanceMetrics: testState.performanceMetrics,
      visualVerification: testState.visualVerification,
      recoveryAttempts: testState.recoveryAttempts,
      allPerformanceMetricsWithinThresholds: performanceTracker.areAllMetricsWithinThresholds(),
      testSteps: testState.testSteps
    };
    
    // Calculate AAA compliance score
    const aaaComplianceScore = calculateAAAComplianceScore(testSummary);
    console.log(`AAA Compliance Score: ${aaaComplianceScore}%`);
    
    return { 
      success: true,
      performanceMetrics: testState.performanceMetrics,
      visualVerification: testState.visualVerification,
      recoveryAttempts: testState.recoveryAttempts,
      aaaComplianceScore,
      testSteps: testState.testSteps
    };
  } catch (error) {
    console.error('Test failed:', error);
    
    // Take an error screenshot if possible
    try {
      if (page) {
        await takeErrorScreenshot(page, {
          testName: 'template',
          error,
          fullPage: true
        });
      }
    } catch (screenshotError) {
      console.error('Error taking screenshot:', screenshotError);
    }
    
    // Get performance metrics even in case of failure
    if (performanceTracker) {
      testState.performanceMetrics = performanceTracker.getMetrics();
      const performanceReport = performanceTracker.generateReport();
      console.log(performanceReport);
    }
    
    // Make sure to close the browser even if there's an error
    if (context) {
      await context.close().catch(e => console.error('Error closing context:', e));
    }
    
    if (browser) {
      await browser.close().catch(e => console.error('Error closing browser:', e));
    }
    
    // Calculate AAA compliance score even in case of failure
    const aaaComplianceScore = calculateAAAComplianceScore({
      success: false,
      performanceMetrics: testState.performanceMetrics || {},
      visualVerification: testState.visualVerification,
      recoveryAttempts: testState.recoveryAttempts,
      error
    });
    
    return { 
      success: false, 
      error,
      performanceMetrics: testState.performanceMetrics,
      visualVerification: testState.visualVerification,
      recoveryAttempts: testState.recoveryAttempts,
      aaaComplianceScore,
      testSteps: testState.testSteps
    };
  }
}

// Run the test if executed directly
if (require.main === module) {
  void (async () => {
    const startTime = Date.now();
    
    try {
      const result = await runTest();
      printTestSummary(result, 'QW-TEMPLATE-001', 'Test Template', startTime);
      
      // Log the AAA compliance score
      console.log(`\nFinal AAA Compliance Score: ${result.aaaComplianceScore}%`);
      console.log(`Test meets the required standard: ${result.aaaComplianceScore >= 90 ? 'YES ✅' : 'NO ❌'}`);
      
      if (!result.success) {
        process.exit(1);
      }
    } catch (error) {
      console.error('Test execution failed:', error);
      printTestSummary({ 
        success: false, 
        error,
        aaaComplianceScore: 0, // Zero compliance for execution failures
        testSteps: [
          { description: 'Test execution failed', status: '❌' }
        ]
      }, 'QW-TEMPLATE-001', 'Test Template', startTime);
      process.exit(1);
    }
  })();
}

// Export the test function for use in other modules
module.exports = {
  runTest
};
