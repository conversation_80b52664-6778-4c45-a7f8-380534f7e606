/**
 * Screenshot processor for screenshot manager
 */

import * as sharp from 'sharp';
import { ScreenshotOptions, ScreenshotProcessorOptions } from './types';
import { EventBus, EventType } from '@qawolf/core';

/**
 * Screenshot processor
 */
export class ScreenshotProcessor {
  /**
   * Whether to compress screenshots
   */
  private compressScreenshots: boolean;
  
  /**
   * Compression quality (0-100, only for JPEG)
   */
  private compressionQuality: number;
  
  /**
   * Whether to resize screenshots
   */
  private resizeScreenshots: boolean;
  
  /**
   * Maximum width of screenshots
   */
  private maxWidth?: number;
  
  /**
   * Maximum height of screenshots
   */
  private maxHeight?: number;
  
  /**
   * Whether to add watermark
   */
  private addWatermark: boolean;
  
  /**
   * Watermark text
   */
  private watermarkText: string;
  
  /**
   * Watermark position
   */
  private watermarkPosition: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right';
  
  /**
   * Whether to add metadata
   */
  private addMetadata: boolean;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options Screenshot processor options
   */
  constructor(options: ScreenshotProcessorOptions = {}) {
    this.compressScreenshots = options.compressScreenshots !== false;
    this.compressionQuality = options.compressionQuality || 80;
    this.resizeScreenshots = options.resizeScreenshots || false;
    this.maxWidth = options.maxWidth;
    this.maxHeight = options.maxHeight;
    this.addWatermark = options.addWatermark || false;
    this.watermarkText = options.watermarkText || 'QA Wolf';
    this.watermarkPosition = options.watermarkPosition || 'bottom-right';
    this.addMetadata = options.addMetadata !== false;
    this.eventBus = EventBus.getInstance();
  }
  
  /**
   * Process screenshot
   * @param buffer Screenshot buffer
   * @param options Screenshot options
   * @returns Processed screenshot buffer
   */
  async processScreenshot(buffer: Buffer, options: ScreenshotOptions): Promise<Buffer> {
    let image = sharp(buffer);
    
    // Get image metadata
    const metadata = await image.metadata();
    
    // Mask sensitive data
    if (options.maskSensitiveData && options.maskAreas && options.maskAreas.length > 0) {
      image = await this.maskSensitiveData(image, options.maskAreas, options.maskColor || '#000000');
    }
    
    // Resize image
    if (this.resizeScreenshots && (this.maxWidth || this.maxHeight)) {
      image = await this.resizeImage(image, metadata);
    }
    
    // Add watermark
    if (this.addWatermark) {
      image = await this.addWatermarkToImage(image, metadata);
    }
    
    // Add metadata
    if (this.addMetadata && options.metadata) {
      image = this.addMetadataToImage(image, options.metadata);
    }
    
    // Compress image
    if (this.compressScreenshots) {
      image = this.compressImage(image, options);
    }
    
    // Get processed buffer
    const processedBuffer = await image.toBuffer();
    
    // Emit event
    this.eventBus.emit(EventType.SCREENSHOT_PROCESSED, {
      originalSize: buffer.length,
      processedSize: processedBuffer.length,
      options
    });
    
    return processedBuffer;
  }
  
  /**
   * Mask sensitive data
   * @param image Sharp image
   * @param areas Areas to mask
   * @param color Mask color
   * @returns Sharp image
   */
  private async maskSensitiveData(image: sharp.Sharp, areas: { x: number; y: number; width: number; height: number }[], color: string): Promise<sharp.Sharp> {
    const metadata = await image.metadata();
    const width = metadata.width || 0;
    const height = metadata.height || 0;
    
    // Create SVG with rectangles for each area
    const svg = `
      <svg width="${width}" height="${height}">
        ${areas.map(area => `
          <rect x="${area.x}" y="${area.y}" width="${area.width}" height="${area.height}" fill="${color}" />
        `).join('')}
      </svg>
    `;
    
    // Composite SVG over image
    return image.composite([
      {
        input: Buffer.from(svg),
        top: 0,
        left: 0
      }
    ]);
  }
  
  /**
   * Resize image
   * @param image Sharp image
   * @param metadata Image metadata
   * @returns Sharp image
   */
  private async resizeImage(image: sharp.Sharp, metadata: sharp.Metadata): Promise<sharp.Sharp> {
    const width = metadata.width || 0;
    const height = metadata.height || 0;
    
    // Calculate new dimensions
    let newWidth = width;
    let newHeight = height;
    
    if (this.maxWidth && width > this.maxWidth) {
      newWidth = this.maxWidth;
      newHeight = Math.round(height * (newWidth / width));
    }
    
    if (this.maxHeight && newHeight > this.maxHeight) {
      newHeight = this.maxHeight;
      newWidth = Math.round(width * (newHeight / height));
    }
    
    // Resize image
    return image.resize(newWidth, newHeight);
  }
  
  /**
   * Add watermark to image
   * @param image Sharp image
   * @param metadata Image metadata
   * @returns Sharp image
   */
  private async addWatermarkToImage(image: sharp.Sharp, metadata: sharp.Metadata): Promise<sharp.Sharp> {
    const width = metadata.width || 0;
    const height = metadata.height || 0;
    
    // Calculate watermark position
    let x = 10;
    let y = 10;
    
    switch (this.watermarkPosition) {
      case 'top-right':
        x = width - 150;
        y = 10;
        break;
      case 'bottom-left':
        x = 10;
        y = height - 30;
        break;
      case 'bottom-right':
        x = width - 150;
        y = height - 30;
        break;
    }
    
    // Create SVG with watermark text
    const svg = `
      <svg width="${width}" height="${height}">
        <text x="${x}" y="${y}" font-family="Arial" font-size="16" fill="rgba(255, 255, 255, 0.5)">${this.watermarkText}</text>
      </svg>
    `;
    
    // Composite SVG over image
    return image.composite([
      {
        input: Buffer.from(svg),
        top: 0,
        left: 0
      }
    ]);
  }
  
  /**
   * Add metadata to image
   * @param image Sharp image
   * @param metadata Metadata
   * @returns Sharp image
   */
  private addMetadataToImage(image: sharp.Sharp, metadata: Record<string, any>): sharp.Sharp {
    // Convert metadata to string
    const metadataString = JSON.stringify(metadata);
    
    // Add metadata to image
    return image.withMetadata({
      exif: {
        IFD0: {
          UserComment: metadataString
        }
      }
    });
  }
  
  /**
   * Compress image
   * @param image Sharp image
   * @param options Screenshot options
   * @returns Sharp image
   */
  private compressImage(image: sharp.Sharp, options: ScreenshotOptions): sharp.Sharp {
    if (options.format === 'jpeg') {
      return image.jpeg({
        quality: this.compressionQuality
      });
    } else {
      return image.png({
        compressionLevel: 9
      });
    }
  }
  
  /**
   * Get compression quality
   * @returns Compression quality
   */
  getCompressionQuality(): number {
    return this.compressionQuality;
  }
  
  /**
   * Set compression quality
   * @param quality Compression quality
   * @returns This instance for chaining
   */
  setCompressionQuality(quality: number): ScreenshotProcessor {
    this.compressionQuality = quality;
    return this;
  }
  
  /**
   * Enable compression
   * @returns This instance for chaining
   */
  enableCompression(): ScreenshotProcessor {
    this.compressScreenshots = true;
    return this;
  }
  
  /**
   * Disable compression
   * @returns This instance for chaining
   */
  disableCompression(): ScreenshotProcessor {
    this.compressScreenshots = false;
    return this;
  }
  
  /**
   * Enable resizing
   * @param maxWidth Maximum width
   * @param maxHeight Maximum height
   * @returns This instance for chaining
   */
  enableResizing(maxWidth?: number, maxHeight?: number): ScreenshotProcessor {
    this.resizeScreenshots = true;
    this.maxWidth = maxWidth;
    this.maxHeight = maxHeight;
    return this;
  }
  
  /**
   * Disable resizing
   * @returns This instance for chaining
   */
  disableResizing(): ScreenshotProcessor {
    this.resizeScreenshots = false;
    return this;
  }
  
  /**
   * Enable watermark
   * @param text Watermark text
   * @param position Watermark position
   * @returns This instance for chaining
   */
  enableWatermark(text?: string, position?: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right'): ScreenshotProcessor {
    this.addWatermark = true;
    
    if (text) {
      this.watermarkText = text;
    }
    
    if (position) {
      this.watermarkPosition = position;
    }
    
    return this;
  }
  
  /**
   * Disable watermark
   * @returns This instance for chaining
   */
  disableWatermark(): ScreenshotProcessor {
    this.addWatermark = false;
    return this;
  }
  
  /**
   * Enable metadata
   * @returns This instance for chaining
   */
  enableMetadata(): ScreenshotProcessor {
    this.addMetadata = true;
    return this;
  }
  
  /**
   * Disable metadata
   * @returns This instance for chaining
   */
  disableMetadata(): ScreenshotProcessor {
    this.addMetadata = false;
    return this;
  }
}

/**
 * Create screenshot processor
 * @param options Screenshot processor options
 * @returns Screenshot processor
 */
export function createScreenshotProcessor(options: ScreenshotProcessorOptions = {}): ScreenshotProcessor {
  return new ScreenshotProcessor(options);
}
