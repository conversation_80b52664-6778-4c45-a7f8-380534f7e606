/**
 * QA Wolf CI Greenlight Script
 * 
 * This script polls the QA Wolf API to check if the tests have passed (greenlight).
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  apiKey: process.env.QA_WOLF_API_KEY,
  teamId: process.env.QA_WOLF_TEAM_ID || 'clux0gjs50sb3ak01fnh7wvja',
  apiUrl: 'https://app.qawolf.com/api/ci',
  reportsDir: './qawolf-reports',
  maxPolls: 30, // Maximum number of polling attempts
  pollInterval: 60000 // Polling interval in milliseconds (1 minute)
};

// Load deployment info
let deploymentInfo;
try {
  deploymentInfo = JSON.parse(
    fs.readFileSync(path.join(config.reportsDir, 'deployment-info.json'), 'utf8')
  );
} catch (error) {
  console.error('Error loading deployment info:', error.message);
  process.exit(1);
}

// Poll for CI greenlight
async function pollForGreenlight() {
  console.log('Polling for QA Wolf CI greenlight...');
  
  let pollCount = 0;
  let greenlightStatus = null;
  
  const pollResults = [];
  
  while (pollCount < config.maxPolls) {
    pollCount++;
    
    try {
      console.log(`Polling attempt ${pollCount}/${config.maxPolls}...`);
      
      const response = await axios.get(
        `${config.apiUrl}/greenlight?teamId=${config.teamId}&environment=${deploymentInfo.environment}&branch=${deploymentInfo.branch}`,
        {
          headers: {
            'Authorization': `Bearer ${config.apiKey}`
          }
        }
      );
      
      const result = response.data;
      
      console.log('Poll result:', result);
      
      pollResults.push({
        timestamp: new Date().toISOString(),
        attempt: pollCount,
        result
      });
      
      // Save poll results to file
      fs.writeFileSync(
        path.join(config.reportsDir, 'greenlight-polls.json'),
        JSON.stringify(pollResults, null, 2)
      );
      
      // Check if we have a greenlight
      if (result.runStage === 'complete') {
        greenlightStatus = result.greenlight;
        
        console.log(`QA Wolf tests completed with greenlight status: ${greenlightStatus}`);
        
        // Save final result to file
        fs.writeFileSync(
          path.join(config.reportsDir, 'greenlight-result.json'),
          JSON.stringify(result, null, 2)
        );
        
        break;
      }
      
      // If tests are still running, wait and poll again
      console.log(`Tests are still running (stage: ${result.runStage}). Waiting ${config.pollInterval / 1000} seconds before next poll...`);
      
      await new Promise(resolve => setTimeout(resolve, config.pollInterval));
    } catch (error) {
      console.error('Error polling for greenlight:', error.message);
      
      if (error.response) {
        console.error('Response data:', error.response.data);
        console.error('Response status:', error.response.status);
      }
      
      // Save error to file
      fs.writeFileSync(
        path.join(config.reportsDir, 'greenlight-error.json'),
        JSON.stringify({
          error: error.message,
          response: error.response ? {
            data: error.response.data,
            status: error.response.status
          } : null,
          timestamp: new Date().toISOString()
        }, null, 2)
      );
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, config.pollInterval));
    }
  }
  
  // Check if we reached the maximum number of polls without a result
  if (pollCount >= config.maxPolls && greenlightStatus === null) {
    console.error('Maximum number of polling attempts reached without a result.');
    process.exit(1);
  }
  
  // Exit with appropriate status code based on greenlight
  if (greenlightStatus === true) {
    console.log('QA Wolf tests passed! CI greenlight received.');
    process.exit(0);
  } else {
    console.error('QA Wolf tests failed! No CI greenlight received.');
    process.exit(1);
  }
}

// Run the polling
pollForGreenlight();
