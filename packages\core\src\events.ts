/**
 * Events module for QA Wolf Metrics Framework
 */

import { EventEmitter } from 'events';

/**
 * Event types
 */
export enum EventType {
  // Test events
  TEST_STARTED = 'test:started',
  TEST_COMPLETED = 'test:completed',
  TEST_FAILED = 'test:failed',
  TEST_SKIPPED = 'test:skipped',
  
  // Screenshot events
  SCREENSHOT_TAKEN = 'screenshot:taken',
  SCREENSHOT_FAILED = 'screenshot:failed',
  
  // QA Wolf events
  QAWOLF_DEPLOYMENT_NOTIFIED = 'qawolf:deployment:notified',
  QAWOLF_GREENLIGHT_RECEIVED = 'qawolf:greenlight:received',
  QAWOLF_GREENLIGHT_FAILED = 'qawolf:greenlight:failed',
  
  // Testmo events
  TESTMO_RUN_CREATED = 'testmo:run:created',
  TESTMO_RESULTS_SUBMITTED = 'testmo:results:submitted',
  TESTMO_RUN_COMPLETED = 'testmo:run:completed',
  
  // GitHub events
  GITHUB_PR_CREATED = 'github:pr:created',
  GITHUB_PR_UPDATED = 'github:pr:updated',
  GITHUB_PR_MERGED = 'github:pr:merged',
  
  // Linear events
  LINEAR_ISSUE_CREATED = 'linear:issue:created',
  LINEAR_ISSUE_UPDATED = 'linear:issue:updated',
  LINEAR_ISSUE_CLOSED = 'linear:issue:closed',
  
  // Report events
  REPORT_GENERATED = 'report:generated',
  
  // Error events
  ERROR_OCCURRED = 'error:occurred'
}

/**
 * Event data interface
 */
export interface EventData {
  timestamp: number;
  [key: string]: any;
}

/**
 * Event bus class
 */
export class EventBus {
  private static instance: EventBus;
  private emitter: EventEmitter;
  
  private constructor() {
    this.emitter = new EventEmitter();
    
    // Set max listeners to a high number to avoid warnings
    this.emitter.setMaxListeners(100);
  }
  
  /**
   * Get event bus instance
   * @returns EventBus instance
   */
  public static getInstance(): EventBus {
    if (!EventBus.instance) {
      EventBus.instance = new EventBus();
    }
    
    return EventBus.instance;
  }
  
  /**
   * Emit event
   * @param eventType Event type
   * @param data Event data
   */
  public emit(eventType: EventType, data: Omit<EventData, 'timestamp'>): void {
    const eventData: EventData = {
      timestamp: Date.now(),
      ...data
    };
    
    this.emitter.emit(eventType, eventData);
  }
  
  /**
   * Subscribe to event
   * @param eventType Event type
   * @param listener Event listener
   */
  public on(eventType: EventType, listener: (data: EventData) => void): void {
    this.emitter.on(eventType, listener);
  }
  
  /**
   * Subscribe to event once
   * @param eventType Event type
   * @param listener Event listener
   */
  public once(eventType: EventType, listener: (data: EventData) => void): void {
    this.emitter.once(eventType, listener);
  }
  
  /**
   * Unsubscribe from event
   * @param eventType Event type
   * @param listener Event listener
   */
  public off(eventType: EventType, listener: (data: EventData) => void): void {
    this.emitter.off(eventType, listener);
  }
  
  /**
   * Remove all listeners for event
   * @param eventType Event type
   */
  public removeAllListeners(eventType?: EventType): void {
    this.emitter.removeAllListeners(eventType);
  }
}

// Export default instance
export default EventBus.getInstance();
