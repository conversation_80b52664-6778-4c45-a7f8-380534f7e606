/**
 * Convert Playwright Test Results to QA Wolf Format
 * 
 * This script converts Playwright test results to QA Wolf format for integration with Testmo.
 */

const fs = require('fs');
const path = require('path');

// Configuration
const config = {
  playwrightReportDir: './playwright-report',
  qaWolfReportDir: './qawolf-reports',
  outputFile: 'qawolf-results.json'
};

// Create QA Wolf report directory if it doesn't exist
if (!fs.existsSync(config.qaWolfReportDir)) {
  fs.mkdirSync(config.qaWolfReportDir, { recursive: true });
}

/**
 * Convert Playwright test results to QA Wolf format
 * @param {string} reportFile - Path to Playwright report file
 * @returns {Array<Object>} - QA Wolf test results
 */
function convertPlaywrightResults(reportFile) {
  try {
    console.log(`Converting Playwright test results from ${reportFile}...`);
    
    // Read Playwright test results
    const playwrightResults = JSON.parse(fs.readFileSync(reportFile, 'utf8'));
    
    // Convert to QA Wolf format
    const qaWolfResults = [];
    
    for (const suite of playwrightResults.suites) {
      processPlaywrightSuite(suite, qaWolfResults);
    }
    
    return qaWolfResults;
  } catch (error) {
    console.error('Error converting Playwright test results:', error.message);
    throw error;
  }
}

/**
 * Process a Playwright test suite
 * @param {Object} suite - Playwright test suite
 * @param {Array<Object>} qaWolfResults - QA Wolf test results
 */
function processPlaywrightSuite(suite, qaWolfResults) {
  // Process specs in the suite
  for (const spec of suite.specs || []) {
    processPlaywrightSpec(spec, qaWolfResults);
  }
  
  // Process nested suites
  for (const nestedSuite of suite.suites || []) {
    processPlaywrightSuite(nestedSuite, qaWolfResults);
  }
}

/**
 * Process a Playwright test spec
 * @param {Object} spec - Playwright test spec
 * @param {Array<Object>} qaWolfResults - QA Wolf test results
 */
function processPlaywrightSpec(spec, qaWolfResults) {
  // Get test title
  const title = spec.title;
  
  // Get test file
  const file = spec.file;
  
  // Process test results
  for (const test of spec.tests || []) {
    processPlaywrightTest(test, title, file, qaWolfResults);
  }
}

/**
 * Process a Playwright test
 * @param {Object} test - Playwright test
 * @param {string} title - Test title
 * @param {string} file - Test file
 * @param {Array<Object>} qaWolfResults - QA Wolf test results
 */
function processPlaywrightTest(test, title, file, qaWolfResults) {
  // Get test result
  const result = test.results[0];
  
  // Create QA Wolf test result
  const qaWolfResult = {
    id: `${file}:${title}:${test.title}`,
    name: `${title} - ${test.title}`,
    status: result.status,
    duration: result.duration,
    file,
    steps: [],
    attachments: []
  };
  
  // Add error message if test failed
  if (result.status === 'failed') {
    qaWolfResult.errorMessage = result.error?.message || 'Unknown error';
    qaWolfResult.errorStack = result.error?.stack || '';
  }
  
  // Process steps
  for (const step of result.steps || []) {
    processPlaywrightStep(step, qaWolfResult.steps);
  }
  
  // Process attachments
  for (const attachment of result.attachments || []) {
    processPlaywrightAttachment(attachment, qaWolfResult.attachments);
  }
  
  // Add to QA Wolf results
  qaWolfResults.push(qaWolfResult);
}

/**
 * Process a Playwright test step
 * @param {Object} step - Playwright test step
 * @param {Array<Object>} steps - QA Wolf test steps
 */
function processPlaywrightStep(step, steps) {
  // Create QA Wolf test step
  const qaWolfStep = {
    name: step.title,
    status: step.error ? 'failed' : 'passed',
    duration: step.duration
  };
  
  // Add error message if step failed
  if (step.error) {
    qaWolfStep.errorMessage = step.error.message || 'Unknown error';
    qaWolfStep.errorStack = step.error.stack || '';
  }
  
  // Add to steps
  steps.push(qaWolfStep);
  
  // Process nested steps
  for (const nestedStep of step.steps || []) {
    processPlaywrightStep(nestedStep, steps);
  }
}

/**
 * Process a Playwright attachment
 * @param {Object} attachment - Playwright attachment
 * @param {Array<Object>} attachments - QA Wolf attachments
 */
function processPlaywrightAttachment(attachment, attachments) {
  // Create QA Wolf attachment
  const qaWolfAttachment = {
    name: attachment.name,
    contentType: attachment.contentType,
    path: attachment.path
  };
  
  // Read attachment data if available
  if (attachment.path && fs.existsSync(attachment.path)) {
    try {
      const data = fs.readFileSync(attachment.path);
      qaWolfAttachment.data = data.toString('base64');
    } catch (error) {
      console.warn(`Warning: Could not read attachment data from ${attachment.path}:`, error.message);
    }
  } else if (attachment.body) {
    qaWolfAttachment.data = attachment.body.toString('base64');
  }
  
  // Add to attachments
  attachments.push(qaWolfAttachment);
}

/**
 * Find the latest Playwright report file
 * @returns {string} - Path to the latest Playwright report file
 */
function findLatestPlaywrightReport() {
  try {
    // Check if Playwright report directory exists
    if (!fs.existsSync(config.playwrightReportDir)) {
      throw new Error(`Playwright report directory ${config.playwrightReportDir} does not exist`);
    }
    
    // Find all report files
    const reportFiles = fs.readdirSync(config.playwrightReportDir)
      .filter(file => file.endsWith('.json'))
      .map(file => path.join(config.playwrightReportDir, file));
    
    if (reportFiles.length === 0) {
      throw new Error(`No report files found in ${config.playwrightReportDir}`);
    }
    
    // Find the latest report file
    const latestReportFile = reportFiles.reduce((latest, current) => {
      const latestStat = fs.statSync(latest);
      const currentStat = fs.statSync(current);
      
      return currentStat.mtime > latestStat.mtime ? current : latest;
    });
    
    return latestReportFile;
  } catch (error) {
    console.error('Error finding latest Playwright report:', error.message);
    throw error;
  }
}

/**
 * Convert Playwright test results to QA Wolf format and save to file
 * @param {string} reportFile - Path to Playwright report file
 * @param {string} outputFile - Path to output file
 * @returns {string} - Path to output file
 */
function convertAndSave(reportFile, outputFile) {
  try {
    console.log(`Converting Playwright test results from ${reportFile} to QA Wolf format...`);
    
    // Convert Playwright test results to QA Wolf format
    const qaWolfResults = convertPlaywrightResults(reportFile);
    
    // Save QA Wolf test results to file
    const outputPath = path.join(config.qaWolfReportDir, outputFile);
    fs.writeFileSync(outputPath, JSON.stringify(qaWolfResults, null, 2));
    
    console.log(`QA Wolf test results saved to ${outputPath}`);
    
    return outputPath;
  } catch (error) {
    console.error('Error converting and saving test results:', error.message);
    throw error;
  }
}

// Export functions for use in other scripts
module.exports = {
  convertPlaywrightResults,
  findLatestPlaywrightReport,
  convertAndSave
};

// Run the script if called directly
if (require.main === module) {
  try {
    // Get report file from command line arguments or find the latest
    const reportFile = process.argv[2] || findLatestPlaywrightReport();
    
    // Get output file from command line arguments or use default
    const outputFile = process.argv[3] || config.outputFile;
    
    // Convert and save
    const outputPath = convertAndSave(reportFile, outputFile);
    
    console.log('Done!');
    console.log('Output file:', outputPath);
    
    process.exit(0);
  } catch (error) {
    console.error('Error:', error.message);
    process.exit(1);
  }
}
