/**
 * Generate Visual Regression Comparison Report
 * 
 * This script generates an HTML report for visual regression testing results.
 * It reads the comparison results from the screenshots/comparison directory
 * and generates an HTML report with side-by-side comparisons of baseline and
 * current screenshots, highlighting the differences.
 * 
 * Usage:
 * node scripts/generate_comparison_report.js [--test=test_name]
 * 
 * Examples:
 * node scripts/generate_comparison_report.js
 * node scripts/generate_comparison_report.js --test=login_test
 * 
 * @module generate_comparison_report
 * <AUTHOR> Wolf Team
 * @date 2025-01-01
 */

const fs = require('fs');
const path = require('path');
const { format } = require('date-fns');
const { SCREENSHOTS_BASE_DIR } = require('../src/utils/screenshot-utils');

// Parse command line arguments
const args = process.argv.slice(2);
let testName = null;

args.forEach(arg => {
  if (arg.startsWith('--test=')) {
    testName = arg.substring('--test='.length);
  }
});

/**
 * Generate HTML report for visual regression testing results
 * 
 * @param {Object} options - Report options
 * @param {string} [options.testName] - Test name to generate report for (optional)
 * @returns {Promise<Object>} - Report results
 */
async function generateReport(options = {}) {
  const { testName } = options;
  
  console.log(`Generating visual regression comparison report${testName ? ` for test "${testName}"` : ''}`);
  
  // Create report directory
  const reportDir = path.join(SCREENSHOTS_BASE_DIR, 'comparison-report');
  if (!fs.existsSync(reportDir)) {
    fs.mkdirSync(reportDir, { recursive: true });
  }
  
  // Get comparison results
  const comparisonDir = path.join(SCREENSHOTS_BASE_DIR, 'comparison');
  if (!fs.existsSync(comparisonDir)) {
    console.error(`Error: Comparison directory not found: ${comparisonDir}`);
    console.log('Run comparison first using scripts/compare_with_baseline.js');
    return {
      success: false,
      error: 'Comparison directory not found'
    };
  }
  
  // Get test directories
  let testDirs = [];
  if (testName) {
    const testDir = path.join(comparisonDir, testName);
    if (fs.existsSync(testDir)) {
      testDirs.push(testName);
    } else {
      console.error(`Error: Test directory not found: ${testDir}`);
      return {
        success: false,
        error: 'Test directory not found'
      };
    }
  } else {
    testDirs = fs.readdirSync(comparisonDir)
      .filter(dir => {
        const stats = fs.statSync(path.join(comparisonDir, dir));
        return stats.isDirectory();
      });
  }
  
  console.log(`Found ${testDirs.length} test directories`);
  
  // Generate report for each test
  const testReports = [];
  for (const testDir of testDirs) {
    const testReportPath = await generateTestReport(testDir, reportDir);
    testReports.push({
      testName: testDir,
      reportPath: testReportPath
    });
  }
  
  // Generate index page
  const indexPath = generateIndexPage(testReports, reportDir);
  
  console.log(`Generated report index: ${indexPath}`);
  console.log(`Generated ${testReports.length} test reports`);
  
  return {
    success: true,
    indexPath,
    testReports
  };
}

/**
 * Generate HTML report for a specific test
 * 
 * @param {string} testName - Test name
 * @param {string} reportDir - Report directory
 * @returns {Promise<string>} - Path to the generated report
 */
async function generateTestReport(testName, reportDir) {
  console.log(`Generating report for test "${testName}"`);
  
  // Get comparison results
  const testComparisonDir = path.join(SCREENSHOTS_BASE_DIR, 'comparison', testName);
  const comparisonFiles = fs.readdirSync(testComparisonDir)
    .filter(file => file.endsWith('.json'))
    .map(file => {
      const filePath = path.join(testComparisonDir, file);
      const data = JSON.parse(fs.readFileSync(filePath, 'utf8'));
      return {
        filename: file.replace('.json', ''),
        data
      };
    });
  
  console.log(`Found ${comparisonFiles.length} comparison results`);
  
  // Generate HTML content
  let htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Visual Regression Report - ${testName}</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }
    h1, h2, h3 {
      color: #333;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
    .summary {
      margin-bottom: 20px;
      padding: 15px;
      border-radius: 5px;
    }
    .summary.pass {
      background-color: #e6ffe6;
      border: 1px solid #99cc99;
    }
    .summary.fail {
      background-color: #ffe6e6;
      border: 1px solid #cc9999;
    }
    .comparison {
      margin-bottom: 30px;
      padding: 15px;
      border: 1px solid #ddd;
      border-radius: 5px;
    }
    .comparison-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
    }
    .comparison-status {
      padding: 5px 10px;
      border-radius: 3px;
      font-weight: bold;
    }
    .comparison-status.pass {
      background-color: #99cc99;
      color: white;
    }
    .comparison-status.fail {
      background-color: #cc9999;
      color: white;
    }
    .comparison-images {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
    }
    .comparison-image {
      flex: 1;
      min-width: 300px;
    }
    .comparison-image img {
      max-width: 100%;
      border: 1px solid #ddd;
    }
    .comparison-details {
      margin-top: 10px;
      font-size: 14px;
    }
    .back-link {
      display: inline-block;
      margin-bottom: 20px;
      color: #0066cc;
      text-decoration: none;
    }
    .back-link:hover {
      text-decoration: underline;
    }
  </style>
</head>
<body>
  <div class="container">
    <a href="index.html" class="back-link">← Back to Index</a>
    <h1>Visual Regression Report</h1>
    <h2>Test: ${testName}</h2>
`;

  // Add summary
  const passCount = comparisonFiles.filter(file => file.data.match).length;
  const failCount = comparisonFiles.length - passCount;
  const passRate = comparisonFiles.length > 0 ? (passCount / comparisonFiles.length * 100).toFixed(2) : 0;
  
  htmlContent += `
    <div class="summary ${failCount > 0 ? 'fail' : 'pass'}">
      <h3>Summary</h3>
      <p>Total comparisons: ${comparisonFiles.length}</p>
      <p>Passed: ${passCount} (${passRate}%)</p>
      <p>Failed: ${failCount}</p>
    </div>
`;

  // Add comparisons
  for (const comparison of comparisonFiles) {
    const { filename, data } = comparison;
    const baselineImage = path.relative(reportDir, data.baselineScreenshot).replace(/\\/g, '/');
    const currentImage = path.relative(reportDir, data.currentScreenshot).replace(/\\/g, '/');
    const diffImage = path.relative(reportDir, data.diffScreenshot).replace(/\\/g, '/');
    
    htmlContent += `
    <div class="comparison">
      <div class="comparison-header">
        <h3>${filename}</h3>
        <span class="comparison-status ${data.match ? 'pass' : 'fail'}">${data.match ? 'PASS' : 'FAIL'}</span>
      </div>
      <div class="comparison-images">
        <div class="comparison-image">
          <h4>Baseline</h4>
          <img src="../${baselineImage}" alt="Baseline">
        </div>
        <div class="comparison-image">
          <h4>Current</h4>
          <img src="../${currentImage}" alt="Current">
        </div>
        <div class="comparison-image">
          <h4>Difference</h4>
          <img src="../${diffImage}" alt="Difference">
        </div>
      </div>
      <div class="comparison-details">
        <p>Pixel difference: ${(data.pixelDifference * 100).toFixed(2)}%</p>
        <p>Threshold: ${(data.threshold * 100).toFixed(2)}%</p>
      </div>
    </div>
`;
  }

  htmlContent += `
  </div>
</body>
</html>
`;

  // Write HTML file
  const reportPath = path.join(reportDir, `${testName}.html`);
  fs.writeFileSync(reportPath, htmlContent);
  
  console.log(`Generated report: ${reportPath}`);
  
  return reportPath;
}

/**
 * Generate index page for all test reports
 * 
 * @param {Array<Object>} testReports - Test reports
 * @param {string} reportDir - Report directory
 * @returns {string} - Path to the index page
 */
function generateIndexPage(testReports, reportDir) {
  console.log('Generating index page');
  
  // Generate HTML content
  let htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Visual Regression Reports</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }
    h1, h2 {
      color: #333;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    }
    .test-list {
      list-style: none;
      padding: 0;
    }
    .test-item {
      padding: 10px 15px;
      border-bottom: 1px solid #eee;
    }
    .test-item:last-child {
      border-bottom: none;
    }
    .test-link {
      display: block;
      color: #0066cc;
      text-decoration: none;
      font-weight: bold;
    }
    .test-link:hover {
      text-decoration: underline;
    }
    .timestamp {
      margin-top: 20px;
      font-size: 14px;
      color: #666;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Visual Regression Reports</h1>
    <p>Generated on ${format(new Date(), 'yyyy-MM-dd HH:mm:ss')}</p>
    
    <h2>Test Reports</h2>
    <ul class="test-list">
`;

  // Add test reports
  for (const report of testReports) {
    const reportFilename = path.basename(report.reportPath);
    htmlContent += `
      <li class="test-item">
        <a href="${reportFilename}" class="test-link">${report.testName}</a>
      </li>
`;
  }

  htmlContent += `
    </ul>
    
    <div class="timestamp">
      <p>Generated by QA Wolf Screenshot Management System</p>
    </div>
  </div>
</body>
</html>
`;

  // Write HTML file
  const indexPath = path.join(reportDir, 'index.html');
  fs.writeFileSync(indexPath, htmlContent);
  
  console.log(`Generated index page: ${indexPath}`);
  
  return indexPath;
}

// Generate report
generateReport({ testName })
  .catch(error => {
    console.error('Error generating report:', error);
    process.exit(1);
  });
