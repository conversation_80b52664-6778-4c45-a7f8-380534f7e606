# Creating a Basic Test

This tutorial will guide you through creating a basic test using the QA Wolf testing framework.

## Prerequisites

Before you begin, make sure you have:

- Installed the QA Wolf testing framework
- Configured your environment

If you haven't done these steps yet, see the [Getting Started Guide](../guides/getting-started.md).

## Step 1: Create a Test File

Create a new file called `basic-test.spec.js` in your `tests` directory:

```javascript
const { test, expect } = require('@playwright/test');
const { createMcpController, createSelfHealingController } = require('@qawolf/test-framework');

test('basic test', async ({ page }) => {
  // We'll add our test code here
});
```

## Step 2: Set Up Controllers

Add code to create the MCP controller and self-healing controller:

```javascript
const { test, expect } = require('@playwright/test');
const { createMcpController, createSelfHealingController } = require('@qawolf/test-framework');

test('basic test', async ({ page }) => {
  // Create an MCP controller
  const mcpController = createMcpController();
  
  // Create a self-healing controller
  const selfHealingController = createSelfHealingController();
  
  try {
    // We'll add our test code here
  } finally {
    // Clean up resources
    await mcpController.cleanup();
    await selfHealingController.cleanup();
  }
});
```

## Step 3: Start the Test Run

Add code to start the test run:

```javascript
const { test, expect } = require('@playwright/test');
const { createMcpController, createSelfHealingController } = require('@qawolf/test-framework');

test('basic test', async ({ page }) => {
  // Create an MCP controller
  const mcpController = createMcpController();
  
  // Create a self-healing controller
  const selfHealingController = createSelfHealingController();
  
  try {
    // Start the test run
    await selfHealingController.startTest({
      testId: 'basic-test',
      testName: 'Basic Test'
    });
    
    // Create a self-healing page
    const selfHealingPage = selfHealingController.createPage(page);
    
    // We'll add our test code here
    
    // End the test run
    await selfHealingController.endTest({
      success: true
    });
  } catch (error) {
    // End the test run with failure
    await selfHealingController.endTest({
      success: false,
      error
    });
    
    throw error;
  } finally {
    // Clean up resources
    await mcpController.cleanup();
    await selfHealingController.cleanup();
  }
});
```

## Step 4: Add Test Logic

Now, let's add the actual test logic. We'll navigate to a website and check its title:

```javascript
const { test, expect } = require('@playwright/test');
const { createMcpController, createSelfHealingController } = require('@qawolf/test-framework');

test('basic test', async ({ page }) => {
  // Create an MCP controller
  const mcpController = createMcpController();
  
  // Create a self-healing controller
  const selfHealingController = createSelfHealingController();
  
  try {
    // Start the test run
    await selfHealingController.startTest({
      testId: 'basic-test',
      testName: 'Basic Test'
    });
    
    // Create a self-healing page
    const selfHealingPage = selfHealingController.createPage(page);
    
    // Navigate to a website
    await selfHealingPage.goto('https://example.com');
    
    // Check the title
    const title = await selfHealingPage.title();
    expect(title).toBe('Example Domain');
    
    // End the test run
    await selfHealingController.endTest({
      success: true
    });
  } catch (error) {
    // End the test run with failure
    await selfHealingController.endTest({
      success: false,
      error
    });
    
    throw error;
  } finally {
    // Clean up resources
    await mcpController.cleanup();
    await selfHealingController.cleanup();
  }
});
```

## Step 5: Add Performance Tracking

Let's add performance tracking to measure how long each operation takes:

```javascript
const { test, expect } = require('@playwright/test');
const { createMcpController, createSelfHealingController, test: testUtils } = require('@qawolf/test-framework');

test('basic test', async ({ page }) => {
  // Create an MCP controller
  const mcpController = createMcpController();
  
  // Create a self-healing controller
  const selfHealingController = createSelfHealingController();
  
  try {
    // Start the test run
    await selfHealingController.startTest({
      testId: 'basic-test',
      testName: 'Basic Test'
    });
    
    // Create a self-healing page
    const selfHealingPage = selfHealingController.createPage(page);
    
    // Create a performance tracker
    const performanceTracker = new testUtils.PerformanceTracker();
    
    // Navigate to a website
    performanceTracker.startOperation('navigation');
    await selfHealingPage.goto('https://example.com');
    performanceTracker.endOperation();
    
    // Check the title
    performanceTracker.startOperation('title_check');
    const title = await selfHealingPage.title();
    expect(title).toBe('Example Domain');
    performanceTracker.endOperation();
    
    // Track performance metrics
    await selfHealingController.trackPerformance(performanceTracker.getMetrics());
    
    // End the test run
    await selfHealingController.endTest({
      success: true
    });
  } catch (error) {
    // End the test run with failure
    await selfHealingController.endTest({
      success: false,
      error
    });
    
    throw error;
  } finally {
    // Clean up resources
    await mcpController.cleanup();
    await selfHealingController.cleanup();
  }
});
```

## Step 6: Run the Test

Now, let's run the test:

```bash
npx playwright test tests/basic-test.spec.js
```

You should see output indicating that the test passed.

## Step 7: View the Test Report

You can view the test report by running:

```bash
npx playwright show-report
```

This will open the Playwright HTML report in your browser, showing detailed information about your test run.

## Step 8: Generate a Self-Healing Report

You can generate a self-healing report to see how the self-healing automation performed:

```javascript
// Generate a report
const report = await selfHealingController.generateReport({
  format: 'json'
});

console.log(report);
```

Add this code before the `endTest` call in your test.

## Conclusion

Congratulations! You've created a basic test using the QA Wolf testing framework. This test:

1. Navigates to a website
2. Checks the title
3. Tracks performance metrics
4. Uses self-healing automation

You can use this as a starting point for more complex tests. For more information, see:

- [MCP Integration Guide](../guides/mcp-integration.md)
- [Self-Healing Automation Guide](../guides/self-healing.md)
- [Performance Optimization Guide](../guides/performance-optimization.md)