# Getting Started

This guide will help you get started with the QA Wolf testing framework.

## Prerequisites

Before you begin, make sure you have the following installed:

- Node.js 20 or later
- npm 9 or later
- A QA Wolf API key (Team ID)

## Installation

You can install the QA Wolf testing framework using the installation script:

```bash
npx @qawolf/test-framework install
```

This will guide you through the installation process and set up a basic project structure.

Alternatively, you can install the packages manually:

```bash
npm install @qawolf/shared-utils @qawolf/mcp-optimizer @qawolf/test-framework
npm install --save-dev @playwright/test
```

## Configuration

After installation, you need to configure the framework. You can use the configuration script:

```bash
npx @qawolf/test-framework configure
```

This will guide you through the configuration process and create a `qawolf.config.js` file.

Alternatively, you can create a `.env` file with the following variables:

```
QAWOLF_API_KEY=your-api-key
QAWOLF_EMAIL=your-test-email
QAWOLF_PASSWORD=your-test-password
```

## Creating Your First Test

Let's create a simple test that logs in to a website:

```javascript
const { test, expect } = require('@playwright/test');
const { createMcpController, createSelfHealingController } = require('@qawolf/test-framework');

test('login test', async ({ page }) => {
  // Create an MCP controller
  const mcpController = createMcpController();
  
  // Create a self-healing controller
  const selfHealingController = createSelfHealingController();
  
  try {
    // Start the test run
    await selfHealingController.startTest({
      testId: 'login-test',
      testName: 'Login Test'
    });
    
    // Create a self-healing page
    const selfHealingPage = selfHealingController.createPage(page);
    
    // Navigate to the app
    await selfHealingPage.goto('https://app.lidostaging.com');
    
    // Fill in login form
    await selfHealingPage.fill('[data-test-id="SignInEmail"]', '<EMAIL>');
    await selfHealingPage.fill('[data-test-id="SignInPassword"]', 'vhc!tGK289IS&');
    
    // Click the login button
    await selfHealingPage.click(':text("Log in with email")');
    
    // Wait for login to complete
    await selfHealingPage.waitForSelector('div[class*="FilesTable"]', { timeout: 15000 });
    
    // Verify login was successful
    const isLoggedIn = await selfHealingPage.isVisible('div[class*="FilesTable"]');
    expect(isLoggedIn).toBe(true);
    
    // End the test run
    await selfHealingController.endTest({
      success: true
    });
  } catch (error) {
    // End the test run with failure
    await selfHealingController.endTest({
      success: false,
      error
    });
    
    throw error;
  } finally {
    // Clean up resources
    await mcpController.cleanup();
    await selfHealingController.cleanup();
  }
});
```

Save this file as `tests/login.spec.js`.

## Running Your First Test

You can run your test using the Playwright test runner:

```bash
npx playwright test tests/login.spec.js
```

This will run your test and show the results in the console.

## Understanding the Results

After running your test, you'll see the results in the console. The test will either pass or fail, and you'll see any errors that occurred.

You can also view the test report by running:

```bash
npx playwright show-report
```

This will open the Playwright HTML report in your browser, showing detailed information about your test run.

## Next Steps

Now that you've created and run your first test, you can:

- [Learn more about MCP integration](./mcp-integration.md)
- [Learn more about self-healing automation](./self-healing.md)
- [Learn more about performance optimization](./performance-optimization.md)
- [Learn more about reliability improvement](./reliability-improvement.md)
- [Explore the API reference](../api/test-framework.md)