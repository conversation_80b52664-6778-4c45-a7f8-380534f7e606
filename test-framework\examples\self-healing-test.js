/**
 * Example Test Script with Self-Healing Automation
 * 
 * This script demonstrates how to use the self-healing automation capabilities
 * in a test script.
 */

const { test } = require('@playwright/test');
const { createSelfHealingController, test: testUtils } = require('../src');

test('Login with self-healing automation', async ({ page }) => {
  // Create a self-healing controller
  const selfHealingController = createSelfHealingController({
    selectorHealing: {
      enabled: true,
      maxAttempts: 3,
      strategies: ['css-relaxation', 'attribute-based', 'text-based', 'xpath']
    },
    recovery: {
      enabled: true,
      maxAttempts: 3,
      strategies: ['retry', 'wait', 'selector', 'refresh', 'screenshot']
    }
  });
  
  try {
    // Start the test run
    await selfHealingController.startTest({
      testId: 'login-self-healing',
      testName: 'Login with self-healing automation'
    });
    
    // Create a self-healing page
    const selfHealingPage = selfHealingController.createPage(page);
    
    // Start performance tracking
    const performanceTracker = new testUtils.PerformanceTracker();
    
    // Navigate to the app
    performanceTracker.startOperation('navigation_to_app');
    await selfHealingPage.goto('https://app.lidostaging.com');
    performanceTracker.endOperation();
    
    // Fill in login form
    performanceTracker.startOperation('login');
    
    // These selectors will be automatically healed if they fail
    await selfHealingPage.fill('[data-test-id="SignInEmail"]', '<EMAIL>');
    await selfHealingPage.fill('[data-test-id="SignInPassword"]', 'vhc!tGK289IS&');
    
    // Use a locator with self-healing
    const loginButton = selfHealingPage.locator(':text("Log in with email")');
    await loginButton.click();
    
    // Wait for login to complete
    await selfHealingPage.waitForSelector('div[class*="FilesTable"]', { timeout: 15000 });
    performanceTracker.endOperation();
    
    // Take a screenshot
    const screenshotPath = await testUtils.takeScreenshot(page, {
      testName: 'login-self-healing',
      action: 'after-login',
      fullPage: false
    });
    
    // Track performance metrics
    await selfHealingController.trackPerformance(performanceTracker.getMetrics());
    
    // End the test run
    await selfHealingController.endTest({
      success: true
    });
    
    // Generate a report
    const report = await selfHealingController.generateReport();
    console.log('Self-healing report:', report);
    
    // Analyze feedback
    const analysis = await selfHealingController.analyzeFeedback();
    console.log('Feedback analysis:', JSON.stringify(analysis, null, 2));
    
    // Suggest improvements
    const improvements = await selfHealingController.suggestImprovements(__filename);
    console.log('Improvement suggestions:', JSON.stringify(improvements, null, 2));
    
    // Print test summary
    testUtils.printTestSummary(
      {
        success: true,
        performanceMetrics: performanceTracker.getMetrics(),
        aaaComplianceScore: testUtils.calculateAAAComplianceScore({
          success: true,
          performanceMetrics: performanceTracker.getMetrics(),
          allPerformanceMetricsWithinThresholds: performanceTracker.areAllMetricsWithinThresholds()
        })
      },
      'login-self-healing',
      'Login with self-healing automation',
      performanceTracker.startTime
    );
  } catch (error) {
    // End the test run with failure
    await selfHealingController.endTest({
      success: false,
      error
    });
    
    throw error;
  } finally {
    // Clean up resources
    await selfHealingController.cleanup();
  }
});