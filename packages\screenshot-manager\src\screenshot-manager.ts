/**
 * Screenshot manager for QA Wolf Metrics Framework
 */

import { v4 as uuidv4 } from 'uuid';
import { <PERSON>, Locator, Browser, BrowserContext } from 'playwright';
import { ScreenshotType, ScreenshotFormat, ScreenshotOptions, ScreenshotInfo, ScreenshotManagerOptions } from './types';
import { ScreenshotStorage } from './screenshot-storage';
import { ScreenshotProcessor } from './screenshot-processor';
import { ScreenshotReporter } from './screenshot-reporter';
import { ScreenshotComparator } from './screenshot-comparator';
import { getScreenshotInfo, getBrowserInfo, getOsInfo } from './utils';
import { EventBus, EventType } from '@qawolf/core';

/**
 * Screenshot manager
 */
export class ScreenshotManager {
  /**
   * Screenshot storage
   */
  private storage: ScreenshotStorage;
  
  /**
   * Screenshot processor
   */
  private processor: ScreenshotProcessor;
  
  /**
   * Screenshot reporter
   */
  private reporter: ScreenshotReporter;
  
  /**
   * Screenshot comparator
   */
  private comparator: ScreenshotComparator;
  
  /**
   * Default screenshot options
   */
  private defaultOptions: ScreenshotOptions;
  
  /**
   * Current test name
   */
  private testName?: string;
  
  /**
   * Current test ID
   */
  private testId?: string;
  
  /**
   * Current browser
   */
  private browser?: Browser;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options Screenshot manager options
   */
  constructor(options: ScreenshotManagerOptions = {}) {
    this.storage = new ScreenshotStorage({
      baseDir: options.baseDir || 'screenshots',
      organizeByDate: options.organizeByDate !== false,
      organizeByTestName: options.organizeByTestName !== false,
      organizeByTestId: options.organizeByTestId !== false,
      organizeByBrowser: options.organizeByBrowser !== false,
      maxAge: options.maxAge || 7,
      maxCount: options.maxCount
    });
    
    this.processor = new ScreenshotProcessor({
      compressScreenshots: options.compressScreenshots !== false,
      addMetadata: options.addMetadata !== false
    });
    
    this.reporter = new ScreenshotReporter();
    this.comparator = new ScreenshotComparator();
    
    this.defaultOptions = {
      type: ScreenshotType.VIEWPORT,
      format: ScreenshotFormat.PNG,
      quality: 90,
      omitBackground: false,
      maskSensitiveData: false,
      addTimestamp: true,
      addTestName: true,
      addBrowserInfo: true,
      addMetadata: true,
      ...options.defaultOptions
    };
    
    this.eventBus = EventBus.getInstance();
  }
  
  /**
   * Set test context
   * @param testName Test name
   * @param testId Test ID
   * @returns This instance for chaining
   */
  setTestContext(testName: string, testId?: string): ScreenshotManager {
    this.testName = testName;
    this.testId = testId || uuidv4();
    
    return this;
  }
  
  /**
   * Set browser
   * @param browser Browser
   * @returns This instance for chaining
   */
  setBrowser(browser: Browser): ScreenshotManager {
    this.browser = browser;
    
    return this;
  }
  
  /**
   * Take screenshot
   * @param page Page
   * @param name Screenshot name
   * @param options Screenshot options
   * @returns Screenshot info
   */
  async takeScreenshot(page: Page, name: string, options: Partial<ScreenshotOptions> = {}): Promise<ScreenshotInfo> {
    // Merge options with defaults
    const mergedOptions: ScreenshotOptions = {
      ...this.defaultOptions,
      ...options
    };
    
    // Generate screenshot ID
    const screenshotId = uuidv4();
    
    // Get browser info
    const browserInfo = this.browser ? await getBrowserInfo(this.browser) : undefined;
    
    // Get OS info
    const osInfo = getOsInfo();
    
    // Get viewport size
    const viewportSize = page.viewportSize();
    
    // Get URL
    const url = page.url();
    
    // Take screenshot
    const screenshotBuffer = await page.screenshot({
      fullPage: mergedOptions.type === ScreenshotType.FULL_PAGE,
      type: mergedOptions.format,
      quality: mergedOptions.format === ScreenshotFormat.JPEG ? mergedOptions.quality : undefined,
      omitBackground: mergedOptions.omitBackground,
      clip: mergedOptions.clip,
      timeout: mergedOptions.timeout
    });
    
    // Process screenshot
    const processedBuffer = await this.processor.processScreenshot(screenshotBuffer, mergedOptions);
    
    // Save screenshot
    const screenshotPath = await this.storage.saveScreenshot(processedBuffer, {
      id: screenshotId,
      name,
      format: mergedOptions.format || ScreenshotFormat.PNG,
      testName: this.testName,
      testId: this.testId,
      browserName: browserInfo?.name,
      browserVersion: browserInfo?.version
    });
    
    // Get screenshot info
    const screenshotInfo = await getScreenshotInfo(screenshotPath);
    
    // Add additional info
    const fullScreenshotInfo: ScreenshotInfo = {
      ...screenshotInfo,
      id: screenshotId,
      name,
      testName: this.testName,
      testId: this.testId,
      browserName: browserInfo?.name,
      browserVersion: browserInfo?.version,
      os: osInfo?.name,
      osVersion: osInfo?.version,
      viewportWidth: viewportSize?.width,
      viewportHeight: viewportSize?.height,
      url,
      metadata: mergedOptions.metadata
    };
    
    // Emit event
    this.eventBus.emit(EventType.SCREENSHOT_TAKEN, {
      screenshotInfo: fullScreenshotInfo
    });
    
    return fullScreenshotInfo;
  }
  
  /**
   * Take element screenshot
   * @param page Page
   * @param selector Selector or locator
   * @param name Screenshot name
   * @param options Screenshot options
   * @returns Screenshot info
   */
  async takeElementScreenshot(page: Page, selector: string | Locator, name: string, options: Partial<ScreenshotOptions> = {}): Promise<ScreenshotInfo> {
    // Get locator
    const locator = typeof selector === 'string' ? page.locator(selector) : selector;
    
    // Merge options with defaults
    const mergedOptions: ScreenshotOptions = {
      ...this.defaultOptions,
      ...options,
      type: ScreenshotType.ELEMENT
    };
    
    // Generate screenshot ID
    const screenshotId = uuidv4();
    
    // Get browser info
    const browserInfo = this.browser ? await getBrowserInfo(this.browser) : undefined;
    
    // Get OS info
    const osInfo = getOsInfo();
    
    // Get viewport size
    const viewportSize = page.viewportSize();
    
    // Get URL
    const url = page.url();
    
    // Take screenshot
    const screenshotBuffer = await locator.screenshot({
      type: mergedOptions.format,
      quality: mergedOptions.format === ScreenshotFormat.JPEG ? mergedOptions.quality : undefined,
      omitBackground: mergedOptions.omitBackground,
      timeout: mergedOptions.timeout
    });
    
    // Process screenshot
    const processedBuffer = await this.processor.processScreenshot(screenshotBuffer, mergedOptions);
    
    // Save screenshot
    const screenshotPath = await this.storage.saveScreenshot(processedBuffer, {
      id: screenshotId,
      name,
      format: mergedOptions.format || ScreenshotFormat.PNG,
      testName: this.testName,
      testId: this.testId,
      browserName: browserInfo?.name,
      browserVersion: browserInfo?.version
    });
    
    // Get screenshot info
    const screenshotInfo = await getScreenshotInfo(screenshotPath);
    
    // Add additional info
    const fullScreenshotInfo: ScreenshotInfo = {
      ...screenshotInfo,
      id: screenshotId,
      name,
      testName: this.testName,
      testId: this.testId,
      browserName: browserInfo?.name,
      browserVersion: browserInfo?.version,
      os: osInfo?.name,
      osVersion: osInfo?.version,
      viewportWidth: viewportSize?.width,
      viewportHeight: viewportSize?.height,
      url,
      selector: typeof selector === 'string' ? selector : undefined,
      metadata: mergedOptions.metadata
    };
    
    // Emit event
    this.eventBus.emit(EventType.SCREENSHOT_TAKEN, {
      screenshotInfo: fullScreenshotInfo
    });
    
    return fullScreenshotInfo;
  }
  
  /**
   * Compare screenshots
   * @param screenshotPath1 First screenshot path
   * @param screenshotPath2 Second screenshot path
   * @param options Comparison options
   * @returns Comparison result
   */
  async compareScreenshots(screenshotPath1: string, screenshotPath2: string, options?: any): Promise<any> {
    return this.comparator.compareScreenshots(screenshotPath1, screenshotPath2, options);
  }
  
  /**
   * Generate report
   * @param outputDir Output directory
   * @param formats Report formats
   * @returns Report file paths
   */
  async generateReport(outputDir?: string, formats?: ('json' | 'html' | 'csv')[]): Promise<string[]> {
    const screenshots = await this.storage.listScreenshots();
    return this.reporter.generateReport(screenshots, outputDir, formats);
  }
  
  /**
   * Clean up old screenshots
   * @param maxAge Maximum age in days
   * @returns Number of deleted screenshots
   */
  async cleanupOldScreenshots(maxAge?: number): Promise<number> {
    return this.storage.cleanupOldScreenshots(maxAge);
  }
  
  /**
   * Get screenshot storage
   * @returns Screenshot storage
   */
  getStorage(): ScreenshotStorage {
    return this.storage;
  }
  
  /**
   * Get screenshot processor
   * @returns Screenshot processor
   */
  getProcessor(): ScreenshotProcessor {
    return this.processor;
  }
  
  /**
   * Get screenshot reporter
   * @returns Screenshot reporter
   */
  getReporter(): ScreenshotReporter {
    return this.reporter;
  }
  
  /**
   * Get screenshot comparator
   * @returns Screenshot comparator
   */
  getComparator(): ScreenshotComparator {
    return this.comparator;
  }
  
  /**
   * Get default screenshot options
   * @returns Default screenshot options
   */
  getDefaultOptions(): ScreenshotOptions {
    return { ...this.defaultOptions };
  }
  
  /**
   * Set default screenshot options
   * @param options Default screenshot options
   * @returns This instance for chaining
   */
  setDefaultOptions(options: Partial<ScreenshotOptions>): ScreenshotManager {
    this.defaultOptions = {
      ...this.defaultOptions,
      ...options
    };
    
    return this;
  }
}

/**
 * Create screenshot manager
 * @param options Screenshot manager options
 * @returns Screenshot manager
 */
export function createScreenshotManager(options: ScreenshotManagerOptions = {}): ScreenshotManager {
  return new ScreenshotManager(options);
}
