/**
 * Selector Healer
 * 
 * This module provides functionality for healing selectors that fail to locate elements.
 */

const fs = require('fs');
const path = require('path');
const { createDefaultConfig, mergeConfigs } = require('./config');

/**
 * Class for healing selectors
 */
class SelectorHealer {
  /**
   * Create a new SelectorHealer
   * @param {Object} [config] - Configuration options
   */
  constructor(config = {}) {
    this.config = mergeConfigs(createDefaultConfig().selectorHealing, config);
    this.selectorHistory = {};
    this.initialized = false;
    this.initializationPromise = null;
  }
  
  /**
   * Initialize the selector healer
   * @returns {Promise<void>}
   */
  async initialize() {
    if (this.initialized) {
      return;
    }
    
    if (this.initializationPromise) {
      return this.initializationPromise;
    }
    
    this.initializationPromise = (async () => {
      if (this.config.persistHistory) {
        try {
          await this.loadHistory();
        } catch (error) {
          console.warn('Failed to load selector history:', error);
        }
      }
      
      this.initialized = true;
    })();
    
    return this.initializationPromise;
  }
  
  /**
   * Load selector history from file
   * @returns {Promise<void>}
   */
  async loadHistory() {
    try {
      if (fs.existsSync(this.config.historyPath)) {
        const historyData = await fs.promises.readFile(this.config.historyPath, 'utf8');
        this.selectorHistory = JSON.parse(historyData);
      }
    } catch (error) {
      console.error('Failed to load selector history:', error);
      this.selectorHistory = {};
    }
  }
  
  /**
   * Save selector history to file
   * @returns {Promise<void>}
   */
  async saveHistory() {
    if (!this.config.persistHistory) {
      return;
    }
    
    try {
      const historyDir = path.dirname(this.config.historyPath);
      if (!fs.existsSync(historyDir)) {
        await fs.promises.mkdir(historyDir, { recursive: true });
      }
      
      await fs.promises.writeFile(
        this.config.historyPath,
        JSON.stringify(this.selectorHistory, null, 2),
        'utf8'
      );
    } catch (error) {
      console.error('Failed to save selector history:', error);
    }
  }
  
  /**
   * Heal a selector that failed to locate an element
   * @param {string} selector - The selector that failed
   * @param {Object} context - Context information
   * @returns {Promise<{selector: string, confidence: number, healed: boolean}>} - Healed selector info
   */
  async healSelector(selector, context = {}) {
    await this.initialize();
    
    if (!this.config.enabled) {
      return { selector, confidence: 1, healed: false };
    }
    
    // Get selector history
    const history = this.getSelectorHistory(selector);
    
    // If the selector has a high success rate, just return it
    if (history.successRate > this.config.confidenceThreshold && history.attempts >= this.config.optimizationThreshold) {
      return { selector, confidence: history.successRate, healed: false };
    }
    
    // Generate alternative selectors
    const alternatives = await this.generateAlternatives(selector, context);
    
    // If no alternatives, return the original selector
    if (!alternatives || alternatives.length === 0) {
      return { selector, confidence: history.successRate || 0.5, healed: false };
    }
    
    // Find the best alternative
    let bestAlternative = null;
    let bestConfidence = 0;
    
    for (const alt of alternatives) {
      const altHistory = this.getSelectorHistory(alt);
      const confidence = altHistory.successRate || 0.5;
      
      if (confidence > bestConfidence) {
        bestAlternative = alt;
        bestConfidence = confidence;
      }
    }
    
    // If no good alternative, return the original selector
    if (!bestAlternative || bestConfidence < this.config.confidenceThreshold) {
      return { selector, confidence: history.successRate || 0.5, healed: false };
    }
    
    return {
      selector: bestAlternative,
      confidence: bestConfidence,
      healed: true,
      original: selector
    };
  }
  
  /**
   * Generate alternative selectors
   * @param {string} selector - The original selector
   * @param {Object} context - Context information
   * @returns {Promise<Array<string>>} - Alternative selectors
   */
  async generateAlternatives(selector, context = {}) {
    const alternatives = [];
    
    // Apply enabled strategies
    for (const strategy of this.config.strategies) {
      switch (strategy) {
        case 'css-relaxation':
          alternatives.push(...this.generateCssRelaxationAlternatives(selector));
          break;
        case 'attribute-based':
          alternatives.push(...this.generateAttributeBasedAlternatives(selector, context));
          break;
        case 'text-based':
          alternatives.push(...this.generateTextBasedAlternatives(selector, context));
          break;
        case 'xpath':
          alternatives.push(...this.generateXPathAlternatives(selector));
          break;
      }
    }
    
    // Filter out duplicates and the original selector
    return [...new Set(alternatives)].filter(alt => alt !== selector);
  }
  
  /**
   * Generate alternatives by relaxing CSS selectors
   * @param {string} selector - The original selector
   * @returns {Array<string>} - Alternative selectors
   */
  generateCssRelaxationAlternatives(selector) {
    const alternatives = [];
    
    // Skip if not a CSS selector
    if (selector.startsWith('//') || selector.startsWith('xpath=')) {
      return alternatives;
    }
    
    // Remove specific classes
    if (selector.includes('.')) {
      const parts = selector.split('.');
      const base = parts[0];
      
      // Remove one class at a time
      for (let i = 1; i < parts.length; i++) {
        const classes = [...parts.slice(1)];
        classes.splice(i - 1, 1);
        
        if (classes.length > 0) {
          alternatives.push(`${base}.${classes.join('.')}`);
        } else {
          alternatives.push(base);
        }
      }
      
      // Remove all classes
      alternatives.push(base);
    }
    
    // Remove specific IDs
    if (selector.includes('#')) {
      const parts = selector.split('#');
      const base = parts[0];
      
      // Remove the ID
      alternatives.push(base);
    }
    
    // Remove specific attributes
    if (selector.includes('[') && selector.includes(']')) {
      const beforeAttr = selector.split('[')[0];
      const afterAttr = selector.split(']').slice(1).join(']');
      
      // Remove the attribute
      alternatives.push(beforeAttr + afterAttr);
    }
    
    // Remove :nth-child
    if (selector.includes(':nth-child')) {
      alternatives.push(selector.replace(/:nth-child\(\d+\)/g, ''));
    }
    
    return alternatives;
  }
  
  /**
   * Generate alternatives based on attributes
   * @param {string} selector - The original selector
   * @param {Object} context - Context information
   * @returns {Array<string>} - Alternative selectors
   */
  generateAttributeBasedAlternatives(selector, context = {}) {
    const alternatives = [];
    
    // If we have element information in the context, use it
    if (context.element) {
      const { tagName, attributes } = context.element;
      
      if (tagName) {
        // By tag name
        alternatives.push(tagName);
        
        // By tag name and common attributes
        if (attributes) {
          // By ID
          if (attributes.id) {
            alternatives.push(`#${attributes.id}`);
            alternatives.push(`[id="${attributes.id}"]`);
            alternatives.push(`${tagName}#${attributes.id}`);
            alternatives.push(`${tagName}[id="${attributes.id}"]`);
          }
          
          // By name
          if (attributes.name) {
            alternatives.push(`[name="${attributes.name}"]`);
            alternatives.push(`${tagName}[name="${attributes.name}"]`);
          }
          
          // By type
          if (attributes.type) {
            alternatives.push(`[type="${attributes.type}"]`);
            alternatives.push(`${tagName}[type="${attributes.type}"]`);
          }
          
          // By role
          if (attributes.role) {
            alternatives.push(`[role="${attributes.role}"]`);
            alternatives.push(`${tagName}[role="${attributes.role}"]`);
          }
          
          // By data-testid
          if (attributes['data-testid']) {
            alternatives.push(`[data-testid="${attributes['data-testid']}"]`);
            alternatives.push(`${tagName}[data-testid="${attributes['data-testid']}"]`);
          }
          
          // By data-test-id
          if (attributes['data-test-id']) {
            alternatives.push(`[data-test-id="${attributes['data-test-id']}"]`);
            alternatives.push(`${tagName}[data-test-id="${attributes['data-test-id']}"]`);
          }
          
          // By data-test
          if (attributes['data-test']) {
            alternatives.push(`[data-test="${attributes['data-test']}"]`);
            alternatives.push(`${tagName}[data-test="${attributes['data-test']}"]`);
          }
          
          // By aria-label
          if (attributes['aria-label']) {
            alternatives.push(`[aria-label="${attributes['aria-label']}"]`);
            alternatives.push(`${tagName}[aria-label="${attributes['aria-label']}"]`);
          }
        }
      }
    }
    
    return alternatives;
  }
  
  /**
   * Generate alternatives based on text content
   * @param {string} selector - The original selector
   * @param {Object} context - Context information
   * @returns {Array<string>} - Alternative selectors
   */
  generateTextBasedAlternatives(selector, context = {}) {
    const alternatives = [];
    
    // If we have element information in the context, use it
    if (context.element && context.element.text) {
      const { text, tagName } = context.element;
      
      // By text content
      alternatives.push(`:text("${text}")`);
      
      // By tag and text content
      if (tagName) {
        alternatives.push(`${tagName}:text("${text}")`);
      }
      
      // By text content with contains
      alternatives.push(`:text-matches("${text}", "i")`);
      
      // By tag and text content with contains
      if (tagName) {
        alternatives.push(`${tagName}:text-matches("${text}", "i")`);
      }
    }
    
    return alternatives;
  }
  
  /**
   * Generate XPath alternatives
   * @param {string} selector - The original selector
   * @returns {Array<string>} - Alternative selectors
   */
  generateXPathAlternatives(selector) {
    const alternatives = [];
    
    // Skip if already an XPath selector
    if (selector.startsWith('//') || selector.startsWith('xpath=')) {
      return alternatives;
    }
    
    // Try to convert CSS to XPath
    try {
      // This is a simplified conversion and won't work for all CSS selectors
      if (selector.startsWith('#')) {
        // ID selector
        const id = selector.substring(1);
        alternatives.push(`//*[@id="${id}"]`);
      } else if (selector.startsWith('.')) {
        // Class selector
        const className = selector.substring(1);
        alternatives.push(`//*[contains(@class, "${className}")]`);
      } else if (selector.includes('#')) {
        // Element with ID
        const [tag, id] = selector.split('#');
        alternatives.push(`//${tag}[@id="${id}"]`);
      } else if (selector.includes('.')) {
        // Element with class
        const [tag, className] = selector.split('.');
        alternatives.push(`//${tag}[contains(@class, "${className}")]`);
      } else if (selector.includes('[') && selector.includes(']')) {
        // Element with attribute
        const tag = selector.split('[')[0];
        const attrPart = selector.match(/\[(.*?)\]/)[1];
        
        if (attrPart.includes('=')) {
          const [attr, value] = attrPart.split('=');
          const cleanValue = value.replace(/['"]/g, '');
          alternatives.push(`//${tag}[@${attr}="${cleanValue}"]`);
        }
      } else {
        // Simple tag selector
        alternatives.push(`//${selector}`);
      }
    } catch (error) {
      console.warn('Failed to convert CSS to XPath:', error);
    }
    
    return alternatives;
  }
  
  /**
   * Track the result of using a selector
   * @param {string} selector - The selector
   * @param {boolean} success - Whether the selector was successful
   * @param {Object} context - Context information
   * @returns {Promise<void>}
   */
  async trackSelectorResult(selector, success, context = {}) {
    await this.initialize();
    
    if (!this.config.enabled) {
      return;
    }
    
    // Get or create selector history
    const history = this.getSelectorHistory(selector);
    
    // Update history
    history.attempts += 1;
    if (success) {
      history.successes += 1;
    } else {
      history.failures += 1;
    }
    history.successRate = history.successes / history.attempts;
    history.lastUsed = new Date();
    
    // Update context information
    const contextKey = this.getContextKey(context);
    if (contextKey) {
      if (!history.contexts[contextKey]) {
        history.contexts[contextKey] = {
          attempts: 0,
          successes: 0,
          failures: 0,
          successRate: 0
        };
      }
      
      history.contexts[contextKey].attempts += 1;
      if (success) {
        history.contexts[contextKey].successes += 1;
      } else {
        history.contexts[contextKey].failures += 1;
      }
      history.contexts[contextKey].successRate = history.contexts[contextKey].successes / history.contexts[contextKey].attempts;
    }
    
    // If this is a healed selector, update the original selector's alternatives
    if (context.original) {
      const originalHistory = this.getSelectorHistory(context.original);
      if (!originalHistory.alternativesUsed.includes(selector)) {
        originalHistory.alternativesUsed.push(selector);
      }
    }
    
    // Save history
    this.selectorHistory[selector] = history;
    await this.saveHistory();
  }
  
  /**
   * Get the history for a selector
   * @param {string} selector - The selector
   * @returns {Object} - Selector history
   */
  getSelectorHistory(selector) {
    if (!this.selectorHistory[selector]) {
      this.selectorHistory[selector] = {
        selector,
        attempts: 0,
        successes: 0,
        failures: 0,
        successRate: 0,
        alternativesUsed: [],
        contexts: {},
        lastUsed: new Date(),
        created: new Date()
      };
    }
    
    return this.selectorHistory[selector];
  }
  
  /**
   * Get a key for the context
   * @param {Object} context - Context information
   * @returns {string|null} - Context key
   */
  getContextKey(context) {
    if (!context || Object.keys(context).length === 0) {
      return null;
    }
    
    const keyParts = [];
    
    if (context.testId) {
      keyParts.push(`test:${context.testId}`);
    }
    
    if (context.action) {
      keyParts.push(`action:${context.action}`);
    }
    
    if (context.element && context.element.tagName) {
      keyParts.push(`tag:${context.element.tagName}`);
    }
    
    return keyParts.length > 0 ? keyParts.join('|') : null;
  }
  
  /**
   * Optimize a selector based on history
   * @param {string} selector - The selector to optimize
   * @param {Object} context - Context information
   * @returns {Promise<{selector: string, confidence: number, optimized: boolean}>} - Optimized selector info
   */
  async optimizeSelector(selector, context = {}) {
    await this.initialize();
    
    if (!this.config.enabled) {
      return { selector, confidence: 1, optimized: false };
    }
    
    // Get selector history
    const history = this.getSelectorHistory(selector);
    
    // If not enough attempts, don't optimize
    if (history.attempts < this.config.optimizationThreshold) {
      return { selector, confidence: history.successRate || 0.5, optimized: false };
    }
    
    // If success rate is high, don't optimize
    if (history.successRate >= this.config.confidenceThreshold) {
      return { selector, confidence: history.successRate, optimized: false };
    }
    
    // Check if any alternatives have been used successfully
    if (history.alternativesUsed.length > 0) {
      let bestAlternative = null;
      let bestConfidence = 0;
      
      for (const alt of history.alternativesUsed) {
        const altHistory = this.getSelectorHistory(alt);
        const confidence = altHistory.successRate || 0;
        
        if (confidence > bestConfidence) {
          bestAlternative = alt;
          bestConfidence = confidence;
        }
      }
      
      if (bestAlternative && bestConfidence > history.successRate) {
        return {
          selector: bestAlternative,
          confidence: bestConfidence,
          optimized: true,
          original: selector
        };
      }
    }
    
    // Generate new alternatives
    const alternatives = await this.generateAlternatives(selector, context);
    
    // If no alternatives, return the original selector
    if (!alternatives || alternatives.length === 0) {
      return { selector, confidence: history.successRate || 0.5, optimized: false };
    }
    
    // Find the best alternative
    let bestAlternative = null;
    let bestConfidence = 0;
    
    for (const alt of alternatives) {
      const altHistory = this.getSelectorHistory(alt);
      const confidence = altHistory.successRate || 0.5;
      
      if (confidence > bestConfidence) {
        bestAlternative = alt;
        bestConfidence = confidence;
      }
    }
    
    // If no good alternative, return the original selector
    if (!bestAlternative || bestConfidence <= history.successRate) {
      return { selector, confidence: history.successRate || 0.5, optimized: false };
    }
    
    return {
      selector: bestAlternative,
      confidence: bestConfidence,
      optimized: true,
      original: selector
    };
  }
  
  /**
   * Clean up resources
   * @returns {Promise<void>}
   */
  async cleanup() {
    if (this.config.persistHistory) {
      await this.saveHistory();
    }
  }
}

module.exports = SelectorHealer;