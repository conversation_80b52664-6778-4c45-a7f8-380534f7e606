/**
 * Recovery Tests
 * 
 * This file contains tests for the recovery capabilities.
 */

const { test, expect } = require('@playwright/test');
const { selfHealing } = require('../../src');
const { createTestApp, createMetricsCollector, createReportGenerator } = require('../fixtures');

test.describe('Recovery Tests', () => {
  let testApp;
  let metricsCollector;
  let reportGenerator;
  
  test.beforeEach(async () => {
    testApp = await createTestApp();
    metricsCollector = createMetricsCollector();
    reportGenerator = createReportGenerator();
  });
  
  test.afterEach(async () => {
    await testApp.cleanup();
  });
  
  test('should recover from timeout errors', async () => {
    const testName = 'recover-timeout';
    const startTime = Date.now();
    
    // Create a self-healing controller
    const selfHealingController = selfHealing.createSelfHealingController({
      recovery: {
        enabled: true,
        maxAttempts: 3,
        strategies: ['retry', 'wait', 'selector', 'refresh', 'screenshot'],
        timeout: 5000,
        waitTime: 1000
      }
    });
    
    try {
      // Initialize the controller
      await selfHealingController.initialize();
      
      // Create a self-healing page
      const selfHealingPage = selfHealingController.createPage(testApp.page);
      
      // Navigate to the app
      await selfHealingPage.goto(testApp.baseUrl);
      
      // Create a timeout error
      const timeoutError = new Error('Timeout 30000ms exceeded');
      timeoutError.name = 'TimeoutError';
      
      // Try to recover from the timeout error
      const recoveryResult = await selfHealingController.recoveryManager.recoverFromFailure(timeoutError, {
        action: 'waitForSelector',
        selector: 'div[class*="FilesTable"]',
        page: testApp.page,
        args: ['div[class*="FilesTable"]', { timeout: 30000 }]
      });
      
      // Record recovery result
      metricsCollector.recordSelfHealingResult(testName, recoveryResult.success);
      
      // Record metrics
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      metricsCollector.recordExecutionTime(testName, duration);
      metricsCollector.recordTestResult(testName, true);
      
      // Generate report
      await reportGenerator.generateReport(metricsCollector.getMetrics(), 'recovery-tests');
    } finally {
      // Clean up resources
      await selfHealingController.cleanup();
    }
  });
  
  test('should recover from element not found errors', async () => {
    const testName = 'recover-element-not-found';
    const startTime = Date.now();
    
    // Create a self-healing controller
    const selfHealingController = selfHealing.createSelfHealingController({
      recovery: {
        enabled: true,
        maxAttempts: 3,
        strategies: ['retry', 'wait', 'selector', 'refresh', 'screenshot'],
        timeout: 5000,
        waitTime: 1000
      },
      selectorHealing: {
        enabled: true,
        maxAttempts: 3,
        strategies: ['css-relaxation', 'attribute-based', 'text-based', 'xpath'],
        persistHistory: false
      }
    });
    
    try {
      // Initialize the controller
      await selfHealingController.initialize();
      
      // Create a self-healing page
      const selfHealingPage = selfHealingController.createPage(testApp.page);
      
      // Navigate to the app
      await selfHealingPage.goto(testApp.baseUrl);
      
      // Register a working selector
      await selfHealingController.selectorHealer.trackSelectorResult('[data-test-id="SignInEmail"]', true, {
        action: 'fill',
        page: testApp.page
      });
      
      // Create an element not found error
      const elementNotFoundError = new Error('Element not found');
      elementNotFoundError.name = 'ElementNotFoundError';
      
      // Try to recover from the element not found error
      const recoveryResult = await selfHealingController.recoveryManager.recoverFromFailure(elementNotFoundError, {
        action: 'fill',
        selector: '[data-test-id="SignInEmail-not-found"]',
        page: testApp.page,
        args: ['[data-test-id="SignInEmail-not-found"]', '<EMAIL>'],
        selectorHealer: selfHealingController.selectorHealer
      });
      
      // Record recovery result
      metricsCollector.recordSelfHealingResult(testName, recoveryResult.success);
      
      // Record metrics
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      metricsCollector.recordExecutionTime(testName, duration);
      metricsCollector.recordTestResult(testName, true);
      
      // Generate report
      await reportGenerator.generateReport(metricsCollector.getMetrics(), 'recovery-tests');
    } finally {
      // Clean up resources
      await selfHealingController.cleanup();
    }
  });
  
  test('should recover from stale element reference errors', async () => {
    const testName = 'recover-stale-element';
    const startTime = Date.now();
    
    // Create a self-healing controller
    const selfHealingController = selfHealing.createSelfHealingController({
      recovery: {
        enabled: true,
        maxAttempts: 3,
        strategies: ['retry', 'wait', 'selector', 'refresh', 'screenshot'],
        timeout: 5000,
        waitTime: 1000
      }
    });
    
    try {
      // Initialize the controller
      await selfHealingController.initialize();
      
      // Create a self-healing page
      const selfHealingPage = selfHealingController.createPage(testApp.page);
      
      // Navigate to the app
      await selfHealingPage.goto(testApp.baseUrl);
      
      // Create a stale element reference error
      const staleElementError = new Error('stale element reference');
      staleElementError.name = 'StaleElementReferenceError';
      
      // Try to recover from the stale element reference error
      const recoveryResult = await selfHealingController.recoveryManager.recoverFromFailure(staleElementError, {
        action: 'click',
        selector: '[data-test-id="SignInEmail"]',
        page: testApp.page,
        args: ['[data-test-id="SignInEmail"]']
      });
      
      // Record recovery result
      metricsCollector.recordSelfHealingResult(testName, recoveryResult.success);
      
      // Record metrics
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      metricsCollector.recordExecutionTime(testName, duration);
      metricsCollector.recordTestResult(testName, true);
      
      // Generate report
      await reportGenerator.generateReport(metricsCollector.getMetrics(), 'recovery-tests');
    } finally {
      // Clean up resources
      await selfHealingController.cleanup();
    }
  });
});