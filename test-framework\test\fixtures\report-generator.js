/**
 * Test Report Generator
 * 
 * This module provides utilities for generating test reports.
 */

const fs = require('fs');
const path = require('path');

/**
 * Create a report generator
 * @param {Object} options - Options for the report generator
 * @returns {Object} - Report generator
 */
function createReportGenerator(options = {}) {
  const {
    outputDir = path.join(process.cwd(), 'reports'),
    format = 'markdown'
  } = options;
  
  return {
    /**
     * Generate a report from metrics
     * @param {Object} metrics - Test metrics
     * @param {string} [reportName='test-report'] - Report name
     * @returns {Promise<string>} - Report file path
     */
    async generateReport(metrics, reportName = 'test-report') {
      // Create output directory if it doesn't exist
      if (!fs.existsSync(outputDir)) {
        await fs.promises.mkdir(outputDir, { recursive: true });
      }
      
      // Generate report content
      let content = '';
      
      if (format === 'markdown') {
        content = this.generateMarkdownReport(metrics, reportName);
      } else if (format === 'json') {
        content = JSON.stringify(metrics, null, 2);
      } else {
        throw new Error(`Unsupported report format: ${format}`);
      }
      
      // Write report to file
      const timestamp = new Date().toISOString().replace(/:/g, '-');
      const fileName = `${reportName}-${timestamp}.${format === 'markdown' ? 'md' : 'json'}`;
      const filePath = path.join(outputDir, fileName);
      
      await fs.promises.writeFile(filePath, content, 'utf8');
      
      return filePath;
    },
    
    /**
     * Generate a markdown report
     * @param {Object} metrics - Test metrics
     * @param {string} reportName - Report name
     * @returns {string} - Markdown report
     */
    generateMarkdownReport(metrics, reportName) {
      let report = `# ${reportName}\n\n`;
      report += `Generated: ${new Date().toISOString()}\n\n`;
      
      // Summary
      report += '## Summary\n\n';
      
      const successRate = metrics.successRate.success / metrics.successRate.total;
      report += `- **Success Rate**: ${(successRate * 100).toFixed(2)}% (${metrics.successRate.success}/${metrics.successRate.total})\n`;
      
      if (metrics.selfHealing.total > 0) {
        const selfHealingRate = metrics.selfHealing.success / metrics.selfHealing.total;
        report += `- **Self-Healing Success Rate**: ${(selfHealingRate * 100).toFixed(2)}% (${metrics.selfHealing.success}/${metrics.selfHealing.total})\n`;
      }
      
      const avgExecutionTime = Object.values(metrics.executionTime.tests).reduce((a, b) => a + b, 0) / Object.keys(metrics.executionTime.tests).length;
      report += `- **Average Execution Time**: ${avgExecutionTime.toFixed(2)}ms\n`;
      
      if (Object.keys(metrics.tokenUsage.tests).length > 0) {
        const avgTokenUsage = metrics.tokenUsage.total / Object.keys(metrics.tokenUsage.tests).length;
        report += `- **Average Token Usage**: ${avgTokenUsage.toFixed(2)}\n`;
      }
      
      report += '\n';
      
      // Execution Time
      report += '## Execution Time\n\n';
      report += '| Test | Duration (ms) |\n';
      report += '|------|---------------|\n';
      
      for (const [testName, duration] of Object.entries(metrics.executionTime.tests)) {
        report += `| ${testName} | ${duration.toFixed(2)} |\n`;
      }
      
      report += '\n';
      
      // Success Rate
      report += '## Success Rate\n\n';
      report += '| Test | Result |\n';
      report += '|------|--------|\n';
      
      for (const [testName, success] of Object.entries(metrics.successRate.tests)) {
        report += `| ${testName} | ${success ? '✅ Pass' : '❌ Fail'} |\n`;
      }
      
      report += '\n';
      
      // Self-Healing
      if (metrics.selfHealing.total > 0) {
        report += '## Self-Healing\n\n';
        report += '| Test | Success | Failure | Success Rate |\n';
        report += '|------|---------|---------|-------------|\n';
        
        for (const [testName, data] of Object.entries(metrics.selfHealing.tests)) {
          const successRate = data.success / data.total;
          report += `| ${testName} | ${data.success} | ${data.failure} | ${(successRate * 100).toFixed(2)}% |\n`;
        }
        
        report += '\n';
      }
      
      // Token Usage
      if (Object.keys(metrics.tokenUsage.tests).length > 0) {
        report += '## Token Usage\n\n';
        report += '| Test | Tokens |\n';
        report += '|------|--------|\n';
        
        for (const [testName, tokens] of Object.entries(metrics.tokenUsage.tests)) {
          report += `| ${testName} | ${tokens} |\n`;
        }
        
        report += '\n';
      }
      
      // Resource Usage
      if (Object.keys(metrics.resourceUsage.cpu.tests).length > 0) {
        report += '## Resource Usage\n\n';
        report += '| Test | CPU (%) | Memory (MB) |\n';
        report += '|------|---------|-------------|\n';
        
        for (const testName of Object.keys(metrics.resourceUsage.cpu.tests)) {
          const cpu = metrics.resourceUsage.cpu.tests[testName];
          const memory = metrics.resourceUsage.memory.tests[testName];
          report += `| ${testName} | ${cpu.toFixed(2)} | ${memory.toFixed(2)} |\n`;
        }
        
        report += '\n';
      }
      
      return report;
    }
  };
}

module.exports = {
  createReportGenerator
};