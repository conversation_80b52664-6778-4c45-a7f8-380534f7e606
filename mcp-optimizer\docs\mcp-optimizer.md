# MCP Optimizer

The MCP Optimizer is a package that provides optimization and orchestration for Machine-Controlled Programs (MCPs) in the QA Wolf testing framework.

## Installation

```bash
npm install @qawolf/mcp-optimizer
```

## Usage

### API

```javascript
const mcpOptimizer = require('@qawolf/mcp-optimizer');

// Optimize selectors
const optimizationResult = await mcpOptimizer.optimizeSelectors({
  selectors: ['button.login-button', 'input[name="username"]'],
  options: {
    generateFallbacks: true,
    prioritizeTestIds: true
  }
});

// Select the appropriate MCP tool for a task
const toolSelection = await mcpOptimizer.selectMcpTool({
  type: 'browser_interaction',
  subtype: 'click'
});

// Generate a report
const report = await mcpOptimizer.generateReport(optimizationResult, {
  format: 'markdown',
  includeTimestamps: true,
  includeTokenUsage: true
});
```

### CLI

The MCP Optimizer also provides a CLI for running optimizations and explaining decisions.

```bash
# Run the optimizer on a test
npx mcp-optimize run --test tests/login.spec.js --report

# Explain optimization decisions
npx mcp-optimize explain --task browser_interaction --subtype click
```

## API Reference

### Core Modules

#### Analyzer

- `analyzeScreenshot(input)` - Analyze a screenshot to identify UI elements
- `analyzePageContent(input)` - Analyze page content to identify potential issues
- `analyzeTestFailure(input)` - Analyze test failure to identify potential causes

#### Optimizer

- `optimizeSelectors(config)` - Optimize selectors for better reliability
- `optimizeTestCode(config)` - Optimize test code for better reliability and performance
- `optimizeMcpToolUsage(config)` - Optimize MCP tool usage for better token efficiency

#### Selector

- `selectMcpTool(task, options)` - Select the appropriate MCP tool for a given task
- `getMcpToolConfig(tool, options)` - Get configuration for a specific MCP tool

### Reporting Modules

#### Reporter

- `generateReport(results, options)` - Generate a report on MCP usage
- `generateTokenUsageReport(usage, options)` - Generate a token usage report
- `generateOptimizationReport(optimization, options)` - Generate an optimization report

### Tool Integrations

#### Playwright MCP

- `startPlaywrightMCP(options)` - Start the Playwright MCP server
- `isPlaywrightMCPRunning(port)` - Check if Playwright MCP server is running
- `generatePlaywrightTest(qaWolfTestPath, options)` - Generate a Playwright test from a QA Wolf test
- `generateOptimizedSelector(selector, pageContext, options)` - Generate an optimized selector

#### Browser Tools

- `takeScreenshot(options)` - Take a screenshot using Browser Tools MCP
- `getConsoleLogs()` - Get console logs using Browser Tools MCP
- `getNetworkErrors()` - Get network errors using Browser Tools MCP
- `runAccessibilityAudit(options)` - Run an accessibility audit using Browser Tools MCP
- `runPerformanceAudit(options)` - Run a performance audit using Browser Tools MCP
- `runDebuggerMode()` - Run debugger mode using Browser Tools MCP

#### Desktop Commander

- `readFile(options)` - Read a file using Desktop Commander MCP
- `readMultipleFiles(options)` - Read multiple files using Desktop Commander MCP
- `writeFile(options)` - Write a file using Desktop Commander MCP
- `editBlock(options)` - Edit a block of code using Desktop Commander MCP
- `searchCode(options)` - Search for code patterns using Desktop Commander MCP

#### Quillopy

- `searchDocumentation(options)` - Search for documentation using Quillopy MCP
- `chatWithCode(options)` - Chat with code using Quillopy MCP
- `getApiDocumentation(options)` - Get documentation for a specific API

#### Sequential Thinking

- `solveWithSequentialThinking(options)` - Solve a problem using Sequential Thinking MCP
- `analyzeTestFailure(options)` - Analyze a test failure using Sequential Thinking MCP
- `planTestStrategy(options)` - Plan a test strategy using Sequential Thinking MCP

## CLI Reference

### Commands

- `run` - Run the optimizer on a test or set of tests
- `explain` - Explain the optimization decisions made
- `help` - Show help message
- `version` - Show version information

### Run Options

- `--test <path>` - Path to the test file
- `--dir <path>` - Path to the directory containing test files
- `--selectors <list>` - Comma-separated list of selectors to optimize
- `--fallbacks` - Generate fallback selectors (default: true)
- `--testIds` - Prioritize test IDs (default: true)
- `--report` - Generate a report (default: false)
- `--format <format>` - Report format (markdown, json, html)
- `--timestamps` - Include timestamps in the report (default: true)
- `--tokens` - Include token usage in the report (default: true)
- `--startMcp` - Start Playwright MCP (default: false)
- `--port <port>` - Port for Playwright MCP (default: 8932)
- `--headless` - Run Playwright MCP in headless mode (default: false)

### Explain Options

- `--task <type>` - Task type (e.g., browser_interaction, file_operation)
- `--subtype <type>` - Task subtype (e.g., click, type, read)
- `--tokenOptimized` - Optimize for token usage (default: true)
- `--performanceOptimized` - Optimize for performance (default: false)