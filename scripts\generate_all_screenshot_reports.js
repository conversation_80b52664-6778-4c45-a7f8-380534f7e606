/**
 * Generate Screenshot Reports for All Tests
 * 
 * This script generates HTML reports for all tests that have screenshots.
 * It scans the screenshots directory and generates a report for each test.
 * 
 * Usage:
 * node scripts/generate_all_screenshot_reports.js [--date=YYYY-MM-DD]
 * 
 * @module generate_all_screenshot_reports
 * <AUTHOR> Wolf Team
 * @date 2025-01-01
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const { format } = require('date-fns');
const { SCREENSHOTS_BASE_DIR } = require('../src/utils/screenshot-utils');

// Parse command line arguments
const args = process.argv.slice(2);
let date = null;

args.forEach(arg => {
  if (arg.startsWith('--date=')) {
    date = arg.substring('--date='.length);
  }
});

// If no date is specified, use today's date
if (!date) {
  date = format(new Date(), 'yyyy-MM-dd');
}

/**
 * Generate reports for all tests
 * 
 * @param {string} date - Date in YYYY-MM-DD format
 * @returns {Promise<void>}
 */
async function generateAllReports(date) {
  try {
    console.log(`Generating reports for all tests on ${date}...`);
    
    // Check if the date directory exists
    const dateDir = path.join(SCREENSHOTS_BASE_DIR, date);
    if (!fs.existsSync(dateDir)) {
      console.error(`Error: No screenshots found for date ${date}`);
      process.exit(1);
    }
    
    // Get all run directories for this date
    const runDirs = fs.readdirSync(dateDir)
      .filter(dir => dir.startsWith('run-'))
      .map(dir => path.join(dateDir, dir));
    
    if (runDirs.length === 0) {
      console.error(`Error: No runs found for date ${date}`);
      process.exit(1);
    }
    
    console.log(`Found ${runDirs.length} runs for date ${date}`);
    
    // Get all test directories across all runs
    const testDirs = [];
    runDirs.forEach(runDir => {
      const dirs = fs.readdirSync(runDir)
        .filter(dir => fs.statSync(path.join(runDir, dir)).isDirectory())
        .map(dir => ({
          path: path.join(runDir, dir),
          name: dir,
          runId: path.basename(runDir)
        }));
      
      testDirs.push(...dirs);
    });
    
    if (testDirs.length === 0) {
      console.error(`Error: No tests found for date ${date}`);
      process.exit(1);
    }
    
    console.log(`Found ${testDirs.length} test directories`);
    
    // Group test directories by test name
    const testsByName = {};
    testDirs.forEach(testDir => {
      // Extract the test name from the directory name
      // Format: test_name or ticket_id_test_name
      const parts = testDir.name.split('_');
      let testName = testDir.name;
      let ticketId = null;
      
      // If the directory name starts with a ticket ID (e.g., QW-123_test_name)
      if (parts.length > 1 && parts[0].includes('-')) {
        ticketId = parts[0];
        testName = parts.slice(1).join('_');
      }
      
      if (!testsByName[testName]) {
        testsByName[testName] = [];
      }
      
      testsByName[testName].push({
        ...testDir,
        ticketId
      });
    });
    
    console.log(`Found ${Object.keys(testsByName).length} unique tests`);
    
    // Generate a report for each test
    for (const [testName, testDirs] of Object.entries(testsByName)) {
      console.log(`Generating report for test "${testName}"...`);
      
      // Generate a report for each run of the test
      for (const testDir of testDirs) {
        const runId = testDir.runId;
        const ticketId = testDir.ticketId;
        
        // Build the command to generate the report
        let command = `node scripts/generate_screenshot_report.js --test=${testName} --date=${date} --run=${runId}`;
        if (ticketId) {
          command += ` --ticket=${ticketId}`;
        }
        
        // Execute the command
        try {
          console.log(`Executing: ${command}`);
          execSync(command, { stdio: 'inherit' });
        } catch (error) {
          console.error(`Error generating report for test "${testName}" with run ID "${runId}":`, error.message);
        }
      }
      
      // Generate a combined report for all runs of the test
      try {
        let command = `node scripts/generate_screenshot_report.js --test=${testName} --date=${date}`;
        if (testDirs[0].ticketId) {
          command += ` --ticket=${testDirs[0].ticketId}`;
        }
        
        console.log(`Executing: ${command}`);
        execSync(command, { stdio: 'inherit' });
      } catch (error) {
        console.error(`Error generating combined report for test "${testName}":`, error.message);
      }
    }
    
    console.log('All reports generated successfully!');
    
    // Generate an index.html file that links to all reports
    generateIndexFile(date, testsByName);
  } catch (error) {
    console.error('Error generating reports:', error);
    process.exit(1);
  }
}

/**
 * Generate an index.html file that links to all reports
 * 
 * @param {string} date - Date in YYYY-MM-DD format
 * @param {Object} testsByName - Tests grouped by name
 */
function generateIndexFile(date, testsByName) {
  try {
    console.log('Generating index.html file...');
    
    // Create reports directory if it doesn't exist
    const reportsDir = path.join(process.cwd(), 'reports', 'screenshots');
    if (!fs.existsSync(reportsDir)) {
      fs.mkdirSync(reportsDir, { recursive: true });
    }
    
    // Generate the HTML content
    let htmlContent = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Screenshot Reports - ${date}</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }
    h1, h2 {
      color: #2c3e50;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
      background-color: white;
      box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
      border-radius: 4px;
    }
    .test-list {
      list-style-type: none;
      padding: 0;
    }
    .test-item {
      margin-bottom: 20px;
      padding: 15px;
      background-color: #f9f9f9;
      border-left: 4px solid #3498db;
      border-radius: 4px;
    }
    .test-name {
      font-size: 1.2em;
      font-weight: bold;
      margin-bottom: 10px;
    }
    .run-list {
      list-style-type: none;
      padding: 0;
      margin-left: 20px;
    }
    .run-item {
      margin-bottom: 5px;
    }
    a {
      color: #3498db;
      text-decoration: none;
    }
    a:hover {
      text-decoration: underline;
    }
    .timestamp {
      color: #666;
      font-size: 0.9em;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Screenshot Reports - ${date}</h1>
    <p>Generated on ${new Date().toLocaleString()}</p>
    
    <h2>Tests</h2>
    <ul class="test-list">
`;
    
    // Add links to all reports
    Object.entries(testsByName).forEach(([testName, testDirs]) => {
      const ticketId = testDirs[0].ticketId;
      const testDirName = ticketId ? `${ticketId}_${testName}` : testName;
      const combinedReportFilename = `${date}_${testDirName}_screenshots.html`;
      
      htmlContent += `
      <li class="test-item">
        <div class="test-name">${testName} ${ticketId ? `(${ticketId})` : ''}</div>
        <a href="${combinedReportFilename}">View Combined Report</a>
        
        <h3>Runs</h3>
        <ul class="run-list">
`;
      
      // Add links to individual run reports
      testDirs.forEach(testDir => {
        const runId = testDir.runId;
        const runReportFilename = `${date}_${testDirName}_${runId}_screenshots.html`;
        
        htmlContent += `
          <li class="run-item">
            <a href="${runReportFilename}">Run ${runId.replace('run-', '')}</a>
          </li>
`;
      });
      
      htmlContent += `
        </ul>
      </li>
`;
    });
    
    htmlContent += `
    </ul>
  </div>
</body>
</html>
`;
    
    // Write the HTML content to a file
    const indexPath = path.join(reportsDir, `index_${date}.html`);
    fs.writeFileSync(indexPath, htmlContent);
    
    console.log(`Index file generated: ${indexPath}`);
  } catch (error) {
    console.error('Error generating index file:', error);
  }
}

// Generate reports for all tests
generateAllReports(date);
