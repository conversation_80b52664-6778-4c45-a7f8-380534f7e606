/**
 * Performance analyzer for performance tracking
 */

import { PerformanceMetrics, PerformanceAnalyzerOptions, OperationType } from './types';
import { EventBus, EventType } from '@qawolf/core';

/**
 * Performance analysis result
 */
export interface PerformanceAnalysisResult {
  /**
   * Whether the performance is acceptable
   */
  acceptable: boolean;
  
  /**
   * Issues found
   */
  issues: {
    /**
     * Issue type
     */
    type: string;
    
    /**
     * Issue message
     */
    message: string;
    
    /**
     * Issue severity
     */
    severity: 'info' | 'warning' | 'error';
    
    /**
     * Issue details
     */
    details?: any;
  }[];
  
  /**
   * Recommendations
   */
  recommendations: string[];
}

/**
 * Performance analyzer
 */
export class PerformanceAnalyzer {
  /**
   * Thresholds for performance metrics
   */
  private thresholds: {
    /**
     * Maximum test duration in milliseconds
     */
    maxTestDuration: number;
    
    /**
     * Maximum operation durations by type in milliseconds
     */
    maxOperationDurations: {
      [type: string]: number;
    };
    
    /**
     * Maximum resource usage
     */
    maxResourceUsage: {
      /**
       * Maximum memory usage
       */
      memory: {
        /**
         * Maximum Resident Set Size
         */
        rss: number;
        
        /**
         * Maximum heap total
         */
        heapTotal: number;
        
        /**
         * Maximum heap used
         */
        heapUsed: number;
      };
    };
  };
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options Performance analyzer options
   */
  constructor(options: PerformanceAnalyzerOptions = {}) {
    this.thresholds = {
      maxTestDuration: options.thresholds?.maxTestDuration || 60000, // 60 seconds
      maxOperationDurations: {
        [OperationType.NAVIGATION]: options.thresholds?.maxOperationDurations?.[OperationType.NAVIGATION] || 5000, // 5 seconds
        [OperationType.CLICK]: options.thresholds?.maxOperationDurations?.[OperationType.CLICK] || 1000, // 1 second
        [OperationType.FILL]: options.thresholds?.maxOperationDurations?.[OperationType.FILL] || 1000, // 1 second
        [OperationType.SELECT]: options.thresholds?.maxOperationDurations?.[OperationType.SELECT] || 1000, // 1 second
        [OperationType.HOVER]: options.thresholds?.maxOperationDurations?.[OperationType.HOVER] || 1000, // 1 second
        [OperationType.WAIT]: options.thresholds?.maxOperationDurations?.[OperationType.WAIT] || 5000, // 5 seconds
        [OperationType.SCREENSHOT]: options.thresholds?.maxOperationDurations?.[OperationType.SCREENSHOT] || 1000, // 1 second
        [OperationType.ASSERTION]: options.thresholds?.maxOperationDurations?.[OperationType.ASSERTION] || 1000, // 1 second
        [OperationType.CUSTOM]: options.thresholds?.maxOperationDurations?.[OperationType.CUSTOM] || 5000, // 5 seconds
        ...options.thresholds?.maxOperationDurations
      },
      maxResourceUsage: {
        memory: {
          rss: options.thresholds?.maxResourceUsage?.memory?.rss || 500 * 1024 * 1024, // 500 MB
          heapTotal: options.thresholds?.maxResourceUsage?.memory?.heapTotal || 200 * 1024 * 1024, // 200 MB
          heapUsed: options.thresholds?.maxResourceUsage?.memory?.heapUsed || 150 * 1024 * 1024 // 150 MB
        }
      }
    };
    
    this.eventBus = EventBus.getInstance();
  }
  
  /**
   * Analyze performance metrics
   * @param metrics Performance metrics
   * @returns Performance analysis result
   */
  analyze(metrics: PerformanceMetrics): PerformanceAnalysisResult {
    const issues: PerformanceAnalysisResult['issues'] = [];
    const recommendations: string[] = [];
    
    // Check test duration
    if (metrics.duration > this.thresholds.maxTestDuration) {
      issues.push({
        type: 'test-duration',
        message: `Test duration (${metrics.duration}ms) exceeds maximum threshold (${this.thresholds.maxTestDuration}ms)`,
        severity: 'warning'
      });
      
      recommendations.push('Consider optimizing the test to reduce its duration');
    }
    
    // Check operation durations
    for (const operation of metrics.operations) {
      const maxDuration = this.thresholds.maxOperationDurations[operation.type] || this.thresholds.maxOperationDurations[OperationType.CUSTOM];
      
      if (operation.duration > maxDuration) {
        issues.push({
          type: 'operation-duration',
          message: `Operation "${operation.name}" of type "${operation.type}" duration (${operation.duration}ms) exceeds maximum threshold (${maxDuration}ms)`,
          severity: 'warning',
          details: operation
        });
        
        recommendations.push(`Investigate why operation "${operation.name}" is taking longer than expected`);
      }
    }
    
    // Check resource usage
    if (metrics.resourceStats.memory.maxRss > this.thresholds.maxResourceUsage.memory.rss) {
      issues.push({
        type: 'memory-usage',
        message: `Maximum RSS (${metrics.resourceStats.memory.maxRss} bytes) exceeds maximum threshold (${this.thresholds.maxResourceUsage.memory.rss} bytes)`,
        severity: 'warning'
      });
      
      recommendations.push('Check for memory leaks in the test');
    }
    
    if (metrics.resourceStats.memory.maxHeapTotal > this.thresholds.maxResourceUsage.memory.heapTotal) {
      issues.push({
        type: 'memory-usage',
        message: `Maximum heap total (${metrics.resourceStats.memory.maxHeapTotal} bytes) exceeds maximum threshold (${this.thresholds.maxResourceUsage.memory.heapTotal} bytes)`,
        severity: 'warning'
      });
      
      recommendations.push('Check for memory leaks in the test');
    }
    
    if (metrics.resourceStats.memory.maxHeapUsed > this.thresholds.maxResourceUsage.memory.heapUsed) {
      issues.push({
        type: 'memory-usage',
        message: `Maximum heap used (${metrics.resourceStats.memory.maxHeapUsed} bytes) exceeds maximum threshold (${this.thresholds.maxResourceUsage.memory.heapUsed} bytes)`,
        severity: 'warning'
      });
      
      recommendations.push('Check for memory leaks in the test');
    }
    
    // Check for slow operations
    const slowOperations = metrics.operations
      .filter(operation => operation.duration > 1000) // Operations taking more than 1 second
      .sort((a, b) => b.duration - a.duration); // Sort by duration (descending)
    
    if (slowOperations.length > 0) {
      issues.push({
        type: 'slow-operations',
        message: `Found ${slowOperations.length} slow operations (taking more than 1 second)`,
        severity: 'info',
        details: slowOperations
      });
      
      recommendations.push('Optimize the slowest operations to improve overall test performance');
    }
    
    // Check for operation type distribution
    const operationTypeDistribution = Object.entries(metrics.operationStats.byType)
      .map(([type, stats]) => ({ type, count: stats.count, percentage: (stats.count / metrics.operationStats.count) * 100 }))
      .sort((a, b) => b.count - a.count);
    
    if (operationTypeDistribution.length > 0 && operationTypeDistribution[0].percentage > 50) {
      issues.push({
        type: 'operation-type-distribution',
        message: `Operation type "${operationTypeDistribution[0].type}" accounts for ${operationTypeDistribution[0].percentage.toFixed(2)}% of all operations`,
        severity: 'info',
        details: operationTypeDistribution
      });
      
      recommendations.push(`Consider if the high number of "${operationTypeDistribution[0].type}" operations is necessary`);
    }
    
    // Determine if performance is acceptable
    const acceptable = !issues.some(issue => issue.severity === 'error');
    
    // Create analysis result
    const result: PerformanceAnalysisResult = {
      acceptable,
      issues,
      recommendations: [...new Set(recommendations)] // Remove duplicates
    };
    
    // Emit event
    this.eventBus.emit(EventType.PERFORMANCE_ANALYSIS_COMPLETED, {
      metrics,
      result
    });
    
    return result;
  }
  
  /**
   * Get thresholds
   * @returns Thresholds
   */
  getThresholds(): typeof this.thresholds {
    return { ...this.thresholds };
  }
  
  /**
   * Set thresholds
   * @param thresholds Thresholds
   * @returns This instance for chaining
   */
  setThresholds(thresholds: Partial<typeof this.thresholds>): PerformanceAnalyzer {
    this.thresholds = {
      ...this.thresholds,
      ...thresholds,
      maxOperationDurations: {
        ...this.thresholds.maxOperationDurations,
        ...thresholds.maxOperationDurations
      },
      maxResourceUsage: {
        memory: {
          ...this.thresholds.maxResourceUsage.memory,
          ...thresholds.maxResourceUsage?.memory
        }
      }
    };
    
    return this;
  }
}

/**
 * Create performance analyzer
 * @param options Performance analyzer options
 * @returns Performance analyzer
 */
export function createPerformanceAnalyzer(options: PerformanceAnalyzerOptions = {}): PerformanceAnalyzer {
  return new PerformanceAnalyzer(options);
}
