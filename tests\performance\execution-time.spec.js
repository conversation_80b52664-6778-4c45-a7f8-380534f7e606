/**
 * Execution Time Performance Tests
 * 
 * This file contains tests for measuring execution time performance.
 * It validates that operations complete within acceptable time limits.
 */

const { test, expect, login, navigateToFilesPage, createFile, deleteFile } = require('../utils/test-helpers');
const { PerformanceTracker } = require('../utils/performance-tracker');
const { getConfig } = require('../config/test.config');
const { createMcpController, createSelfHealingController } = require('@qawolf/test-framework');

// Test configuration
const config = getConfig();

test.describe('Execution Time Performance', () => {
  /**
   * Test: Login performance
   * Purpose: Measure the execution time of the login operation
   * Input: Valid credentials
   * Expected: Login completes within acceptable time limit
   */
  test('should measure login performance', async ({ page }) => {
    // Performance tracking
    const performanceTracker = new PerformanceTracker({
      thresholds: config.performance.thresholds
    }).start();
    
    try {
      // ARRANGE: Set up the test environment
      const email = config.auth.email;
      const password = config.auth.password;
      const url = config.baseUrl;
      
      // Take a screenshot before login
      await page.takeScreenshot({
        action: 'before-login-performance',
        description: 'Before login performance test'
      });
      
      // ACT: Measure login performance
      const loginStartTime = Date.now();
      
      // Navigate to the login page
      await page.goto(url);
      
      // Fill in login credentials and submit
      await page.fill('[data-test-id="SignInEmail"]', email);
      await page.fill('[data-test-id="SignInPassword"]', password);
      await page.locator(':text("Log in with email")').click();
      
      // Wait for navigation to complete
      await page.waitForNavigation();
      
      const loginEndTime = Date.now();
      const loginDuration = loginEndTime - loginStartTime;
      
      // Track the operation
      performanceTracker.trackOperation({
        name: 'Login',
        type: 'performance',
        duration: loginDuration
      });
      
      // Take a screenshot after login
      await page.takeScreenshot({
        action: 'after-login-performance',
        description: 'After login performance test'
      });
      
      // ASSERT: Verify login performance
      console.log(`Login duration: ${loginDuration}ms`);
      expect(loginDuration).toBeLessThan(config.performance.thresholds.executionTime);
      
      // Verify successful login
      await expect(page).not.toHaveURL(/login/);
      await expect(page.locator('div[class*="FilesTable__Wrapper"]')).toBeVisible();
    } catch (error) {
      // Take a screenshot on error
      await page.takeErrorScreenshot({
        error
      });
      
      throw error;
    } finally {
      // Stop performance tracking
      const metrics = performanceTracker.stop();
      console.log('Performance metrics:', JSON.stringify(metrics, null, 2));
    }
  });
  
  /**
   * Test: File creation performance
   * Purpose: Measure the execution time of file creation with and without MCP optimization
   * Input: None
   * Expected: File creation with MCP optimization is faster than without
   */
  test('should measure file creation performance with and without MCP optimization', async ({ page }) => {
    // Performance tracking
    const performanceTracker = new PerformanceTracker({
      thresholds: config.performance.thresholds
    }).start();
    
    try {
      // ARRANGE: Set up the test environment
      // Log in
      await login(page);
      
      // Navigate to the files page
      await navigateToFilesPage(page);
      
      // Take a screenshot before file creation
      await page.takeScreenshot({
        action: 'before-file-creation-performance',
        description: 'Before file creation performance test'
      });
      
      // ACT: Measure file creation performance without MCP optimization
      const withoutMcpStartTime = Date.now();
      
      // Create a file without MCP optimization
      const fileNameWithoutMcp = await createFile(page);
      
      const withoutMcpEndTime = Date.now();
      const withoutMcpDuration = withoutMcpEndTime - withoutMcpStartTime;
      
      // Track the operation
      performanceTracker.trackOperation({
        name: 'File Creation Without MCP',
        type: 'performance',
        duration: withoutMcpDuration
      });
      
      // Navigate back to the main menu
      try {
        await page.click('button[aria-label="Back"], a[href="/"]');
        await page.waitForTimeout(1000);
      } catch {
        // If that fails, try direct navigation
        await page.goto(config.baseUrl);
      }
      
      // Wait for the files table to appear
      await page.waitForSelector('div[class*="FilesTable"]', { timeout: 5000 });
      
      // Create MCP controller
      const mcpController = createMcpController({
        autoStartPlaywrightMcp: config.mcp.autoStartPlaywrightMcp,
        generateFallbacks: config.mcp.generateFallbacks,
        prioritizeTestIds: config.mcp.prioritizeTestIds
      });
      
      // Initialize the controller
      await mcpController.initialize();
      
      // Optimize selectors for file creation
      const selectors = [
        'div[class*="pages__NewFileButton"]',
        'span[class*="FileTitle"]',
        'div[class*="styled_FileName"]',
        'button[aria-label="Back"]',
        'a[href="/"]'
      ];
      
      await mcpController.optimizeSelectors(selectors);
      
      // Measure file creation performance with MCP optimization
      const withMcpStartTime = Date.now();
      
      // Create a file with MCP optimization
      const fileNameWithMcp = await createFile(page);
      
      const withMcpEndTime = Date.now();
      const withMcpDuration = withMcpEndTime - withMcpStartTime;
      
      // Track the operation
      performanceTracker.trackOperation({
        name: 'File Creation With MCP',
        type: 'performance',
        duration: withMcpDuration
      });
      
      // Take a screenshot after file creation
      await page.takeScreenshot({
        action: 'after-file-creation-performance',
        description: 'After file creation performance test'
      });
      
      // ASSERT: Verify file creation performance
      console.log(`File creation without MCP duration: ${withoutMcpDuration}ms`);
      console.log(`File creation with MCP duration: ${withMcpDuration}ms`);
      
      // Clean up: Delete the files we created
      await deleteFile(page, { fileName: fileNameWithoutMcp });
      await deleteFile(page, { fileName: fileNameWithMcp });
      
      // Clean up MCP controller
      await mcpController.cleanup();
    } catch (error) {
      // Take a screenshot on error
      await page.takeErrorScreenshot({
        error
      });
      
      throw error;
    } finally {
      // Stop performance tracking
      const metrics = performanceTracker.stop();
      console.log('Performance metrics:', JSON.stringify(metrics, null, 2));
    }
  });
  
  /**
   * Test: Self-healing performance
   * Purpose: Measure the execution time of operations with and without self-healing
   * Input: None
   * Expected: Operations with self-healing complete within acceptable time limits
   */
  test('should measure self-healing performance', async ({ page }) => {
    // Performance tracking
    const performanceTracker = new PerformanceTracker({
      thresholds: config.performance.thresholds
    }).start();
    
    try {
      // ARRANGE: Set up the test environment
      const url = config.baseUrl;
      
      // Take a screenshot before self-healing performance test
      await page.takeScreenshot({
        action: 'before-self-healing-performance',
        description: 'Before self-healing performance test'
      });
      
      // ACT: Measure performance without self-healing
      const withoutSelfHealingStartTime = Date.now();
      
      // Navigate to the login page
      await page.goto(url);
      
      // Fill in login credentials and submit
      await page.fill('[data-test-id="SignInEmail"]', config.auth.email);
      await page.fill('[data-test-id="SignInPassword"]', config.auth.password);
      await page.locator(':text("Log in with email")').click();
      
      // Wait for navigation to complete
      await page.waitForNavigation();
      
      const withoutSelfHealingEndTime = Date.now();
      const withoutSelfHealingDuration = withoutSelfHealingEndTime - withoutSelfHealingStartTime;
      
      // Track the operation
      performanceTracker.trackOperation({
        name: 'Login Without Self-Healing',
        type: 'performance',
        duration: withoutSelfHealingDuration
      });
      
      // Log out
      // Find and click the user menu
      try {
        await page.click('button[aria-label="User menu"]');
      } catch {
        try {
          await page.click('div[class*="UserMenu"]');
        } catch {
          // If we can't find a specific user menu, look for any button that might be a user menu
          const buttons = await page.locator('button').all();
          
          // Click the last button, which is often the user menu
          if (buttons.length > 0) {
            await buttons[buttons.length - 1].click();
          }
        }
      }
      
      // Wait for the menu to appear
      await page.waitForTimeout(1000);
      
      // Click the logout option
      try {
        await page.click('text=Log out');
      } catch {
        try {
          await page.click('a:has-text("Log out")');
        } catch {
          // If we can't find a specific logout option, look for any element that might be a logout option
          await page.click('text=/logout|sign out/i');
        }
      }
      
      // Wait for navigation to complete
      await page.waitForNavigation();
      
      // Create self-healing controller
      const selfHealingController = createSelfHealingController({
        enabled: true,
        selectorHealing: {
          enabled: true,
          maxAttempts: 3
        },
        recovery: {
          enabled: true,
          maxAttempts: 3
        },
        feedbackCollection: {
          enabled: true,
          collectScreenshots: true
        }
      });
      
      // Initialize the controller
      await selfHealingController.initialize();
      
      // Start test run
      await selfHealingController.startTest({
        name: 'Self-Healing Performance Test',
        file: 'execution-time.spec.js',
        project: 'performance'
      });
      
      // Create self-healing page
      const selfHealingPage = selfHealingController.createPage(page);
      
      // Measure performance with self-healing
      const withSelfHealingStartTime = Date.now();
      
      // Navigate to the login page
      await selfHealingPage.goto(url);
      
      // Fill in login credentials and submit
      await selfHealingPage.fill('[data-test-id="SignInEmail"]', config.auth.email);
      await selfHealingPage.fill('[data-test-id="SignInPassword"]', config.auth.password);
      await selfHealingPage.locator(':text("Log in with email")').click();
      
      // Wait for navigation to complete
      await selfHealingPage.waitForNavigation();
      
      const withSelfHealingEndTime = Date.now();
      const withSelfHealingDuration = withSelfHealingEndTime - withSelfHealingStartTime;
      
      // Track the operation
      performanceTracker.trackOperation({
        name: 'Login With Self-Healing',
        type: 'performance',
        duration: withSelfHealingDuration
      });
      
      // Take a screenshot after self-healing performance test
      await page.takeScreenshot({
        action: 'after-self-healing-performance',
        description: 'After self-healing performance test'
      });
      
      // ASSERT: Verify self-healing performance
      console.log(`Login without self-healing duration: ${withoutSelfHealingDuration}ms`);
      console.log(`Login with self-healing duration: ${withSelfHealingDuration}ms`);
      
      // End test run
      await selfHealingController.endTest({
        name: 'Self-Healing Performance Test',
        status: 'passed',
        duration: performanceTracker.getMetrics().executionTime.duration
      });
      
      // Clean up
      await selfHealingController.cleanup();
    } catch (error) {
      // Take a screenshot on error
      await page.takeErrorScreenshot({
        error
      });
      
      throw error;
    } finally {
      // Stop performance tracking
      const metrics = performanceTracker.stop();
      console.log('Performance metrics:', JSON.stringify(metrics, null, 2));
    }
  });
});
