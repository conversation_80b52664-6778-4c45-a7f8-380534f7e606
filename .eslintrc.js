/**
 * ESLint Configuration for QA Wolf Metrics Framework
 * 
 * This configuration includes custom rules to enforce best practices
 * for screenshot management and other QA Wolf-specific patterns.
 * 
 * @module .eslintrc.js
 * <AUTHOR> Wolf Team
 * @date 2025-01-01
 */

module.exports = {
  env: {
    node: true,
    es2021: true,
    jest: true,
  },
  extends: [
    'eslint:recommended',
  ],
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module',
  },
  rules: {
    // Custom rules
    'no-direct-screenshot': 'error',
    
    // Standard rules
    'no-unused-vars': ['warn', { 
      'argsIgnorePattern': '^_',
      'varsIgnorePattern': '^_' 
    }],
    'no-console': 'off',
    'no-debugger': 'warn',
    'no-alert': 'warn',
    'no-constant-condition': 'warn',
    'no-empty': 'warn',
    'no-extra-boolean-cast': 'warn',
    'no-fallthrough': 'warn',
    'no-irregular-whitespace': 'warn',
    'no-mixed-spaces-and-tabs': 'warn',
    'no-multiple-empty-lines': ['warn', { 'max': 2 }],
    'no-trailing-spaces': 'warn',
    'no-undef': 'error',
    'no-unreachable': 'warn',
    'no-var': 'warn',
    'prefer-const': 'warn',
    'semi': ['warn', 'always'],
    'quotes': ['warn', 'single', { 'avoidEscape': true }],
  },
  // Load custom rules
  plugins: [],
  // Use require to load custom rules
  overrides: [
    {
      files: ['**/*.js'],
      rules: {
        'no-direct-screenshot': require('./eslint-rules/no-direct-screenshot'),
      },
    },
  ],
};
