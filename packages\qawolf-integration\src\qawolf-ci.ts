/**
 * QA Wolf CI for QA Wolf integration
 */

import axios, { AxiosInstance } from 'axios';
import { QAWolfCIOptions, QAWolfAPIResponse, QAWolfCIRun, QAWolfCIGreenlight } from './types';
import { EventBus, EventType } from '@qawolf/core';

/**
 * QA Wolf CI
 */
export class QAWolfCI {
  /**
   * API key
   */
  private apiKey: string;
  
  /**
   * Team ID
   */
  private teamId: string;
  
  /**
   * API URL
   */
  private apiUrl: string;
  
  /**
   * CI provider
   */
  private provider: string;
  
  /**
   * Repository URL
   */
  private repositoryUrl: string;
  
  /**
   * Branch
   */
  private branch: string;
  
  /**
   * Commit
   */
  private commit: string;
  
  /**
   * Pull request
   */
  private pullRequest: string;
  
  /**
   * Environment
   */
  private environment: string;
  
  /**
   * Timeout in milliseconds
   */
  private timeout: number;
  
  /**
   * Axios instance
   */
  private axios: AxiosInstance;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options QA Wolf CI options
   */
  constructor(options: QAWolfCIOptions = {}) {
    this.apiKey = options.apiKey || '';
    this.teamId = options.teamId || '';
    this.apiUrl = options.apiUrl || 'https://app.qawolf.com/api';
    this.provider = options.provider || 'github';
    this.repositoryUrl = options.repositoryUrl || '';
    this.branch = options.branch || '';
    this.commit = options.commit || '';
    this.pullRequest = options.pullRequest || '';
    this.environment = options.environment || 'production';
    this.timeout = options.timeout || 30000;
    
    this.axios = axios.create({
      baseURL: this.apiUrl,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        'X-Team-ID': this.teamId
      }
    });
    
    this.eventBus = EventBus.getInstance();
  }
  
  /**
   * Start CI run
   * @param provider CI provider
   * @param repositoryUrl Repository URL
   * @param branch Branch
   * @param commit Commit
   * @param pullRequest Pull request
   * @param metadata Metadata
   * @returns CI run
   */
  async startCIRun(provider?: string, repositoryUrl?: string, branch?: string, commit?: string, pullRequest?: string, metadata?: Record<string, any>): Promise<QAWolfCIRun | null> {
    try {
      const response = await this.axios.post<QAWolfAPIResponse<QAWolfCIRun>>('/ci/runs', {
        provider: provider || this.provider,
        repositoryUrl: repositoryUrl || this.repositoryUrl,
        branch: branch || this.branch,
        commit: commit || this.commit,
        pullRequest: pullRequest || this.pullRequest,
        metadata
      });
      
      // Emit event
      this.eventBus.emit(EventType.QAWOLF_CI_START_RUN, {
        ciRun: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, 'Failed to start CI run');
      return null;
    }
  }
  
  /**
   * Get CI run by ID
   * @param ciRunId CI run ID
   * @returns CI run
   */
  async getCIRunById(ciRunId: string): Promise<QAWolfCIRun | null> {
    try {
      const response = await this.axios.get<QAWolfAPIResponse<QAWolfCIRun>>(`/ci/runs/${ciRunId}`);
      
      // Emit event
      this.eventBus.emit(EventType.QAWOLF_CI_GET_RUN_BY_ID, {
        ciRun: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, `Failed to get CI run with ID ${ciRunId}`);
      return null;
    }
  }
  
  /**
   * Update CI run
   * @param ciRunId CI run ID
   * @param status Status
   * @param metadata Metadata
   * @returns CI run
   */
  async updateCIRun(ciRunId: string, status?: string, metadata?: Record<string, any>): Promise<QAWolfCIRun | null> {
    try {
      const response = await this.axios.put<QAWolfAPIResponse<QAWolfCIRun>>(`/ci/runs/${ciRunId}`, {
        status,
        metadata
      });
      
      // Emit event
      this.eventBus.emit(EventType.QAWOLF_CI_UPDATE_RUN, {
        ciRun: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, `Failed to update CI run with ID ${ciRunId}`);
      return null;
    }
  }
  
  /**
   * Cancel CI run
   * @param ciRunId CI run ID
   * @returns True if cancelled, false otherwise
   */
  async cancelCIRun(ciRunId: string): Promise<boolean> {
    try {
      await this.axios.post<QAWolfAPIResponse<void>>(`/ci/runs/${ciRunId}/cancel`);
      
      // Emit event
      this.eventBus.emit(EventType.QAWOLF_CI_CANCEL_RUN, {
        ciRunId
      });
      
      return true;
    } catch (error) {
      this.handleError(error, `Failed to cancel CI run with ID ${ciRunId}`);
      return false;
    }
  }
  
  /**
   * Get CI greenlight
   * @param ciRunId CI run ID
   * @returns CI greenlight
   */
  async getCIGreenlight(ciRunId: string): Promise<QAWolfCIGreenlight | null> {
    try {
      const response = await this.axios.get<QAWolfAPIResponse<QAWolfCIGreenlight>>(`/ci/runs/${ciRunId}/greenlight`);
      
      // Emit event
      this.eventBus.emit(EventType.QAWOLF_CI_GET_GREENLIGHT, {
        ciGreenlight: response.data.data
      });
      
      return response.data.data;
    } catch (error) {
      this.handleError(error, `Failed to get CI greenlight for run with ID ${ciRunId}`);
      return null;
    }
  }
  
  /**
   * Poll CI greenlight
   * @param ciRunId CI run ID
   * @param interval Polling interval in milliseconds
   * @param timeout Timeout in milliseconds
   * @returns CI greenlight
   */
  async pollCIGreenlight(ciRunId: string, interval: number = 5000, timeout: number = 300000): Promise<QAWolfCIGreenlight | null> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      const greenlight = await this.getCIGreenlight(ciRunId);
      
      if (greenlight && greenlight.status === 'complete') {
        return greenlight;
      }
      
      // Wait for the specified interval
      await new Promise(resolve => setTimeout(resolve, interval));
    }
    
    this.handleError(new Error('Timeout'), `Timeout waiting for CI greenlight for run with ID ${ciRunId}`);
    return null;
  }
  
  /**
   * Notify deployment
   * @param environment Environment
   * @param url URL
   * @param metadata Metadata
   * @returns True if notified, false otherwise
   */
  async notifyDeployment(environment?: string, url?: string, metadata?: Record<string, any>): Promise<boolean> {
    try {
      await this.axios.post<QAWolfAPIResponse<void>>('/ci/deployments', {
        environment: environment || this.environment,
        url,
        metadata
      });
      
      // Emit event
      this.eventBus.emit(EventType.QAWOLF_CI_NOTIFY_DEPLOYMENT, {
        environment: environment || this.environment,
        url
      });
      
      return true;
    } catch (error) {
      this.handleError(error, `Failed to notify deployment for environment ${environment || this.environment}`);
      return false;
    }
  }
  
  /**
   * Handle error
   * @param error Error
   * @param message Error message
   */
  private handleError(error: any, message: string): void {
    console.error(message, error);
    
    // Emit event
    this.eventBus.emit(EventType.QAWOLF_CI_ERROR, {
      error,
      message
    });
  }
  
  /**
   * Get API key
   * @returns API key
   */
  getAPIKey(): string {
    return this.apiKey;
  }
  
  /**
   * Get team ID
   * @returns Team ID
   */
  getTeamId(): string {
    return this.teamId;
  }
  
  /**
   * Get API URL
   * @returns API URL
   */
  getAPIUrl(): string {
    return this.apiUrl;
  }
  
  /**
   * Get provider
   * @returns Provider
   */
  getProvider(): string {
    return this.provider;
  }
  
  /**
   * Get repository URL
   * @returns Repository URL
   */
  getRepositoryUrl(): string {
    return this.repositoryUrl;
  }
  
  /**
   * Get branch
   * @returns Branch
   */
  getBranch(): string {
    return this.branch;
  }
  
  /**
   * Get commit
   * @returns Commit
   */
  getCommit(): string {
    return this.commit;
  }
  
  /**
   * Get pull request
   * @returns Pull request
   */
  getPullRequest(): string {
    return this.pullRequest;
  }
  
  /**
   * Get environment
   * @returns Environment
   */
  getEnvironment(): string {
    return this.environment;
  }
  
  /**
   * Get timeout
   * @returns Timeout in milliseconds
   */
  getTimeout(): number {
    return this.timeout;
  }
  
  /**
   * Set API key
   * @param apiKey API key
   * @returns This instance for chaining
   */
  setAPIKey(apiKey: string): QAWolfCI {
    this.apiKey = apiKey;
    
    this.axios = axios.create({
      baseURL: this.apiUrl,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        'X-Team-ID': this.teamId
      }
    });
    
    return this;
  }
  
  /**
   * Set team ID
   * @param teamId Team ID
   * @returns This instance for chaining
   */
  setTeamId(teamId: string): QAWolfCI {
    this.teamId = teamId;
    
    this.axios = axios.create({
      baseURL: this.apiUrl,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        'X-Team-ID': this.teamId
      }
    });
    
    return this;
  }
  
  /**
   * Set API URL
   * @param apiUrl API URL
   * @returns This instance for chaining
   */
  setAPIUrl(apiUrl: string): QAWolfCI {
    this.apiUrl = apiUrl;
    
    this.axios = axios.create({
      baseURL: this.apiUrl,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        'X-Team-ID': this.teamId
      }
    });
    
    return this;
  }
  
  /**
   * Set provider
   * @param provider Provider
   * @returns This instance for chaining
   */
  setProvider(provider: string): QAWolfCI {
    this.provider = provider;
    return this;
  }
  
  /**
   * Set repository URL
   * @param repositoryUrl Repository URL
   * @returns This instance for chaining
   */
  setRepositoryUrl(repositoryUrl: string): QAWolfCI {
    this.repositoryUrl = repositoryUrl;
    return this;
  }
  
  /**
   * Set branch
   * @param branch Branch
   * @returns This instance for chaining
   */
  setBranch(branch: string): QAWolfCI {
    this.branch = branch;
    return this;
  }
  
  /**
   * Set commit
   * @param commit Commit
   * @returns This instance for chaining
   */
  setCommit(commit: string): QAWolfCI {
    this.commit = commit;
    return this;
  }
  
  /**
   * Set pull request
   * @param pullRequest Pull request
   * @returns This instance for chaining
   */
  setPullRequest(pullRequest: string): QAWolfCI {
    this.pullRequest = pullRequest;
    return this;
  }
  
  /**
   * Set environment
   * @param environment Environment
   * @returns This instance for chaining
   */
  setEnvironment(environment: string): QAWolfCI {
    this.environment = environment;
    return this;
  }
  
  /**
   * Set timeout
   * @param timeout Timeout in milliseconds
   * @returns This instance for chaining
   */
  setTimeout(timeout: number): QAWolfCI {
    this.timeout = timeout;
    
    this.axios = axios.create({
      baseURL: this.apiUrl,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
        'X-Team-ID': this.teamId
      }
    });
    
    return this;
  }
}

/**
 * Create QA Wolf CI
 * @param options QA Wolf CI options
 * @returns QA Wolf CI
 */
export function createQAWolfCI(options: QAWolfCIOptions = {}): QAWolfCI {
  return new QAWolfCI(options);
}
