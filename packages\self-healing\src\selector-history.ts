/**
 * Selector history for self-healing tests
 */

import * as fs from 'fs';
import * as path from 'path';
import { SelectorHistory, SelectorHistoryEntry } from './types';
import { fileExists, ensureDirectoryExists, readFileAsJson, writeJsonToFile } from '@qawolf/shared-utils';
import { getConfig } from '@qawolf/core';

// Get configuration
const config = getConfig();

/**
 * Default selector history file path
 */
const DEFAULT_SELECTOR_HISTORY_PATH = 'selector-history.json';

/**
 * Selector history manager
 */
export class SelectorHistoryManager {
  private history: SelectorHistory;
  private filePath: string;
  private saveToFile: boolean;
  
  /**
   * Constructor
   * @param filePath Selector history file path
   * @param saveToFile Whether to save selector history to file
   */
  constructor(filePath: string = DEFAULT_SELECTOR_HISTORY_PATH, saveToFile: boolean = true) {
    this.filePath = filePath;
    this.saveToFile = saveToFile;
    this.history = this.loadHistory();
  }
  
  /**
   * Load selector history from file
   * @returns Selector history
   */
  private loadHistory(): SelectorHistory {
    if (this.saveToFile && fileExists(this.filePath)) {
      try {
        return readFileAsJson<SelectorHistory>(this.filePath);
      } catch (error) {
        console.error(`Error loading selector history from ${this.filePath}:`, error);
      }
    }
    
    return {
      entries: {},
      lastUpdated: Date.now()
    };
  }
  
  /**
   * Save selector history to file
   */
  private saveHistory(): void {
    if (this.saveToFile) {
      try {
        this.history.lastUpdated = Date.now();
        writeJsonToFile(this.filePath, this.history);
      } catch (error) {
        console.error(`Error saving selector history to ${this.filePath}:`, error);
      }
    }
  }
  
  /**
   * Get alternative selector
   * @param selector Original selector
   * @returns Alternative selector or null if not found
   */
  getAlternative(selector: string): string | null {
    const entry = this.history.entries[selector];
    
    if (entry) {
      // Update usage count and last used
      entry.usageCount++;
      entry.lastUsed = Date.now();
      
      // Save history
      this.saveHistory();
      
      return entry.healedSelector;
    }
    
    return null;
  }
  
  /**
   * Add alternative selector
   * @param originalSelector Original selector
   * @param healedSelector Healed selector
   * @param strategy Strategy used for healing
   * @param similarityScore Similarity score
   */
  addAlternative(originalSelector: string, healedSelector: string, strategy: string, similarityScore?: number): void {
    // Don't add if original and healed selectors are the same
    if (originalSelector === healedSelector) {
      return;
    }
    
    this.history.entries[originalSelector] = {
      originalSelector,
      healedSelector,
      strategy,
      similarityScore,
      timestamp: Date.now(),
      usageCount: 1,
      lastUsed: Date.now()
    };
    
    // Save history
    this.saveHistory();
  }
  
  /**
   * Remove alternative selector
   * @param selector Original selector
   */
  removeAlternative(selector: string): void {
    if (this.history.entries[selector]) {
      delete this.history.entries[selector];
      
      // Save history
      this.saveHistory();
    }
  }
  
  /**
   * Clear selector history
   */
  clearHistory(): void {
    this.history = {
      entries: {},
      lastUpdated: Date.now()
    };
    
    // Save history
    this.saveHistory();
  }
  
  /**
   * Get all entries
   * @returns All selector history entries
   */
  getAllEntries(): { [originalSelector: string]: SelectorHistoryEntry } {
    return { ...this.history.entries };
  }
  
  /**
   * Get entry count
   * @returns Number of entries in selector history
   */
  getEntryCount(): number {
    return Object.keys(this.history.entries).length;
  }
  
  /**
   * Get last updated
   * @returns Last time the history was updated
   */
  getLastUpdated(): number {
    return this.history.lastUpdated;
  }
  
  /**
   * Export selector history
   * @param exportPath Export file path
   */
  exportHistory(exportPath: string): void {
    try {
      writeJsonToFile(exportPath, this.history);
    } catch (error) {
      console.error(`Error exporting selector history to ${exportPath}:`, error);
    }
  }
  
  /**
   * Import selector history
   * @param importPath Import file path
   */
  importHistory(importPath: string): void {
    if (fileExists(importPath)) {
      try {
        const importedHistory = readFileAsJson<SelectorHistory>(importPath);
        
        // Merge imported history with current history
        for (const [selector, entry] of Object.entries(importedHistory.entries)) {
          if (!this.history.entries[selector]) {
            this.history.entries[selector] = entry;
          }
        }
        
        // Update last updated
        this.history.lastUpdated = Date.now();
        
        // Save history
        this.saveHistory();
      } catch (error) {
        console.error(`Error importing selector history from ${importPath}:`, error);
      }
    }
  }
  
  /**
   * Prune old entries
   * @param maxAge Maximum age in milliseconds
   * @returns Number of pruned entries
   */
  pruneOldEntries(maxAge: number): number {
    const now = Date.now();
    let prunedCount = 0;
    
    for (const [selector, entry] of Object.entries(this.history.entries)) {
      if (now - entry.lastUsed > maxAge) {
        delete this.history.entries[selector];
        prunedCount++;
      }
    }
    
    if (prunedCount > 0) {
      // Save history
      this.saveHistory();
    }
    
    return prunedCount;
  }
}

/**
 * Create selector history manager
 * @param filePath Selector history file path
 * @param saveToFile Whether to save selector history to file
 * @returns Selector history manager
 */
export function createSelectorHistoryManager(filePath: string = DEFAULT_SELECTOR_HISTORY_PATH, saveToFile: boolean = true): SelectorHistoryManager {
  return new SelectorHistoryManager(filePath, saveToFile);
}

/**
 * Default selector history manager
 */
export const defaultSelectorHistoryManager = createSelectorHistoryManager();
