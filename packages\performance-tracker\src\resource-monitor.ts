/**
 * Resource monitor for performance tracking
 */

import * as os from 'os';
import { ResourceUsage, ResourceMonitorOptions } from './types';
import { EventBus, EventType } from '@qawolf/core';

/**
 * Resource monitor
 */
export class ResourceMonitor {
  /**
   * Resource usage
   */
  private resourceUsage: ResourceUsage[] = [];
  
  /**
   * Monitoring interval
   */
  private monitorInterval: number;
  
  /**
   * Whether to monitor resources
   */
  private monitorResources: boolean;
  
  /**
   * Interval ID
   */
  private intervalId: NodeJS.Timeout | null = null;
  
  /**
   * Previous CPU usage
   */
  private previousCPUUsage: { user: number; system: number } | null = null;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options Resource monitor options
   */
  constructor(options: ResourceMonitorOptions = {}) {
    this.monitorResources = options.monitorResources !== false;
    this.monitorInterval = options.monitorInterval || 1000;
    this.eventBus = EventBus.getInstance();
  }
  
  /**
   * Start monitoring resources
   * @returns This instance for chaining
   */
  start(): ResourceMonitor {
    if (!this.monitorResources || this.intervalId) {
      return this;
    }
    
    // Get initial CPU usage
    this.previousCPUUsage = process.cpuUsage();
    
    // Start monitoring
    this.intervalId = setInterval(() => {
      this.captureResourceUsage();
    }, this.monitorInterval);
    
    // Capture initial resource usage
    this.captureResourceUsage();
    
    return this;
  }
  
  /**
   * Stop monitoring resources
   * @returns This instance for chaining
   */
  stop(): ResourceMonitor {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    
    return this;
  }
  
  /**
   * Capture resource usage
   * @returns Resource usage
   */
  private captureResourceUsage(): ResourceUsage {
    // Get memory usage
    const memoryUsage = process.memoryUsage();
    
    // Get CPU usage
    const cpuUsage = process.cpuUsage(this.previousCPUUsage || undefined);
    this.previousCPUUsage = process.cpuUsage();
    
    // Create resource usage
    const resourceUsage: ResourceUsage = {
      timestamp: Date.now(),
      memory: {
        rss: memoryUsage.rss,
        heapTotal: memoryUsage.heapTotal,
        heapUsed: memoryUsage.heapUsed,
        external: memoryUsage.external,
        arrayBuffers: memoryUsage.arrayBuffers
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system
      }
    };
    
    // Add to resource usage
    this.resourceUsage.push(resourceUsage);
    
    // Emit event
    this.eventBus.emit(EventType.RESOURCE_USAGE_CAPTURED, {
      resourceUsage
    });
    
    return resourceUsage;
  }
  
  /**
   * Get resource usage
   * @returns Resource usage
   */
  getResourceUsage(): ResourceUsage[] {
    return [...this.resourceUsage];
  }
  
  /**
   * Clear resource usage
   * @returns This instance for chaining
   */
  clearResourceUsage(): ResourceMonitor {
    this.resourceUsage = [];
    return this;
  }
  
  /**
   * Enable resource monitoring
   * @returns This instance for chaining
   */
  enable(): ResourceMonitor {
    this.monitorResources = true;
    return this;
  }
  
  /**
   * Disable resource monitoring
   * @returns This instance for chaining
   */
  disable(): ResourceMonitor {
    this.monitorResources = false;
    this.stop();
    return this;
  }
  
  /**
   * Check if resource monitoring is enabled
   * @returns True if resource monitoring is enabled, false otherwise
   */
  isEnabled(): boolean {
    return this.monitorResources;
  }
  
  /**
   * Get monitoring interval
   * @returns Monitoring interval
   */
  getMonitorInterval(): number {
    return this.monitorInterval;
  }
  
  /**
   * Set monitoring interval
   * @param interval Monitoring interval
   * @returns This instance for chaining
   */
  setMonitorInterval(interval: number): ResourceMonitor {
    this.monitorInterval = interval;
    
    // Restart monitoring if already running
    if (this.intervalId) {
      this.stop();
      this.start();
    }
    
    return this;
  }
}

/**
 * Create resource monitor
 * @param options Resource monitor options
 * @returns Resource monitor
 */
export function createResourceMonitor(options: ResourceMonitorOptions = {}): ResourceMonitor {
  return new ResourceMonitor(options);
}
