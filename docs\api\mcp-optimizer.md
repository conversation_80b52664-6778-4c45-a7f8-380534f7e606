# MCP Optimizer API

The `@qawolf/mcp-optimizer` package provides utilities for optimizing selectors and integrating with MCP tools.

## Installation

```bash
npm install @qawolf/mcp-optimizer
```

## Usage

```javascript
const { createMcpOptimizer } = require('@qawolf/mcp-optimizer');

// Create an MCP optimizer
const mcpOptimizer = createMcpOptimizer({
  autoStartPlaywrightMcp: true,
  generateFallbacks: true,
  prioritizeTestIds: true
});

// Optimize selectors
const selectors = await mcpOptimizer.optimizeSelectors([
  '[data-test-id="SignInEmail"]',
  '[data-test-id="SignInPassword"]',
  ':text("Log in with email")'
]);

// Clean up resources
await mcpOptimizer.cleanup();
```

## API Reference

### MCP Optimizer

#### `createMcpOptimizer(options)`

Creates an MCP optimizer for optimizing selectors and integrating with MCP tools.

**Parameters:**

- `options` (Object, optional): Configuration options for the MCP optimizer.
  - `autoStartPlaywrightMcp` (boolean, default: true): Whether to automatically start the Playwright MCP server.
  - `playwrightMcpPort` (number, default: 8932): Port for the Playwright MCP server.
  - `generateFallbacks` (boolean, default: true): Whether to generate fallback selectors.
  - `prioritizeTestIds` (boolean, default: true): Whether to prioritize test IDs.
  - `mcpTools` (Array<string>, default: ['playwright-mcp', 'browser-tools-mcp']): MCP tools to use.

**Returns:**

- (Object): MCP optimizer with the following methods:
  - `optimizeSelectors(selectors)`: Optimizes selectors for better reliability.
  - `selectTool(task)`: Selects the appropriate MCP tool for a task.
  - `analyzeScreenshot(screenshotPath)`: Analyzes a screenshot to identify UI elements.
  - `startPlaywrightMcp()`: Starts the Playwright MCP server.
  - `stopPlaywrightMcp()`: Stops the Playwright MCP server.
  - `cleanup()`: Cleans up resources.

**Example:**

```javascript
const { createMcpOptimizer } = require('@qawolf/mcp-optimizer');

const mcpOptimizer = createMcpOptimizer({
  autoStartPlaywrightMcp: true,
  generateFallbacks: true,
  prioritizeTestIds: true
});

// Optimize selectors
const selectors = await mcpOptimizer.optimizeSelectors([
  '[data-test-id="SignInEmail"]',
  '[data-test-id="SignInPassword"]',
  ':text("Log in with email")'
]);

// Clean up resources
await mcpOptimizer.cleanup();
```

### Selector Optimization

#### `optimizeSelectors(selectors, options)`

Optimizes selectors for better reliability.

**Parameters:**

- `selectors` (Array<string>): Selectors to optimize.
- `options` (Object, optional): Options for selector optimization.
  - `page` (Object, optional): Playwright page to use for optimization.
  - `generateFallbacks` (boolean, default: true): Whether to generate fallback selectors.
  - `prioritizeTestIds` (boolean, default: true): Whether to prioritize test IDs.

**Returns:**

- (Object): Optimization results with the following properties:
  - `selectors` (Array<Object>): Optimized selectors with the following properties:
    - `original` (string): Original selector.
    - `optimized` (string): Optimized selector.
    - `fallbacks` (Array<string>): Fallback selectors.
    - `reliability` (number): Reliability score (0-1).

**Example:**

```javascript
const { optimizeSelectors } = require('@qawolf/mcp-optimizer');

// Optimize selectors
const selectors = await optimizeSelectors([
  '[data-test-id="SignInEmail"]',
  '[data-test-id="SignInPassword"]',
  ':text("Log in with email")'
], {
  generateFallbacks: true,
  prioritizeTestIds: true
});
```

### Tool Selection

#### `selectTool(task, options)`

Selects the appropriate MCP tool for a task.

**Parameters:**

- `task` (Object): Task to select a tool for.
  - `type` (string): Task type.
  - `description` (string): Task description.
  - `parameters` (Object): Task parameters.
- `options` (Object, optional): Options for tool selection.
  - `mcpTools` (Array<string>, default: ['playwright-mcp', 'browser-tools-mcp']): MCP tools to consider.

**Returns:**

- (Object): Selected tool with the following properties:
  - `name` (string): Tool name.
  - `command` (string): Tool command.
  - `parameters` (Object): Tool parameters.
  - `reliability` (number): Reliability score (0-1).

**Example:**

```javascript
const { selectTool } = require('@qawolf/mcp-optimizer');

// Select a tool for a task
const tool = await selectTool({
  type: 'click',
  description: 'Click the login button',
  parameters: {
    selector: ':text("Log in with email")'
  }
}, {
  mcpTools: ['playwright-mcp', 'browser-tools-mcp']
});
```

### Screenshot Analysis

#### `analyzeScreenshot(screenshotPath, options)`

Analyzes a screenshot to identify UI elements.

**Parameters:**

- `screenshotPath` (string): Path to the screenshot.
- `options` (Object, optional): Options for screenshot analysis.
  - `mcpTools` (Array<string>, default: ['browser-tools-mcp']): MCP tools to use for analysis.

**Returns:**

- (Object): Analysis results with the following properties:
  - `elements` (Array<Object>): Identified elements with the following properties:
    - `type` (string): Element type.
    - `text` (string): Element text.
    - `selector` (string): Element selector.
    - `boundingBox` (Object): Element bounding box.
    - `attributes` (Object): Element attributes.
  - `screenshot` (string): Screenshot path.

**Example:**

```javascript
const { analyzeScreenshot } = require('@qawolf/mcp-optimizer');

// Analyze a screenshot
const analysis = await analyzeScreenshot('screenshot.png', {
  mcpTools: ['browser-tools-mcp']
});
```

### Playwright MCP

#### `startPlaywrightMcp(options)`

Starts the Playwright MCP server.

**Parameters:**

- `options` (Object, optional): Options for starting the Playwright MCP server.
  - `port` (number, default: 8932): Port for the Playwright MCP server.

**Returns:**

- (Object): Playwright MCP server with the following properties:
  - `port` (number): Server port.
  - `url` (string): Server URL.
  - `stop()`: Function to stop the server.

**Example:**

```javascript
const { startPlaywrightMcp } = require('@qawolf/mcp-optimizer');

// Start the Playwright MCP server
const server = await startPlaywrightMcp({
  port: 8932
});

// Stop the server
await server.stop();
```

#### `stopPlaywrightMcp(server)`

Stops the Playwright MCP server.

**Parameters:**

- `server` (Object): Playwright MCP server to stop.

**Returns:**

- (void)

**Example:**

```javascript
const { startPlaywrightMcp, stopPlaywrightMcp } = require('@qawolf/mcp-optimizer');

// Start the Playwright MCP server
const server = await startPlaywrightMcp({
  port: 8932
});

// Stop the server
await stopPlaywrightMcp(server);
```