/**
 * Performance Tracker
 * 
 * This module provides utilities for tracking performance metrics in tests.
 */

/**
 * Default performance thresholds
 */
const DEFAULT_THRESHOLDS = {
  executionTime: 15000, // 15 seconds
  tokenUsage: 1000, // 1000 tokens
  resourceUsage: {
    cpu: 80, // 80% CPU usage
    memory: 500 // 500 MB memory usage
  }
};

/**
 * Performance tracker for measuring test performance
 */
class PerformanceTracker {
  /**
   * Create a new PerformanceTracker
   * 
   * @param {Object} [options] - Performance tracker options
   * @param {Object} [options.thresholds] - Performance thresholds
   * @param {boolean} [options.trackTokenUsage] - Whether to track token usage
   * @param {boolean} [options.trackResourceUsage] - Whether to track resource usage
   */
  constructor(options = {}) {
    this.thresholds = options.thresholds || DEFAULT_THRESHOLDS;
    this.trackTokenUsage = options.trackTokenUsage !== undefined ? options.trackTokenUsage : true;
    this.trackResourceUsage = options.trackResourceUsage !== undefined ? options.trackResourceUsage : true;
    
    this.metrics = {
      executionTime: {
        start: null,
        end: null,
        duration: null
      },
      tokenUsage: {
        total: 0,
        byOperation: {}
      },
      resourceUsage: {
        cpu: [],
        memory: []
      },
      operations: []
    };
    
    this.resourceUsageInterval = null;
  }
  
  /**
   * Start tracking performance
   * 
   * @returns {PerformanceTracker} - This performance tracker
   */
  start() {
    this.metrics.executionTime.start = Date.now();
    
    if (this.trackResourceUsage) {
      this.startResourceUsageTracking();
    }
    
    return this;
  }
  
  /**
   * Stop tracking performance
   * 
   * @returns {Object} - Performance metrics
   */
  stop() {
    this.metrics.executionTime.end = Date.now();
    this.metrics.executionTime.duration = this.metrics.executionTime.end - this.metrics.executionTime.start;
    
    if (this.trackResourceUsage) {
      this.stopResourceUsageTracking();
    }
    
    return this.getMetrics();
  }
  
  /**
   * Start tracking resource usage
   * 
   * @private
   */
  startResourceUsageTracking() {
    // Clear any existing interval
    if (this.resourceUsageInterval) {
      clearInterval(this.resourceUsageInterval);
    }
    
    // Start tracking resource usage
    this.resourceUsageInterval = setInterval(() => {
      // This is a simplified implementation
      // In a real implementation, we would use process.cpuUsage() and process.memoryUsage()
      const cpuUsage = Math.random() * 100;
      const memoryUsage = Math.random() * 1000;
      
      this.metrics.resourceUsage.cpu.push(cpuUsage);
      this.metrics.resourceUsage.memory.push(memoryUsage);
    }, 1000);
  }
  
  /**
   * Stop tracking resource usage
   * 
   * @private
   */
  stopResourceUsageTracking() {
    if (this.resourceUsageInterval) {
      clearInterval(this.resourceUsageInterval);
      this.resourceUsageInterval = null;
    }
  }
  
  /**
   * Track an operation
   * 
   * @param {Object} operation - Operation to track
   * @param {string} operation.name - Name of the operation
   * @param {string} operation.type - Type of the operation
   * @param {number} operation.duration - Duration of the operation in milliseconds
   * @param {number} [operation.tokenUsage] - Token usage of the operation
   * @returns {PerformanceTracker} - This performance tracker
   */
  trackOperation(operation) {
    const { name, type, duration, tokenUsage } = operation;
    
    this.metrics.operations.push({
      name,
      type,
      duration,
      tokenUsage,
      timestamp: Date.now()
    });
    
    if (this.trackTokenUsage && tokenUsage) {
      this.metrics.tokenUsage.total += tokenUsage;
      
      if (!this.metrics.tokenUsage.byOperation[type]) {
        this.metrics.tokenUsage.byOperation[type] = 0;
      }
      
      this.metrics.tokenUsage.byOperation[type] += tokenUsage;
    }
    
    return this;
  }
  
  /**
   * Get performance metrics
   * 
   * @returns {Object} - Performance metrics
   */
  getMetrics() {
    // Calculate average resource usage
    let avgCpuUsage = 0;
    let avgMemoryUsage = 0;
    
    if (this.metrics.resourceUsage.cpu.length > 0) {
      avgCpuUsage = this.metrics.resourceUsage.cpu.reduce((sum, value) => sum + value, 0) / this.metrics.resourceUsage.cpu.length;
    }
    
    if (this.metrics.resourceUsage.memory.length > 0) {
      avgMemoryUsage = this.metrics.resourceUsage.memory.reduce((sum, value) => sum + value, 0) / this.metrics.resourceUsage.memory.length;
    }
    
    // Calculate performance scores
    const executionTimeScore = this.calculateScore(this.metrics.executionTime.duration, this.thresholds.executionTime, true);
    const tokenUsageScore = this.calculateScore(this.metrics.tokenUsage.total, this.thresholds.tokenUsage, true);
    const cpuUsageScore = this.calculateScore(avgCpuUsage, this.thresholds.resourceUsage.cpu, true);
    const memoryUsageScore = this.calculateScore(avgMemoryUsage, this.thresholds.resourceUsage.memory, true);
    
    // Calculate overall score
    const overallScore = (executionTimeScore + tokenUsageScore + cpuUsageScore + memoryUsageScore) / 4;
    
    return {
      executionTime: {
        start: this.metrics.executionTime.start,
        end: this.metrics.executionTime.end,
        duration: this.metrics.executionTime.duration,
        score: executionTimeScore
      },
      tokenUsage: {
        total: this.metrics.tokenUsage.total,
        byOperation: this.metrics.tokenUsage.byOperation,
        score: tokenUsageScore
      },
      resourceUsage: {
        cpu: {
          values: this.metrics.resourceUsage.cpu,
          average: avgCpuUsage,
          score: cpuUsageScore
        },
        memory: {
          values: this.metrics.resourceUsage.memory,
          average: avgMemoryUsage,
          score: memoryUsageScore
        }
      },
      operations: this.metrics.operations,
      overallScore
    };
  }
  
  /**
   * Calculate a performance score
   * 
   * @private
   * @param {number} value - Value to score
   * @param {number} threshold - Threshold for the value
   * @param {boolean} lowerIsBetter - Whether a lower value is better
   * @returns {number} - Score between 0 and 1
   */
  calculateScore(value, threshold, lowerIsBetter = true) {
    if (lowerIsBetter) {
      return Math.max(0, Math.min(1, 1 - (value / threshold)));
    } else {
      return Math.max(0, Math.min(1, value / threshold));
    }
  }
}

module.exports = {
  PerformanceTracker,
  DEFAULT_THRESHOLDS
};
