/**
 * Self-healing locator for self-healing tests
 */

import { Locator, Page, ElementHandle } from 'playwright';
import { HealingStrategy, HealingOptions, SelfHealingLocatorOptions } from './types';
import { defaultHealingStrategies } from './healing-strategies';
import { SelfHealingPage } from './self-healing-page';
import { EventBus, EventType } from '@qawolf/core';

/**
 * Self-healing locator
 */
export class SelfHealingLocator implements Locator {
  /**
   * Self-healing page
   */
  private selfHealingPage: SelfHealingPage;
  
  /**
   * Playwright page
   */
  private page: Page;
  
  /**
   * Original selector
   */
  private originalSelector: string;
  
  /**
   * Current selector
   */
  private currentSelector: string;
  
  /**
   * Healing strategies
   */
  private strategies: HealingStrategy[];
  
  /**
   * Default healing options
   */
  private healingOptions: HealingOptions;
  
  /**
   * Whether to log healing attempts
   */
  private logging: boolean;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param selfHealingPage Self-healing page
   * @param page Playwright page
   * @param selector Selector
   * @param options Self-healing locator options
   */
  constructor(selfHealingPage: SelfHealingPage, page: Page, selector: string, options: SelfHealingLocatorOptions = {}) {
    this.selfHealingPage = selfHealingPage;
    this.page = page;
    this.originalSelector = selector;
    this.currentSelector = selector;
    this.strategies = options.strategies || defaultHealingStrategies;
    this.healingOptions = options.healingOptions || { similarityThreshold: 0.7, maxCandidates: 5, timeout: 30000, logging: true };
    this.logging = options.logging !== false;
    this.eventBus = EventBus.getInstance();
  }
  
  /**
   * Get current selector
   * @returns Current selector
   */
  getSelector(): string {
    return this.currentSelector;
  }
  
  /**
   * Get original selector
   * @returns Original selector
   */
  getOriginalSelector(): string {
    return this.originalSelector;
  }
  
  /**
   * Heal selector
   * @returns Healed selector
   */
  private async heal(): Promise<void> {
    try {
      // Check if current selector is valid
      const count = await this.page.locator(this.currentSelector).count();
      
      if (count > 0) {
        return;
      }
    } catch (error) {
      // Ignore error
    }
    
    // Heal selector
    const result = await this.selfHealingPage.healSelector(this.originalSelector, this.healingOptions);
    
    if (result.success && result.healedSelector) {
      this.currentSelector = result.healedSelector;
    }
  }
  
  /**
   * Get playwright locator
   * @returns Playwright locator
   */
  private async getLocator(): Promise<Locator> {
    await this.heal();
    return this.page.locator(this.currentSelector);
  }
  
  // Implement Locator interface
  
  // Properties
  
  get first(): Locator {
    return new SelfHealingLocator(this.selfHealingPage, this.page, `${this.originalSelector} >> nth=0`, {
      strategies: this.strategies,
      healingOptions: this.healingOptions,
      logging: this.logging
    });
  }
  
  get last(): Locator {
    return new SelfHealingLocator(this.selfHealingPage, this.page, `${this.originalSelector} >> nth=-1`, {
      strategies: this.strategies,
      healingOptions: this.healingOptions,
      logging: this.logging
    });
  }
  
  // Methods
  
  all(): Promise<Locator[]> {
    return this.getLocator().then(locator => locator.all());
  }
  
  allInnerTexts(): Promise<string[]> {
    return this.getLocator().then(locator => locator.allInnerTexts());
  }
  
  allTextContents(): Promise<string[]> {
    return this.getLocator().then(locator => locator.allTextContents());
  }
  
  and(locator: Locator): Locator {
    return new SelfHealingLocator(this.selfHealingPage, this.page, `${this.originalSelector} >> internal:and=${locator}`, {
      strategies: this.strategies,
      healingOptions: this.healingOptions,
      logging: this.logging
    });
  }
  
  boundingBox(options?: { timeout?: number | undefined; } | undefined): Promise<{ x: number; y: number; width: number; height: number; } | null> {
    return this.getLocator().then(locator => locator.boundingBox(options));
  }
  
  check(options?: { position?: { x: number; y: number; } | undefined; force?: boolean | undefined; noWaitAfter?: boolean | undefined; timeout?: number | undefined; trial?: boolean | undefined; } | undefined): Promise<void> {
    return this.getLocator().then(locator => locator.check(options));
  }
  
  clear(options?: { force?: boolean | undefined; noWaitAfter?: boolean | undefined; timeout?: number | undefined; } | undefined): Promise<void> {
    return this.getLocator().then(locator => locator.clear(options));
  }
  
  click(options?: { button?: "left" | "right" | "middle" | undefined; clickCount?: number | undefined; delay?: number | undefined; position?: { x: number; y: number; } | undefined; force?: boolean | undefined; noWaitAfter?: boolean | undefined; timeout?: number | undefined; trial?: boolean | undefined; } | undefined): Promise<void> {
    return this.getLocator().then(locator => locator.click(options));
  }
  
  count(): Promise<number> {
    return this.getLocator().then(locator => locator.count());
  }
  
  dblclick(options?: { button?: "left" | "right" | "middle" | undefined; delay?: number | undefined; position?: { x: number; y: number; } | undefined; force?: boolean | undefined; noWaitAfter?: boolean | undefined; timeout?: number | undefined; trial?: boolean | undefined; } | undefined): Promise<void> {
    return this.getLocator().then(locator => locator.dblclick(options));
  }
  
  dispatchEvent(type: string, eventInit?: any, options?: { timeout?: number | undefined; } | undefined): Promise<void> {
    return this.getLocator().then(locator => locator.dispatchEvent(type, eventInit, options));
  }
  
  dragTo(target: Locator, options?: { force?: boolean | undefined; noWaitAfter?: boolean | undefined; timeout?: number | undefined; trial?: boolean | undefined; sourcePosition?: { x: number; y: number; } | undefined; targetPosition?: { x: number; y: number; } | undefined; } | undefined): Promise<void> {
    return this.getLocator().then(locator => locator.dragTo(target, options));
  }
  
  elementHandle(options?: { timeout?: number | undefined; } | undefined): Promise<ElementHandle<SVGElement | HTMLElement> | null> {
    return this.getLocator().then(locator => locator.elementHandle(options));
  }
  
  elementHandles(): Promise<ElementHandle<SVGElement | HTMLElement>[]> {
    return this.getLocator().then(locator => locator.elementHandles());
  }
  
  evaluate<R, Arg>(pageFunction: (element: SVGElement | HTMLElement, arg: Arg) => R | Promise<R>, arg: Arg, options?: { timeout?: number | undefined; } | undefined): Promise<R>;
  evaluate<R>(pageFunction: (element: SVGElement | HTMLElement) => R | Promise<R>, options?: { timeout?: number | undefined; } | undefined): Promise<R>;
  evaluate<R, Arg>(pageFunction: (element: SVGElement | HTMLElement, arg?: Arg) => R | Promise<R>, arg?: Arg | { timeout?: number | undefined; } | undefined, options?: { timeout?: number | undefined; } | undefined): Promise<R> {
    return this.getLocator().then(locator => {
      if (typeof arg === 'object' && arg !== null && 'timeout' in arg) {
        return locator.evaluate(pageFunction as any, arg);
      } else {
        return locator.evaluate(pageFunction as any, arg as Arg, options);
      }
    });
  }
  
  evaluateAll<R, Arg>(pageFunction: (elements: (SVGElement | HTMLElement)[], arg: Arg) => R | Promise<R>, arg: Arg): Promise<R>;
  evaluateAll<R>(pageFunction: (elements: (SVGElement | HTMLElement)[]) => R | Promise<R>): Promise<R>;
  evaluateAll<R, Arg>(pageFunction: (elements: (SVGElement | HTMLElement)[], arg?: Arg) => R | Promise<R>, arg?: Arg): Promise<R> {
    return this.getLocator().then(locator => locator.evaluateAll(pageFunction as any, arg));
  }
  
  evaluateHandle<R, Arg>(pageFunction: (element: SVGElement | HTMLElement, arg: Arg) => R | Promise<R>, arg: Arg, options?: { timeout?: number | undefined; } | undefined): Promise<any>;
  evaluateHandle<R>(pageFunction: (element: SVGElement | HTMLElement) => R | Promise<R>, options?: { timeout?: number | undefined; } | undefined): Promise<any>;
  evaluateHandle<R, Arg>(pageFunction: (element: SVGElement | HTMLElement, arg?: Arg) => R | Promise<R>, arg?: Arg | { timeout?: number | undefined; } | undefined, options?: { timeout?: number | undefined; } | undefined): Promise<any> {
    return this.getLocator().then(locator => {
      if (typeof arg === 'object' && arg !== null && 'timeout' in arg) {
        return locator.evaluateHandle(pageFunction as any, arg);
      } else {
        return locator.evaluateHandle(pageFunction as any, arg as Arg, options);
      }
    });
  }
  
  fill(value: string, options?: { noWaitAfter?: boolean | undefined; timeout?: number | undefined; force?: boolean | undefined; } | undefined): Promise<void> {
    return this.getLocator().then(locator => locator.fill(value, options));
  }
  
  filter(options: { has?: Locator | undefined; hasNot?: Locator | undefined; hasText?: string | RegExp | undefined; hasNotText?: string | RegExp | undefined; }): Locator {
    return new SelfHealingLocator(this.selfHealingPage, this.page, `${this.originalSelector} >> internal:filter=${JSON.stringify(options)}`, {
      strategies: this.strategies,
      healingOptions: this.healingOptions,
      logging: this.logging
    });
  }
  
  focus(options?: { timeout?: number | undefined; } | undefined): Promise<void> {
    return this.getLocator().then(locator => locator.focus(options));
  }
  
  frameLocator(selector: string): Locator {
    return new SelfHealingLocator(this.selfHealingPage, this.page, `${this.originalSelector} >> internal:control=enter-frame >> ${selector}`, {
      strategies: this.strategies,
      healingOptions: this.healingOptions,
      logging: this.logging
    });
  }
  
  getAttribute(name: string, options?: { timeout?: number | undefined; } | undefined): Promise<string | null> {
    return this.getLocator().then(locator => locator.getAttribute(name, options));
  }
  
  highlight(): Promise<void> {
    return this.getLocator().then(locator => locator.highlight());
  }
  
  hover(options?: { position?: { x: number; y: number; } | undefined; force?: boolean | undefined; noWaitAfter?: boolean | undefined; timeout?: number | undefined; trial?: boolean | undefined; } | undefined): Promise<void> {
    return this.getLocator().then(locator => locator.hover(options));
  }
  
  innerHTML(options?: { timeout?: number | undefined; } | undefined): Promise<string> {
    return this.getLocator().then(locator => locator.innerHTML(options));
  }
  
  innerText(options?: { timeout?: number | undefined; } | undefined): Promise<string> {
    return this.getLocator().then(locator => locator.innerText(options));
  }
  
  inputValue(options?: { timeout?: number | undefined; } | undefined): Promise<string> {
    return this.getLocator().then(locator => locator.inputValue(options));
  }
  
  isChecked(options?: { timeout?: number | undefined; } | undefined): Promise<boolean> {
    return this.getLocator().then(locator => locator.isChecked(options));
  }
  
  isDisabled(options?: { timeout?: number | undefined; } | undefined): Promise<boolean> {
    return this.getLocator().then(locator => locator.isDisabled(options));
  }
  
  isEditable(options?: { timeout?: number | undefined; } | undefined): Promise<boolean> {
    return this.getLocator().then(locator => locator.isEditable(options));
  }
  
  isEnabled(options?: { timeout?: number | undefined; } | undefined): Promise<boolean> {
    return this.getLocator().then(locator => locator.isEnabled(options));
  }
  
  isHidden(options?: { timeout?: number | undefined; } | undefined): Promise<boolean> {
    return this.getLocator().then(locator => locator.isHidden(options));
  }
  
  isVisible(options?: { timeout?: number | undefined; } | undefined): Promise<boolean> {
    return this.getLocator().then(locator => locator.isVisible(options));
  }
  
  locator(selector: string, options?: { has?: Locator | undefined; hasNot?: Locator | undefined; hasText?: string | RegExp | undefined; hasNotText?: string | RegExp | undefined; } | undefined): Locator {
    return new SelfHealingLocator(this.selfHealingPage, this.page, `${this.originalSelector} >> ${selector}`, {
      strategies: this.strategies,
      healingOptions: this.healingOptions,
      logging: this.logging
    });
  }
  
  nth(index: number): Locator {
    return new SelfHealingLocator(this.selfHealingPage, this.page, `${this.originalSelector} >> nth=${index}`, {
      strategies: this.strategies,
      healingOptions: this.healingOptions,
      logging: this.logging
    });
  }
  
  or(locator: Locator): Locator {
    return new SelfHealingLocator(this.selfHealingPage, this.page, `${this.originalSelector} >> internal:or=${locator}`, {
      strategies: this.strategies,
      healingOptions: this.healingOptions,
      logging: this.logging
    });
  }
  
  page(): Page {
    return this.page;
  }
  
  press(key: string, options?: { delay?: number | undefined; noWaitAfter?: boolean | undefined; timeout?: number | undefined; } | undefined): Promise<void> {
    return this.getLocator().then(locator => locator.press(key, options));
  }
  
  screenshot(options?: { timeout?: number | undefined; type?: "png" | "jpeg" | undefined; path?: string | undefined; quality?: number | undefined; omitBackground?: boolean | undefined; animations?: "disabled" | "allow" | undefined; caret?: "hide" | "initial" | undefined; scale?: "css" | "device" | undefined; mask?: Locator[] | undefined; } | undefined): Promise<Buffer> {
    return this.getLocator().then(locator => locator.screenshot(options));
  }
  
  scrollIntoViewIfNeeded(options?: { timeout?: number | undefined; } | undefined): Promise<void> {
    return this.getLocator().then(locator => locator.scrollIntoViewIfNeeded(options));
  }
  
  selectOption(values: string | string[] | null | ElementHandle<SVGElement | HTMLElement> | ElementHandle<SVGElement | HTMLElement>[] | { value?: string | undefined; label?: string | undefined; index?: number | undefined; } | { value?: string | undefined; label?: string | undefined; index?: number | undefined; }[], options?: { force?: boolean | undefined; noWaitAfter?: boolean | undefined; timeout?: number | undefined; } | undefined): Promise<string[]> {
    return this.getLocator().then(locator => locator.selectOption(values, options));
  }
  
  selectText(options?: { force?: boolean | undefined; timeout?: number | undefined; } | undefined): Promise<void> {
    return this.getLocator().then(locator => locator.selectText(options));
  }
  
  setChecked(checked: boolean, options?: { position?: { x: number; y: number; } | undefined; force?: boolean | undefined; noWaitAfter?: boolean | undefined; timeout?: number | undefined; trial?: boolean | undefined; } | undefined): Promise<void> {
    return this.getLocator().then(locator => locator.setChecked(checked, options));
  }
  
  setInputFiles(files: string | string[] | { name: string; mimeType: string; buffer: Buffer; } | { name: string; mimeType: string; buffer: Buffer; }[], options?: { noWaitAfter?: boolean | undefined; timeout?: number | undefined; } | undefined): Promise<void> {
    return this.getLocator().then(locator => locator.setInputFiles(files, options));
  }
  
  tap(options?: { position?: { x: number; y: number; } | undefined; force?: boolean | undefined; noWaitAfter?: boolean | undefined; timeout?: number | undefined; trial?: boolean | undefined; } | undefined): Promise<void> {
    return this.getLocator().then(locator => locator.tap(options));
  }
  
  textContent(options?: { timeout?: number | undefined; } | undefined): Promise<string | null> {
    return this.getLocator().then(locator => locator.textContent(options));
  }
  
  type(text: string, options?: { delay?: number | undefined; noWaitAfter?: boolean | undefined; timeout?: number | undefined; } | undefined): Promise<void> {
    return this.getLocator().then(locator => locator.type(text, options));
  }
  
  uncheck(options?: { position?: { x: number; y: number; } | undefined; force?: boolean | undefined; noWaitAfter?: boolean | undefined; timeout?: number | undefined; trial?: boolean | undefined; } | undefined): Promise<void> {
    return this.getLocator().then(locator => locator.uncheck(options));
  }
  
  waitFor(options?: { state?: "attached" | "detached" | "visible" | "hidden" | undefined; timeout?: number | undefined; } | undefined): Promise<void> {
    return this.getLocator().then(locator => locator.waitFor(options));
  }
}
