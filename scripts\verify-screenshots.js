/**
 * Screenshot Verification Script
 * 
 * This script verifies that all screenshots are in the correct directory structure.
 * It fails if any screenshots are found outside of the screenshots directory.
 * 
 * Usage:
 * node verify-screenshots.js [--fix]
 * 
 * Options:
 * --fix    Move stray screenshots to the screenshots directory
 * 
 * @module verify-screenshots
 * <AUTHOR> Wolf Team
 * @date 2025-01-01
 */

const { verifyScreenshotStructure, moveStrayScreenshots } = require('../src/utils/screenshot-utils');

async function main() {
  try {
    // Check if we should fix stray screenshots
    const args = process.argv.slice(2);
    const shouldFix = args.includes('--fix');
    
    console.log('Verifying screenshot structure...');
    const isValid = verifyScreenshotStructure();
    
    if (isValid) {
      console.log('Screenshot verification passed: All screenshots are in the correct directory structure');
      return;
    }
    
    if (shouldFix) {
      console.log('Moving stray screenshots to the screenshots directory...');
      const movedCount = await moveStrayScreenshots('stray-screenshots');
      
      if (movedCount > 0) {
        console.log(`Successfully moved ${movedCount} stray screenshots`);
        
        // Verify again after moving
        const isValidAfterFix = verifyScreenshotStructure();
        
        if (isValidAfterFix) {
          console.log('Screenshot verification passed after fixing');
          return;
        } else {
          console.error('Screenshot verification failed even after fixing');
          process.exit(1);
        }
      } else {
        console.error('Failed to move stray screenshots');
        process.exit(1);
      }
    } else {
      console.error('Screenshot verification failed: Run with --fix to move stray screenshots');
      process.exit(1);
    }
  } catch (error) {
    console.error('Screenshot verification failed:', error);
    process.exit(1);
  }
}

// Run the script
main();
