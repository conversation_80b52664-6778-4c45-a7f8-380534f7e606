/**
 * Configuration Loader
 * 
 * This module provides utilities for loading and managing configuration
 * from files or environment variables.
 */

const fs = require('fs');
const path = require('path');
require('dotenv').config();

/**
 * Default configuration values
 */
const defaultConfig = {
  // Environment
  environment: process.env.NODE_ENV || 'development',
  
  // Application URLs
  appUrl: process.env.URL || 'https://app.lidostaging.com',
  
  // Authentication
  email: process.env.EMAIL || '<EMAIL>',
  password: process.env.PASSWORD || 'vhc!tGK289IS&',
  invalidEmail: process.env.INVALID_EMAIL || '<EMAIL>',
  invalidPassword: process.env.INVALID_PASSWORD || 'invalid-password',
  
  // Screenshots
  screenshotDir: process.env.SCREENSHOT_DIR || path.join(process.cwd(), 'screenshots'),
  screenshotRetentionDays: parseInt(process.env.SCREENSHOT_RETENTION_DAYS || '7', 10),
  
  // CI/CD
  isCi: process.env.CI === 'true',
  buildNumber: process.env.CI_BUILD_NUMBER || process.env.GITHUB_RUN_ID || `local-${Date.now()}`,
  
  // Browser
  headless: process.env.HEADLESS === 'true' || process.env.CI === 'true',
  slowMo: parseInt(process.env.SLOW_MO || '0', 10),
  
  // Test
  testTimeout: parseInt(process.env.TEST_TIMEOUT || '60000', 10),
  retryCount: parseInt(process.env.RETRY_COUNT || '2', 10)
};

/**
 * Load configuration from a file
 * 
 * @param {string} [configPath] - Path to the configuration file
 * @returns {Object} - Configuration object
 */
function loadConfigFromFile(configPath) {
  try {
    if (!configPath) {
      // Try to find config file in standard locations
      const possiblePaths = [
        path.join(process.cwd(), 'config.json'),
        path.join(process.cwd(), 'config', 'config.json'),
        path.join(process.cwd(), '.config.json')
      ];
      
      for (const possiblePath of possiblePaths) {
        if (fs.existsSync(possiblePath)) {
          configPath = possiblePath;
          break;
        }
      }
      
      if (!configPath) {
        console.log('No configuration file found, using default configuration');
        return { ...defaultConfig };
      }
    }
    
    if (!fs.existsSync(configPath)) {
      console.warn(`Configuration file not found: ${configPath}, using default configuration`);
      return { ...defaultConfig };
    }
    
    const configContent = fs.readFileSync(configPath, 'utf8');
    const config = JSON.parse(configContent);
    
    return { ...defaultConfig, ...config };
  } catch (error) {
    console.error('Failed to load configuration from file:', error);
    return { ...defaultConfig };
  }
}

/**
 * Get configuration value
 * 
 * @param {string} key - Configuration key
 * @param {*} [defaultValue] - Default value if key is not found
 * @returns {*} - Configuration value
 */
function getConfig(key, defaultValue) {
  if (key in defaultConfig) {
    return defaultConfig[key];
  }
  
  if (process.env[key]) {
    return process.env[key];
  }
  
  return defaultValue;
}

/**
 * Get all configuration values
 * 
 * @returns {Object} - All configuration values
 */
function getAllConfig() {
  return { ...defaultConfig };
}

/**
 * Set configuration value
 * 
 * @param {string} key - Configuration key
 * @param {*} value - Configuration value
 */
function setConfig(key, value) {
  defaultConfig[key] = value;
}

module.exports = {
  loadConfigFromFile,
  getConfig,
  getAllConfig,
  setConfig,
  defaultConfig
};