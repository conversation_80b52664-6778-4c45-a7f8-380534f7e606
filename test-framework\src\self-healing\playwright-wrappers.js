/**
 * Playwright Wrappers
 * 
 * This module provides self-healing wrappers for Playwright methods.
 */

/**
 * Create a self-healing page wrapper
 * @param {Object} page - Playwright page
 * @param {Object} options - Options
 * @param {Object} options.selectorHealer - Selector healer
 * @param {Object} options.recoveryManager - Recovery manager
 * @param {Object} options.feedbackCollector - Feedback collector
 * @param {Object} options.config - Configuration
 * @returns {Object} - Self-healing page wrapper
 */
function createSelfHealingPage(page, options) {
  const {
    selectorHealer,
    recoveryManager,
    feedbackCollector,
    config = {}
  } = options;
  
  // Create a proxy to intercept method calls
  return new Proxy(page, {
    get(target, prop, receiver) {
      const originalMethod = Reflect.get(target, prop, receiver);
      
      // Only wrap functions
      if (typeof originalMethod !== 'function') {
        return originalMethod;
      }
      
      // Methods that take a selector as the first argument
      const selectorMethods = [
        'click', 'dblclick', 'tap', 'fill', 'type', 'press',
        'check', 'uncheck', 'selectOption', 'setInputFiles',
        'focus', 'hover', 'waitForSelector', 'isVisible',
        'isHidden', 'isEnabled', 'isDisabled', 'isEditable',
        'isChecked', 'textContent', 'innerText', 'innerHTML',
        'getAttribute', 'inputValue', 'selectText', 'setChecked',
        'dispatchEvent', 'evaluate', 'evaluateHandle'
      ];
      
      // If this is a selector method, wrap it with self-healing
      if (selectorMethods.includes(prop)) {
        return async function(...args) {
          // The first argument is the selector
          const selector = args[0];
          
          // Skip self-healing if not enabled or no selector
          if (!config.enabled || !selector || typeof selector !== 'string') {
            return originalMethod.apply(target, args);
          }
          
          try {
            // Try with the original selector
            const result = await originalMethod.apply(target, args);
            
            // Track successful selector usage
            if (selectorHealer) {
              await selectorHealer.trackSelectorResult(selector, true, {
                action: prop,
                page: target,
                args
              });
            }
            
            // Collect feedback
            if (feedbackCollector) {
              await feedbackCollector.trackSelectorResult(selector, true, {
                action: prop,
                args
              });
            }
            
            return result;
          } catch (error) {
            console.error(`Error in ${prop}(${selector}):`, error.message);
            
            // Track failed selector usage
            if (selectorHealer) {
              await selectorHealer.trackSelectorResult(selector, false, {
                action: prop,
                page: target,
                args,
                error
              });
            }
            
            // Collect feedback
            if (feedbackCollector) {
              await feedbackCollector.trackSelectorResult(selector, false, {
                action: prop,
                args,
                error
              });
              
              await feedbackCollector.collectEvent(`error:${prop}`, {
                selector,
                action: prop,
                error: {
                  name: error.name,
                  message: error.message,
                  stack: error.stack
                }
              });
            }
            
            // Try to recover
            if (recoveryManager) {
              const recoveryResult = await recoveryManager.recoverFromFailure(error, {
                selector,
                action: prop,
                args,
                page: target,
                selectorHealer,
                error
              });
              
              // Collect recovery feedback
              if (feedbackCollector) {
                await feedbackCollector.trackRecoveryResult(recoveryResult, {
                  selector,
                  action: prop,
                  args
                });
              }
              
              // If recovery was successful, return
              if (recoveryResult.success) {
                console.log(`Successfully recovered from error in ${prop}(${selector})`);
                return;
              }
            }
            
            // If we couldn't recover, throw the original error
            throw error;
          }
        };
      }
      
      // For locator method, wrap it to use self-healing selectors
      if (prop === 'locator') {
        return async function(selector, options) {
          // Skip self-healing if not enabled or no selector
          if (!config.enabled || !selector || typeof selector !== 'string') {
            return originalMethod.call(target, selector, options);
          }
          
          try {
            // Try to optimize the selector
            let healedSelector = selector;
            
            if (selectorHealer) {
              const optimizationResult = await selectorHealer.optimizeSelector(selector, {
                page: target
              });
              
              if (optimizationResult.optimized) {
                console.log(`Using optimized selector: ${optimizationResult.selector} (confidence: ${optimizationResult.confidence})`);
                healedSelector = optimizationResult.selector;
              }
            }
            
            // Create the locator with the healed selector
            const locator = originalMethod.call(target, healedSelector, options);
            
            // Return a proxy to the locator to track method calls
            return createSelfHealingLocator(locator, {
              selectorHealer,
              recoveryManager,
              feedbackCollector,
              config,
              selector: healedSelector,
              originalSelector: selector
            });
          } catch (error) {
            console.error(`Error in locator(${selector}):`, error.message);
            
            // If we couldn't optimize, use the original selector
            const locator = originalMethod.call(target, selector, options);
            
            // Return a proxy to the locator to track method calls
            return createSelfHealingLocator(locator, {
              selectorHealer,
              recoveryManager,
              feedbackCollector,
              config,
              selector,
              originalSelector: selector
            });
          }
        };
      }
      
      // For other methods, just return the original
      return originalMethod;
    }
  });
}

/**
 * Create a self-healing locator wrapper
 * @param {Object} locator - Playwright locator
 * @param {Object} options - Options
 * @returns {Object} - Self-healing locator wrapper
 */
function createSelfHealingLocator(locator, options) {
  const {
    selectorHealer,
    recoveryManager,
    feedbackCollector,
    config = {},
    selector,
    originalSelector
  } = options;
  
  // Create a proxy to intercept method calls
  return new Proxy(locator, {
    get(target, prop, receiver) {
      const originalMethod = Reflect.get(target, prop, receiver);
      
      // Only wrap functions
      if (typeof originalMethod !== 'function') {
        return originalMethod;
      }
      
      // Methods that perform actions
      const actionMethods = [
        'click', 'dblclick', 'tap', 'fill', 'type', 'press',
        'check', 'uncheck', 'selectOption', 'setInputFiles',
        'focus', 'hover', 'waitFor', 'isVisible',
        'isHidden', 'isEnabled', 'isDisabled', 'isEditable',
        'isChecked', 'textContent', 'innerText', 'innerHTML',
        'getAttribute', 'inputValue', 'selectText', 'setChecked',
        'dispatchEvent', 'evaluate', 'evaluateHandle'
      ];
      
      // If this is an action method, wrap it with self-healing
      if (actionMethods.includes(prop)) {
        return async function(...args) {
          // Skip self-healing if not enabled
          if (!config.enabled) {
            return originalMethod.apply(target, args);
          }
          
          try {
            // Try with the current locator
            const result = await originalMethod.apply(target, args);
            
            // Track successful selector usage
            if (selectorHealer) {
              await selectorHealer.trackSelectorResult(selector, true, {
                action: prop,
                locator: target,
                args,
                originalSelector
              });
            }
            
            // Collect feedback
            if (feedbackCollector) {
              await feedbackCollector.trackSelectorResult(selector, true, {
                action: prop,
                args,
                originalSelector
              });
            }
            
            return result;
          } catch (error) {
            console.error(`Error in locator(${selector}).${prop}():`, error.message);
            
            // Track failed selector usage
            if (selectorHealer) {
              await selectorHealer.trackSelectorResult(selector, false, {
                action: prop,
                locator: target,
                args,
                error,
                originalSelector
              });
            }
            
            // Collect feedback
            if (feedbackCollector) {
              await feedbackCollector.trackSelectorResult(selector, false, {
                action: prop,
                args,
                error,
                originalSelector
              });
              
              await feedbackCollector.collectEvent(`error:${prop}`, {
                selector,
                action: prop,
                error: {
                  name: error.name,
                  message: error.message,
                  stack: error.stack
                },
                originalSelector
              });
            }
            
            // Try to recover
            if (recoveryManager) {
              const recoveryResult = await recoveryManager.recoverFromFailure(error, {
                selector,
                action: prop,
                args,
                locator: target,
                selectorHealer,
                error,
                originalSelector
              });
              
              // Collect recovery feedback
              if (feedbackCollector) {
                await feedbackCollector.trackRecoveryResult(recoveryResult, {
                  selector,
                  action: prop,
                  args,
                  originalSelector
                });
              }
              
              // If recovery was successful, return
              if (recoveryResult.success) {
                console.log(`Successfully recovered from error in locator(${selector}).${prop}()`);
                return;
              }
            }
            
            // If we couldn't recover, throw the original error
            throw error;
          }
        };
      }
      
      // For other methods, just return the original
      return originalMethod;
    }
  });
}

module.exports = {
  createSelfHealingPage,
  createSelfHealingLocator
};