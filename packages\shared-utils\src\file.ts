/**
 * File utilities for QA Wolf Metrics Framework
 */

import * as fs from 'fs';
import * as path from 'path';

/**
 * Check if file exists
 * @param filePath File path
 * @returns True if file exists, false otherwise
 */
export function fileExists(filePath: string): boolean {
  try {
    return fs.existsSync(filePath);
  } catch (error) {
    return false;
  }
}

/**
 * Create directory if it doesn't exist
 * @param dirPath Directory path
 */
export function ensureDirectoryExists(dirPath: string): void {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

/**
 * Read file as string
 * @param filePath File path
 * @returns File content as string
 */
export function readFileAsString(filePath: string): string {
  return fs.readFileSync(filePath, 'utf8');
}

/**
 * Read file as JSON
 * @param filePath File path
 * @returns File content as JSON
 */
export function readFileAsJson<T>(filePath: string): T {
  const content = readFileAsString(filePath);
  return JSON.parse(content) as T;
}

/**
 * Write string to file
 * @param filePath File path
 * @param content File content
 */
export function writeStringToFile(filePath: string, content: string): void {
  ensureDirectoryExists(path.dirname(filePath));
  fs.writeFileSync(filePath, content);
}

/**
 * Write JSON to file
 * @param filePath File path
 * @param content File content
 * @param pretty Pretty print JSON
 */
export function writeJsonToFile(filePath: string, content: any, pretty: boolean = true): void {
  const jsonContent = pretty ? JSON.stringify(content, null, 2) : JSON.stringify(content);
  writeStringToFile(filePath, jsonContent);
}

/**
 * Delete file
 * @param filePath File path
 */
export function deleteFile(filePath: string): void {
  if (fileExists(filePath)) {
    fs.unlinkSync(filePath);
  }
}

/**
 * Delete directory
 * @param dirPath Directory path
 * @param recursive Delete recursively
 */
export function deleteDirectory(dirPath: string, recursive: boolean = true): void {
  if (fs.existsSync(dirPath)) {
    fs.rmSync(dirPath, { recursive, force: true });
  }
}

/**
 * List files in directory
 * @param dirPath Directory path
 * @param recursive List files recursively
 * @returns List of file paths
 */
export function listFiles(dirPath: string, recursive: boolean = false): string[] {
  if (!fs.existsSync(dirPath)) {
    return [];
  }
  
  const files: string[] = [];
  const entries = fs.readdirSync(dirPath, { withFileTypes: true });
  
  for (const entry of entries) {
    const entryPath = path.join(dirPath, entry.name);
    
    if (entry.isFile()) {
      files.push(entryPath);
    } else if (entry.isDirectory() && recursive) {
      files.push(...listFiles(entryPath, recursive));
    }
  }
  
  return files;
}

/**
 * List directories in directory
 * @param dirPath Directory path
 * @param recursive List directories recursively
 * @returns List of directory paths
 */
export function listDirectories(dirPath: string, recursive: boolean = false): string[] {
  if (!fs.existsSync(dirPath)) {
    return [];
  }
  
  const directories: string[] = [];
  const entries = fs.readdirSync(dirPath, { withFileTypes: true });
  
  for (const entry of entries) {
    const entryPath = path.join(dirPath, entry.name);
    
    if (entry.isDirectory()) {
      directories.push(entryPath);
      
      if (recursive) {
        directories.push(...listDirectories(entryPath, recursive));
      }
    }
  }
  
  return directories;
}

/**
 * Copy file
 * @param sourcePath Source file path
 * @param destinationPath Destination file path
 */
export function copyFile(sourcePath: string, destinationPath: string): void {
  ensureDirectoryExists(path.dirname(destinationPath));
  fs.copyFileSync(sourcePath, destinationPath);
}

/**
 * Copy directory
 * @param sourcePath Source directory path
 * @param destinationPath Destination directory path
 * @param recursive Copy recursively
 */
export function copyDirectory(sourcePath: string, destinationPath: string, recursive: boolean = true): void {
  if (!fs.existsSync(sourcePath)) {
    return;
  }
  
  ensureDirectoryExists(destinationPath);
  
  const entries = fs.readdirSync(sourcePath, { withFileTypes: true });
  
  for (const entry of entries) {
    const sourceEntryPath = path.join(sourcePath, entry.name);
    const destinationEntryPath = path.join(destinationPath, entry.name);
    
    if (entry.isFile()) {
      copyFile(sourceEntryPath, destinationEntryPath);
    } else if (entry.isDirectory() && recursive) {
      copyDirectory(sourceEntryPath, destinationEntryPath, recursive);
    }
  }
}

/**
 * Get file size
 * @param filePath File path
 * @returns File size in bytes
 */
export function getFileSize(filePath: string): number {
  const stats = fs.statSync(filePath);
  return stats.size;
}

/**
 * Get file modification time
 * @param filePath File path
 * @returns File modification time
 */
export function getFileModificationTime(filePath: string): Date {
  const stats = fs.statSync(filePath);
  return stats.mtime;
}

/**
 * Get file creation time
 * @param filePath File path
 * @returns File creation time
 */
export function getFileCreationTime(filePath: string): Date {
  const stats = fs.statSync(filePath);
  return stats.birthtime;
}

/**
 * Get file extension
 * @param filePath File path
 * @returns File extension
 */
export function getFileExtension(filePath: string): string {
  return path.extname(filePath);
}

/**
 * Get file name
 * @param filePath File path
 * @param withExtension Include extension
 * @returns File name
 */
export function getFileName(filePath: string, withExtension: boolean = true): string {
  return withExtension ? path.basename(filePath) : path.basename(filePath, path.extname(filePath));
}

/**
 * Get directory name
 * @param filePath File path
 * @returns Directory name
 */
export function getDirectoryName(filePath: string): string {
  return path.dirname(filePath);
}
