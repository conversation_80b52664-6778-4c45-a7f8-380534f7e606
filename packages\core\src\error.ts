/**
 * Error module for QA Wolf Metrics Framework
 */

/**
 * Base error class
 */
export class BaseError extends Error {
  constructor(message: string) {
    super(message);
    this.name = this.constructor.name;
    Error.captureStackTrace(this, this.constructor);
  }
}

/**
 * Configuration error
 */
export class ConfigurationError extends BaseError {
  constructor(message: string) {
    super(`Configuration error: ${message}`);
  }
}

/**
 * API error
 */
export class ApiError extends BaseError {
  public statusCode: number;
  public data: any;
  
  constructor(message: string, statusCode: number = 500, data: any = null) {
    super(`API error: ${message}`);
    this.statusCode = statusCode;
    this.data = data;
  }
}

/**
 * Test error
 */
export class TestError extends BaseError {
  public testName: string;
  
  constructor(message: string, testName: string) {
    super(`Test error in ${testName}: ${message}`);
    this.testName = testName;
  }
}

/**
 * Validation error
 */
export class ValidationError extends BaseError {
  public errors: string[];
  
  constructor(message: string, errors: string[] = []) {
    super(`Validation error: ${message}`);
    this.errors = errors;
  }
}

/**
 * Integration error
 */
export class IntegrationError extends BaseError {
  public integration: string;
  
  constructor(message: string, integration: string) {
    super(`Integration error with ${integration}: ${message}`);
    this.integration = integration;
  }
}

/**
 * QA Wolf error
 */
export class QAWolfError extends IntegrationError {
  constructor(message: string) {
    super(message, 'QA Wolf');
  }
}

/**
 * Testmo error
 */
export class TestmoError extends IntegrationError {
  constructor(message: string) {
    super(message, 'Testmo');
  }
}

/**
 * GitHub error
 */
export class GitHubError extends IntegrationError {
  constructor(message: string) {
    super(message, 'GitHub');
  }
}

/**
 * Linear error
 */
export class LinearError extends IntegrationError {
  constructor(message: string) {
    super(message, 'Linear');
  }
}

/**
 * Error handler
 */
export class ErrorHandler {
  private static instance: ErrorHandler;
  
  private constructor() {
    // Set up global error handlers
    process.on('uncaughtException', this.handleUncaughtException.bind(this));
    process.on('unhandledRejection', this.handleUnhandledRejection.bind(this));
  }
  
  /**
   * Get error handler instance
   * @returns ErrorHandler instance
   */
  public static getInstance(): ErrorHandler {
    if (!ErrorHandler.instance) {
      ErrorHandler.instance = new ErrorHandler();
    }
    
    return ErrorHandler.instance;
  }
  
  /**
   * Handle error
   * @param error Error to handle
   */
  public handleError(error: Error): void {
    console.error('Error:', error.message);
    console.error('Stack:', error.stack);
    
    // Log error to file
    const logEntry = {
      timestamp: new Date().toISOString(),
      name: error.name,
      message: error.message,
      stack: error.stack
    };
    
    // TODO: Log error to file
  }
  
  /**
   * Handle uncaught exception
   * @param error Error to handle
   */
  private handleUncaughtException(error: Error): void {
    console.error('Uncaught exception:');
    this.handleError(error);
    
    // Exit process with error code
    process.exit(1);
  }
  
  /**
   * Handle unhandled rejection
   * @param reason Rejection reason
   * @param promise Promise that was rejected
   */
  private handleUnhandledRejection(reason: any, promise: Promise<any>): void {
    console.error('Unhandled rejection:');
    
    if (reason instanceof Error) {
      this.handleError(reason);
    } else {
      console.error('Reason:', reason);
    }
    
    // Exit process with error code
    process.exit(1);
  }
}

// Export default instance
export default ErrorHandler.getInstance();
