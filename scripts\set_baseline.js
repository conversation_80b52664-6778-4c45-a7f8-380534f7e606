/**
 * Set Baseline for Visual Regression Testing
 * 
 * This script sets a baseline for visual regression testing by copying screenshots
 * from a specific test run to a baseline directory.
 * 
 * Usage:
 * node scripts/set_baseline.js --test=test_name --run=run_id [--date=YYYY-MM-DD]
 * 
 * Examples:
 * node scripts/set_baseline.js --test=login_test --run=run-12345
 * node scripts/set_baseline.js --test=login_test --run=run-12345 --date=2025-05-18
 * 
 * @module set_baseline
 * <AUTHOR> Wolf Team
 * @date 2025-01-01
 */

const fs = require('fs');
const path = require('path');
const { format } = require('date-fns');
const { SCREENSHOTS_BASE_DIR } = require('../src/utils/screenshot-utils');

// Parse command line arguments
const args = process.argv.slice(2);
let testName = null;
let runId = null;
let date = null;

args.forEach(arg => {
  if (arg.startsWith('--test=')) {
    testName = arg.substring('--test='.length);
  } else if (arg.startsWith('--run=')) {
    runId = arg.substring('--run='.length);
  } else if (arg.startsWith('--date=')) {
    date = arg.substring('--date='.length);
  }
});

// Validate required parameters
if (!testName || !runId) {
  console.error('Error: Missing required parameters');
  console.log('Usage: node scripts/set_baseline.js --test=test_name --run=run_id [--date=YYYY-MM-DD]');
  process.exit(1);
}

// If no date is specified, use today's date
if (!date) {
  date = format(new Date(), 'yyyy-MM-dd');
}

/**
 * Set baseline for visual regression testing
 * 
 * @param {Object} options - Baseline options
 * @param {string} options.testName - Test name
 * @param {string} options.runId - Run ID
 * @param {string} options.date - Date in YYYY-MM-DD format
 * @returns {Promise<Object>} - Baseline results
 */
async function setBaseline(options) {
  const { testName, runId, date } = options;
  
  console.log(`Setting baseline for test "${testName}" on ${date} using run ${runId}`);
  
  // Get source screenshots
  const sourceDir = path.join(SCREENSHOTS_BASE_DIR, date, runId, testName);
  if (!fs.existsSync(sourceDir)) {
    console.error(`Error: Source directory not found: ${sourceDir}`);
    process.exit(1);
  }
  
  const sourceScreenshots = fs.readdirSync(sourceDir)
    .filter(file => file.endsWith('.png'))
    .map(file => ({
      path: path.join(sourceDir, file),
      filename: file
    }));
  
  console.log(`Found ${sourceScreenshots.length} source screenshots`);
  
  // Create baseline directory
  const baselineDir = path.join(SCREENSHOTS_BASE_DIR, 'baseline', testName);
  if (!fs.existsSync(baselineDir)) {
    fs.mkdirSync(baselineDir, { recursive: true });
  }
  
  // Copy screenshots to baseline directory
  let copiedCount = 0;
  for (const screenshot of sourceScreenshots) {
    const baselinePath = path.join(baselineDir, screenshot.filename);
    fs.copyFileSync(screenshot.path, baselinePath);
    copiedCount++;
  }
  
  console.log(`Copied ${copiedCount} screenshots to baseline directory: ${baselineDir}`);
  
  // Create a metadata file
  const metadataPath = path.join(baselineDir, 'baseline.json');
  const metadata = {
    testName,
    runId,
    date,
    createdAt: new Date().toISOString(),
    screenshots: sourceScreenshots.map(s => s.filename)
  };
  
  fs.writeFileSync(metadataPath, JSON.stringify(metadata, null, 2));
  
  console.log(`Created metadata file: ${metadataPath}`);
  
  return {
    testName,
    runId,
    date,
    baselineDir,
    screenshots: sourceScreenshots.map(s => s.filename)
  };
}

// Set baseline
setBaseline({
  testName,
  runId,
  date
}).catch(error => {
  console.error('Error setting baseline:', error);
  process.exit(1);
});
