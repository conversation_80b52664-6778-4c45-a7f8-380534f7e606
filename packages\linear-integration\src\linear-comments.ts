/**
 * Linear comments for Linear integration
 */

import { LinearClient, Comment, CommentConnection } from '@linear/sdk';
import { LinearCommentsOptions, LinearCommentFilter, LinearCommentCreateInput, LinearCommentUpdateInput } from './types';
import { EventBus, EventType } from '@qawolf/core';

/**
 * Linear comments
 */
export class LinearComments {
  /**
   * Linear client
   */
  private client: LinearClient;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options Linear comments options
   */
  constructor(options: LinearCommentsOptions = {}) {
    if (!options.client && !options.apiKey) {
      throw new Error('Linear client or API key is required');
    }
    
    if (options.client) {
      this.client = options.client;
    } else {
      this.client = new LinearClient({
        apiKey: options.apiKey
      });
    }
    
    this.eventBus = EventBus.getInstance();
  }
  
  /**
   * Get comments
   * @param filter Comment filter
   * @returns Comments
   */
  async getComments(filter?: LinearCommentFilter): Promise<Comment[]> {
    try {
      // Build filter
      const queryFilter: Record<string, any> = {};
      
      if (filter?.id) {
        queryFilter.id = { eq: filter.id };
      }
      
      if (filter?.body) {
        queryFilter.body = { contains: filter.body };
      }
      
      if (filter?.issue) {
        queryFilter.issue = { id: { eq: filter.issue } };
      }
      
      if (filter?.user) {
        queryFilter.user = { id: { eq: filter.user } };
      }
      
      if (filter?.createdAfter) {
        queryFilter.createdAt = { ...queryFilter.createdAt, gt: filter.createdAfter };
      }
      
      if (filter?.createdBefore) {
        queryFilter.createdAt = { ...queryFilter.createdAt, lt: filter.createdBefore };
      }
      
      if (filter?.updatedAfter) {
        queryFilter.updatedAt = { ...queryFilter.updatedAt, gt: filter.updatedAfter };
      }
      
      if (filter?.updatedBefore) {
        queryFilter.updatedAt = { ...queryFilter.updatedAt, lt: filter.updatedBefore };
      }
      
      // Build order by
      const orderBy = filter?.sortBy ? `${filter.sortBy}_${filter.sortOrder || 'ASC'}` : undefined;
      
      // Get comments
      const comments = await this.client.comments({
        filter: queryFilter,
        orderBy
      });
      
      // Get all comments
      const allComments = await this.getAllComments(comments);
      
      // Emit event
      this.eventBus.emit(EventType.LINEAR_COMMENTS_GET_COMMENTS, {
        comments: allComments
      });
      
      return allComments;
    } catch (error) {
      this.handleError(error, 'Failed to get comments');
      return [];
    }
  }
  
  /**
   * Get comment by ID
   * @param id Comment ID
   * @returns Comment
   */
  async getCommentById(id: string): Promise<Comment | null> {
    try {
      const comment = await this.client.comment(id);
      
      // Emit event
      this.eventBus.emit(EventType.LINEAR_COMMENTS_GET_COMMENT_BY_ID, {
        comment
      });
      
      return comment;
    } catch (error) {
      this.handleError(error, `Failed to get comment with ID ${id}`);
      return null;
    }
  }
  
  /**
   * Get comments by issue ID
   * @param issueId Issue ID
   * @returns Comments
   */
  async getCommentsByIssueId(issueId: string): Promise<Comment[]> {
    try {
      const comments = await this.getComments({
        issue: issueId
      });
      
      // Emit event
      this.eventBus.emit(EventType.LINEAR_COMMENTS_GET_COMMENTS_BY_ISSUE_ID, {
        comments,
        issueId
      });
      
      return comments;
    } catch (error) {
      this.handleError(error, `Failed to get comments for issue with ID ${issueId}`);
      return [];
    }
  }
  
  /**
   * Create comment
   * @param input Comment create input
   * @returns Comment
   */
  async createComment(input: LinearCommentCreateInput): Promise<Comment | null> {
    try {
      const comment = await this.client.commentCreate({
        body: input.body,
        issueId: input.issueId
      });
      
      // Emit event
      this.eventBus.emit(EventType.LINEAR_COMMENTS_CREATE_COMMENT, {
        comment: comment.comment
      });
      
      return comment.comment;
    } catch (error) {
      this.handleError(error, 'Failed to create comment');
      return null;
    }
  }
  
  /**
   * Update comment
   * @param id Comment ID
   * @param input Comment update input
   * @returns Comment
   */
  async updateComment(id: string, input: LinearCommentUpdateInput): Promise<Comment | null> {
    try {
      const comment = await this.client.commentUpdate(id, {
        body: input.body
      });
      
      // Emit event
      this.eventBus.emit(EventType.LINEAR_COMMENTS_UPDATE_COMMENT, {
        comment: comment.comment
      });
      
      return comment.comment;
    } catch (error) {
      this.handleError(error, `Failed to update comment with ID ${id}`);
      return null;
    }
  }
  
  /**
   * Delete comment
   * @param id Comment ID
   * @returns True if deleted, false otherwise
   */
  async deleteComment(id: string): Promise<boolean> {
    try {
      const result = await this.client.commentDelete(id);
      
      // Emit event
      this.eventBus.emit(EventType.LINEAR_COMMENTS_DELETE_COMMENT, {
        id,
        success: result.success
      });
      
      return result.success;
    } catch (error) {
      this.handleError(error, `Failed to delete comment with ID ${id}`);
      return false;
    }
  }
  
  /**
   * Get all comments
   * @param comments Comment connection
   * @returns Comments
   */
  private async getAllComments(comments: CommentConnection): Promise<Comment[]> {
    const allComments: Comment[] = [];
    let currentPage = comments;
    
    while (true) {
      const nodes = await currentPage.nodes;
      allComments.push(...nodes);
      
      if (!(await currentPage.hasNextPage)) {
        break;
      }
      
      currentPage = await currentPage.next();
    }
    
    return allComments;
  }
  
  /**
   * Handle error
   * @param error Error
   * @param message Error message
   */
  private handleError(error: any, message: string): void {
    console.error(message, error);
    
    // Emit event
    this.eventBus.emit(EventType.LINEAR_COMMENTS_ERROR, {
      error,
      message
    });
  }
}

/**
 * Create Linear comments
 * @param options Linear comments options
 * @returns Linear comments
 */
export function createLinearComments(options: LinearCommentsOptions = {}): LinearComments {
  return new LinearComments(options);
}
