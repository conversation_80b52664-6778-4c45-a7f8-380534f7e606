/**
 * Linear teams for Linear integration
 */

import { LinearClient, Team, TeamConnection } from '@linear/sdk';
import { LinearTeamsOptions, LinearTeamFilter } from './types';
import { EventBus, EventType } from '@qawolf/core';

/**
 * Linear teams
 */
export class LinearTeams {
  /**
   * Linear client
   */
  private client: LinearClient;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options Linear teams options
   */
  constructor(options: LinearTeamsOptions = {}) {
    if (!options.client && !options.apiKey) {
      throw new Error('Linear client or API key is required');
    }
    
    if (options.client) {
      this.client = options.client;
    } else {
      this.client = new LinearClient({
        apiKey: options.apiKey
      });
    }
    
    this.eventBus = EventBus.getInstance();
  }
  
  /**
   * Get teams
   * @param filter Team filter
   * @returns Teams
   */
  async getTeams(filter?: LinearTeamFilter): Promise<Team[]> {
    try {
      // Build filter
      const queryFilter: Record<string, any> = {};
      
      if (filter?.id) {
        queryFilter.id = { eq: filter.id };
      }
      
      if (filter?.name) {
        queryFilter.name = { contains: filter.name };
      }
      
      if (filter?.key) {
        queryFilter.key = { eq: filter.key };
      }
      
      if (filter?.description) {
        queryFilter.description = { contains: filter.description };
      }
      
      if (filter?.lead) {
        queryFilter.lead = { id: { eq: filter.lead } };
      }
      
      if (filter?.members && filter.members.length > 0) {
        queryFilter.members = { id: { in: filter.members } };
      }
      
      if (filter?.createdAfter) {
        queryFilter.createdAt = { ...queryFilter.createdAt, gt: filter.createdAfter };
      }
      
      if (filter?.createdBefore) {
        queryFilter.createdAt = { ...queryFilter.createdAt, lt: filter.createdBefore };
      }
      
      if (filter?.updatedAfter) {
        queryFilter.updatedAt = { ...queryFilter.updatedAt, gt: filter.updatedAfter };
      }
      
      if (filter?.updatedBefore) {
        queryFilter.updatedAt = { ...queryFilter.updatedAt, lt: filter.updatedBefore };
      }
      
      // Build order by
      const orderBy = filter?.sortBy ? `${filter.sortBy}_${filter.sortOrder || 'ASC'}` : undefined;
      
      // Get teams
      const teams = await this.client.teams({
        filter: queryFilter,
        orderBy
      });
      
      // Get all teams
      const allTeams = await this.getAllTeams(teams);
      
      // Emit event
      this.eventBus.emit(EventType.LINEAR_TEAMS_GET_TEAMS, {
        teams: allTeams
      });
      
      return allTeams;
    } catch (error) {
      this.handleError(error, 'Failed to get teams');
      return [];
    }
  }
  
  /**
   * Get team by ID
   * @param id Team ID
   * @returns Team
   */
  async getTeamById(id: string): Promise<Team | null> {
    try {
      const team = await this.client.team(id);
      
      // Emit event
      this.eventBus.emit(EventType.LINEAR_TEAMS_GET_TEAM_BY_ID, {
        team
      });
      
      return team;
    } catch (error) {
      this.handleError(error, `Failed to get team with ID ${id}`);
      return null;
    }
  }
  
  /**
   * Get team by key
   * @param key Team key
   * @returns Team
   */
  async getTeamByKey(key: string): Promise<Team | null> {
    try {
      const teams = await this.getTeams({
        key
      });
      
      if (teams.length === 0) {
        return null;
      }
      
      // Emit event
      this.eventBus.emit(EventType.LINEAR_TEAMS_GET_TEAM_BY_KEY, {
        team: teams[0]
      });
      
      return teams[0];
    } catch (error) {
      this.handleError(error, `Failed to get team with key ${key}`);
      return null;
    }
  }
  
  /**
   * Get all teams
   * @param teams Team connection
   * @returns Teams
   */
  private async getAllTeams(teams: TeamConnection): Promise<Team[]> {
    const allTeams: Team[] = [];
    let currentPage = teams;
    
    while (true) {
      const nodes = await currentPage.nodes;
      allTeams.push(...nodes);
      
      if (!(await currentPage.hasNextPage)) {
        break;
      }
      
      currentPage = await currentPage.next();
    }
    
    return allTeams;
  }
  
  /**
   * Handle error
   * @param error Error
   * @param message Error message
   */
  private handleError(error: any, message: string): void {
    console.error(message, error);
    
    // Emit event
    this.eventBus.emit(EventType.LINEAR_TEAMS_ERROR, {
      error,
      message
    });
  }
}

/**
 * Create Linear teams
 * @param options Linear teams options
 * @returns Linear teams
 */
export function createLinearTeams(options: LinearTeamsOptions = {}): LinearTeams {
  return new LinearTeams(options);
}
