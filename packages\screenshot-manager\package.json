{"name": "@qawolf/screenshot-manager", "version": "0.1.0", "description": "Screenshot manager for QA Wolf Metrics Framework", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "test": "jest", "lint": "eslint .", "lint:fix": "eslint . --fix"}, "keywords": ["<PERSON><PERSON><PERSON><PERSON>", "testing", "screenshot"], "author": "", "license": "MIT", "dependencies": {"@playwright/test": "^1.52.0", "@qawolf/core": "^0.1.0", "@qawolf/shared-utils": "^0.1.0", "@qawolf/test-framework": "^0.1.0", "image-size": "^1.1.1", "playwright": "^1.52.0", "sharp": "^0.33.3"}, "devDependencies": {"@types/jest": "^29.5.12", "@types/node": "^20.11.30", "jest": "^29.7.0", "ts-jest": "^29.1.2", "typescript": "^5.4.3"}}