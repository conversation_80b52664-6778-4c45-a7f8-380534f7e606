/**
 * Utilities for GitHub integration
 */

import * as dotenv from 'dotenv';
import { GitHubRepository, GitHubBranch, GitHubCommit, GitHubPullRequest, GitHubIssue, GitHubWorkflow, GitHubWorkflowRun, GitHubRelease, GitHubTag, GitHubCheckRun, GitHubCheckSuite, GitHubWorkflowRunStatus, GitHubWorkflowRunConclusion, GitHubCheckRunStatus, GitHubCheckRunConclusion, GitHubIssueState, GitHubPullRequestState, GitHubReleaseType, GitHubPagesBuildStatus } from './types';

// Load environment variables
dotenv.config();

/**
 * Get token from environment
 * @returns API token
 */
export function getTokenFromEnv(): string {
  return process.env.GITHUB_TOKEN || process.env.GITHUB_API_TOKEN || '';
}

/**
 * Get owner from environment
 * @returns Owner
 */
export function getOwnerFromEnv(): string {
  return process.env.GITHUB_OWNER || '';
}

/**
 * Get repository from environment
 * @returns Repository
 */
export function getRepoFromEnv(): string {
  return process.env.GITHUB_REPO || '';
}

/**
 * Get base URL from environment
 * @returns Base URL
 */
export function getBaseUrlFromEnv(): string {
  return process.env.GITHUB_API_URL || 'https://api.github.com';
}

/**
 * Format date
 * @param date Date
 * @returns Formatted date
 */
export function formatDate(date: string | Date): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toISOString();
}

/**
 * Get repository URL
 * @param repository Repository
 * @returns Repository URL
 */
export function getRepositoryURL(repository: GitHubRepository): string {
  return repository.html_url;
}

/**
 * Get repository clone URL
 * @param repository Repository
 * @returns Repository clone URL
 */
export function getRepositoryCloneURL(repository: GitHubRepository): string {
  return repository.clone_url;
}

/**
 * Get repository SSH URL
 * @param repository Repository
 * @returns Repository SSH URL
 */
export function getRepositorySSHURL(repository: GitHubRepository): string {
  return repository.ssh_url;
}

/**
 * Get branch URL
 * @param repository Repository
 * @param branch Branch
 * @returns Branch URL
 */
export function getBranchURL(repository: GitHubRepository, branch: GitHubBranch): string {
  return `${repository.html_url}/tree/${branch.name}`;
}

/**
 * Get commit URL
 * @param repository Repository
 * @param commit Commit
 * @returns Commit URL
 */
export function getCommitURL(repository: GitHubRepository, commit: GitHubCommit): string {
  return `${repository.html_url}/commit/${commit.sha}`;
}

/**
 * Get pull request URL
 * @param repository Repository
 * @param pullRequest Pull request
 * @returns Pull request URL
 */
export function getPullRequestURL(repository: GitHubRepository, pullRequest: GitHubPullRequest): string {
  return `${repository.html_url}/pull/${pullRequest.number}`;
}

/**
 * Get issue URL
 * @param repository Repository
 * @param issue Issue
 * @returns Issue URL
 */
export function getIssueURL(repository: GitHubRepository, issue: GitHubIssue): string {
  return `${repository.html_url}/issues/${issue.number}`;
}

/**
 * Get workflow URL
 * @param repository Repository
 * @param workflow Workflow
 * @returns Workflow URL
 */
export function getWorkflowURL(repository: GitHubRepository, workflow: GitHubWorkflow): string {
  return `${repository.html_url}/actions/workflows/${workflow.id}`;
}

/**
 * Get workflow run URL
 * @param repository Repository
 * @param workflowRun Workflow run
 * @returns Workflow run URL
 */
export function getWorkflowRunURL(repository: GitHubRepository, workflowRun: GitHubWorkflowRun): string {
  return `${repository.html_url}/actions/runs/${workflowRun.id}`;
}

/**
 * Get release URL
 * @param repository Repository
 * @param release Release
 * @returns Release URL
 */
export function getReleaseURL(repository: GitHubRepository, release: GitHubRelease): string {
  return `${repository.html_url}/releases/tag/${release.tag_name}`;
}

/**
 * Get tag URL
 * @param repository Repository
 * @param tag Tag
 * @returns Tag URL
 */
export function getTagURL(repository: GitHubRepository, tag: GitHubTag): string {
  return `${repository.html_url}/tree/${tag.name}`;
}

/**
 * Get check run URL
 * @param repository Repository
 * @param checkRun Check run
 * @returns Check run URL
 */
export function getCheckRunURL(repository: GitHubRepository, checkRun: GitHubCheckRun): string {
  return `${repository.html_url}/runs/${checkRun.id}`;
}

/**
 * Get check suite URL
 * @param repository Repository
 * @param checkSuite Check suite
 * @returns Check suite URL
 */
export function getCheckSuiteURL(repository: GitHubRepository, checkSuite: GitHubCheckSuite): string {
  return `${repository.html_url}/suites/${checkSuite.id}`;
}

/**
 * Get workflow run status
 * @param workflowRun Workflow run
 * @returns Workflow run status
 */
export function getWorkflowRunStatus(workflowRun: GitHubWorkflowRun): GitHubWorkflowRunStatus {
  return workflowRun.status as GitHubWorkflowRunStatus;
}

/**
 * Get workflow run conclusion
 * @param workflowRun Workflow run
 * @returns Workflow run conclusion
 */
export function getWorkflowRunConclusion(workflowRun: GitHubWorkflowRun): GitHubWorkflowRunConclusion | null {
  return workflowRun.conclusion as GitHubWorkflowRunConclusion || null;
}

/**
 * Get check run status
 * @param checkRun Check run
 * @returns Check run status
 */
export function getCheckRunStatus(checkRun: GitHubCheckRun): GitHubCheckRunStatus {
  return checkRun.status as GitHubCheckRunStatus;
}

/**
 * Get check run conclusion
 * @param checkRun Check run
 * @returns Check run conclusion
 */
export function getCheckRunConclusion(checkRun: GitHubCheckRun): GitHubCheckRunConclusion | null {
  return checkRun.conclusion as GitHubCheckRunConclusion || null;
}

/**
 * Get issue state
 * @param issue Issue
 * @returns Issue state
 */
export function getIssueState(issue: GitHubIssue): GitHubIssueState {
  return issue.state as GitHubIssueState;
}

/**
 * Get pull request state
 * @param pullRequest Pull request
 * @returns Pull request state
 */
export function getPullRequestState(pullRequest: GitHubPullRequest): GitHubPullRequestState {
  return pullRequest.state as GitHubPullRequestState;
}

/**
 * Get release type
 * @param release Release
 * @returns Release type
 */
export function getReleaseType(release: GitHubRelease): GitHubReleaseType {
  if (release.draft) {
    return GitHubReleaseType.DRAFT;
  } else if (release.prerelease) {
    return GitHubReleaseType.PRERELEASE;
  } else {
    return GitHubReleaseType.RELEASE;
  }
}

/**
 * Get workflow run duration
 * @param workflowRun Workflow run
 * @returns Workflow run duration in milliseconds
 */
export function getWorkflowRunDuration(workflowRun: GitHubWorkflowRun): number {
  if (workflowRun.created_at && workflowRun.updated_at) {
    return new Date(workflowRun.updated_at).getTime() - new Date(workflowRun.created_at).getTime();
  }
  
  return 0;
}

/**
 * Format workflow run duration
 * @param workflowRun Workflow run
 * @returns Formatted workflow run duration
 */
export function formatWorkflowRunDuration(workflowRun: GitHubWorkflowRun): string {
  const duration = getWorkflowRunDuration(workflowRun);
  
  if (duration < 1000) {
    return `${duration}ms`;
  } else if (duration < 60000) {
    return `${(duration / 1000).toFixed(2)}s`;
  } else {
    const minutes = Math.floor(duration / 60000);
    const seconds = ((duration % 60000) / 1000).toFixed(2);
    return `${minutes}m ${seconds}s`;
  }
}

/**
 * Get workflow run summary
 * @param workflowRun Workflow run
 * @returns Workflow run summary
 */
export function getWorkflowRunSummary(workflowRun: GitHubWorkflowRun): string {
  const status = getWorkflowRunStatus(workflowRun);
  const conclusion = getWorkflowRunConclusion(workflowRun);
  const duration = formatWorkflowRunDuration(workflowRun);
  
  return `Workflow run ${workflowRun.id} (${status}${conclusion ? `, ${conclusion}` : ''}) - ${duration}`;
}

/**
 * Get check run summary
 * @param checkRun Check run
 * @returns Check run summary
 */
export function getCheckRunSummary(checkRun: GitHubCheckRun): string {
  const status = getCheckRunStatus(checkRun);
  const conclusion = getCheckRunConclusion(checkRun);
  
  return `Check run ${checkRun.name} (${status}${conclusion ? `, ${conclusion}` : ''})`;
}

/**
 * Get issue summary
 * @param issue Issue
 * @returns Issue summary
 */
export function getIssueSummary(issue: GitHubIssue): string {
  const state = getIssueState(issue);
  
  return `Issue #${issue.number}: ${issue.title} (${state})`;
}

/**
 * Get pull request summary
 * @param pullRequest Pull request
 * @returns Pull request summary
 */
export function getPullRequestSummary(pullRequest: GitHubPullRequest): string {
  const state = getPullRequestState(pullRequest);
  
  return `Pull request #${pullRequest.number}: ${pullRequest.title} (${state})`;
}

/**
 * Get release summary
 * @param release Release
 * @returns Release summary
 */
export function getReleaseSummary(release: GitHubRelease): string {
  const type = getReleaseType(release);
  
  return `Release ${release.name || release.tag_name} (${type})`;
}

/**
 * Get pages build status summary
 * @param status Pages build status
 * @returns Pages build status summary
 */
export function getPagesBuildStatusSummary(status: GitHubPagesBuildStatus): string {
  switch (status) {
    case GitHubPagesBuildStatus.BUILDING:
      return 'GitHub Pages is building';
    case GitHubPagesBuildStatus.BUILT:
      return 'GitHub Pages has been built';
    case GitHubPagesBuildStatus.ERRORED:
      return 'GitHub Pages build errored';
    default:
      return 'GitHub Pages build status unknown';
  }
}
