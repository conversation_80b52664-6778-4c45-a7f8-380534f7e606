/**
 * QA Wolf Report Generator
 * 
 * This module provides functionality to generate reports from QA Wolf test results.
 */

import * as fs from 'fs';
import * as path from 'path';
import axios from 'axios';
import { EventBus, EventType } from '@qawolf/core';
import { ReportGenerator, ReportFormat, ReportSection, ReportTable, ReportChart, ReportChartType } from '@qawolf/reporting';

/**
 * Report generator options
 */
export interface QAWolfReportGeneratorOptions {
  /**
   * API key
   */
  apiKey?: string;
  
  /**
   * Team ID
   */
  teamId?: string;
  
  /**
   * API URL
   */
  apiUrl?: string;
  
  /**
   * Reports directory
   */
  reportsDir?: string;
  
  /**
   * Environment
   */
  environment?: string;
  
  /**
   * Branch
   */
  branch?: string;
  
  /**
   * Report title
   */
  title?: string;
  
  /**
   * Report description
   */
  description?: string;
  
  /**
   * Report format
   */
  format?: ReportFormat;
  
  /**
   * Report template
   */
  template?: string;
}

/**
 * Test result
 */
export interface TestResult {
  /**
   * Test ID
   */
  id?: string;
  
  /**
   * Test name
   */
  name?: string;
  
  /**
   * Test status
   */
  status: 'passed' | 'failed' | 'skipped';
  
  /**
   * Test duration
   */
  duration?: number;
  
  /**
   * Error message
   */
  errorMessage?: string;
  
  /**
   * Test steps
   */
  steps?: TestStep[];
  
  /**
   * Test attachments
   */
  attachments?: TestAttachment[];
}

/**
 * Test step
 */
export interface TestStep {
  /**
   * Step name
   */
  name?: string;
  
  /**
   * Expected result
   */
  expected?: string;
  
  /**
   * Actual result
   */
  actual?: string;
  
  /**
   * Step status
   */
  status?: 'passed' | 'failed' | 'skipped';
}

/**
 * Test attachment
 */
export interface TestAttachment {
  /**
   * Attachment name
   */
  name?: string;
  
  /**
   * Content type
   */
  contentType?: string;
  
  /**
   * Attachment data
   */
  data?: string;
}

/**
 * Test metrics
 */
export interface TestMetrics {
  /**
   * Total tests
   */
  totalTests: number;
  
  /**
   * Passed tests
   */
  passedTests: number;
  
  /**
   * Failed tests
   */
  failedTests: number;
  
  /**
   * Skipped tests
   */
  skippedTests: number;
  
  /**
   * Pass rate
   */
  passRate: number;
  
  /**
   * Average duration
   */
  averageDuration: number;
  
  /**
   * Timestamp
   */
  timestamp: string;
}

/**
 * QA Wolf report generator
 */
export class QAWolfReportGenerator {
  /**
   * API key
   */
  private apiKey: string;
  
  /**
   * Team ID
   */
  private teamId: string;
  
  /**
   * API URL
   */
  private apiUrl: string;
  
  /**
   * Reports directory
   */
  private reportsDir: string;
  
  /**
   * Environment
   */
  private environment: string;
  
  /**
   * Branch
   */
  private branch: string;
  
  /**
   * Report title
   */
  private title: string;
  
  /**
   * Report description
   */
  private description: string;
  
  /**
   * Report format
   */
  private format: ReportFormat;
  
  /**
   * Report template
   */
  private template: string;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options Report generator options
   */
  constructor(options: QAWolfReportGeneratorOptions = {}) {
    this.apiKey = options.apiKey || process.env.QA_WOLF_API_KEY || '';
    this.teamId = options.teamId || process.env.QA_WOLF_TEAM_ID || 'clux0gjs50sb3ak01fnh7wvja';
    this.apiUrl = options.apiUrl || 'https://app.qawolf.com/api/ci';
    this.reportsDir = options.reportsDir || './qawolf-reports';
    this.environment = options.environment || '';
    this.branch = options.branch || '';
    this.title = options.title || 'QA Wolf Test Report';
    this.description = options.description || 'QA Wolf Test Report';
    this.format = options.format || ReportFormat.HTML;
    this.template = options.template || 'default';
    
    this.eventBus = EventBus.getInstance();
    
    // Create reports directory if it doesn't exist
    if (!fs.existsSync(this.reportsDir)) {
      fs.mkdirSync(this.reportsDir, { recursive: true });
    }
    
    // Load deployment info if environment and branch are not provided
    if (!this.environment || !this.branch) {
      try {
        const deploymentInfo = JSON.parse(
          fs.readFileSync(path.join(this.reportsDir, 'deployment-info.json'), 'utf8')
        );
        
        this.environment = this.environment || deploymentInfo.environment;
        this.branch = this.branch || deploymentInfo.branch;
      } catch (error) {
        console.error('Error loading deployment info:', error.message);
      }
    }
  }
  
  /**
   * Fetch test results
   * @returns Promise<TestResult[]> - Test results
   */
  async fetchTestResults(): Promise<TestResult[]> {
    console.log('Fetching QA Wolf test results...');
    
    if (!this.apiKey) {
      throw new Error('QA Wolf API key is required');
    }
    
    if (!this.teamId) {
      throw new Error('QA Wolf team ID is required');
    }
    
    if (!this.environment) {
      throw new Error('Environment is required');
    }
    
    if (!this.branch) {
      throw new Error('Branch is required');
    }
    
    try {
      const response = await axios.get(
        `${this.apiUrl}/results?teamId=${this.teamId}&environment=${this.environment}&branch=${this.branch}`,
        {
          headers: {
            'Authorization': `Bearer ${this.apiKey}`
          }
        }
      );
      
      const testResults = response.data;
      
      console.log(`Fetched ${testResults.length} test results.`);
      
      // Save raw test results to file
      fs.writeFileSync(
        path.join(this.reportsDir, 'test-results.json'),
        JSON.stringify(testResults, null, 2)
      );
      
      // Emit event
      this.eventBus.emit(EventType.QA_WOLF_REPORT_GENERATOR_FETCH_TEST_RESULTS, {
        testResults
      });
      
      return testResults;
    } catch (error) {
      console.error('Error fetching QA Wolf test results:', error.message);
      
      if (error.response) {
        console.error('Response data:', error.response.data);
        console.error('Response status:', error.response.status);
      }
      
      // Save error to file
      fs.writeFileSync(
        path.join(this.reportsDir, 'fetch-test-results-error.json'),
        JSON.stringify({
          error: error.message,
          response: error.response ? {
            data: error.response.data,
            status: error.response.status
          } : null,
          timestamp: new Date().toISOString()
        }, null, 2)
      );
      
      // Emit event
      this.eventBus.emit(EventType.QA_WOLF_REPORT_GENERATOR_FETCH_TEST_RESULTS_ERROR, {
        error
      });
      
      throw error;
    }
  }
  
  /**
   * Calculate metrics
   * @param testResults Test results
   * @returns Test metrics
   */
  calculateMetrics(testResults: TestResult[]): TestMetrics {
    const totalTests = testResults.length;
    const passedTests = testResults.filter(test => test.status === 'passed').length;
    const failedTests = testResults.filter(test => test.status === 'failed').length;
    const skippedTests = testResults.filter(test => test.status === 'skipped').length;
    
    const passRate = totalTests > 0 ? (passedTests / totalTests) * 100 : 0;
    
    // Calculate average duration
    const durations = testResults
      .filter(test => test.duration)
      .map(test => test.duration || 0);
    
    const averageDuration = durations.length > 0
      ? durations.reduce((sum, duration) => sum + duration, 0) / durations.length
      : 0;
    
    const metrics = {
      totalTests,
      passedTests,
      failedTests,
      skippedTests,
      passRate,
      averageDuration,
      timestamp: new Date().toISOString()
    };
    
    // Save metrics to file
    fs.writeFileSync(
      path.join(this.reportsDir, 'metrics.json'),
      JSON.stringify(metrics, null, 2)
    );
    
    // Emit event
    this.eventBus.emit(EventType.QA_WOLF_REPORT_GENERATOR_CALCULATE_METRICS, {
      metrics
    });
    
    return metrics;
  }
  
  /**
   * Generate report
   * @returns Promise<string> - Report file path
   */
  async generateReport(): Promise<string> {
    console.log('Generating QA Wolf test report...');
    
    try {
      // Fetch test results
      const testResults = await this.fetchTestResults();
      
      // Calculate metrics
      const metrics = this.calculateMetrics(testResults);
      
      // Load greenlight result
      let greenlightResult;
      try {
        greenlightResult = JSON.parse(
          fs.readFileSync(path.join(this.reportsDir, 'greenlight-result.json'), 'utf8')
        );
      } catch (error) {
        console.warn('Warning: Greenlight result not found. Report may be incomplete.');
        greenlightResult = null;
      }
      
      // Create report generator
      const reportGenerator = new ReportGenerator({
        title: this.title,
        description: this.description,
        format: this.format,
        template: this.template,
        outputDir: this.reportsDir,
        filename: 'report',
        date: new Date(),
        author: 'QA Wolf',
        version: '1.0.0',
        data: {
          testResults,
          metrics,
          greenlightResult,
          environment: this.environment,
          branch: this.branch
        }
      });
      
      // Add summary section
      reportGenerator.addSection({
        id: 'summary',
        title: 'Summary',
        description: 'Summary of test results',
        content: `
          <p><strong>Environment:</strong> ${this.environment}</p>
          <p><strong>Branch:</strong> ${this.branch}</p>
          <p><strong>Generated:</strong> ${new Date().toLocaleString()}</p>
          ${greenlightResult ? `
            <div class="greenlight ${greenlightResult.greenlight ? 'passed' : 'failed'}">
              CI Greenlight: ${greenlightResult.greenlight ? 'PASSED' : 'FAILED'}
            </div>
          ` : ''}
        `,
        order: 1,
        visible: true
      });
      
      // Add metrics section
      reportGenerator.addSection({
        id: 'metrics',
        title: 'Metrics',
        description: 'Test metrics',
        charts: [
          {
            id: 'test-results-chart',
            title: 'Test Results',
            description: 'Test results by status',
            type: ReportChartType.PIE,
            data: {
              labels: ['Passed', 'Failed', 'Skipped'],
              datasets: [
                {
                  data: [metrics.passedTests, metrics.failedTests, metrics.skippedTests],
                  backgroundColor: ['#4caf50', '#f44336', '#ff9800']
                }
              ]
            },
            width: 400,
            height: 400,
            order: 1,
            visible: true
          }
        ],
        tables: [
          {
            id: 'metrics-table',
            title: 'Test Metrics',
            description: 'Test metrics',
            headers: ['Metric', 'Value'],
            rows: [
              ['Total Tests', metrics.totalTests.toString()],
              ['Passed Tests', metrics.passedTests.toString()],
              ['Failed Tests', metrics.failedTests.toString()],
              ['Skipped Tests', metrics.skippedTests.toString()],
              ['Pass Rate', `${metrics.passRate.toFixed(2)}%`],
              ['Average Duration', `${(metrics.averageDuration / 1000).toFixed(2)}s`]
            ],
            order: 2,
            visible: true
          }
        ],
        order: 2,
        visible: true
      });
      
      // Add test results section
      reportGenerator.addSection({
        id: 'test-results',
        title: 'Test Results',
        description: 'Detailed test results',
        tables: [
          {
            id: 'test-results-table',
            title: 'Test Results',
            description: 'Detailed test results',
            headers: ['Test Name', 'Status', 'Duration', 'Error Message'],
            rows: testResults.map(test => [
              test.name || 'Unnamed Test',
              test.status,
              test.duration ? `${(test.duration / 1000).toFixed(2)}s` : 'N/A',
              test.errorMessage || ''
            ]),
            order: 1,
            visible: true,
            sortable: true,
            filterable: true
          }
        ],
        order: 3,
        visible: true
      });
      
      // Generate report
      const filePath = await reportGenerator.export();
      
      console.log('QA Wolf test report generated successfully!');
      console.log(`Report saved to: ${filePath}`);
      
      // Emit event
      this.eventBus.emit(EventType.QA_WOLF_REPORT_GENERATOR_GENERATE_REPORT, {
        filePath
      });
      
      return filePath;
    } catch (error) {
      console.error('Error generating QA Wolf test report:', error.message);
      
      // Save error to file
      fs.writeFileSync(
        path.join(this.reportsDir, 'report-error.json'),
        JSON.stringify({
          error: error.message,
          timestamp: new Date().toISOString()
        }, null, 2)
      );
      
      // Emit event
      this.eventBus.emit(EventType.QA_WOLF_REPORT_GENERATOR_GENERATE_REPORT_ERROR, {
        error
      });
      
      throw error;
    }
  }
  
  /**
   * Get API key
   * @returns API key
   */
  getApiKey(): string {
    return this.apiKey;
  }
  
  /**
   * Get team ID
   * @returns Team ID
   */
  getTeamId(): string {
    return this.teamId;
  }
  
  /**
   * Get API URL
   * @returns API URL
   */
  getApiUrl(): string {
    return this.apiUrl;
  }
  
  /**
   * Get reports directory
   * @returns Reports directory
   */
  getReportsDir(): string {
    return this.reportsDir;
  }
  
  /**
   * Get environment
   * @returns Environment
   */
  getEnvironment(): string {
    return this.environment;
  }
  
  /**
   * Get branch
   * @returns Branch
   */
  getBranch(): string {
    return this.branch;
  }
  
  /**
   * Get report title
   * @returns Report title
   */
  getTitle(): string {
    return this.title;
  }
  
  /**
   * Get report description
   * @returns Report description
   */
  getDescription(): string {
    return this.description;
  }
  
  /**
   * Get report format
   * @returns Report format
   */
  getFormat(): ReportFormat {
    return this.format;
  }
  
  /**
   * Get report template
   * @returns Report template
   */
  getTemplate(): string {
    return this.template;
  }
  
  /**
   * Set API key
   * @param apiKey API key
   * @returns This instance for chaining
   */
  setApiKey(apiKey: string): QAWolfReportGenerator {
    this.apiKey = apiKey;
    return this;
  }
  
  /**
   * Set team ID
   * @param teamId Team ID
   * @returns This instance for chaining
   */
  setTeamId(teamId: string): QAWolfReportGenerator {
    this.teamId = teamId;
    return this;
  }
  
  /**
   * Set API URL
   * @param apiUrl API URL
   * @returns This instance for chaining
   */
  setApiUrl(apiUrl: string): QAWolfReportGenerator {
    this.apiUrl = apiUrl;
    return this;
  }
  
  /**
   * Set reports directory
   * @param reportsDir Reports directory
   * @returns This instance for chaining
   */
  setReportsDir(reportsDir: string): QAWolfReportGenerator {
    this.reportsDir = reportsDir;
    return this;
  }
  
  /**
   * Set environment
   * @param environment Environment
   * @returns This instance for chaining
   */
  setEnvironment(environment: string): QAWolfReportGenerator {
    this.environment = environment;
    return this;
  }
  
  /**
   * Set branch
   * @param branch Branch
   * @returns This instance for chaining
   */
  setBranch(branch: string): QAWolfReportGenerator {
    this.branch = branch;
    return this;
  }
  
  /**
   * Set report title
   * @param title Report title
   * @returns This instance for chaining
   */
  setTitle(title: string): QAWolfReportGenerator {
    this.title = title;
    return this;
  }
  
  /**
   * Set report description
   * @param description Report description
   * @returns This instance for chaining
   */
  setDescription(description: string): QAWolfReportGenerator {
    this.description = description;
    return this;
  }
  
  /**
   * Set report format
   * @param format Report format
   * @returns This instance for chaining
   */
  setFormat(format: ReportFormat): QAWolfReportGenerator {
    this.format = format;
    return this;
  }
  
  /**
   * Set report template
   * @param template Report template
   * @returns This instance for chaining
   */
  setTemplate(template: string): QAWolfReportGenerator {
    this.template = template;
    return this;
  }
}

/**
 * Create QA Wolf report generator
 * @param options Report generator options
 * @returns QA Wolf report generator
 */
export function createQAWolfReportGenerator(options: QAWolfReportGeneratorOptions = {}): QAWolfReportGenerator {
  return new QAWolfReportGenerator(options);
}
