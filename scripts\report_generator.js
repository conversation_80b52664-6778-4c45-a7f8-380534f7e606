/**
 * Test Report Generator
 * 
 * This script generates a comprehensive HTML report combining
 * test evaluation and execution results.
 */

const fs = require('fs');
const path = require('path');

// Function to generate HTML report
function generateReport(evaluationResults, executionResults) {
  // Calculate overall evaluation score
  let totalEvalScore = 0;
  evaluationResults.forEach(result => {
    totalEvalScore += result.totalScore;
  });
  const avgEvalScore = totalEvalScore / evaluationResults.length;
  
  // Calculate execution success rate
  const totalTests = executionResults.length;
  const passedTests = executionResults.filter(r => r.success).length;
  const successRate = (passedTests / totalTests) * 100;
  
  // Generate HTML
  const html = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>QA Wolf Test Report</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 0; padding: 20px; color: #333; }
    h1, h2, h3 { color: #2c3e50; }
    .summary { background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
    .summary-item { margin-bottom: 10px; }
    .pass { color: #28a745; }
    .fail { color: #dc3545; }
    .warning { color: #ffc107; }
    .test-details { margin-top: 30px; }
    table { width: 100%; border-collapse: collapse; margin-top: 20px; }
    th, td { padding: 12px 15px; text-align: left; border-bottom: 1px solid #ddd; }
    th { background-color: #f2f2f2; }
    tr:hover { background-color: #f5f5f5; }
    .progress-container { width: 100%; background-color: #e0e0e0; border-radius: 4px; }
    .progress-bar { height: 20px; border-radius: 4px; }
    .progress-bar.good { background-color: #28a745; }
    .progress-bar.warning { background-color: #ffc107; }
    .progress-bar.poor { background-color: #dc3545; }
    .details-list { list-style-type: none; padding-left: 0; }
    .details-list li { margin-bottom: 5px; }
  </style>
</head>
<body>
  <h1>QA Wolf Test Report</h1>
  
  <div class="summary">
    <h2>Summary</h2>
    <div class="summary-item">
      <strong>Code Quality Score:</strong> 
      <span class="${avgEvalScore >= 90 ? 'pass' : avgEvalScore >= 70 ? 'warning' : 'fail'}">
        ${avgEvalScore.toFixed(2)}%
      </span>
      <div class="progress-container">
        <div class="progress-bar ${avgEvalScore >= 90 ? 'good' : avgEvalScore >= 70 ? 'warning' : 'poor'}" 
             style="width: ${avgEvalScore}%"></div>
      </div>
    </div>
    <div class="summary-item">
      <strong>Test Execution Success:</strong> 
      <span class="${successRate >= 90 ? 'pass' : successRate >= 70 ? 'warning' : 'fail'}">
        ${successRate.toFixed(2)}%
      </span>
      <div class="progress-container">
        <div class="progress-bar ${successRate >= 90 ? 'good' : successRate >= 70 ? 'warning' : 'poor'}" 
             style="width: ${successRate}%"></div>
      </div>
    </div>
    <div class="summary-item">
      <strong>Tests Evaluated:</strong> ${evaluationResults.length}
    </div>
    <div class="summary-item">
      <strong>Tests Executed:</strong> ${executionResults.length}
    </div>
    <div class="summary-item">
      <strong>Tests Passed:</strong> ${passedTests}
    </div>
    <div class="summary-item">
      <strong>Overall Status:</strong> 
      <span class="${avgEvalScore >= 90 && successRate >= 90 ? 'pass' : 'fail'}">
        ${avgEvalScore >= 90 && successRate >= 90 ? 'PASSED' : 'FAILED'}
      </span>
    </div>
  </div>
  
  <div class="test-details">
    <h2>Test Details</h2>
    <table>
      <thead>
        <tr>
          <th>Test File</th>
          <th>Quality Score</th>
          <th>Execution Status</th>
          <th>Details</th>
        </tr>
      </thead>
      <tbody>
        ${evaluationResults.map(eval => {
          const exec = executionResults.find(e => e.file === eval.file);
          return `
            <tr>
              <td>${eval.file}</td>
              <td class="${eval.totalScore >= 90 ? 'pass' : eval.totalScore >= 70 ? 'warning' : 'fail'}">
                ${eval.totalScore}%
              </td>
              <td class="${exec && exec.success ? 'pass' : 'fail'}">
                ${exec ? (exec.success ? 'PASSED' : 'FAILED') : 'NOT RUN'}
              </td>
              <td>
                <details>
                  <summary>View Details</summary>
                  <ul class="details-list">
                    ${eval.details.map(detail => `<li>${detail}</li>`).join('')}
                  </ul>
                </details>
              </td>
            </tr>
          `;
        }).join('')}
      </tbody>
    </table>
  </div>
  
  <div class="recommendations">
    <h2>Recommendations</h2>
    <ul>
      ${avgEvalScore < 90 ? `
        <li>Improve AAA pattern implementation in tests</li>
        <li>Add proper assertions to verify test outcomes</li>
        <li>Implement proper error handling with try/catch blocks</li>
        <li>Use centralized selectors for better maintainability</li>
        <li>Add proper documentation to all test files</li>
      ` : '<li>Code quality meets the required standards</li>'}
      
      ${successRate < 90 ? `
        <li>Fix failing tests to improve execution success rate</li>
        <li>Implement more robust selectors to handle UI changes</li>
        <li>Add retry logic for flaky tests</li>
        <li>Improve error handling in test execution</li>
      ` : '<li>Test execution meets the required standards</li>'}
    </ul>
  </div>
  
  <footer style="margin-top: 50px; text-align: center; color: #777;">
    <p>Generated on ${new Date().toLocaleString()}</p>
  </footer>
</body>
</html>
  `;
  
  return html;
}

// Main function
async function main() {
  // Create reports directory if it doesn't exist
  const reportsDir = path.join(__dirname, '..', 'reports');
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir, { recursive: true });
  }
  
  // Check if evaluation results exist
  const evalResultsPath = path.join(reportsDir, 'test-evaluation-results.json');
  if (!fs.existsSync(evalResultsPath)) {
    console.error('Evaluation results not found. Run test_evaluator.js first.');
    return;
  }
  
  // Check if execution results exist
  const execResultsPath = path.join(reportsDir, 'test-execution-results.json');
  if (!fs.existsSync(execResultsPath)) {
    console.error('Execution results not found. Run test_runner.js first.');
    return;
  }
  
  // Read evaluation results
  const evaluationResults = JSON.parse(
    fs.readFileSync(evalResultsPath, 'utf8')
  );
  
  // Read execution results
  const executionResults = JSON.parse(
    fs.readFileSync(execResultsPath, 'utf8')
  );
  
  // Generate report
  const reportHtml = generateReport(evaluationResults, executionResults);
  
  // Write report to file
  const reportPath = path.join(reportsDir, 'aaa-compliance-report.html');
  fs.writeFileSync(reportPath, reportHtml);
  
  console.log(`Report generated: ${reportPath}`);
}

// Export functions for use in other scripts
module.exports = {
  generateReport,
  main
};

// Run the script if executed directly
if (require.main === module) {
  main().catch(console.error);
}
