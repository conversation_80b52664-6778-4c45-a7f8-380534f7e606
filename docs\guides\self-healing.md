# Self-Healing Automation

This guide explains how to use self-healing automation in the QA Wolf testing framework.

## What is Self-Healing Automation?

Self-healing automation is a technology that enables tests to automatically recover from failures and adapt to changes in the application under test. In the context of the QA Wolf testing framework, self-healing automation is used to heal broken selectors, recover from failures, and learn from feedback.

## Self-Healing Components

The QA Wolf testing framework includes the following self-healing components:

- **Selector Healing**: Automatically heals broken selectors.
- **Recovery**: Automatically recovers from failures.
- **Feedback Collection**: Collects feedback from test runs.
- **Learning**: Learns from feedback to improve reliability.

## Creating a Self-Healing Controller

To use self-healing automation, you need to create a self-healing controller:

```javascript
const { createSelfHealingController } = require('@qawolf/test-framework');

const selfHealingController = createSelfHealingController({
  selectorHealing: {
    enabled: true,
    maxAttempts: 3,
    strategies: ['css-relaxation', 'attribute-based', 'text-based', 'xpath']
  },
  recovery: {
    enabled: true,
    maxAttempts: 3,
    strategies: ['retry', 'wait', 'selector', 'refresh', 'screenshot']
  },
  feedbackCollection: {
    enabled: true,
    collectScreenshots: true,
    persistFeedback: true
  },
  learning: {
    enabled: true,
    autoOptimize: true
  }
});
```

The `createSelfHealingController` function accepts the following options:

- `selectorHealing` (Object, optional): Configuration for selector healing.
  - `enabled` (boolean, default: true): Whether to enable selector healing.
  - `maxAttempts` (number, default: 3): Maximum number of attempts to heal a selector.
  - `strategies` (Array<string>, default: ['css-relaxation', 'attribute-based', 'text-based', 'xpath']): Strategies to use for healing selectors.
- `recovery` (Object, optional): Configuration for recovery.
  - `enabled` (boolean, default: true): Whether to enable recovery.
  - `maxAttempts` (number, default: 3): Maximum number of attempts to recover from a failure.
  - `strategies` (Array<string>, default: ['retry', 'wait', 'selector', 'refresh', 'screenshot']): Strategies to use for recovery.
- `feedbackCollection` (Object, optional): Configuration for feedback collection.
  - `enabled` (boolean, default: true): Whether to enable feedback collection.
  - `collectScreenshots` (boolean, default: true): Whether to collect screenshots.
  - `persistFeedback` (boolean, default: true): Whether to persist feedback.
- `learning` (Object, optional): Configuration for learning.
  - `enabled` (boolean, default: true): Whether to enable learning.
  - `autoOptimize` (boolean, default: true): Whether to automatically optimize based on learning.

## Creating a Self-Healing Page

Once you have a self-healing controller, you can create a self-healing page wrapper around a Playwright page:

```javascript
const selfHealingPage = selfHealingController.createPage(page);
```

The self-healing page wrapper provides the same API as a Playwright page, but with self-healing capabilities.

## Starting and Ending a Test Run

To enable feedback collection and learning, you should start a test run at the beginning of your test and end it at the end:

```javascript
// Start a test run
await selfHealingController.startTest({
  testId: 'login-test',
  testName: 'Login Test'
});

// ... test code ...

// End the test run
await selfHealingController.endTest({
  success: true
});
```

The `startTest` function accepts the following parameters:

- `testId` (string): A unique identifier for the test.
- `testName` (string): A human-readable name for the test.

The `endTest` function accepts the following parameters:

- `success` (boolean): Whether the test was successful.
- `error` (Error, optional): The error that caused the test to fail.

## Using Self-Healing Capabilities

Once you have a self-healing page, you can use it just like a regular Playwright page:

```javascript
// Navigate to the app
await selfHealingPage.goto('https://app.lidostaging.com');

// Fill in login form
await selfHealingPage.fill('[data-test-id="SignInEmail"]', '<EMAIL>');
await selfHealingPage.fill('[data-test-id="SignInPassword"]', 'vhc!tGK289IS&');

// Click the login button
await selfHealingPage.click(':text("Log in with email")');

// Wait for login to complete
await selfHealingPage.waitForSelector('div[class*="FilesTable"]', { timeout: 15000 });
```

If any of these operations fail due to a broken selector or other issue, the self-healing controller will attempt to recover automatically.

## Tracking Performance

You can track performance metrics during your test:

```javascript
const { test } = require('@qawolf/test-framework');

// Create a performance tracker
const performanceTracker = new test.PerformanceTracker();

// Start an operation
performanceTracker.startOperation('login');

// ... login code ...

// End the operation
performanceTracker.endOperation();

// Track performance metrics
await selfHealingController.trackPerformance(performanceTracker.getMetrics());
```

The `trackPerformance` function accepts a performance metrics object, which is typically obtained from a `PerformanceTracker` instance.

## Generating Reports

You can generate reports from the feedback collected during test runs:

```javascript
// Generate a report
const report = await selfHealingController.generateReport({
  format: 'json'
});

console.log(report);
```

The `generateReport` function accepts the following options:

- `format` (string, default: 'json'): The format of the report ('json' or 'markdown').

## Analyzing Feedback

You can analyze the feedback collected during test runs to identify patterns and issues:

```javascript
// Analyze feedback
const analysis = await selfHealingController.analyzeFeedback();

console.log(analysis);
```

The `analyzeFeedback` function returns an analysis of the feedback collected during test runs, including:

- Selector reliability
- Recovery success rates
- Performance metrics
- Common failure patterns

## Suggesting Improvements

You can get suggestions for improving your test scripts:

```javascript
// Suggest improvements
const suggestions = await selfHealingController.suggestImprovements('tests/login.spec.js');

console.log(suggestions);
```

The `suggestImprovements` function accepts a test script path and returns suggestions for improving the script, including:

- Selector improvements
- Recovery strategy improvements
- Performance optimizations
- Reliability improvements

## Cleaning Up

When you're done with the self-healing controller, you should clean up its resources:

```javascript
await selfHealingController.cleanup();
```

This will release any resources used by the self-healing controller.

## Best Practices

Here are some best practices for using self-healing automation:

1. **Create the self-healing controller at the beginning of your test**: This ensures that the self-healing controller is available throughout your test.

2. **Start and end test runs**: This enables feedback collection and learning.

3. **Track performance metrics**: This provides valuable information for optimizing your tests.

4. **Clean up resources at the end of your test**: This prevents resource leaks.

5. **Use multiple selector healing strategies**: Different strategies work better for different types of selectors.

6. **Use multiple recovery strategies**: Different strategies work better for different types of failures.

## Conclusion

Self-healing automation provides powerful capabilities for healing broken selectors, recovering from failures, and learning from feedback. By using these capabilities, you can create more reliable and maintainable tests.