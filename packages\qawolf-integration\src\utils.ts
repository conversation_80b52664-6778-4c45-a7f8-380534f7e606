/**
 * Utilities for QA Wolf integration
 */

import * as fs from 'fs';
import * as path from 'path';
import * as dotenv from 'dotenv';
import { QAWolfTest, QAWolfRun, QAWolfArtifact, QAWolfDeployment, QAWolfCIRun, QAWolfCIGreenlight, QAWolfMetrics as QAWolfMetricsType, QAWolfReport } from './types';

// Load environment variables
dotenv.config();

/**
 * Get API key from environment
 * @returns API key
 */
export function getAPIKeyFromEnv(): string {
  return process.env.QAWOLF_API_KEY || '';
}

/**
 * Get team ID from environment
 * @returns Team ID
 */
export function getTeamIdFromEnv(): string {
  return process.env.QAWOLF_TEAM_ID || '';
}

/**
 * Get API URL from environment
 * @returns API URL
 */
export function getAPIUrlFromEnv(): string {
  return process.env.QAWOLF_API_URL || 'https://app.qawolf.com/api';
}

/**
 * Format duration
 * @param duration Duration in milliseconds
 * @returns Formatted duration
 */
export function formatDuration(duration: number): string {
  if (duration < 1000) {
    return `${duration}ms`;
  } else if (duration < 60000) {
    return `${(duration / 1000).toFixed(2)}s`;
  } else {
    const minutes = Math.floor(duration / 60000);
    const seconds = ((duration % 60000) / 1000).toFixed(2);
    return `${minutes}m ${seconds}s`;
  }
}

/**
 * Format date
 * @param date Date
 * @returns Formatted date
 */
export function formatDate(date: string | Date): string {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toISOString();
}

/**
 * Format percentage
 * @param value Value
 * @param decimals Decimal places
 * @returns Formatted percentage
 */
export function formatPercentage(value: number, decimals: number = 2): string {
  return `${(value * 100).toFixed(decimals)}%`;
}

/**
 * Get test status
 * @param test Test
 * @returns Test status
 */
export function getTestStatus(test: QAWolfTest): string {
  return test.status;
}

/**
 * Get run status
 * @param run Run
 * @returns Run status
 */
export function getRunStatus(run: QAWolfRun): string {
  return run.status;
}

/**
 * Get CI run status
 * @param ciRun CI run
 * @returns CI run status
 */
export function getCIRunStatus(ciRun: QAWolfCIRun): string {
  return ciRun.status;
}

/**
 * Get CI greenlight status
 * @param ciGreenlight CI greenlight
 * @returns CI greenlight status
 */
export function getCIGreenlightStatus(ciGreenlight: QAWolfCIGreenlight): string {
  return ciGreenlight.status;
}

/**
 * Get CI greenlight result
 * @param ciGreenlight CI greenlight
 * @returns CI greenlight result
 */
export function getCIGreenlightResult(ciGreenlight: QAWolfCIGreenlight): boolean {
  return ciGreenlight.result;
}

/**
 * Get run duration
 * @param run Run
 * @returns Run duration in milliseconds
 */
export function getRunDuration(run: QAWolfRun): number {
  if (run.duration) {
    return run.duration;
  }
  
  if (run.startedAt && run.completedAt) {
    return new Date(run.completedAt).getTime() - new Date(run.startedAt).getTime();
  }
  
  return 0;
}

/**
 * Get CI run duration
 * @param ciRun CI run
 * @returns CI run duration in milliseconds
 */
export function getCIRunDuration(ciRun: QAWolfCIRun): number {
  if (ciRun.duration) {
    return ciRun.duration;
  }
  
  if (ciRun.startedAt && ciRun.completedAt) {
    return new Date(ciRun.completedAt).getTime() - new Date(ciRun.startedAt).getTime();
  }
  
  return 0;
}

/**
 * Get artifact URL
 * @param artifact Artifact
 * @returns Artifact URL
 */
export function getArtifactURL(artifact: QAWolfArtifact): string {
  return artifact.url;
}

/**
 * Get artifact type
 * @param artifact Artifact
 * @returns Artifact type
 */
export function getArtifactType(artifact: QAWolfArtifact): string {
  return artifact.type;
}

/**
 * Get deployment URL
 * @param deployment Deployment
 * @returns Deployment URL
 */
export function getDeploymentURL(deployment: QAWolfDeployment): string {
  return deployment.url;
}

/**
 * Get deployment environment
 * @param deployment Deployment
 * @returns Deployment environment
 */
export function getDeploymentEnvironment(deployment: QAWolfDeployment): string {
  return deployment.environment;
}

/**
 * Get report URL
 * @param report Report
 * @returns Report URL
 */
export function getReportURL(report: QAWolfReport): string {
  return report.url;
}

/**
 * Get report type
 * @param report Report
 * @returns Report type
 */
export function getReportType(report: QAWolfReport): string {
  return report.type;
}

/**
 * Get report format
 * @param report Report
 * @returns Report format
 */
export function getReportFormat(report: QAWolfReport): string {
  return report.format;
}

/**
 * Get metrics duration
 * @param metrics Metrics
 * @returns Metrics duration in milliseconds
 */
export function getMetricsDuration(metrics: QAWolfMetricsType): number {
  return metrics.duration;
}

/**
 * Get metrics pass rate
 * @param metrics Metrics
 * @returns Metrics pass rate
 */
export function getMetricsPassRate(metrics: QAWolfMetricsType): number {
  return metrics.passRate;
}

/**
 * Get metrics flakiness rate
 * @param metrics Metrics
 * @returns Metrics flakiness rate
 */
export function getMetricsFlakinessRate(metrics: QAWolfMetricsType): number {
  return metrics.flakinessRate;
}

/**
 * Get metrics execution time
 * @param metrics Metrics
 * @returns Metrics execution time
 */
export function getMetricsExecutionTime(metrics: QAWolfMetricsType): number {
  return metrics.executionTime;
}

/**
 * Get metrics resource usage
 * @param metrics Metrics
 * @returns Metrics resource usage
 */
export function getMetricsResourceUsage(metrics: QAWolfMetricsType): { cpu: number; memory: number; network: number } {
  return metrics.resourceUsage;
}

/**
 * Save test code to file
 * @param testId Test ID
 * @param code Test code
 * @param outputDir Output directory
 * @returns File path
 */
export function saveTestCodeToFile(testId: string, code: string, outputDir: string = 'tests'): string {
  // Create output directory if it doesn't exist
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  
  // Create file path
  const filePath = path.join(outputDir, `${testId}.js`);
  
  // Write code to file
  fs.writeFileSync(filePath, code);
  
  return filePath;
}

/**
 * Load test code from file
 * @param filePath File path
 * @returns Test code
 */
export function loadTestCodeFromFile(filePath: string): string {
  // Check if file exists
  if (!fs.existsSync(filePath)) {
    throw new Error(`File ${filePath} does not exist`);
  }
  
  // Read code from file
  return fs.readFileSync(filePath, 'utf-8');
}

/**
 * Save artifact to file
 * @param artifact Artifact
 * @param outputDir Output directory
 * @returns File path
 */
export async function saveArtifactToFile(artifact: QAWolfArtifact, outputDir: string = 'artifacts'): Promise<string> {
  // Create output directory if it doesn't exist
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  
  // Create file path
  const filePath = path.join(outputDir, artifact.name);
  
  // Fetch artifact content
  const response = await fetch(artifact.url);
  const buffer = await response.arrayBuffer();
  
  // Write content to file
  fs.writeFileSync(filePath, Buffer.from(buffer));
  
  return filePath;
}

/**
 * Save report to file
 * @param report Report
 * @param outputDir Output directory
 * @returns File path
 */
export async function saveReportToFile(report: QAWolfReport, outputDir: string = 'reports'): Promise<string> {
  // Create output directory if it doesn't exist
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }
  
  // Create file path
  const extension = report.format === 'html' ? 'html' : report.format === 'json' ? 'json' : report.format === 'csv' ? 'csv' : 'pdf';
  const filePath = path.join(outputDir, `${report.name}.${extension}`);
  
  // Fetch report content
  const response = await fetch(report.url);
  const buffer = await response.arrayBuffer();
  
  // Write content to file
  fs.writeFileSync(filePath, Buffer.from(buffer));
  
  return filePath;
}
