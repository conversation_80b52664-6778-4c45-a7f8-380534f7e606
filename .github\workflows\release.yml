name: Release

on:
  release:
    types: [created]

jobs:
  build:
    name: Build and Test
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: 20
        cache: 'npm'
    
    - name: Install dependencies
      run: |
        npm ci
        npm run bootstrap
    
    - name: Lint
      run: npm run lint
    
    - name: Build
      run: npm run build
    
    - name: Test
      run: npm test
  
  publish:
    name: Publish to npm
    runs-on: ubuntu-latest
    needs: build
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: 20
        cache: 'npm'
        registry-url: 'https://registry.npmjs.org'
    
    - name: Install dependencies
      run: |
        npm ci
        npm run bootstrap
    
    - name: Build
      run: npm run build
    
    - name: Extract version from tag
      id: get_version
      run: echo "VERSION=${GITHUB_REF#refs/tags/v}" >> $GITHUB_ENV
    
    - name: Update package versions
      run: node scripts/deploy/version-packages.js $VERSION
    
    - name: Publish packages
      run: node scripts/deploy/deploy-packages.js
      env:
        NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
  
  docs:
    name: Publish Documentation
    runs-on: ubuntu-latest
    needs: publish
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: 20
        cache: 'npm'
    
    - name: Install dependencies
      run: |
        npm ci
        npm run bootstrap
    
    - name: Build documentation
      run: npm run docs:build
    
    - name: Deploy to GitHub Pages
      uses: JamesIves/github-pages-deploy-action@v4
      with:
        folder: docs/build
        branch: gh-pages