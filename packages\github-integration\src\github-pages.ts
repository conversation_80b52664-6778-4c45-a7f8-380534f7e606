/**
 * GitHub pages for GitHub integration
 */

import { Octokit } from '@octokit/rest';
import { GitHubPagesOptions, GitHubPagesBuildStatus } from './types';
import { EventBus, EventType } from '@qawolf/core';

/**
 * GitHub pages
 */
export class GitHubPages {
  /**
   * API token
   */
  private token: string;
  
  /**
   * Owner
   */
  private owner: string;
  
  /**
   * Repository
   */
  private repo: string;
  
  /**
   * Base URL
   */
  private baseUrl: string;
  
  /**
   * Timeout in milliseconds
   */
  private timeout: number;
  
  /**
   * Octokit instance
   */
  private octokit: Octokit;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options GitHub pages options
   */
  constructor(options: GitHubPagesOptions = {}) {
    this.token = options.token || '';
    this.owner = options.owner || '';
    this.repo = options.repo || '';
    this.baseUrl = options.baseUrl || 'https://api.github.com';
    this.timeout = options.timeout || 30000;
    
    this.octokit = new Octokit({
      auth: this.token,
      baseUrl: this.baseUrl,
      request: {
        timeout: this.timeout
      }
    });
    
    this.eventBus = EventBus.getInstance();
  }
  
  /**
   * Get pages
   * @param owner Owner
   * @param repo Repository
   * @returns Pages
   */
  async getPages(owner?: string, repo?: string): Promise<any | null> {
    try {
      const response = await this.octokit.repos.getPages({
        owner: owner || this.owner,
        repo: repo || this.repo
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_PAGES_GET_PAGES, {
        pages: response.data
      });
      
      return response.data;
    } catch (error) {
      this.handleError(error, `Failed to get pages for repository ${owner || this.owner}/${repo || this.repo}`);
      return null;
    }
  }
  
  /**
   * Get pages builds
   * @param owner Owner
   * @param repo Repository
   * @returns Pages builds
   */
  async getPagesBuilds(owner?: string, repo?: string): Promise<any[]> {
    try {
      const response = await this.octokit.repos.listPagesBuilds({
        owner: owner || this.owner,
        repo: repo || this.repo
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_PAGES_GET_PAGES_BUILDS, {
        pagesBuilds: response.data
      });
      
      return response.data;
    } catch (error) {
      this.handleError(error, `Failed to get pages builds for repository ${owner || this.owner}/${repo || this.repo}`);
      return [];
    }
  }
  
  /**
   * Get latest pages build
   * @param owner Owner
   * @param repo Repository
   * @returns Latest pages build
   */
  async getLatestPagesBuild(owner?: string, repo?: string): Promise<any | null> {
    try {
      const response = await this.octokit.repos.getLatestPagesBuild({
        owner: owner || this.owner,
        repo: repo || this.repo
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_PAGES_GET_LATEST_PAGES_BUILD, {
        pagesBuild: response.data
      });
      
      return response.data;
    } catch (error) {
      this.handleError(error, `Failed to get latest pages build for repository ${owner || this.owner}/${repo || this.repo}`);
      return null;
    }
  }
  
  /**
   * Get pages build
   * @param buildId Build ID
   * @param owner Owner
   * @param repo Repository
   * @returns Pages build
   */
  async getPagesBuild(buildId: number, owner?: string, repo?: string): Promise<any | null> {
    try {
      const response = await this.octokit.repos.getPagesBuild({
        owner: owner || this.owner,
        repo: repo || this.repo,
        build_id: buildId
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_PAGES_GET_PAGES_BUILD, {
        pagesBuild: response.data
      });
      
      return response.data;
    } catch (error) {
      this.handleError(error, `Failed to get pages build ${buildId} for repository ${owner || this.owner}/${repo || this.repo}`);
      return null;
    }
  }
  
  /**
   * Create pages build
   * @param owner Owner
   * @param repo Repository
   * @returns Pages build
   */
  async createPagesBuild(owner?: string, repo?: string): Promise<any | null> {
    try {
      const response = await this.octokit.repos.requestPagesBuild({
        owner: owner || this.owner,
        repo: repo || this.repo
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_PAGES_CREATE_PAGES_BUILD, {
        pagesBuild: response.data
      });
      
      return response.data;
    } catch (error) {
      this.handleError(error, `Failed to create pages build for repository ${owner || this.owner}/${repo || this.repo}`);
      return null;
    }
  }
  
  /**
   * Enable pages
   * @param source Source
   * @param branch Branch
   * @param path Path
   * @param owner Owner
   * @param repo Repository
   * @returns True if enabled, false otherwise
   */
  async enablePages(source: 'gh-pages' | 'master' | 'master/docs', branch?: string, path?: string, owner?: string, repo?: string): Promise<boolean> {
    try {
      await this.octokit.repos.enablePages({
        owner: owner || this.owner,
        repo: repo || this.repo,
        source: {
          branch: branch || source.split('/')[0],
          path: path || (source.includes('/') ? source.split('/')[1] : '')
        }
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_PAGES_ENABLE_PAGES, {
        source,
        branch,
        path
      });
      
      return true;
    } catch (error) {
      this.handleError(error, `Failed to enable pages for repository ${owner || this.owner}/${repo || this.repo}`);
      return false;
    }
  }
  
  /**
   * Disable pages
   * @param owner Owner
   * @param repo Repository
   * @returns True if disabled, false otherwise
   */
  async disablePages(owner?: string, repo?: string): Promise<boolean> {
    try {
      await this.octokit.repos.disablePages({
        owner: owner || this.owner,
        repo: repo || this.repo
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_PAGES_DISABLE_PAGES, {});
      
      return true;
    } catch (error) {
      this.handleError(error, `Failed to disable pages for repository ${owner || this.owner}/${repo || this.repo}`);
      return false;
    }
  }
  
  /**
   * Update pages
   * @param branch Branch
   * @param path Path
   * @param owner Owner
   * @param repo Repository
   * @returns True if updated, false otherwise
   */
  async updatePages(branch: string, path?: string, owner?: string, repo?: string): Promise<boolean> {
    try {
      await this.octokit.repos.updateInformationAboutPagesSite({
        owner: owner || this.owner,
        repo: repo || this.repo,
        source: {
          branch,
          path: path || ''
        }
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_PAGES_UPDATE_PAGES, {
        branch,
        path
      });
      
      return true;
    } catch (error) {
      this.handleError(error, `Failed to update pages for repository ${owner || this.owner}/${repo || this.repo}`);
      return false;
    }
  }
  
  /**
   * Get pages build status
   * @param owner Owner
   * @param repo Repository
   * @returns Pages build status
   */
  async getPagesBuildStatus(owner?: string, repo?: string): Promise<GitHubPagesBuildStatus | null> {
    try {
      const latestBuild = await this.getLatestPagesBuild(owner, repo);
      
      if (!latestBuild) {
        return null;
      }
      
      if (latestBuild.status === 'building') {
        return GitHubPagesBuildStatus.BUILDING;
      } else if (latestBuild.status === 'built') {
        return GitHubPagesBuildStatus.BUILT;
      } else if (latestBuild.status === 'errored') {
        return GitHubPagesBuildStatus.ERRORED;
      }
      
      return null;
    } catch (error) {
      this.handleError(error, `Failed to get pages build status for repository ${owner || this.owner}/${repo || this.repo}`);
      return null;
    }
  }
  
  /**
   * Wait for pages build
   * @param interval Polling interval in milliseconds
   * @param timeout Timeout in milliseconds
   * @param owner Owner
   * @param repo Repository
   * @returns Pages build
   */
  async waitForPagesBuild(interval: number = 5000, timeout: number = 300000, owner?: string, repo?: string): Promise<any | null> {
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      const status = await this.getPagesBuildStatus(owner, repo);
      
      if (status === GitHubPagesBuildStatus.BUILT) {
        return await this.getLatestPagesBuild(owner, repo);
      } else if (status === GitHubPagesBuildStatus.ERRORED) {
        const latestBuild = await this.getLatestPagesBuild(owner, repo);
        this.handleError(new Error('Pages build errored'), `Pages build errored for repository ${owner || this.owner}/${repo || this.repo}`);
        return latestBuild;
      }
      
      // Wait for the specified interval
      await new Promise(resolve => setTimeout(resolve, interval));
    }
    
    this.handleError(new Error('Timeout'), `Timeout waiting for pages build for repository ${owner || this.owner}/${repo || this.repo}`);
    return null;
  }
  
  /**
   * Handle error
   * @param error Error
   * @param message Error message
   */
  private handleError(error: any, message: string): void {
    console.error(message, error);
    
    // Emit event
    this.eventBus.emit(EventType.GITHUB_PAGES_ERROR, {
      error,
      message
    });
  }
  
  /**
   * Get token
   * @returns API token
   */
  getToken(): string {
    return this.token;
  }
  
  /**
   * Get owner
   * @returns Owner
   */
  getOwner(): string {
    return this.owner;
  }
  
  /**
   * Get repository
   * @returns Repository
   */
  getRepo(): string {
    return this.repo;
  }
  
  /**
   * Get base URL
   * @returns Base URL
   */
  getBaseUrl(): string {
    return this.baseUrl;
  }
  
  /**
   * Get timeout
   * @returns Timeout in milliseconds
   */
  getTimeout(): number {
    return this.timeout;
  }
  
  /**
   * Set token
   * @param token API token
   * @returns This instance for chaining
   */
  setToken(token: string): GitHubPages {
    this.token = token;
    
    this.octokit = new Octokit({
      auth: this.token,
      baseUrl: this.baseUrl,
      request: {
        timeout: this.timeout
      }
    });
    
    return this;
  }
  
  /**
   * Set owner
   * @param owner Owner
   * @returns This instance for chaining
   */
  setOwner(owner: string): GitHubPages {
    this.owner = owner;
    return this;
  }
  
  /**
   * Set repository
   * @param repo Repository
   * @returns This instance for chaining
   */
  setRepo(repo: string): GitHubPages {
    this.repo = repo;
    return this;
  }
  
  /**
   * Set base URL
   * @param baseUrl Base URL
   * @returns This instance for chaining
   */
  setBaseUrl(baseUrl: string): GitHubPages {
    this.baseUrl = baseUrl;
    
    this.octokit = new Octokit({
      auth: this.token,
      baseUrl: this.baseUrl,
      request: {
        timeout: this.timeout
      }
    });
    
    return this;
  }
  
  /**
   * Set timeout
   * @param timeout Timeout in milliseconds
   * @returns This instance for chaining
   */
  setTimeout(timeout: number): GitHubPages {
    this.timeout = timeout;
    
    this.octokit = new Octokit({
      auth: this.token,
      baseUrl: this.baseUrl,
      request: {
        timeout: this.timeout
      }
    });
    
    return this;
  }
}

/**
 * Create GitHub pages
 * @param options GitHub pages options
 * @returns GitHub pages
 */
export function createGitHubPages(options: GitHubPagesOptions = {}): GitHubPages {
  return new GitHubPages(options);
}
