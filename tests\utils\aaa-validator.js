/**
 * AAA Validator
 *
 * This module provides utilities for validating and scoring tests based on the
 * Arrange-Act-Assert (AAA) pattern compliance.
 */

/**
 * Scoring weights for different aspects of AAA compliance
 */
const SCORING_WEIGHTS = {
  arrangementClarity: 0.3,
  actionFocus: 0.3,
  assertionCompleteness: 0.3,
  documentation: 0.1
};

/**
 * Validates a test file for AAA pattern compliance
 *
 * @param {string} testContent - The content of the test file
 * @returns {Object} - Validation results with score and recommendations
 */
function validateAAACompliance(testContent) {
  // Parse the test content
  const testBlocks = parseTestBlocks(testContent);

  // Validate each test block
  const results = testBlocks.map(validateTestBlock);

  // Calculate overall score
  const overallScore = results.reduce((sum, result) => sum + result.score, 0) / results.length;

  // Generate recommendations for tests below threshold
  const recommendations = results
    .filter(result => result.score < 0.9)
    .map(result => ({
      testName: result.testName,
      score: result.score,
      recommendations: result.recommendations
    }));

  return {
    overallScore,
    testResults: results,
    recommendations,
    passesThreshold: overallScore >= 0.9
  };
}

/**
 * Parses a test file into individual test blocks
 *
 * @param {string} testContent - The content of the test file
 * @returns {Array<Object>} - Array of test blocks
 */
function parseTestBlocks(testContent) {
  // Regular expression to match test blocks
  const testBlockRegex = /test\s*\(\s*['"]([^'"]+)['"]\s*,\s*async\s*\(\s*{[^}]*}\s*\)\s*=>\s*{([\s\S]*?)}\s*\)/g;

  const testBlocks = [];
  let match;

  while ((match = testBlockRegex.exec(testContent)) !== null) {
    const testName = match[1];
    const testBody = match[2];

    testBlocks.push({
      testName,
      testBody
    });
  }

  return testBlocks;
}

/**
 * Validates a single test block for AAA pattern compliance
 *
 * @param {Object} testBlock - The test block to validate
 * @returns {Object} - Validation results with score and recommendations
 */
function validateTestBlock(testBlock) {
  const { testName, testBody } = testBlock;

  // Check for Arrange section
  const hasArrangeComment = /\/\/\s*ARRANGE|\/\/\s*Arrange/.test(testBody);
  const hasArrangeSection = hasArrangeComment ||
                           /await\s+page\.goto/.test(testBody) ||
                           /const\s+[a-zA-Z0-9_]+\s*=/.test(testBody) ||
                           /await\s+navigateTo/.test(testBody) ||
                           /await\s+login\(/.test(testBody);

  // Check for Act section
  const hasActComment = /\/\/\s*ACT|\/\/\s*Act/.test(testBody);
  const hasActSection = hasActComment ||
                       /await\s+page\.(click|fill|type|check|uncheck|select)/.test(testBody) ||
                       /await\s+createFile\(/.test(testBody) ||
                       /await\s+deleteFile\(/.test(testBody) ||
                       /await\s+selfHealingPage\./.test(testBody);

  // Check for Assert section
  const hasAssertComment = /\/\/\s*ASSERT|\/\/\s*Assert/.test(testBody);
  const hasAssertSection = hasAssertComment ||
                          /await\s+expect/.test(testBody) ||
                          /expect\(/.test(testBody);

  // Check for clear separation between sections
  const hasClearSeparation = (hasArrangeComment && hasActComment && hasAssertComment);

  // Check for proper error handling
  const hasErrorHandling = /try\s*{/.test(testBody);

  // Check for screenshot capture
  const hasScreenshotCapture = /takeScreenshot/.test(testBody);

  // Check for documentation
  const hasDocumentation = /\/\*\*[\s\S]*?\*\//.test(testBody) ||
                          /\/\/\s*Purpose:/.test(testBody) ||
                          /This test follows the AAA pattern/.test(testBody);

  // Calculate scores for each aspect
  const arrangementClarityScore = calculateArrangementClarityScore(testBody, hasArrangeComment, hasArrangeSection);
  const actionFocusScore = calculateActionFocusScore(testBody, hasActComment, hasActSection);
  const assertionCompletenessScore = calculateAssertionCompletenessScore(testBody, hasAssertComment, hasAssertSection);
  const documentationScore = calculateDocumentationScore(testBody, hasDocumentation);

  // Calculate overall score
  const score = (
    arrangementClarityScore * SCORING_WEIGHTS.arrangementClarity +
    actionFocusScore * SCORING_WEIGHTS.actionFocus +
    assertionCompletenessScore * SCORING_WEIGHTS.assertionCompleteness +
    documentationScore * SCORING_WEIGHTS.documentation
  );

  // Generate recommendations
  const recommendations = [];

  if (arrangementClarityScore < 1) {
    recommendations.push('Improve arrangement clarity by adding a clear "// Arrange" comment and ensuring proper setup code.');
  }

  if (actionFocusScore < 1) {
    recommendations.push('Improve action focus by adding a clear "// Act" comment and ensuring the action is focused and separated from arrangement and assertion.');
  }

  if (assertionCompletenessScore < 1) {
    recommendations.push('Improve assertion completeness by adding a clear "// Assert" comment and ensuring comprehensive verification of expected outcomes.');
  }

  if (documentationScore < 1) {
    recommendations.push('Improve documentation by adding a clear purpose statement and documenting inputs and expected outputs.');
  }

  if (!hasErrorHandling) {
    recommendations.push('Add proper error handling with try/catch blocks.');
  }

  if (!hasScreenshotCapture) {
    recommendations.push('Add screenshot capture for better debugging and documentation.');
  }

  return {
    testName,
    score,
    aspects: {
      arrangementClarity: arrangementClarityScore,
      actionFocus: actionFocusScore,
      assertionCompleteness: assertionCompletenessScore,
      documentation: documentationScore
    },
    recommendations
  };
}

/**
 * Calculates the arrangement clarity score
 *
 * @param {string} testBody - The body of the test
 * @param {boolean} hasArrangeComment - Whether the test has an Arrange comment
 * @param {boolean} hasArrangeSection - Whether the test has an Arrange section
 * @returns {number} - Score between 0 and 1
 */
function calculateArrangementClarityScore(testBody, hasArrangeComment, hasArrangeSection) {
  let score = 0;

  // Check for Arrange comment
  if (hasArrangeComment) {
    score += 0.4;
  }

  // Check for Arrange section
  if (hasArrangeSection) {
    score += 0.3;
  }

  // Check for clear separation
  if (hasArrangeComment && /\/\/\s*Arrange[\s\S]*?\/\/\s*Act/.test(testBody)) {
    score += 0.3;
  }

  return Math.min(score, 1);
}

/**
 * Calculates the action focus score
 *
 * @param {string} testBody - The body of the test
 * @param {boolean} hasActComment - Whether the test has an Act comment
 * @param {boolean} hasActSection - Whether the test has an Act section
 * @returns {number} - Score between 0 and 1
 */
function calculateActionFocusScore(testBody, hasActComment, hasActSection) {
  let score = 0;

  // Check for Act comment
  if (hasActComment) {
    score += 0.4;
  }

  // Check for Act section
  if (hasActSection) {
    score += 0.3;
  }

  // Check for clear separation
  if (hasActComment && /\/\/\s*Act[\s\S]*?\/\/\s*Assert/.test(testBody)) {
    score += 0.3;
  }

  return Math.min(score, 1);
}

/**
 * Calculates the assertion completeness score
 *
 * @param {string} testBody - The body of the test
 * @param {boolean} hasAssertComment - Whether the test has an Assert comment
 * @param {boolean} hasAssertSection - Whether the test has an Assert section
 * @returns {number} - Score between 0 and 1
 */
function calculateAssertionCompletenessScore(testBody, hasAssertComment, hasAssertSection) {
  let score = 0;

  // Check for Assert comment
  if (hasAssertComment) {
    score += 0.4;
  }

  // Check for Assert section
  if (hasAssertSection) {
    score += 0.3;
  }

  // Check for multiple assertions
  const assertionCount = (testBody.match(/expect\(/g) || []).length;
  if (assertionCount >= 2) {
    score += 0.3;
  } else if (assertionCount === 1) {
    score += 0.15;
  }

  return Math.min(score, 1);
}

/**
 * Calculates the documentation score
 *
 * @param {string} testBody - The body of the test
 * @param {boolean} hasDocumentation - Whether the test has documentation
 * @returns {number} - Score between 0 and 1
 */
function calculateDocumentationScore(testBody, hasDocumentation) {
  let score = 0;

  // Check for documentation
  if (hasDocumentation) {
    score += 0.5;
  }

  // Check for detailed documentation
  if (/Purpose:/.test(testBody) || /This test follows the AAA pattern/.test(testBody)) {
    score += 0.25;
  }

  // Check for input/output documentation
  if (/Input:/.test(testBody) || /Output:/.test(testBody) || /Expected:/.test(testBody) ||
      /Arrange:/.test(testBody) || /Act:/.test(testBody) || /Assert:/.test(testBody)) {
    score += 0.25;
  }

  return Math.min(score, 1);
}

module.exports = {
  validateAAACompliance,
  SCORING_WEIGHTS
};
