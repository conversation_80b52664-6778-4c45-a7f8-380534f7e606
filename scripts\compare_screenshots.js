/**
 * Compare Screenshots
 * 
 * This script compares screenshots from two different test runs to detect visual changes.
 * It uses the pixelmatch library to compare images pixel by pixel.
 * 
 * Usage:
 * node scripts/compare_screenshots.js --test=test_name --baseline=run_id --compare=run_id [--date=YYYY-MM-DD] [--threshold=0.1]
 * 
 * Examples:
 * node scripts/compare_screenshots.js --test=login_test --baseline=run-12345 --compare=run-67890
 * node scripts/compare_screenshots.js --test=login_test --baseline=run-12345 --compare=run-67890 --date=2025-05-18 --threshold=0.05
 * 
 * @module compare_screenshots
 * <AUTHOR> Wolf Team
 * @date 2025-01-01
 */

const fs = require('fs');
const path = require('path');
const { PNG } = require('pngjs');
const pixelmatch = require('pixelmatch');
const { format } = require('date-fns');
const { SCREENSHOTS_BASE_DIR } = require('../src/utils/screenshot-utils');

// Parse command line arguments
const args = process.argv.slice(2);
let testName = null;
let baselineRunId = null;
let compareRunId = null;
let date = null;
let threshold = 0.1; // Default threshold for pixel matching (0-1)

args.forEach(arg => {
  if (arg.startsWith('--test=')) {
    testName = arg.substring('--test='.length);
  } else if (arg.startsWith('--baseline=')) {
    baselineRunId = arg.substring('--baseline='.length);
  } else if (arg.startsWith('--compare=')) {
    compareRunId = arg.substring('--compare='.length);
  } else if (arg.startsWith('--date=')) {
    date = arg.substring('--date='.length);
  } else if (arg.startsWith('--threshold=')) {
    threshold = parseFloat(arg.substring('--threshold='.length));
  }
});

// Validate required parameters
if (!testName || !baselineRunId || !compareRunId) {
  console.error('Error: Missing required parameters');
  console.log('Usage: node scripts/compare_screenshots.js --test=test_name --baseline=run_id --compare=run_id [--date=YYYY-MM-DD] [--threshold=0.1]');
  process.exit(1);
}

// If no date is specified, use today's date
if (!date) {
  date = format(new Date(), 'yyyy-MM-dd');
}

// Validate threshold
if (isNaN(threshold) || threshold < 0 || threshold > 1) {
  console.error('Error: Threshold must be a number between 0 and 1');
  process.exit(1);
}

/**
 * Compare screenshots from two different test runs
 * 
 * @param {Object} options - Comparison options
 * @param {string} options.testName - Test name
 * @param {string} options.baselineRunId - Baseline run ID
 * @param {string} options.compareRunId - Compare run ID
 * @param {string} options.date - Date in YYYY-MM-DD format
 * @param {number} options.threshold - Threshold for pixel matching (0-1)
 * @returns {Promise<Object>} - Comparison results
 */
async function compareScreenshots(options) {
  const { testName, baselineRunId, compareRunId, date, threshold } = options;
  
  console.log(`Comparing screenshots for test "${testName}" on ${date}`);
  console.log(`Baseline run: ${baselineRunId}`);
  console.log(`Compare run: ${compareRunId}`);
  console.log(`Threshold: ${threshold}`);
  
  // Get baseline screenshots
  const baselineDir = path.join(SCREENSHOTS_BASE_DIR, date, baselineRunId, testName);
  if (!fs.existsSync(baselineDir)) {
    console.error(`Error: Baseline directory not found: ${baselineDir}`);
    process.exit(1);
  }
  
  const baselineScreenshots = fs.readdirSync(baselineDir)
    .filter(file => file.endsWith('.png'))
    .map(file => ({
      path: path.join(baselineDir, file),
      filename: file
    }));
  
  console.log(`Found ${baselineScreenshots.length} baseline screenshots`);
  
  // Get compare screenshots
  const compareDir = path.join(SCREENSHOTS_BASE_DIR, date, compareRunId, testName);
  if (!fs.existsSync(compareDir)) {
    console.error(`Error: Compare directory not found: ${compareDir}`);
    process.exit(1);
  }
  
  const compareScreenshots = fs.readdirSync(compareDir)
    .filter(file => file.endsWith('.png'))
    .map(file => ({
      path: path.join(compareDir, file),
      filename: file
    }));
  
  console.log(`Found ${compareScreenshots.length} compare screenshots`);
  
  // Create reports directory if it doesn't exist
  const reportsDir = path.join(process.cwd(), 'reports', 'visual-regression');
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir, { recursive: true });
  }
  
  // Create a directory for the diff images
  const diffDir = path.join(reportsDir, `${date}_${testName}_${baselineRunId}_vs_${compareRunId}`);
  if (!fs.existsSync(diffDir)) {
    fs.mkdirSync(diffDir, { recursive: true });
  }
  
  // Compare screenshots
  const results = [];
  
  for (const baselineScreenshot of baselineScreenshots) {
    // Find matching screenshot in compare run
    const matchingScreenshot = compareScreenshots.find(s => s.filename === baselineScreenshot.filename);
    
    if (!matchingScreenshot) {
      console.log(`No matching screenshot found for ${baselineScreenshot.filename}`);
      results.push({
        filename: baselineScreenshot.filename,
        status: 'missing',
        diffPercentage: 100,
        baselinePath: baselineScreenshot.path,
        comparePath: null,
        diffPath: null
      });
      continue;
    }
    
    // Read baseline image
    const baselineImage = PNG.sync.read(fs.readFileSync(baselineScreenshot.path));
    
    // Read compare image
    const compareImage = PNG.sync.read(fs.readFileSync(matchingScreenshot.path));
    
    // Check if images have the same dimensions
    if (baselineImage.width !== compareImage.width || baselineImage.height !== compareImage.height) {
      console.log(`Image dimensions don't match for ${baselineScreenshot.filename}`);
      results.push({
        filename: baselineScreenshot.filename,
        status: 'dimension_mismatch',
        diffPercentage: 100,
        baselinePath: baselineScreenshot.path,
        comparePath: matchingScreenshot.path,
        diffPath: null,
        dimensions: {
          baseline: { width: baselineImage.width, height: baselineImage.height },
          compare: { width: compareImage.width, height: compareImage.height }
        }
      });
      continue;
    }
    
    // Create diff image
    const { width, height } = baselineImage;
    const diffImage = new PNG({ width, height });
    
    // Compare images
    const numDiffPixels = pixelmatch(
      baselineImage.data,
      compareImage.data,
      diffImage.data,
      width,
      height,
      { threshold }
    );
    
    // Calculate diff percentage
    const diffPercentage = (numDiffPixels / (width * height)) * 100;
    
    // Save diff image
    const diffPath = path.join(diffDir, `diff_${baselineScreenshot.filename}`);
    fs.writeFileSync(diffPath, PNG.sync.write(diffImage));
    
    // Add result
    results.push({
      filename: baselineScreenshot.filename,
      status: diffPercentage > 0 ? 'different' : 'identical',
      diffPercentage,
      numDiffPixels,
      baselinePath: baselineScreenshot.path,
      comparePath: matchingScreenshot.path,
      diffPath,
      dimensions: { width, height }
    });
    
    console.log(`Compared ${baselineScreenshot.filename}: ${diffPercentage.toFixed(2)}% different`);
  }
  
  // Check for screenshots in compare run that are not in baseline
  for (const compareScreenshot of compareScreenshots) {
    const matchingScreenshot = baselineScreenshots.find(s => s.filename === compareScreenshot.filename);
    
    if (!matchingScreenshot) {
      console.log(`New screenshot found in compare run: ${compareScreenshot.filename}`);
      results.push({
        filename: compareScreenshot.filename,
        status: 'new',
        diffPercentage: 100,
        baselinePath: null,
        comparePath: compareScreenshot.path,
        diffPath: null
      });
    }
  }
  
  // Generate HTML report
  generateHtmlReport({
    testName,
    baselineRunId,
    compareRunId,
    date,
    threshold,
    results,
    diffDir,
    reportsDir
  });
  
  return {
    testName,
    baselineRunId,
    compareRunId,
    date,
    threshold,
    results
  };
}

/**
 * Generate HTML report for visual regression test
 * 
 * @param {Object} options - Report options
 * @param {string} options.testName - Test name
 * @param {string} options.baselineRunId - Baseline run ID
 * @param {string} options.compareRunId - Compare run ID
 * @param {string} options.date - Date in YYYY-MM-DD format
 * @param {number} options.threshold - Threshold for pixel matching (0-1)
 * @param {Array<Object>} options.results - Comparison results
 * @param {string} options.diffDir - Directory for diff images
 * @param {string} options.reportsDir - Directory for reports
 */
function generateHtmlReport(options) {
  const { testName, baselineRunId, compareRunId, date, threshold, results, diffDir, reportsDir } = options;
  
  // Copy baseline and compare images to the diff directory
  for (const result of results) {
    if (result.baselinePath) {
      const baselineCopyPath = path.join(diffDir, `baseline_${result.filename}`);
      fs.copyFileSync(result.baselinePath, baselineCopyPath);
    }
    
    if (result.comparePath) {
      const compareCopyPath = path.join(diffDir, `compare_${result.filename}`);
      fs.copyFileSync(result.comparePath, compareCopyPath);
    }
  }
  
  // Calculate summary statistics
  const totalScreenshots = results.length;
  const identicalScreenshots = results.filter(r => r.status === 'identical').length;
  const differentScreenshots = results.filter(r => r.status === 'different').length;
  const missingScreenshots = results.filter(r => r.status === 'missing').length;
  const newScreenshots = results.filter(r => r.status === 'new').length;
  const dimensionMismatchScreenshots = results.filter(r => r.status === 'dimension_mismatch').length;
  
  // Sort results by diff percentage (descending)
  results.sort((a, b) => b.diffPercentage - a.diffPercentage);
  
  // Generate HTML report
  const reportPath = path.join(reportsDir, `${date}_${testName}_${baselineRunId}_vs_${compareRunId}.html`);
  
  const html = `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Visual Regression Report: ${testName}</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      line-height: 1.6;
      margin: 0;
      padding: 20px;
      color: #333;
    }
    h1, h2, h3 {
      color: #2c3e50;
    }
    .summary {
      background-color: #f8f9fa;
      padding: 20px;
      border-radius: 5px;
      margin-bottom: 20px;
    }
    .summary-item {
      margin-bottom: 10px;
    }
    .status-identical {
      color: #27ae60;
    }
    .status-different {
      color: #e67e22;
    }
    .status-missing, .status-dimension_mismatch {
      color: #e74c3c;
    }
    .status-new {
      color: #3498db;
    }
    .comparison {
      margin-bottom: 40px;
      border: 1px solid #ddd;
      border-radius: 5px;
      overflow: hidden;
    }
    .comparison-header {
      background-color: #f8f9fa;
      padding: 10px 20px;
      border-bottom: 1px solid #ddd;
    }
    .comparison-content {
      display: flex;
      flex-wrap: wrap;
      padding: 20px;
    }
    .comparison-image {
      flex: 1;
      min-width: 300px;
      margin: 10px;
      text-align: center;
    }
    .comparison-image img {
      max-width: 100%;
      border: 1px solid #ddd;
    }
    .comparison-image-label {
      margin-top: 10px;
      font-weight: bold;
    }
    .diff-percentage {
      font-size: 1.2em;
      font-weight: bold;
    }
    .diff-percentage.high {
      color: #e74c3c;
    }
    .diff-percentage.medium {
      color: #e67e22;
    }
    .diff-percentage.low {
      color: #f1c40f;
    }
    .diff-percentage.none {
      color: #27ae60;
    }
  </style>
</head>
<body>
  <h1>Visual Regression Report: ${testName}</h1>
  
  <div class="summary">
    <h2>Summary</h2>
    <div class="summary-item"><strong>Date:</strong> ${date}</div>
    <div class="summary-item"><strong>Baseline Run:</strong> ${baselineRunId}</div>
    <div class="summary-item"><strong>Compare Run:</strong> ${compareRunId}</div>
    <div class="summary-item"><strong>Threshold:</strong> ${threshold}</div>
    <div class="summary-item"><strong>Total Screenshots:</strong> ${totalScreenshots}</div>
    <div class="summary-item"><strong>Identical:</strong> <span class="status-identical">${identicalScreenshots}</span></div>
    <div class="summary-item"><strong>Different:</strong> <span class="status-different">${differentScreenshots}</span></div>
    <div class="summary-item"><strong>Missing:</strong> <span class="status-missing">${missingScreenshots}</span></div>
    <div class="summary-item"><strong>New:</strong> <span class="status-new">${newScreenshots}</span></div>
    <div class="summary-item"><strong>Dimension Mismatch:</strong> <span class="status-dimension_mismatch">${dimensionMismatchScreenshots}</span></div>
  </div>
  
  <h2>Comparisons</h2>
  
  ${results.map(result => {
    const diffPercentageClass = 
      result.status === 'identical' ? 'none' :
      result.diffPercentage < 1 ? 'low' :
      result.diffPercentage < 5 ? 'medium' : 'high';
    
    return `
    <div class="comparison">
      <div class="comparison-header">
        <h3>${result.filename}</h3>
        <div><strong>Status:</strong> <span class="status-${result.status}">${result.status}</span></div>
        <div><strong>Diff Percentage:</strong> <span class="diff-percentage ${diffPercentageClass}">${result.diffPercentage.toFixed(2)}%</span></div>
        ${result.dimensions ? `<div><strong>Dimensions:</strong> ${result.dimensions.width}x${result.dimensions.height}</div>` : ''}
      </div>
      <div class="comparison-content">
        ${result.baselinePath ? `
        <div class="comparison-image">
          <img src="${path.relative(reportsDir, path.join(diffDir, `baseline_${result.filename}`)).replace(/\\/g, '/')}" alt="Baseline">
          <div class="comparison-image-label">Baseline</div>
        </div>
        ` : ''}
        
        ${result.comparePath ? `
        <div class="comparison-image">
          <img src="${path.relative(reportsDir, path.join(diffDir, `compare_${result.filename}`)).replace(/\\/g, '/')}" alt="Compare">
          <div class="comparison-image-label">Compare</div>
        </div>
        ` : ''}
        
        ${result.diffPath ? `
        <div class="comparison-image">
          <img src="${path.relative(reportsDir, path.join(diffDir, `diff_${result.filename}`)).replace(/\\/g, '/')}" alt="Diff">
          <div class="comparison-image-label">Diff</div>
        </div>
        ` : ''}
      </div>
    </div>
    `;
  }).join('')}
</body>
</html>
  `;
  
  fs.writeFileSync(reportPath, html);
  
  console.log(`HTML report generated: ${reportPath}`);
}

// Run the comparison
compareScreenshots({
  testName,
  baselineRunId,
  compareRunId,
  date,
  threshold
}).catch(error => {
  console.error('Error comparing screenshots:', error);
  process.exit(1);
});
