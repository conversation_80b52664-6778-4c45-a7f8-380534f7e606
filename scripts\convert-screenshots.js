/**
 * Screenshot Conversion Script
 *
 * This script converts direct calls to page.screenshot() to use the screenshot utilities.
 * It scans all test files in the tests/qawolf directory and replaces direct calls with
 * calls to takeScreenshot() from screenshot-utils.js.
 *
 * Usage:
 * node scripts/convert-screenshots.js
 *
 * @module convert-screenshots
 * <AUTHOR> Wolf Team
 * @date 2025-01-01
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Regular expressions to match direct calls to page.screenshot()
// These are flexible regexes that can match various formats of page.screenshot() calls
const DIRECT_SCREENSHOT_REGEX_1 = /await\s+page\.screenshot\(\s*\{\s*path\s*:\s*[`'"]([^`'"]+(?:\$\{[^}]+\}[^`'"]*)?)[`'"]\s*(?:,\s*fullPage\s*:\s*(true|false))?\s*\}\s*\)/g;
const DIRECT_SCREENSHOT_REGEX_2 = /await\s+page\.screenshot\(\s*\{\s*fullPage\s*:\s*(true|false)\s*,\s*path\s*:\s*[`'"]([^`'"]+(?:\$\{[^}]+\}[^`'"]*)?)[`'"]\s*\}\s*\)/g;
const DIRECT_SCREENSHOT_REGEX_3 = /page\.screenshot\(\s*\{\s*path\s*:\s*[`'"]([^`'"]+(?:\$\{[^}]+\}[^`'"]*)?)[`'"]\s*(?:,\s*fullPage\s*:\s*(true|false))?\s*\}\s*\)/g;
const DIRECT_SCREENSHOT_REGEX_4 = /page\.screenshot\(\s*\{\s*fullPage\s*:\s*(true|false)\s*,\s*path\s*:\s*[`'"]([^`'"]+(?:\$\{[^}]+\}[^`'"]*)?)[`'"]\s*\}\s*\)/g;

/**
 * Convert direct calls to page.screenshot() to use the screenshot utilities
 *
 * @param {string} content - File content
 * @param {string} testName - Test name
 * @returns {string} - Updated file content
 */
function convertScreenshotCalls(content, testName) {
  // Add the import if it's not already there
  let updatedContent = content;
  if (!updatedContent.includes('takeScreenshot') && updatedContent.includes('page.screenshot(')) {
    // Find the last import statement
    const lastImportIndex = updatedContent.lastIndexOf('require(');
    if (lastImportIndex !== -1) {
      // Find the end of the line containing the last import
      const endOfLine = updatedContent.indexOf('\n', lastImportIndex);
      if (endOfLine !== -1) {
        // Insert the new import after the last import
        updatedContent = updatedContent.substring(0, endOfLine + 1) +
                        "const { takeScreenshot, takeErrorScreenshot } = require('../../src/utils/screenshot-utils');\n" +
                        updatedContent.substring(endOfLine + 1);
      }
    }
  }

  // Function to process a single regex match
  const processMatch = (match, screenshotPath, fullPage, isReversed = false) => {
    console.log(`  Replacing: ${match}`);

    // If the parameters are reversed (fullPage first, then path)
    if (isReversed) {
      console.log(`  fullPage: ${fullPage}, screenshotPath: ${screenshotPath}`);
    } else {
      console.log(`  screenshotPath: ${screenshotPath}, fullPage: ${fullPage}`);
    }

    if (!screenshotPath) {
      console.warn(`Could not extract screenshot path from: ${match}`);
      return match;
    }

    // Check if fullPage is specified in the match
    const isFullPage = fullPage === 'true' || match.includes('fullPage: true');

    // Extract the action from the screenshot path
    let action = '';

    // Handle template literals with ${Date.now()} or similar
    if (screenshotPath.includes('${')) {
      // Extract the base part of the filename (before any template expressions)
      const parts = screenshotPath.split('${');
      const basePart = parts[0];

      // Remove file extension and convert to action format
      action = basePart.replace(/\.png$/, '').replace(/[-_]/g, '-');

      // If the action ends with a dash (common with Date.now() patterns), remove it
      action = action.replace(/-$/, '');
    } else {
      // Regular string path
      const filename = path.basename(screenshotPath);
      action = filename.replace('.png', '').replace(/[-_]/g, '-');
    }

    const replacement = `await takeScreenshot(page, {
  testName: '${testName}',
  action: '${action}',
  fullPage: ${isFullPage ? 'true' : 'false'}
})`;

    console.log(`  Replacement: ${replacement}`);
    return replacement;
  };

  // Check for matches with all regexes
  let matches1 = updatedContent.match(DIRECT_SCREENSHOT_REGEX_1) || [];
  let matches2 = updatedContent.match(DIRECT_SCREENSHOT_REGEX_2) || [];
  let matches3 = updatedContent.match(DIRECT_SCREENSHOT_REGEX_3) || [];
  let matches4 = updatedContent.match(DIRECT_SCREENSHOT_REGEX_4) || [];

  const totalMatches = matches1.length + matches2.length + matches3.length + matches4.length;

  if (totalMatches > 0) {
    console.log(`  Found ${totalMatches} screenshot calls to replace`);

    // Log all matches
    [...matches1, ...matches2, ...matches3, ...matches4].forEach(match => {
      console.log(`  Match: ${match}`);
    });
  } else {
    console.log(`  No matches found for regex`);
    // Try a simpler regex to see if we can find any matches
    const simpleMatches = updatedContent.match(/page\.screenshot\(/g);
    if (simpleMatches) {
      console.log(`  Found ${simpleMatches.length} simple matches`);
      simpleMatches.forEach(match => {
        console.log(`  Simple match: ${match}`);
      });

      // Log a few lines around each match to help debug
      simpleMatches.forEach(simpleMatch => {
        const index = updatedContent.indexOf(simpleMatch);
        const start = Math.max(0, index - 50);
        const end = Math.min(updatedContent.length, index + 150);
        console.log(`  Context: ${updatedContent.substring(start, end)}`);
      });
    }
  }

  // Apply all replacements
  let result = updatedContent;

  // Replace with regex 1: await page.screenshot({ path: "...", fullPage: ... })
  result = result.replace(DIRECT_SCREENSHOT_REGEX_1, (match, screenshotPath, fullPage) =>
    processMatch(match, screenshotPath, fullPage));

  // Replace with regex 2: await page.screenshot({ fullPage: ..., path: "..." })
  result = result.replace(DIRECT_SCREENSHOT_REGEX_2, (match, fullPage, screenshotPath) =>
    processMatch(match, screenshotPath, fullPage, true));

  // Replace with regex 3: page.screenshot({ path: "...", fullPage: ... })
  result = result.replace(DIRECT_SCREENSHOT_REGEX_3, (match, screenshotPath, fullPage) =>
    processMatch(match, screenshotPath, fullPage));

  // Replace with regex 4: page.screenshot({ fullPage: ..., path: "..." })
  result = result.replace(DIRECT_SCREENSHOT_REGEX_4, (match, fullPage, screenshotPath) =>
    processMatch(match, screenshotPath, fullPage, true));

  return result;
}

/**
 * Convert error screenshot calls to use the screenshot utilities
 *
 * @param {string} content - File content
 * @returns {string} - Updated file content
 */
function convertErrorScreenshotCalls(content) {
  // Regular expression to match error screenshot calls
  const ERROR_SCREENSHOT_REGEX = /await\s+page\.screenshot\(\s*\{\s*path\s*:\s*['"]error-screenshot\.png['"]\s*(?:,\s*fullPage\s*:\s*(true|false))?\s*\}\s*\)/g;

  // Replace error screenshot calls with calls to takeErrorScreenshot()
  return content.replace(
    ERROR_SCREENSHOT_REGEX,
    (match, fullPage) => {
      return `await takeErrorScreenshot(page, {
  testName: 'unknown_test',
  error,
  fullPage: ${fullPage || 'true'}
})`;
    }
  );
}

/**
 * Extract the test name from a file path
 *
 * @param {string} filePath - File path
 * @returns {string} - Test name
 */
function extractTestName(filePath) {
  const fileName = path.basename(filePath, '.js');
  return fileName.replace(/_/g, '_').toLowerCase();
}

/**
 * Process a single file
 *
 * @param {string} filePath - File path
 * @returns {Promise<boolean>} - Whether the file was updated
 */
async function processFile(filePath) {
  try {
    console.log(`Processing file: ${filePath}`);

    // Read the file
    const content = await fs.promises.readFile(filePath, 'utf8');

    // Skip files that don't contain direct calls to page.screenshot()
    if (!content.includes('page.screenshot(')) {
      console.log(`  No page.screenshot() calls found in ${filePath}`);
      return false;
    }

    console.log(`  Found page.screenshot() calls in ${filePath}`);

    // Extract the test name from the file path
    const testName = extractTestName(filePath);
    console.log(`  Test name: ${testName}`);

    // Convert direct calls to page.screenshot() to use the screenshot utilities
    let updatedContent = convertScreenshotCalls(content, testName);

    // Convert error screenshot calls to use the screenshot utilities
    updatedContent = convertErrorScreenshotCalls(updatedContent);

    // Skip files that weren't updated
    if (updatedContent === content) {
      console.log(`  No changes made to ${filePath}`);
      return false;
    }

    // Write the updated file
    await fs.promises.writeFile(filePath, updatedContent, 'utf8');

    console.log(`Updated ${filePath}`);
    return true;
  } catch (error) {
    console.error(`Error processing ${filePath}:`, error);
    return false;
  }
}

/**
 * Process all test files in the specified directory
 *
 * @param {string} directory - Directory to process
 * @returns {Promise<number>} - Number of updated files
 */
async function processDirectory(directory) {
  try {
    // Get all test files in the directory
    const testDir = path.join(process.cwd(), directory);
    const files = await fs.promises.readdir(testDir);

    // Filter for JavaScript files
    const jsFiles = files.filter(file => file.endsWith('.js'));

    // Process each file
    let updatedCount = 0;
    for (const file of jsFiles) {
      const filePath = path.join(testDir, file);
      const stats = await fs.promises.stat(filePath);

      if (stats.isDirectory()) {
        // Recursively process subdirectories
        const subDirPath = path.join(directory, file);
        updatedCount += await processDirectory(subDirPath);
      } else if (file.endsWith('.js')) {
        const updated = await processFile(filePath);
        if (updated) {
          updatedCount++;
        }
      }
    }

    return updatedCount;
  } catch (error) {
    console.error(`Error processing directory ${directory}:`, error);
    return 0;
  }
}

/**
 * Process all test files in the tests directory
 *
 * @returns {Promise<void>}
 */
async function processAllFiles() {
  try {
    // Process QA Wolf tests
    console.log('Processing QA Wolf tests...');
    const qawolfCount = await processDirectory('tests/qawolf');

    // Process Playwright MCP tests
    console.log('\nProcessing Playwright MCP tests...');
    let playwrightMcpCount = 0;

    // Process each subdirectory in the Playwright MCP directory
    const mcpSubdirs = ['tests/playwright-mcp/generated', 'tests/playwright-mcp/examples', 'tests/playwright-mcp/converted'];
    for (const subdir of mcpSubdirs) {
      try {
        console.log(`\nProcessing ${subdir}...`);
        playwrightMcpCount += await processDirectory(subdir);
      } catch (error) {
        console.error(`Error processing ${subdir}:`, error);
      }
    }

    // Get total file count
    const totalFiles = await getTotalJsFileCount(['tests/qawolf', ...mcpSubdirs]);

    console.log(`\nUpdated ${qawolfCount + playwrightMcpCount} of ${totalFiles} files`);
  } catch (error) {
    console.error('Error processing files:', error);
  }
}

/**
 * Get the total number of JavaScript files in the specified directories
 *
 * @param {string[]} directories - Directories to count
 * @returns {Promise<number>} - Total number of JavaScript files
 */
async function getTotalJsFileCount(directories) {
  let totalCount = 0;

  for (const directory of directories) {
    try {
      const result = execSync(`find ${directory} -name "*.js" | wc -l`, { encoding: 'utf8' });
      totalCount += parseInt(result.trim(), 10);
    } catch (error) {
      // On Windows, use a different approach
      if (process.platform === 'win32') {
        try {
          const result = execSync(`dir /s /b ${directory}\\*.js | find /c ":"`, { encoding: 'utf8' });
          const match = result.match(/(\d+)/);
          if (match) {
            totalCount += parseInt(match[1], 10);
          }
        } catch (winError) {
          console.error(`Error counting files in ${directory}:`, winError);
        }
      } else {
        console.error(`Error counting files in ${directory}:`, error);
      }
    }
  }

  return totalCount;
}

// Run the script
processAllFiles();
