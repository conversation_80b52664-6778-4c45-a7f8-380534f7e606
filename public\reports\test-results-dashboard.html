<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>QA Wolf Test Results Dashboard</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      background-color: #f5f7fa;
      color: #333;
    }
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    header {
      background-color: #2c3e50;
      color: white;
      padding: 20px;
      text-align: center;
      border-radius: 5px 5px 0 0;
      margin-bottom: 20px;
    }
    h1 {
      margin: 0;
      font-size: 28px;
    }
    .timestamp {
      font-size: 14px;
      margin-top: 5px;
      color: #ddd;
    }
    .summary-cards {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      margin-bottom: 30px;
    }
    .card {
      flex: 1;
      min-width: 200px;
      background-color: white;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
      padding: 20px;
      text-align: center;
    }
    .card-title {
      font-size: 16px;
      color: #7f8c8d;
      margin-bottom: 10px;
    }
    .card-value {
      font-size: 36px;
      font-weight: bold;
    }
    .pass-rate {
      font-size: 48px;
      font-weight: bold;
    }
    .pass {
      color: #27ae60;
    }
    .fail {
      color: #e74c3c;
    }
    .warning {
      color: #f39c12;
    }
    .progress-container {
      width: 100%;
      background-color: #ecf0f1;
      border-radius: 5px;
      margin-top: 10px;
      overflow: hidden;
    }
    .progress-bar {
      height: 10px;
      border-radius: 5px;
      transition: width 1s ease-in-out;
    }
    .progress-bar.good {
      background-color: #27ae60;
    }
    .progress-bar.warning {
      background-color: #f39c12;
    }
    .progress-bar.poor {
      background-color: #e74c3c;
    }
    .test-results {
      background-color: white;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
      padding: 20px;
      margin-bottom: 30px;
    }
    .section-title {
      font-size: 20px;
      margin-bottom: 20px;
      color: #2c3e50;
      border-bottom: 2px solid #ecf0f1;
      padding-bottom: 10px;
    }
    table {
      width: 100%;
      border-collapse: collapse;
    }
    th {
      background-color: #f8f9fa;
      padding: 12px 15px;
      text-align: left;
      font-weight: 600;
      color: #2c3e50;
      border-bottom: 2px solid #ecf0f1;
    }
    td {
      padding: 12px 15px;
      border-bottom: 1px solid #ecf0f1;
    }
    tr:hover {
      background-color: #f8f9fa;
    }
    .status-badge {
      display: inline-block;
      padding: 5px 10px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: bold;
      text-transform: uppercase;
    }
    .status-badge.pass {
      background-color: #e6f7ef;
      color: #27ae60;
    }
    .status-badge.fail {
      background-color: #fdeaea;
      color: #e74c3c;
    }
    footer {
      text-align: center;
      margin-top: 30px;
      color: #7f8c8d;
      font-size: 14px;
    }
    .refresh-button {
      display: inline-block;
      background-color: #3498db;
      color: white;
      padding: 10px 20px;
      border-radius: 5px;
      text-decoration: none;
      margin-top: 20px;
      transition: background-color 0.3s;
    }
    .refresh-button:hover {
      background-color: #2980b9;
    }
    @media (max-width: 768px) {
      .summary-cards {
        flex-direction: column;
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <header>
      <h1>QA Wolf Test Results Dashboard</h1>
      <div class="timestamp">Last updated: <span id="timestamp">2025-05-19 13:23:38</span></div>
    </header>

    <div class="summary-cards">
      <div class="card">
        <div class="card-title">TOTAL TESTS</div>
        <div class="card-value">4</div>
      </div>
      <div class="card">
        <div class="card-title">PASSED</div>
        <div class="card-value pass">2</div>
      </div>
      <div class="card">
        <div class="card-title">FAILED</div>
        <div class="card-value fail">2</div>
      </div>
      <div class="card">
        <div class="card-title">PASS RATE</div>
        <div class="pass-rate warning">50%</div>
        <div class="progress-container">
          <div class="progress-bar warning" style="width: 50%"></div>
        </div>
      </div>
    </div>

    <div class="test-results">
      <h2 class="section-title">Test Results</h2>
      <table>
        <thead>
          <tr>
            <th>Test File</th>
            <th>Status</th>
            <th>Pass Rate</th>
            <th>Duration</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>login_test.js</td>
            <td><span class="status-badge pass">PASS</span></td>
            <td>100.0%</td>
            <td>4.5 seconds</td>
          </tr>
          <tr>
            <td>inject_test_failures.js</td>
            <td><span class="status-badge fail">FAIL</span></td>
            <td>33.33%</td>
            <td>5.0 seconds</td>
          </tr>
          <tr>
            <td>collision_test_1.js</td>
            <td><span class="status-badge pass">PASS</span></td>
            <td>100.0%</td>
            <td>4.5 seconds</td>
          </tr>
          <tr>
            <td>collision_test_2.js</td>
            <td><span class="status-badge fail">FAIL</span></td>
            <td>33.33%</td>
            <td>4.8 seconds</td>
          </tr>
        </tbody>
      </table>
    </div>

    <div style="text-align: center;">
      <a href="#" class="refresh-button" onclick="location.reload()">Refresh Results</a>
    </div>

    <footer>
      <p>QA Wolf Metrics Framework &copy; 2025</p>
    </footer>
  </div>

  <script>
    // Update timestamp with current date/time
    document.getElementById('timestamp').textContent = new Date().toLocaleString();
  </script>
</body>
</html>
