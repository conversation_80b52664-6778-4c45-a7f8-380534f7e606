/**
 * Types for performance tracker
 */

/**
 * Operation type
 */
export enum OperationType {
  NAVIGATION = 'navigation',
  CLICK = 'click',
  FILL = 'fill',
  SELECT = 'select',
  HOVER = 'hover',
  WAIT = 'wait',
  SCREENSHOT = 'screenshot',
  ASSERTION = 'assertion',
  CUSTOM = 'custom'
}

/**
 * Operation metadata
 */
export interface OperationMetadata {
  selector?: string;
  url?: string;
  value?: string;
  description?: string;
  [key: string]: any;
}

/**
 * Operation
 */
export interface Operation {
  /**
   * Operation name
   */
  name: string;
  
  /**
   * Operation type
   */
  type: OperationType | string;
  
  /**
   * Operation duration in milliseconds
   */
  duration: number;
  
  /**
   * Operation start time
   */
  startTime: number;
  
  /**
   * Operation end time
   */
  endTime: number;
  
  /**
   * Operation metadata
   */
  metadata?: OperationMetadata;
}

/**
 * Memory usage
 */
export interface MemoryUsage {
  /**
   * Resident Set Size - total memory allocated for the process
   */
  rss: number;
  
  /**
   * Total size of the allocated heap
   */
  heapTotal: number;
  
  /**
   * Actual memory used during execution
   */
  heapUsed: number;
  
  /**
   * Memory used by C++ objects bound to JavaScript objects
   */
  external: number;
  
  /**
   * Memory allocated for ArrayBuffers and SharedArrayBuffers
   */
  arrayBuffers: number;
}

/**
 * CPU usage
 */
export interface CPUUsage {
  /**
   * CPU time spent in user code
   */
  user: number;
  
  /**
   * CPU time spent in system code
   */
  system: number;
}

/**
 * Resource usage
 */
export interface ResourceUsage {
  /**
   * Timestamp
   */
  timestamp: number;
  
  /**
   * Memory usage
   */
  memory: MemoryUsage;
  
  /**
   * CPU usage
   */
  cpu: CPUUsage;
}

/**
 * Operation statistics
 */
export interface OperationStats {
  /**
   * Number of operations
   */
  count: number;
  
  /**
   * Total duration of all operations
   */
  totalDuration: number;
  
  /**
   * Average duration of operations
   */
  averageDuration: number;
  
  /**
   * Minimum duration of operations
   */
  minDuration: number;
  
  /**
   * Maximum duration of operations
   */
  maxDuration: number;
  
  /**
   * Statistics by operation type
   */
  byType: {
    [type: string]: {
      count: number;
      totalDuration: number;
      averageDuration: number;
      minDuration: number;
      maxDuration: number;
    };
  };
}

/**
 * Resource statistics
 */
export interface ResourceStats {
  /**
   * Memory statistics
   */
  memory: {
    /**
     * Maximum Resident Set Size
     */
    maxRss: number;
    
    /**
     * Maximum heap total
     */
    maxHeapTotal: number;
    
    /**
     * Maximum heap used
     */
    maxHeapUsed: number;
    
    /**
     * Maximum external memory
     */
    maxExternal: number;
    
    /**
     * Maximum array buffers
     */
    maxArrayBuffers: number;
  };
  
  /**
   * CPU statistics
   */
  cpu: {
    /**
     * Total user CPU time
     */
    totalUser: number;
    
    /**
     * Total system CPU time
     */
    totalSystem: number;
  };
}

/**
 * Performance metrics
 */
export interface PerformanceMetrics {
  /**
   * Test name
   */
  testName: string;
  
  /**
   * Test ID
   */
  testId: string;
  
  /**
   * Test start time
   */
  startTime: number;
  
  /**
   * Test end time
   */
  endTime: number;
  
  /**
   * Test duration in milliseconds
   */
  duration: number;
  
  /**
   * Operations
   */
  operations: Operation[];
  
  /**
   * Operation statistics
   */
  operationStats: OperationStats;
  
  /**
   * Resource usage
   */
  resourceUsage: ResourceUsage[];
  
  /**
   * Resource statistics
   */
  resourceStats: ResourceStats;
}

/**
 * Performance tracker options
 */
export interface PerformanceTrackerOptions {
  /**
   * Test name
   */
  testName?: string;
  
  /**
   * Test ID
   */
  testId?: string;
  
  /**
   * Whether to monitor resources
   */
  monitorResources?: boolean;
  
  /**
   * Resource monitoring interval in milliseconds
   */
  resourceMonitorInterval?: number;
  
  /**
   * Whether to save metrics to file
   */
  saveMetrics?: boolean;
  
  /**
   * Metrics file path
   */
  metricsFilePath?: string;
}

/**
 * Operation tracker options
 */
export interface OperationTrackerOptions {
  /**
   * Whether to track operations
   */
  trackOperations?: boolean;
}

/**
 * Resource monitor options
 */
export interface ResourceMonitorOptions {
  /**
   * Whether to monitor resources
   */
  monitorResources?: boolean;
  
  /**
   * Resource monitoring interval in milliseconds
   */
  monitorInterval?: number;
}

/**
 * Performance reporter options
 */
export interface PerformanceReporterOptions {
  /**
   * Output directory
   */
  outputDir?: string;
  
  /**
   * Report formats
   */
  formats?: ('json' | 'html' | 'csv')[];
  
  /**
   * Whether to include charts in HTML report
   */
  includeCharts?: boolean;
}

/**
 * Performance analyzer options
 */
export interface PerformanceAnalyzerOptions {
  /**
   * Thresholds for performance metrics
   */
  thresholds?: {
    /**
     * Maximum test duration in milliseconds
     */
    maxTestDuration?: number;
    
    /**
     * Maximum operation durations by type in milliseconds
     */
    maxOperationDurations?: {
      [type: string]: number;
    };
    
    /**
     * Maximum resource usage
     */
    maxResourceUsage?: {
      /**
       * Maximum memory usage
       */
      memory?: {
        /**
         * Maximum Resident Set Size
         */
        rss?: number;
        
        /**
         * Maximum heap total
         */
        heapTotal?: number;
        
        /**
         * Maximum heap used
         */
        heapUsed?: number;
      };
    };
  };
}
