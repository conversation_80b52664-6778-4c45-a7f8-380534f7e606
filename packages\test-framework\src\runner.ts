/**
 * Test runner for QA Wolf Test Framework
 */

import { chromium, firefox, webkit, Browser, BrowserContext, Page } from 'playwright';
import { v4 as uuidv4 } from 'uuid';
import { EventBus, EventType } from '@qawolf/core';
import { fileExists, ensureDirectoryExists } from '@qawolf/shared-utils';
import { Test, TestContext, TestFunction, TestOptions, TestResult, TestRunnerOptions, TestStatus, TestStep, TestStepType, TestSuite, TestSuiteOptions } from './types';
import { createConsoleReporter } from './reporter';

// Global test suites
const testSuites: TestSuite[] = [];

/**
 * Create test suite
 * @param name Test suite name
 * @param options Test suite options
 * @returns Test suite
 */
export function describe(name: string, options: Partial<TestSuiteOptions> = {}): TestSuite {
  const suite: TestSuite = {
    name,
    options: {
      name,
      timeout: options.timeout || 60000,
      retries: options.retries || 2,
      skip: options.skip || false,
      only: options.only || false,
      tags: options.tags || [],
      metadata: options.metadata || {}
    },
    tests: []
  };
  
  testSuites.push(suite);
  
  return suite;
}

/**
 * Create test
 * @param name Test name
 * @param fn Test function
 * @param options Test options
 * @returns Test
 */
export function test(name: string, fn: TestFunction, options: Partial<TestOptions> = {}): Test {
  const test: Test = {
    name,
    options: {
      name,
      timeout: options.timeout || 60000,
      retries: options.retries || 2,
      skip: options.skip || false,
      only: options.only || false,
      tags: options.tags || [],
      metadata: options.metadata || {}
    },
    fn
  };
  
  // Add test to current suite or create a new one
  if (testSuites.length === 0) {
    describe('Default Suite').tests.push(test);
  } else {
    testSuites[testSuites.length - 1].tests.push(test);
  }
  
  return test;
}

/**
 * Skip test
 * @param name Test name
 * @param fn Test function
 * @param options Test options
 * @returns Test
 */
export function skip(name: string, fn: TestFunction, options: Partial<TestOptions> = {}): Test {
  return test(name, fn, { ...options, skip: true });
}

/**
 * Only run this test
 * @param name Test name
 * @param fn Test function
 * @param options Test options
 * @returns Test
 */
export function only(name: string, fn: TestFunction, options: Partial<TestOptions> = {}): Test {
  return test(name, fn, { ...options, only: true });
}

/**
 * Before all hook
 * @param fn Hook function
 */
export function beforeAll(fn: () => Promise<void>): void {
  if (testSuites.length === 0) {
    describe('Default Suite').beforeAll = fn;
  } else {
    testSuites[testSuites.length - 1].beforeAll = fn;
  }
}

/**
 * After all hook
 * @param fn Hook function
 */
export function afterAll(fn: () => Promise<void>): void {
  if (testSuites.length === 0) {
    describe('Default Suite').afterAll = fn;
  } else {
    testSuites[testSuites.length - 1].afterAll = fn;
  }
}

/**
 * Before each hook
 * @param fn Hook function
 */
export function beforeEach(fn: () => Promise<void>): void {
  if (testSuites.length === 0) {
    describe('Default Suite').beforeEach = fn;
  } else {
    testSuites[testSuites.length - 1].beforeEach = fn;
  }
}

/**
 * After each hook
 * @param fn Hook function
 */
export function afterEach(fn: () => Promise<void>): void {
  if (testSuites.length === 0) {
    describe('Default Suite').afterEach = fn;
  } else {
    testSuites[testSuites.length - 1].afterEach = fn;
  }
}

/**
 * Run tests
 * @param options Test runner options
 * @returns Test results
 */
export async function runTests(options: Partial<TestRunnerOptions> = {}): Promise<TestResult[]> {
  const results: TestResult[] = [];
  const eventBus = EventBus.getInstance();
  
  // Create reporter
  const reporter = createConsoleReporter();
  
  // Get all tests
  const tests = getAllTests(options);
  
  // Emit run start event
  eventBus.emit(EventType.TEST_STARTED, { tests });
  reporter.onRunStart(tests);
  
  // Run tests
  for (const test of tests) {
    const result = await runTest(test, options);
    results.push(result);
  }
  
  // Emit run end event
  eventBus.emit(EventType.TEST_COMPLETED, { results });
  reporter.onRunEnd(results);
  
  return results;
}

/**
 * Run test
 * @param test Test to run
 * @param options Test runner options
 * @returns Test result
 */
async function runTest(test: Test, options: Partial<TestRunnerOptions> = {}): Promise<TestResult> {
  const eventBus = EventBus.getInstance();
  const reporter = createConsoleReporter();
  
  // Create test result
  const result: TestResult = {
    id: uuidv4(),
    name: test.name,
    status: TestStatus.PENDING,
    duration: 0,
    startTime: Date.now(),
    endTime: 0,
    steps: [],
    screenshots: [],
    metadata: { ...test.options.metadata }
  };
  
  // Skip test if needed
  if (test.options.skip) {
    result.status = TestStatus.SKIPPED;
    result.endTime = Date.now();
    result.duration = result.endTime - result.startTime;
    
    eventBus.emit(EventType.TEST_SKIPPED, { test, result });
    reporter.onTestEnd(result);
    
    return result;
  }
  
  // Emit test start event
  eventBus.emit(EventType.TEST_STARTED, { test });
  reporter.onTestStart(test);
  
  // Set test status to running
  result.status = TestStatus.RUNNING;
  
  // Launch browser
  const browser = await launchBrowser(options.browser || 'chromium', options.headless !== false);
  
  try {
    // Create browser context
    const context = await browser.newContext();
    
    // Create page
    const page = await context.newPage();
    
    // Create test context
    const testContext: TestContext = {
      page,
      browser,
      context,
      testInfo: {
        id: result.id,
        name: test.name,
        startTime: result.startTime,
        metadata: result.metadata
      },
      step: async (name, type, fn) => {
        const step: TestStep = {
          name,
          type,
          status: TestStepStatus.RUNNING,
          duration: 0,
          startTime: Date.now(),
          endTime: 0
        };
        
        result.steps.push(step);
        
        try {
          await fn();
          
          step.status = TestStepStatus.PASSED;
        } catch (error) {
          step.status = TestStepStatus.FAILED;
          step.error = error as Error;
          
          throw error;
        } finally {
          step.endTime = Date.now();
          step.duration = step.endTime - step.startTime;
        }
      },
      arrange: async (name, fn) => {
        await testContext.step(name, TestStepType.ARRANGE, fn);
      },
      act: async (name, fn) => {
        await testContext.step(name, TestStepType.ACT, fn);
      },
      assert: async (name, fn) => {
        await testContext.step(name, TestStepType.ASSERT, fn);
      },
      screenshot: async (name) => {
        const screenshotPath = `screenshots/${result.id}/${name}.png`;
        
        // Create directory if it doesn't exist
        ensureDirectoryExists(`screenshots/${result.id}`);
        
        // Take screenshot
        await page.screenshot({ path: screenshotPath });
        
        // Add screenshot to result
        result.screenshots.push(screenshotPath);
        
        return screenshotPath;
      }
    };
    
    // Run test
    await test.fn(testContext);
    
    // Set test status to passed
    result.status = TestStatus.PASSED;
  } catch (error) {
    // Set test status to failed
    result.status = TestStatus.FAILED;
    result.error = error as Error;
    
    // Take screenshot on error
    try {
      const page = await browser.newPage();
      const screenshotPath = `screenshots/${result.id}/error.png`;
      
      // Create directory if it doesn't exist
      ensureDirectoryExists(`screenshots/${result.id}`);
      
      // Take screenshot
      await page.screenshot({ path: screenshotPath });
      
      // Add screenshot to result
      result.screenshots.push(screenshotPath);
    } catch (screenshotError) {
      console.error('Failed to take error screenshot:', screenshotError);
    }
    
    // Emit test failed event
    eventBus.emit(EventType.TEST_FAILED, { test, result, error });
  } finally {
    // Close browser
    await browser.close();
    
    // Set test end time and duration
    result.endTime = Date.now();
    result.duration = result.endTime - result.startTime;
    
    // Emit test end event
    eventBus.emit(EventType.TEST_COMPLETED, { test, result });
    reporter.onTestEnd(result);
  }
  
  return result;
}

/**
 * Launch browser
 * @param browserName Browser name
 * @param headless Headless mode
 * @returns Browser
 */
async function launchBrowser(browserName: string, headless: boolean): Promise<Browser> {
  switch (browserName) {
    case 'firefox':
      return firefox.launch({ headless });
    case 'webkit':
      return webkit.launch({ headless });
    case 'chromium':
    default:
      return chromium.launch({ headless });
  }
}

/**
 * Get all tests
 * @param options Test runner options
 * @returns Tests
 */
function getAllTests(options: Partial<TestRunnerOptions> = {}): Test[] {
  const tests: Test[] = [];
  
  // Check if any test has only flag
  const hasOnly = testSuites.some(suite => suite.options.only || suite.tests.some(test => test.options.only));
  
  // Get tests from suites
  for (const suite of testSuites) {
    // Skip suite if needed
    if (suite.options.skip || (hasOnly && !suite.options.only)) {
      continue;
    }
    
    // Get tests from suite
    for (const test of suite.tests) {
      // Skip test if needed
      if (test.options.skip || (hasOnly && !test.options.only)) {
        continue;
      }
      
      tests.push(test);
    }
  }
  
  return tests;
}

/**
 * Test runner
 */
export class TestRunner {
  private options: TestRunnerOptions;
  
  /**
   * Constructor
   * @param options Test runner options
   */
  constructor(options: Partial<TestRunnerOptions> = {}) {
    this.options = {
      browser: options.browser || 'chromium',
      headless: options.headless !== false,
      timeout: options.timeout || 60000,
      retries: options.retries || 2,
      workers: options.workers || 1,
      reporter: options.reporter || ['console'],
      outputDir: options.outputDir || './test-results',
      testMatch: options.testMatch || ['**/*.test.ts', '**/*.spec.ts'],
      testIgnore: options.testIgnore || ['node_modules/**/*'],
      metadata: options.metadata || {}
    };
  }
  
  /**
   * Run tests
   * @returns Test results
   */
  async run(): Promise<TestResult[]> {
    return runTests(this.options);
  }
}
