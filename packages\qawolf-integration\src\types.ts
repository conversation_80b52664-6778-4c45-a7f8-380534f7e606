/**
 * Types for QA Wolf integration
 */

/**
 * QA Wolf client options
 */
export interface QAWolfClientOptions {
  /**
   * API key
   */
  apiKey?: string;
  
  /**
   * Team ID
   */
  teamId?: string;
  
  /**
   * API URL
   */
  apiUrl?: string;
  
  /**
   * Timeout in milliseconds
   */
  timeout?: number;
}

/**
 * QA Wolf test
 */
export interface QAWolfTest {
  /**
   * Test ID
   */
  id: string;
  
  /**
   * Test name
   */
  name: string;
  
  /**
   * Test slug
   */
  slug: string;
  
  /**
   * Test status
   */
  status: 'active' | 'inactive' | 'archived';
  
  /**
   * Test created at
   */
  createdAt: string;
  
  /**
   * Test updated at
   */
  updatedAt: string;
  
  /**
   * Test tags
   */
  tags?: string[];
  
  /**
   * Test metadata
   */
  metadata?: Record<string, any>;
}

/**
 * QA Wolf run
 */
export interface QAWolfRun {
  /**
   * Run ID
   */
  id: string;
  
  /**
   * Test ID
   */
  testId: string;
  
  /**
   * Run status
   */
  status: 'queued' | 'running' | 'passed' | 'failed' | 'timedOut' | 'cancelled';
  
  /**
   * Run created at
   */
  createdAt: string;
  
  /**
   * Run updated at
   */
  updatedAt: string;
  
  /**
   * Run started at
   */
  startedAt?: string;
  
  /**
   * Run completed at
   */
  completedAt?: string;
  
  /**
   * Run duration in milliseconds
   */
  duration?: number;
  
  /**
   * Run environment
   */
  environment?: string;
  
  /**
   * Run browser
   */
  browser?: string;
  
  /**
   * Run device
   */
  device?: string;
  
  /**
   * Run error
   */
  error?: string;
  
  /**
   * Run metadata
   */
  metadata?: Record<string, any>;
}

/**
 * QA Wolf artifact
 */
export interface QAWolfArtifact {
  /**
   * Artifact ID
   */
  id: string;
  
  /**
   * Run ID
   */
  runId: string;
  
  /**
   * Artifact type
   */
  type: 'screenshot' | 'video' | 'trace' | 'log' | 'other';
  
  /**
   * Artifact name
   */
  name: string;
  
  /**
   * Artifact URL
   */
  url: string;
  
  /**
   * Artifact created at
   */
  createdAt: string;
  
  /**
   * Artifact metadata
   */
  metadata?: Record<string, any>;
}

/**
 * QA Wolf deployment
 */
export interface QAWolfDeployment {
  /**
   * Deployment ID
   */
  id: string;
  
  /**
   * Deployment environment
   */
  environment: string;
  
  /**
   * Deployment URL
   */
  url: string;
  
  /**
   * Deployment created at
   */
  createdAt: string;
  
  /**
   * Deployment metadata
   */
  metadata?: Record<string, any>;
}

/**
 * QA Wolf CI run
 */
export interface QAWolfCIRun {
  /**
   * CI run ID
   */
  id: string;
  
  /**
   * CI provider
   */
  provider: 'github' | 'gitlab' | 'bitbucket' | 'azure' | 'jenkins' | 'circle' | 'travis' | 'other';
  
  /**
   * CI run status
   */
  status: 'queued' | 'running' | 'passed' | 'failed' | 'timedOut' | 'cancelled';
  
  /**
   * CI run created at
   */
  createdAt: string;
  
  /**
   * CI run updated at
   */
  updatedAt: string;
  
  /**
   * CI run started at
   */
  startedAt?: string;
  
  /**
   * CI run completed at
   */
  completedAt?: string;
  
  /**
   * CI run duration in milliseconds
   */
  duration?: number;
  
  /**
   * CI run branch
   */
  branch?: string;
  
  /**
   * CI run commit
   */
  commit?: string;
  
  /**
   * CI run pull request
   */
  pullRequest?: string;
  
  /**
   * CI run metadata
   */
  metadata?: Record<string, any>;
  
  /**
   * CI run tests
   */
  tests?: QAWolfRun[];
}

/**
 * QA Wolf CI greenlight
 */
export interface QAWolfCIGreenlight {
  /**
   * Greenlight ID
   */
  id: string;
  
  /**
   * CI run ID
   */
  ciRunId: string;
  
  /**
   * Greenlight status
   */
  status: 'pending' | 'running' | 'complete';
  
  /**
   * Greenlight result
   */
  result: boolean;
  
  /**
   * Greenlight created at
   */
  createdAt: string;
  
  /**
   * Greenlight updated at
   */
  updatedAt: string;
  
  /**
   * Greenlight metadata
   */
  metadata?: Record<string, any>;
}

/**
 * QA Wolf metrics
 */
export interface QAWolfMetrics {
  /**
   * Metrics ID
   */
  id: string;
  
  /**
   * Test ID
   */
  testId: string;
  
  /**
   * Run ID
   */
  runId: string;
  
  /**
   * Metrics created at
   */
  createdAt: string;
  
  /**
   * Test duration in milliseconds
   */
  duration: number;
  
  /**
   * Test pass rate
   */
  passRate: number;
  
  /**
   * Test flakiness rate
   */
  flakinessRate: number;
  
  /**
   * Test execution time
   */
  executionTime: number;
  
  /**
   * Test resource usage
   */
  resourceUsage: {
    /**
     * CPU usage
     */
    cpu: number;
    
    /**
     * Memory usage
     */
    memory: number;
    
    /**
     * Network usage
     */
    network: number;
  };
  
  /**
   * Test metrics metadata
   */
  metadata?: Record<string, any>;
}

/**
 * QA Wolf report
 */
export interface QAWolfReport {
  /**
   * Report ID
   */
  id: string;
  
  /**
   * Report name
   */
  name: string;
  
  /**
   * Report type
   */
  type: 'test' | 'run' | 'ci' | 'metrics' | 'custom';
  
  /**
   * Report format
   */
  format: 'html' | 'json' | 'csv' | 'pdf';
  
  /**
   * Report URL
   */
  url: string;
  
  /**
   * Report created at
   */
  createdAt: string;
  
  /**
   * Report metadata
   */
  metadata?: Record<string, any>;
}

/**
 * QA Wolf API response
 */
export interface QAWolfAPIResponse<T> {
  /**
   * Response data
   */
  data: T;
  
  /**
   * Response status
   */
  status: number;
  
  /**
   * Response message
   */
  message: string;
}

/**
 * QA Wolf API error
 */
export interface QAWolfAPIError {
  /**
   * Error code
   */
  code: string;
  
  /**
   * Error message
   */
  message: string;
  
  /**
   * Error details
   */
  details?: Record<string, any>;
}

/**
 * QA Wolf API options
 */
export interface QAWolfAPIOptions {
  /**
   * API key
   */
  apiKey?: string;
  
  /**
   * Team ID
   */
  teamId?: string;
  
  /**
   * API URL
   */
  apiUrl?: string;
  
  /**
   * Timeout in milliseconds
   */
  timeout?: number;
}

/**
 * QA Wolf CI options
 */
export interface QAWolfCIOptions {
  /**
   * API key
   */
  apiKey?: string;
  
  /**
   * Team ID
   */
  teamId?: string;
  
  /**
   * API URL
   */
  apiUrl?: string;
  
  /**
   * CI provider
   */
  provider?: 'github' | 'gitlab' | 'bitbucket' | 'azure' | 'jenkins' | 'circle' | 'travis' | 'other';
  
  /**
   * Repository URL
   */
  repositoryUrl?: string;
  
  /**
   * Branch
   */
  branch?: string;
  
  /**
   * Commit
   */
  commit?: string;
  
  /**
   * Pull request
   */
  pullRequest?: string;
  
  /**
   * Environment
   */
  environment?: string;
  
  /**
   * Timeout in milliseconds
   */
  timeout?: number;
}

/**
 * QA Wolf metrics options
 */
export interface QAWolfMetricsOptions {
  /**
   * API key
   */
  apiKey?: string;
  
  /**
   * Team ID
   */
  teamId?: string;
  
  /**
   * API URL
   */
  apiUrl?: string;
  
  /**
   * Test ID
   */
  testId?: string;
  
  /**
   * Run ID
   */
  runId?: string;
  
  /**
   * Timeout in milliseconds
   */
  timeout?: number;
}

/**
 * QA Wolf reporter options
 */
export interface QAWolfReporterOptions {
  /**
   * API key
   */
  apiKey?: string;
  
  /**
   * Team ID
   */
  teamId?: string;
  
  /**
   * API URL
   */
  apiUrl?: string;
  
  /**
   * Report name
   */
  reportName?: string;
  
  /**
   * Report type
   */
  reportType?: 'test' | 'run' | 'ci' | 'metrics' | 'custom';
  
  /**
   * Report format
   */
  reportFormat?: 'html' | 'json' | 'csv' | 'pdf';
  
  /**
   * Timeout in milliseconds
   */
  timeout?: number;
}
