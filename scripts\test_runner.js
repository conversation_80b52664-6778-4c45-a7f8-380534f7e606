/**
 * Test Runner for QA Wolf Tests
 * 
 * This script runs test files and collects execution results.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
require('dotenv').config({ path: path.join(__dirname, '..', '.env') });

// Function to run a single test
async function runTest(testPath) {
  console.log(`Running test: ${path.basename(testPath)}`);
  
  try {
    // Run the test using Node.js with a timeout
    execSync(`node ${testPath}`, { 
      stdio: 'inherit',
      timeout: 120000 // 2 minute timeout
    });
    return { file: path.basename(testPath), success: true };
  } catch (error) {
    console.error(`Test failed: ${path.basename(testPath)}`);
    return { 
      file: path.basename(testPath), 
      success: false, 
      error: error.message || 'Unknown error'
    };
  }
}

// Function to run all tests in a directory
async function runAllTests(dirPath, testPattern = null) {
  const results = [];
  const files = fs.readdirSync(dirPath);
  
  for (const file of files) {
    // Skip non-test files and configuration files
    if (!file.endsWith('.js') || 
        file.includes('selectors') || 
        file.endsWith('.config.js')) {
      continue;
    }
    
    // If a test pattern is provided, only run matching tests
    if (testPattern && !file.includes(testPattern)) {
      continue;
    }
    
    const testPath = path.join(dirPath, file);
    const result = await runTest(testPath);
    results.push(result);
  }
  
  return results;
}

// Main function
async function main() {
  // Create reports directory if it doesn't exist
  const reportsDir = path.join(__dirname, '..', 'reports');
  if (!fs.existsSync(reportsDir)) {
    fs.mkdirSync(reportsDir, { recursive: true });
  }
  
  // Get test pattern from command line arguments
  const testPattern = process.argv[2] || null;
  
  const testDir = path.join(__dirname, '..', 'tests', 'qawolf');
  console.log(`Running tests in: ${testDir}`);
  if (testPattern) {
    console.log(`Filtering tests by pattern: ${testPattern}`);
  }
  
  const results = await runAllTests(testDir, testPattern);
  
  // Calculate success rate
  const totalTests = results.length;
  const passedTests = results.filter(r => r.success).length;
  const successRate = (passedTests / totalTests) * 100;
  
  console.log('\n=== TEST EXECUTION RESULTS ===\n');
  console.log(`Tests Run: ${totalTests}`);
  console.log(`Tests Passed: ${passedTests}`);
  console.log(`Success Rate: ${successRate.toFixed(2)}%`);
  
  // List failed tests
  const failedTests = results.filter(r => !r.success);
  if (failedTests.length > 0) {
    console.log('\nFailed Tests:');
    for (const test of failedTests) {
      console.log(`- ${test.file}`);
    }
  }
  
  // Write results to a JSON file
  fs.writeFileSync(
    path.join(reportsDir, 'test-execution-results.json'),
    JSON.stringify(results, null, 2)
  );
  
  console.log('\nResults saved to reports/test-execution-results.json');
  
  return { results, successRate };
}

// Export functions for use in other scripts
module.exports = {
  runTest,
  runAllTests,
  main
};

// Run the script if executed directly
if (require.main === module) {
  main().catch(console.error);
}
