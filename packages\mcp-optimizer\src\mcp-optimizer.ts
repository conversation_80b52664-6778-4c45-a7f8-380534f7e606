/**
 * MCP optimizer for QA Wolf Metrics Framework
 */

import { v4 as uuidv4 } from 'uuid';
import { MCPOperation, MCPOperationPattern, MCPOptimizationSuggestion, MCPOptimizerOptions, MCPAnalysisResult } from './types';
import { MCPAnalyzer } from './mcp-analyzer';
import { MCPTransformer } from './mcp-transformer';
import { MCPCostCalculator } from './mcp-cost-calculator';
import { writeJsonToFile, ensureDirectoryExists } from '@qawolf/shared-utils';
import { EventBus, EventType } from '@qawolf/core';

/**
 * MCP optimizer
 */
export class MCPOptimizer {
  /**
   * MCP analyzer
   */
  private analyzer: MCPAnalyzer;
  
  /**
   * MCP transformer
   */
  private transformer: MCPTransformer;
  
  /**
   * MCP cost calculator
   */
  private costCalculator: MCPCostCalculator;
  
  /**
   * Operations
   */
  private operations: MCPOperation[] = [];
  
  /**
   * Patterns
   */
  private patterns: MCPOperationPattern[] = [];
  
  /**
   * Suggestions
   */
  private suggestions: MCPOptimizationSuggestion[] = [];
  
  /**
   * Whether to analyze patterns
   */
  private analyzePatterns: boolean;
  
  /**
   * Whether to calculate costs
   */
  private calculateCosts: boolean;
  
  /**
   * Whether to generate suggestions
   */
  private generateSuggestions: boolean;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options MCP optimizer options
   */
  constructor(options: MCPOptimizerOptions = {}) {
    this.analyzer = new MCPAnalyzer({
      analyzePatterns: options.analyzePatterns !== false,
      minPatternFrequency: options.minPatternFrequency || 2,
      maxOperations: options.maxOperations
    });
    
    this.transformer = new MCPTransformer({
      generateSuggestions: options.generateSuggestions !== false,
      minCostSavings: options.minCostSavings || 0.01,
      minTokenSavings: options.minTokenSavings || 100,
      includeLowPrioritySuggestions: options.includeLowPrioritySuggestions
    });
    
    this.costCalculator = new MCPCostCalculator();
    
    this.analyzePatterns = options.analyzePatterns !== false;
    this.calculateCosts = options.calculateCosts !== false;
    this.generateSuggestions = options.generateSuggestions !== false;
    
    this.eventBus = EventBus.getInstance();
  }
  
  /**
   * Track operation
   * @param operation Operation to track
   * @returns This instance for chaining
   */
  trackOperation(operation: Omit<MCPOperation, 'id' | 'timestamp'>): MCPOptimizer {
    const fullOperation: MCPOperation = {
      ...operation,
      id: uuidv4(),
      timestamp: Date.now()
    };
    
    this.operations.push(fullOperation);
    
    // Emit event
    this.eventBus.emit(EventType.MCP_OPERATION_TRACKED, {
      operation: fullOperation
    });
    
    return this;
  }
  
  /**
   * Analyze operations
   * @returns Analysis result
   */
  analyze(): MCPAnalysisResult {
    // Calculate costs
    if (this.calculateCosts) {
      this.operations = this.costCalculator.calculateCosts(this.operations);
    }
    
    // Analyze patterns
    if (this.analyzePatterns) {
      this.patterns = this.analyzer.analyzePatterns(this.operations);
    }
    
    // Generate suggestions
    if (this.generateSuggestions) {
      this.suggestions = this.transformer.generateSuggestions(this.operations, this.patterns);
    }
    
    // Create analysis result
    const result = this.createAnalysisResult();
    
    // Emit event
    this.eventBus.emit(EventType.MCP_ANALYSIS_COMPLETED, {
      result
    });
    
    return result;
  }
  
  /**
   * Create analysis result
   * @returns Analysis result
   */
  private createAnalysisResult(): MCPAnalysisResult {
    // Calculate total operations
    const totalOperations = this.operations.length;
    
    // Calculate operations by type
    const operationsByType: Record<string, number> = {};
    
    for (const operation of this.operations) {
      if (!operationsByType[operation.type]) {
        operationsByType[operation.type] = 0;
      }
      
      operationsByType[operation.type]++;
    }
    
    // Calculate operations by tool
    const operationsByTool: Record<string, number> = {};
    
    for (const operation of this.operations) {
      if (!operationsByTool[operation.toolType]) {
        operationsByTool[operation.toolType] = 0;
      }
      
      operationsByTool[operation.toolType]++;
    }
    
    // Calculate total cost
    const totalCost = this.operations.reduce((sum, operation) => sum + (operation.cost || 0), 0);
    
    // Calculate total tokens
    const totalTokens = this.operations.reduce((sum, operation) => sum + (operation.tokenCount || 0), 0);
    
    // Calculate cost by type
    const costByType: Record<string, number> = {};
    
    for (const operation of this.operations) {
      if (!costByType[operation.type]) {
        costByType[operation.type] = 0;
      }
      
      costByType[operation.type] += operation.cost || 0;
    }
    
    // Calculate cost by tool
    const costByTool: Record<string, number> = {};
    
    for (const operation of this.operations) {
      if (!costByTool[operation.toolType]) {
        costByTool[operation.toolType] = 0;
      }
      
      costByTool[operation.toolType] += operation.cost || 0;
    }
    
    // Calculate tokens by type
    const tokensByType: Record<string, number> = {};
    
    for (const operation of this.operations) {
      if (!tokensByType[operation.type]) {
        tokensByType[operation.type] = 0;
      }
      
      tokensByType[operation.type] += operation.tokenCount || 0;
    }
    
    // Calculate tokens by tool
    const tokensByTool: Record<string, number> = {};
    
    for (const operation of this.operations) {
      if (!tokensByTool[operation.toolType]) {
        tokensByTool[operation.toolType] = 0;
      }
      
      tokensByTool[operation.toolType] += operation.tokenCount || 0;
    }
    
    return {
      totalOperations,
      operationsByType,
      operationsByTool,
      patterns: this.patterns,
      totalCost,
      totalTokens,
      costByType,
      costByTool,
      tokensByType,
      tokensByTool,
      suggestions: this.suggestions
    };
  }
  
  /**
   * Save operations to file
   * @param filePath File path
   * @returns This instance for chaining
   */
  saveOperations(filePath: string): MCPOptimizer {
    ensureDirectoryExists(filePath.substring(0, filePath.lastIndexOf('/')));
    writeJsonToFile(filePath, this.operations);
    return this;
  }
  
  /**
   * Load operations from file
   * @param filePath File path
   * @returns This instance for chaining
   */
  loadOperations(filePath: string): MCPOptimizer {
    try {
      const operations = require(filePath);
      this.operations = operations;
    } catch (error) {
      console.error(`Error loading operations from ${filePath}:`, error);
    }
    
    return this;
  }
  
  /**
   * Save analysis result to file
   * @param filePath File path
   * @returns This instance for chaining
   */
  saveAnalysisResult(filePath: string): MCPOptimizer {
    const result = this.createAnalysisResult();
    ensureDirectoryExists(filePath.substring(0, filePath.lastIndexOf('/')));
    writeJsonToFile(filePath, result);
    return this;
  }
  
  /**
   * Get operations
   * @returns Operations
   */
  getOperations(): MCPOperation[] {
    return [...this.operations];
  }
  
  /**
   * Get patterns
   * @returns Patterns
   */
  getPatterns(): MCPOperationPattern[] {
    return [...this.patterns];
  }
  
  /**
   * Get suggestions
   * @returns Suggestions
   */
  getSuggestions(): MCPOptimizationSuggestion[] {
    return [...this.suggestions];
  }
  
  /**
   * Clear operations
   * @returns This instance for chaining
   */
  clearOperations(): MCPOptimizer {
    this.operations = [];
    return this;
  }
  
  /**
   * Clear patterns
   * @returns This instance for chaining
   */
  clearPatterns(): MCPOptimizer {
    this.patterns = [];
    return this;
  }
  
  /**
   * Clear suggestions
   * @returns This instance for chaining
   */
  clearSuggestions(): MCPOptimizer {
    this.suggestions = [];
    return this;
  }
  
  /**
   * Clear all
   * @returns This instance for chaining
   */
  clearAll(): MCPOptimizer {
    this.operations = [];
    this.patterns = [];
    this.suggestions = [];
    return this;
  }
  
  /**
   * Get analyzer
   * @returns Analyzer
   */
  getAnalyzer(): MCPAnalyzer {
    return this.analyzer;
  }
  
  /**
   * Get transformer
   * @returns Transformer
   */
  getTransformer(): MCPTransformer {
    return this.transformer;
  }
  
  /**
   * Get cost calculator
   * @returns Cost calculator
   */
  getCostCalculator(): MCPCostCalculator {
    return this.costCalculator;
  }
}

/**
 * Create MCP optimizer
 * @param options MCP optimizer options
 * @returns MCP optimizer
 */
export function createMCPOptimizer(options: MCPOptimizerOptions = {}): MCPOptimizer {
  return new MCPOptimizer(options);
}
