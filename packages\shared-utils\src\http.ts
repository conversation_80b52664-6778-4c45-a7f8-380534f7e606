/**
 * HTTP utilities for QA Wolf Metrics Framework
 */

import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';

/**
 * HTTP client
 */
export class HttpClient {
  private baseUrl: string;
  private defaultHeaders: Record<string, string>;
  private timeout: number;
  
  /**
   * Constructor
   * @param baseUrl Base URL
   * @param defaultHeaders Default headers
   * @param timeout Timeout in milliseconds
   */
  constructor(baseUrl: string = '', defaultHeaders: Record<string, string> = {}, timeout: number = 30000) {
    this.baseUrl = baseUrl;
    this.defaultHeaders = defaultHeaders;
    this.timeout = timeout;
  }
  
  /**
   * Set base URL
   * @param baseUrl Base URL
   */
  public setBaseUrl(baseUrl: string): void {
    this.baseUrl = baseUrl;
  }
  
  /**
   * Set default headers
   * @param headers Default headers
   */
  public setDefaultHeaders(headers: Record<string, string>): void {
    this.defaultHeaders = headers;
  }
  
  /**
   * Add default header
   * @param name Header name
   * @param value Header value
   */
  public addDefaultHeader(name: string, value: string): void {
    this.defaultHeaders[name] = value;
  }
  
  /**
   * Set timeout
   * @param timeout Timeout in milliseconds
   */
  public setTimeout(timeout: number): void {
    this.timeout = timeout;
  }
  
  /**
   * Get request
   * @param url URL
   * @param config Request configuration
   * @returns Response
   */
  public async get<T = any>(url: string, config: AxiosRequestConfig = {}): Promise<AxiosResponse<T>> {
    return axios.get<T>(this.getFullUrl(url), this.getRequestConfig(config));
  }
  
  /**
   * Post request
   * @param url URL
   * @param data Request data
   * @param config Request configuration
   * @returns Response
   */
  public async post<T = any>(url: string, data?: any, config: AxiosRequestConfig = {}): Promise<AxiosResponse<T>> {
    return axios.post<T>(this.getFullUrl(url), data, this.getRequestConfig(config));
  }
  
  /**
   * Put request
   * @param url URL
   * @param data Request data
   * @param config Request configuration
   * @returns Response
   */
  public async put<T = any>(url: string, data?: any, config: AxiosRequestConfig = {}): Promise<AxiosResponse<T>> {
    return axios.put<T>(this.getFullUrl(url), data, this.getRequestConfig(config));
  }
  
  /**
   * Patch request
   * @param url URL
   * @param data Request data
   * @param config Request configuration
   * @returns Response
   */
  public async patch<T = any>(url: string, data?: any, config: AxiosRequestConfig = {}): Promise<AxiosResponse<T>> {
    return axios.patch<T>(this.getFullUrl(url), data, this.getRequestConfig(config));
  }
  
  /**
   * Delete request
   * @param url URL
   * @param config Request configuration
   * @returns Response
   */
  public async delete<T = any>(url: string, config: AxiosRequestConfig = {}): Promise<AxiosResponse<T>> {
    return axios.delete<T>(this.getFullUrl(url), this.getRequestConfig(config));
  }
  
  /**
   * Get full URL
   * @param url URL
   * @returns Full URL
   */
  private getFullUrl(url: string): string {
    if (url.startsWith('http://') || url.startsWith('https://')) {
      return url;
    }
    
    return `${this.baseUrl}${url}`;
  }
  
  /**
   * Get request configuration
   * @param config Request configuration
   * @returns Request configuration
   */
  private getRequestConfig(config: AxiosRequestConfig): AxiosRequestConfig {
    return {
      ...config,
      headers: {
        ...this.defaultHeaders,
        ...config.headers
      },
      timeout: config.timeout || this.timeout
    };
  }
}

/**
 * Create HTTP client
 * @param baseUrl Base URL
 * @param defaultHeaders Default headers
 * @param timeout Timeout in milliseconds
 * @returns HTTP client
 */
export function createHttpClient(baseUrl: string = '', defaultHeaders: Record<string, string> = {}, timeout: number = 30000): HttpClient {
  return new HttpClient(baseUrl, defaultHeaders, timeout);
}

/**
 * Create HTTP client with authentication
 * @param baseUrl Base URL
 * @param apiKey API key
 * @param timeout Timeout in milliseconds
 * @returns HTTP client
 */
export function createAuthHttpClient(baseUrl: string, apiKey: string, timeout: number = 30000): HttpClient {
  return new HttpClient(baseUrl, {
    'Authorization': `Bearer ${apiKey}`,
    'Content-Type': 'application/json'
  }, timeout);
}

/**
 * Create HTTP client with basic authentication
 * @param baseUrl Base URL
 * @param username Username
 * @param password Password
 * @param timeout Timeout in milliseconds
 * @returns HTTP client
 */
export function createBasicAuthHttpClient(baseUrl: string, username: string, password: string, timeout: number = 30000): HttpClient {
  const auth = Buffer.from(`${username}:${password}`).toString('base64');
  
  return new HttpClient(baseUrl, {
    'Authorization': `Basic ${auth}`,
    'Content-Type': 'application/json'
  }, timeout);
}
