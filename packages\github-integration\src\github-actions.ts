/**
 * GitHub actions for GitHub integration
 */

import { Octokit } from '@octokit/rest';
import { GitHubActionsOptions, GitHubWorkflow, GitHubWorkflowRun, GitHubWorkflowRunFilter, GitHub<PERSON>heckRun, GitHub<PERSON>heck<PERSON>un<PERSON>ilt<PERSON>, GitHubCheckSuite } from './types';
import { EventBus, EventType } from '@qawolf/core';

/**
 * GitHub actions
 */
export class GitHubActions {
  /**
   * API token
   */
  private token: string;
  
  /**
   * Owner
   */
  private owner: string;
  
  /**
   * Repository
   */
  private repo: string;
  
  /**
   * Base URL
   */
  private baseUrl: string;
  
  /**
   * Timeout in milliseconds
   */
  private timeout: number;
  
  /**
   * Octokit instance
   */
  private octokit: Octokit;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options GitHub actions options
   */
  constructor(options: GitHubActionsOptions = {}) {
    this.token = options.token || '';
    this.owner = options.owner || '';
    this.repo = options.repo || '';
    this.baseUrl = options.baseUrl || 'https://api.github.com';
    this.timeout = options.timeout || 30000;
    
    this.octokit = new Octokit({
      auth: this.token,
      baseUrl: this.baseUrl,
      request: {
        timeout: this.timeout
      }
    });
    
    this.eventBus = EventBus.getInstance();
  }
  
  /**
   * Get workflows
   * @param owner Owner
   * @param repo Repository
   * @returns Workflows
   */
  async getWorkflows(owner?: string, repo?: string): Promise<GitHubWorkflow[]> {
    try {
      const response = await this.octokit.actions.listRepoWorkflows({
        owner: owner || this.owner,
        repo: repo || this.repo
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_ACTIONS_GET_WORKFLOWS, {
        workflows: response.data.workflows
      });
      
      return response.data.workflows;
    } catch (error) {
      this.handleError(error, `Failed to get workflows for repository ${owner || this.owner}/${repo || this.repo}`);
      return [];
    }
  }
  
  /**
   * Get workflow
   * @param workflowId Workflow ID
   * @param owner Owner
   * @param repo Repository
   * @returns Workflow
   */
  async getWorkflow(workflowId: number, owner?: string, repo?: string): Promise<GitHubWorkflow | null> {
    try {
      const response = await this.octokit.actions.getWorkflow({
        owner: owner || this.owner,
        repo: repo || this.repo,
        workflow_id: workflowId
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_ACTIONS_GET_WORKFLOW, {
        workflow: response.data
      });
      
      return response.data;
    } catch (error) {
      this.handleError(error, `Failed to get workflow ${workflowId} for repository ${owner || this.owner}/${repo || this.repo}`);
      return null;
    }
  }
  
  /**
   * Get workflow by name
   * @param name Workflow name
   * @param owner Owner
   * @param repo Repository
   * @returns Workflow
   */
  async getWorkflowByName(name: string, owner?: string, repo?: string): Promise<GitHubWorkflow | null> {
    try {
      const workflows = await this.getWorkflows(owner, repo);
      const workflow = workflows.find(workflow => workflow.name === name);
      
      if (workflow) {
        // Emit event
        this.eventBus.emit(EventType.GITHUB_ACTIONS_GET_WORKFLOW_BY_NAME, {
          workflow
        });
      }
      
      return workflow || null;
    } catch (error) {
      this.handleError(error, `Failed to get workflow with name ${name} for repository ${owner || this.owner}/${repo || this.repo}`);
      return null;
    }
  }
  
  /**
   * Get workflow runs
   * @param workflowId Workflow ID
   * @param filter Workflow run filter
   * @param owner Owner
   * @param repo Repository
   * @returns Workflow runs
   */
  async getWorkflowRuns(workflowId: number, filter?: GitHubWorkflowRunFilter, owner?: string, repo?: string): Promise<GitHubWorkflowRun[]> {
    try {
      const response = await this.octokit.actions.listWorkflowRuns({
        owner: owner || this.owner,
        repo: repo || this.repo,
        workflow_id: workflowId,
        branch: filter?.branch,
        event: filter?.event,
        status: filter?.status,
        created: filter?.created,
        actor: filter?.actor,
        head_sha: filter?.headSha
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_ACTIONS_GET_WORKFLOW_RUNS, {
        workflowRuns: response.data.workflow_runs
      });
      
      return response.data.workflow_runs;
    } catch (error) {
      this.handleError(error, `Failed to get workflow runs for workflow ${workflowId} in repository ${owner || this.owner}/${repo || this.repo}`);
      return [];
    }
  }
  
  /**
   * Get workflow run
   * @param runId Run ID
   * @param owner Owner
   * @param repo Repository
   * @returns Workflow run
   */
  async getWorkflowRun(runId: number, owner?: string, repo?: string): Promise<GitHubWorkflowRun | null> {
    try {
      const response = await this.octokit.actions.getWorkflowRun({
        owner: owner || this.owner,
        repo: repo || this.repo,
        run_id: runId
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_ACTIONS_GET_WORKFLOW_RUN, {
        workflowRun: response.data
      });
      
      return response.data;
    } catch (error) {
      this.handleError(error, `Failed to get workflow run ${runId} for repository ${owner || this.owner}/${repo || this.repo}`);
      return null;
    }
  }
  
  /**
   * Trigger workflow
   * @param workflowId Workflow ID
   * @param ref Reference
   * @param inputs Inputs
   * @param owner Owner
   * @param repo Repository
   * @returns True if triggered, false otherwise
   */
  async triggerWorkflow(workflowId: number, ref: string, inputs?: Record<string, string>, owner?: string, repo?: string): Promise<boolean> {
    try {
      await this.octokit.actions.createWorkflowDispatch({
        owner: owner || this.owner,
        repo: repo || this.repo,
        workflow_id: workflowId,
        ref,
        inputs
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_ACTIONS_TRIGGER_WORKFLOW, {
        workflowId,
        ref,
        inputs
      });
      
      return true;
    } catch (error) {
      this.handleError(error, `Failed to trigger workflow ${workflowId} for repository ${owner || this.owner}/${repo || this.repo}`);
      return false;
    }
  }
  
  /**
   * Cancel workflow run
   * @param runId Run ID
   * @param owner Owner
   * @param repo Repository
   * @returns True if cancelled, false otherwise
   */
  async cancelWorkflowRun(runId: number, owner?: string, repo?: string): Promise<boolean> {
    try {
      await this.octokit.actions.cancelWorkflowRun({
        owner: owner || this.owner,
        repo: repo || this.repo,
        run_id: runId
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_ACTIONS_CANCEL_WORKFLOW_RUN, {
        runId
      });
      
      return true;
    } catch (error) {
      this.handleError(error, `Failed to cancel workflow run ${runId} for repository ${owner || this.owner}/${repo || this.repo}`);
      return false;
    }
  }
  
  /**
   * Get check runs
   * @param ref Reference
   * @param filter Check run filter
   * @param owner Owner
   * @param repo Repository
   * @returns Check runs
   */
  async getCheckRuns(ref: string, filter?: GitHubCheckRunFilter, owner?: string, repo?: string): Promise<GitHubCheckRun[]> {
    try {
      const response = await this.octokit.checks.listForRef({
        owner: owner || this.owner,
        repo: repo || this.repo,
        ref,
        check_name: filter?.checkName,
        status: filter?.status,
        filter: filter?.filter
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_ACTIONS_GET_CHECK_RUNS, {
        checkRuns: response.data.check_runs
      });
      
      return response.data.check_runs as GitHubCheckRun[];
    } catch (error) {
      this.handleError(error, `Failed to get check runs for reference ${ref} in repository ${owner || this.owner}/${repo || this.repo}`);
      return [];
    }
  }
  
  /**
   * Get check run
   * @param checkRunId Check run ID
   * @param owner Owner
   * @param repo Repository
   * @returns Check run
   */
  async getCheckRun(checkRunId: number, owner?: string, repo?: string): Promise<GitHubCheckRun | null> {
    try {
      const response = await this.octokit.checks.get({
        owner: owner || this.owner,
        repo: repo || this.repo,
        check_run_id: checkRunId
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_ACTIONS_GET_CHECK_RUN, {
        checkRun: response.data
      });
      
      return response.data;
    } catch (error) {
      this.handleError(error, `Failed to get check run ${checkRunId} for repository ${owner || this.owner}/${repo || this.repo}`);
      return null;
    }
  }
  
  /**
   * Get check suites
   * @param ref Reference
   * @param appId App ID
   * @param owner Owner
   * @param repo Repository
   * @returns Check suites
   */
  async getCheckSuites(ref: string, appId?: number, owner?: string, repo?: string): Promise<GitHubCheckSuite[]> {
    try {
      const response = await this.octokit.checks.listSuitesForRef({
        owner: owner || this.owner,
        repo: repo || this.repo,
        ref,
        app_id: appId
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_ACTIONS_GET_CHECK_SUITES, {
        checkSuites: response.data.check_suites
      });
      
      return response.data.check_suites as GitHubCheckSuite[];
    } catch (error) {
      this.handleError(error, `Failed to get check suites for reference ${ref} in repository ${owner || this.owner}/${repo || this.repo}`);
      return [];
    }
  }
  
  /**
   * Get check suite
   * @param checkSuiteId Check suite ID
   * @param owner Owner
   * @param repo Repository
   * @returns Check suite
   */
  async getCheckSuite(checkSuiteId: number, owner?: string, repo?: string): Promise<GitHubCheckSuite | null> {
    try {
      const response = await this.octokit.checks.getSuite({
        owner: owner || this.owner,
        repo: repo || this.repo,
        check_suite_id: checkSuiteId
      });
      
      // Emit event
      this.eventBus.emit(EventType.GITHUB_ACTIONS_GET_CHECK_SUITE, {
        checkSuite: response.data
      });
      
      return response.data;
    } catch (error) {
      this.handleError(error, `Failed to get check suite ${checkSuiteId} for repository ${owner || this.owner}/${repo || this.repo}`);
      return null;
    }
  }
  
  /**
   * Handle error
   * @param error Error
   * @param message Error message
   */
  private handleError(error: any, message: string): void {
    console.error(message, error);
    
    // Emit event
    this.eventBus.emit(EventType.GITHUB_ACTIONS_ERROR, {
      error,
      message
    });
  }
  
  /**
   * Get token
   * @returns API token
   */
  getToken(): string {
    return this.token;
  }
  
  /**
   * Get owner
   * @returns Owner
   */
  getOwner(): string {
    return this.owner;
  }
  
  /**
   * Get repository
   * @returns Repository
   */
  getRepo(): string {
    return this.repo;
  }
  
  /**
   * Get base URL
   * @returns Base URL
   */
  getBaseUrl(): string {
    return this.baseUrl;
  }
  
  /**
   * Get timeout
   * @returns Timeout in milliseconds
   */
  getTimeout(): number {
    return this.timeout;
  }
  
  /**
   * Set token
   * @param token API token
   * @returns This instance for chaining
   */
  setToken(token: string): GitHubActions {
    this.token = token;
    
    this.octokit = new Octokit({
      auth: this.token,
      baseUrl: this.baseUrl,
      request: {
        timeout: this.timeout
      }
    });
    
    return this;
  }
  
  /**
   * Set owner
   * @param owner Owner
   * @returns This instance for chaining
   */
  setOwner(owner: string): GitHubActions {
    this.owner = owner;
    return this;
  }
  
  /**
   * Set repository
   * @param repo Repository
   * @returns This instance for chaining
   */
  setRepo(repo: string): GitHubActions {
    this.repo = repo;
    return this;
  }
  
  /**
   * Set base URL
   * @param baseUrl Base URL
   * @returns This instance for chaining
   */
  setBaseUrl(baseUrl: string): GitHubActions {
    this.baseUrl = baseUrl;
    
    this.octokit = new Octokit({
      auth: this.token,
      baseUrl: this.baseUrl,
      request: {
        timeout: this.timeout
      }
    });
    
    return this;
  }
  
  /**
   * Set timeout
   * @param timeout Timeout in milliseconds
   * @returns This instance for chaining
   */
  setTimeout(timeout: number): GitHubActions {
    this.timeout = timeout;
    
    this.octokit = new Octokit({
      auth: this.token,
      baseUrl: this.baseUrl,
      request: {
        timeout: this.timeout
      }
    });
    
    return this;
  }
}

/**
 * Create GitHub actions
 * @param options GitHub actions options
 * @returns GitHub actions
 */
export function createGitHubActions(options: GitHubActionsOptions = {}): GitHubActions {
  return new GitHubActions(options);
}
