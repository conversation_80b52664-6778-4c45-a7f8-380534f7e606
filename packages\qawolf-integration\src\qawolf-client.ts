/**
 * QA Wolf client for QA Wolf integration
 */

import * as dotenv from 'dotenv';
import { QAWolfClientOptions } from './types';
import { QAWolfAPI } from './qawolf-api';
import { QAWolfCI } from './qawolf-ci';
import { QAWolfMetrics } from './qawolf-metrics';
import { QAWolfReporter } from './qawolf-reporter';
import { EventBus, EventType } from '@qawolf/core';

// Load environment variables
dotenv.config();

/**
 * QA Wolf client
 */
export class QAWolfClient {
  /**
   * API key
   */
  private apiKey: string;
  
  /**
   * Team ID
   */
  private teamId: string;
  
  /**
   * API URL
   */
  private apiUrl: string;
  
  /**
   * Timeout in milliseconds
   */
  private timeout: number;
  
  /**
   * QA Wolf API
   */
  private api: QAWolfAPI;
  
  /**
   * QA Wolf CI
   */
  private ci: QAWolfCI;
  
  /**
   * QA Wolf metrics
   */
  private metrics: QAWolfMetrics;
  
  /**
   * QA Wolf reporter
   */
  private reporter: QAWolfReporter;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options QA Wolf client options
   */
  constructor(options: QAWolfClientOptions = {}) {
    this.apiKey = options.apiKey || process.env.QAWOLF_API_KEY || '';
    this.teamId = options.teamId || process.env.QAWOLF_TEAM_ID || '';
    this.apiUrl = options.apiUrl || process.env.QAWOLF_API_URL || 'https://app.qawolf.com/api';
    this.timeout = options.timeout || 30000;
    
    if (!this.apiKey) {
      throw new Error('QA Wolf API key is required');
    }
    
    if (!this.teamId) {
      throw new Error('QA Wolf team ID is required');
    }
    
    this.api = new QAWolfAPI({
      apiKey: this.apiKey,
      teamId: this.teamId,
      apiUrl: this.apiUrl,
      timeout: this.timeout
    });
    
    this.ci = new QAWolfCI({
      apiKey: this.apiKey,
      teamId: this.teamId,
      apiUrl: this.apiUrl,
      timeout: this.timeout
    });
    
    this.metrics = new QAWolfMetrics({
      apiKey: this.apiKey,
      teamId: this.teamId,
      apiUrl: this.apiUrl,
      timeout: this.timeout
    });
    
    this.reporter = new QAWolfReporter({
      apiKey: this.apiKey,
      teamId: this.teamId,
      apiUrl: this.apiUrl,
      timeout: this.timeout
    });
    
    this.eventBus = EventBus.getInstance();
    
    // Emit event
    this.eventBus.emit(EventType.QAWOLF_CLIENT_INITIALIZED, {
      apiKey: this.apiKey,
      teamId: this.teamId,
      apiUrl: this.apiUrl
    });
  }
  
  /**
   * Get API
   * @returns QA Wolf API
   */
  getAPI(): QAWolfAPI {
    return this.api;
  }
  
  /**
   * Get CI
   * @returns QA Wolf CI
   */
  getCI(): QAWolfCI {
    return this.ci;
  }
  
  /**
   * Get metrics
   * @returns QA Wolf metrics
   */
  getMetrics(): QAWolfMetrics {
    return this.metrics;
  }
  
  /**
   * Get reporter
   * @returns QA Wolf reporter
   */
  getReporter(): QAWolfReporter {
    return this.reporter;
  }
  
  /**
   * Get API key
   * @returns API key
   */
  getAPIKey(): string {
    return this.apiKey;
  }
  
  /**
   * Get team ID
   * @returns Team ID
   */
  getTeamId(): string {
    return this.teamId;
  }
  
  /**
   * Get API URL
   * @returns API URL
   */
  getAPIUrl(): string {
    return this.apiUrl;
  }
  
  /**
   * Get timeout
   * @returns Timeout in milliseconds
   */
  getTimeout(): number {
    return this.timeout;
  }
  
  /**
   * Set API key
   * @param apiKey API key
   * @returns This instance for chaining
   */
  setAPIKey(apiKey: string): QAWolfClient {
    this.apiKey = apiKey;
    
    this.api.setAPIKey(apiKey);
    this.ci.setAPIKey(apiKey);
    this.metrics.setAPIKey(apiKey);
    this.reporter.setAPIKey(apiKey);
    
    return this;
  }
  
  /**
   * Set team ID
   * @param teamId Team ID
   * @returns This instance for chaining
   */
  setTeamId(teamId: string): QAWolfClient {
    this.teamId = teamId;
    
    this.api.setTeamId(teamId);
    this.ci.setTeamId(teamId);
    this.metrics.setTeamId(teamId);
    this.reporter.setTeamId(teamId);
    
    return this;
  }
  
  /**
   * Set API URL
   * @param apiUrl API URL
   * @returns This instance for chaining
   */
  setAPIUrl(apiUrl: string): QAWolfClient {
    this.apiUrl = apiUrl;
    
    this.api.setAPIUrl(apiUrl);
    this.ci.setAPIUrl(apiUrl);
    this.metrics.setAPIUrl(apiUrl);
    this.reporter.setAPIUrl(apiUrl);
    
    return this;
  }
  
  /**
   * Set timeout
   * @param timeout Timeout in milliseconds
   * @returns This instance for chaining
   */
  setTimeout(timeout: number): QAWolfClient {
    this.timeout = timeout;
    
    this.api.setTimeout(timeout);
    this.ci.setTimeout(timeout);
    this.metrics.setTimeout(timeout);
    this.reporter.setTimeout(timeout);
    
    return this;
  }
}

/**
 * Create QA Wolf client
 * @param options QA Wolf client options
 * @returns QA Wolf client
 */
export function createQAWolfClient(options: QAWolfClientOptions = {}): QAWolfClient {
  return new QAWolfClient(options);
}
