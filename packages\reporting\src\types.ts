/**
 * Types for reporting
 */

/**
 * Report generator options
 */
export interface ReportGeneratorOptions {
  /**
   * Report title
   */
  title?: string;
  
  /**
   * Report description
   */
  description?: string;
  
  /**
   * Report template
   */
  template?: string;
  
  /**
   * Report format
   */
  format?: ReportFormat;
  
  /**
   * Report data
   */
  data?: Record<string, any>;
  
  /**
   * Report metadata
   */
  metadata?: Record<string, any>;
  
  /**
   * Report output directory
   */
  outputDir?: string;
  
  /**
   * Report filename
   */
  filename?: string;
  
  /**
   * Report date
   */
  date?: Date;
  
  /**
   * Report author
   */
  author?: string;
  
  /**
   * Report version
   */
  version?: string;
  
  /**
   * Report logo
   */
  logo?: string;
  
  /**
   * Report stylesheet
   */
  stylesheet?: string;
  
  /**
   * Report scripts
   */
  scripts?: string[];
  
  /**
   * Report theme
   */
  theme?: ReportTheme;
  
  /**
   * Report sections
   */
  sections?: ReportSection[];
  
  /**
   * Report charts
   */
  charts?: ReportChart[];
  
  /**
   * Report tables
   */
  tables?: ReportTable[];
  
  /**
   * Report images
   */
  images?: ReportImage[];
  
  /**
   * Report links
   */
  links?: ReportLink[];
  
  /**
   * Report attachments
   */
  attachments?: ReportAttachment[];
  
  /**
   * Report publish options
   */
  publishOptions?: ReportPublishOptions;
}

/**
 * Report format
 */
export enum ReportFormat {
  /**
   * HTML
   */
  HTML = 'html',
  
  /**
   * Markdown
   */
  MARKDOWN = 'markdown',
  
  /**
   * PDF
   */
  PDF = 'pdf',
  
  /**
   * JSON
   */
  JSON = 'json',
  
  /**
   * XML
   */
  XML = 'xml',
  
  /**
   * CSV
   */
  CSV = 'csv'
}

/**
 * Report theme
 */
export enum ReportTheme {
  /**
   * Light
   */
  LIGHT = 'light',
  
  /**
   * Dark
   */
  DARK = 'dark',
  
  /**
   * Custom
   */
  CUSTOM = 'custom'
}

/**
 * Report section
 */
export interface ReportSection {
  /**
   * Section ID
   */
  id: string;
  
  /**
   * Section title
   */
  title: string;
  
  /**
   * Section description
   */
  description?: string;
  
  /**
   * Section content
   */
  content?: string;
  
  /**
   * Section data
   */
  data?: Record<string, any>;
  
  /**
   * Section charts
   */
  charts?: ReportChart[];
  
  /**
   * Section tables
   */
  tables?: ReportTable[];
  
  /**
   * Section images
   */
  images?: ReportImage[];
  
  /**
   * Section links
   */
  links?: ReportLink[];
  
  /**
   * Section attachments
   */
  attachments?: ReportAttachment[];
  
  /**
   * Section order
   */
  order?: number;
  
  /**
   * Section visible
   */
  visible?: boolean;
  
  /**
   * Section collapsible
   */
  collapsible?: boolean;
  
  /**
   * Section collapsed
   */
  collapsed?: boolean;
}

/**
 * Report chart
 */
export interface ReportChart {
  /**
   * Chart ID
   */
  id: string;
  
  /**
   * Chart title
   */
  title: string;
  
  /**
   * Chart description
   */
  description?: string;
  
  /**
   * Chart type
   */
  type: ReportChartType;
  
  /**
   * Chart data
   */
  data: any;
  
  /**
   * Chart options
   */
  options?: any;
  
  /**
   * Chart width
   */
  width?: number;
  
  /**
   * Chart height
   */
  height?: number;
  
  /**
   * Chart order
   */
  order?: number;
  
  /**
   * Chart visible
   */
  visible?: boolean;
}

/**
 * Report chart type
 */
export enum ReportChartType {
  /**
   * Bar
   */
  BAR = 'bar',
  
  /**
   * Line
   */
  LINE = 'line',
  
  /**
   * Pie
   */
  PIE = 'pie',
  
  /**
   * Doughnut
   */
  DOUGHNUT = 'doughnut',
  
  /**
   * Radar
   */
  RADAR = 'radar',
  
  /**
   * Polar area
   */
  POLAR_AREA = 'polarArea',
  
  /**
   * Bubble
   */
  BUBBLE = 'bubble',
  
  /**
   * Scatter
   */
  SCATTER = 'scatter'
}

/**
 * Report table
 */
export interface ReportTable {
  /**
   * Table ID
   */
  id: string;
  
  /**
   * Table title
   */
  title: string;
  
  /**
   * Table description
   */
  description?: string;
  
  /**
   * Table headers
   */
  headers: string[];
  
  /**
   * Table rows
   */
  rows: any[][];
  
  /**
   * Table footer
   */
  footer?: string[];
  
  /**
   * Table width
   */
  width?: number;
  
  /**
   * Table height
   */
  height?: number;
  
  /**
   * Table order
   */
  order?: number;
  
  /**
   * Table visible
   */
  visible?: boolean;
  
  /**
   * Table sortable
   */
  sortable?: boolean;
  
  /**
   * Table filterable
   */
  filterable?: boolean;
  
  /**
   * Table pageable
   */
  pageable?: boolean;
  
  /**
   * Table page size
   */
  pageSize?: number;
}

/**
 * Report image
 */
export interface ReportImage {
  /**
   * Image ID
   */
  id: string;
  
  /**
   * Image title
   */
  title: string;
  
  /**
   * Image description
   */
  description?: string;
  
  /**
   * Image URL
   */
  url: string;
  
  /**
   * Image alt
   */
  alt?: string;
  
  /**
   * Image width
   */
  width?: number;
  
  /**
   * Image height
   */
  height?: number;
  
  /**
   * Image order
   */
  order?: number;
  
  /**
   * Image visible
   */
  visible?: boolean;
}

/**
 * Report link
 */
export interface ReportLink {
  /**
   * Link ID
   */
  id: string;
  
  /**
   * Link title
   */
  title: string;
  
  /**
   * Link description
   */
  description?: string;
  
  /**
   * Link URL
   */
  url: string;
  
  /**
   * Link target
   */
  target?: '_blank' | '_self' | '_parent' | '_top';
  
  /**
   * Link order
   */
  order?: number;
  
  /**
   * Link visible
   */
  visible?: boolean;
}

/**
 * Report attachment
 */
export interface ReportAttachment {
  /**
   * Attachment ID
   */
  id: string;
  
  /**
   * Attachment title
   */
  title: string;
  
  /**
   * Attachment description
   */
  description?: string;
  
  /**
   * Attachment URL
   */
  url: string;
  
  /**
   * Attachment type
   */
  type: string;
  
  /**
   * Attachment size
   */
  size?: number;
  
  /**
   * Attachment order
   */
  order?: number;
  
  /**
   * Attachment visible
   */
  visible?: boolean;
}

/**
 * Report publish options
 */
export interface ReportPublishOptions {
  /**
   * Publish to GitHub Pages
   */
  githubPages?: {
    /**
     * GitHub token
     */
    token?: string;
    
    /**
     * GitHub owner
     */
    owner?: string;
    
    /**
     * GitHub repository
     */
    repo?: string;
    
    /**
     * GitHub branch
     */
    branch?: string;
    
    /**
     * GitHub path
     */
    path?: string;
    
    /**
     * GitHub message
     */
    message?: string;
  };
  
  /**
   * Publish to S3
   */
  s3?: {
    /**
     * S3 access key
     */
    accessKey?: string;
    
    /**
     * S3 secret key
     */
    secretKey?: string;
    
    /**
     * S3 region
     */
    region?: string;
    
    /**
     * S3 bucket
     */
    bucket?: string;
    
    /**
     * S3 path
     */
    path?: string;
  };
  
  /**
   * Publish to Azure Blob Storage
   */
  azureBlob?: {
    /**
     * Azure connection string
     */
    connectionString?: string;
    
    /**
     * Azure container
     */
    container?: string;
    
    /**
     * Azure path
     */
    path?: string;
  };
  
  /**
   * Publish to Google Cloud Storage
   */
  googleCloudStorage?: {
    /**
     * Google Cloud project ID
     */
    projectId?: string;
    
    /**
     * Google Cloud key file
     */
    keyFile?: string;
    
    /**
     * Google Cloud bucket
     */
    bucket?: string;
    
    /**
     * Google Cloud path
     */
    path?: string;
  };
  
  /**
   * Publish to FTP
   */
  ftp?: {
    /**
     * FTP host
     */
    host?: string;
    
    /**
     * FTP port
     */
    port?: number;
    
    /**
     * FTP username
     */
    username?: string;
    
    /**
     * FTP password
     */
    password?: string;
    
    /**
     * FTP path
     */
    path?: string;
  };
  
  /**
   * Publish to SFTP
   */
  sftp?: {
    /**
     * SFTP host
     */
    host?: string;
    
    /**
     * SFTP port
     */
    port?: number;
    
    /**
     * SFTP username
     */
    username?: string;
    
    /**
     * SFTP password
     */
    password?: string;
    
    /**
     * SFTP private key
     */
    privateKey?: string;
    
    /**
     * SFTP path
     */
    path?: string;
  };
  
  /**
   * Publish to custom
   */
  custom?: Record<string, any>;
}

/**
 * Report template options
 */
export interface ReportTemplateOptions {
  /**
   * Template name
   */
  name: string;
  
  /**
   * Template description
   */
  description?: string;
  
  /**
   * Template engine
   */
  engine: ReportTemplateEngine;
  
  /**
   * Template content
   */
  content: string;
  
  /**
   * Template partials
   */
  partials?: Record<string, string>;
  
  /**
   * Template helpers
   */
  helpers?: Record<string, Function>;
  
  /**
   * Template stylesheet
   */
  stylesheet?: string;
  
  /**
   * Template scripts
   */
  scripts?: string[];
}

/**
 * Report template engine
 */
export enum ReportTemplateEngine {
  /**
   * Handlebars
   */
  HANDLEBARS = 'handlebars',
  
  /**
   * EJS
   */
  EJS = 'ejs',
  
  /**
   * Pug
   */
  PUG = 'pug',
  
  /**
   * Custom
   */
  CUSTOM = 'custom'
}

/**
 * Report formatter options
 */
export interface ReportFormatterOptions {
  /**
   * Formatter name
   */
  name: string;
  
  /**
   * Formatter description
   */
  description?: string;
  
  /**
   * Formatter format
   */
  format: ReportFormat;
  
  /**
   * Formatter options
   */
  options?: Record<string, any>;
}

/**
 * Report exporter options
 */
export interface ReportExporterOptions {
  /**
   * Exporter name
   */
  name: string;
  
  /**
   * Exporter description
   */
  description?: string;
  
  /**
   * Exporter format
   */
  format: ReportFormat;
  
  /**
   * Exporter output directory
   */
  outputDir?: string;
  
  /**
   * Exporter filename
   */
  filename?: string;
  
  /**
   * Exporter options
   */
  options?: Record<string, any>;
}

/**
 * Report publisher options
 */
export interface ReportPublisherOptions {
  /**
   * Publisher name
   */
  name: string;
  
  /**
   * Publisher description
   */
  description?: string;
  
  /**
   * Publisher type
   */
  type: ReportPublisherType;
  
  /**
   * Publisher options
   */
  options?: Record<string, any>;
}

/**
 * Report publisher type
 */
export enum ReportPublisherType {
  /**
   * GitHub Pages
   */
  GITHUB_PAGES = 'github-pages',
  
  /**
   * S3
   */
  S3 = 's3',
  
  /**
   * Azure Blob Storage
   */
  AZURE_BLOB = 'azure-blob',
  
  /**
   * Google Cloud Storage
   */
  GOOGLE_CLOUD_STORAGE = 'google-cloud-storage',
  
  /**
   * FTP
   */
  FTP = 'ftp',
  
  /**
   * SFTP
   */
  SFTP = 'sftp',
  
  /**
   * Custom
   */
  CUSTOM = 'custom'
}
