/**
 * Shared Utils Integration Tests
 * 
 * This file contains integration tests for the shared-utils package.
 */

const { test, expect } = require('@playwright/test');
const { config, screenshot, test: testUtils } = require('../../src');
const path = require('path');
const fs = require('fs');

test.describe('Shared Utils Integration', () => {
  test.describe('Configuration Utilities', () => {
    test('should load configuration from environment variables', () => {
      // Set environment variables
      process.env.TEST_CONFIG_VALUE = 'test-value';
      
      // Get configuration value
      const value = config.getEnv('TEST_CONFIG_VALUE', 'default-value');
      
      // Verify
      expect(value).toBe('test-value');
      
      // Clean up
      delete process.env.TEST_CONFIG_VALUE;
    });
    
    test('should return default value if environment variable is not set', () => {
      // Get configuration value
      const value = config.getEnv('NON_EXISTENT_CONFIG_VALUE', 'default-value');
      
      // Verify
      expect(value).toBe('default-value');
    });
    
    test('should convert environment variables to boolean', () => {
      // Set environment variables
      process.env.TEST_BOOL_TRUE = 'true';
      process.env.TEST_BOOL_FALSE = 'false';
      
      // Get configuration values
      const trueValue = config.getEnvBool('TEST_BOOL_TRUE', false);
      const falseValue = config.getEnvBool('TEST_BOOL_FALSE', true);
      
      // Verify
      expect(trueValue).toBe(true);
      expect(falseValue).toBe(false);
      
      // Clean up
      delete process.env.TEST_BOOL_TRUE;
      delete process.env.TEST_BOOL_FALSE;
    });
    
    test('should convert environment variables to number', () => {
      // Set environment variables
      process.env.TEST_NUMBER = '123';
      
      // Get configuration value
      const value = config.getEnvNumber('TEST_NUMBER', 0);
      
      // Verify
      expect(value).toBe(123);
      
      // Clean up
      delete process.env.TEST_NUMBER;
    });
    
    test('should detect CI environment', () => {
      // Set environment variables
      process.env.CI = 'true';
      
      // Check if in CI environment
      const isCI = config.isCI();
      
      // Verify
      expect(isCI).toBe(true);
      
      // Clean up
      delete process.env.CI;
    });
  });
  
  test.describe('Screenshot Utilities', () => {
    test('should create screenshot directory', async () => {
      // Create a temporary directory for screenshots
      const screenshotDir = path.join(__dirname, 'temp-screenshots');
      
      // Set configuration
      config.setConfig('screenshotDir', screenshotDir);
      
      // Ensure screenshot directory
      const testDir = screenshot.ensureScreenshotDir('test-screenshot');
      
      // Verify
      expect(fs.existsSync(testDir)).toBe(true);
      
      // Clean up
      fs.rmSync(screenshotDir, { recursive: true, force: true });
    });
    
    test('should verify screenshot structure', async () => {
      // Create a temporary directory for screenshots
      const screenshotDir = path.join(__dirname, 'temp-screenshots');
      
      // Set configuration
      config.setConfig('screenshotDir', screenshotDir);
      
      // Verify screenshot structure
      const result = screenshot.verifyScreenshotStructure();
      
      // Verify
      expect(result).toBe(true);
      
      // Clean up
      fs.rmSync(screenshotDir, { recursive: true, force: true });
    });
  });
  
  test.describe('Test Utilities', () => {
    test('should calculate AAA compliance score', () => {
      // Create test summary
      const testSummary = {
        success: true,
        performanceMetrics: {
          'navigation_to_app': 1000,
          'login': 2000
        },
        allPerformanceMetricsWithinThresholds: true
      };
      
      // Calculate AAA compliance score
      const score = testUtils.calculateAAAComplianceScore(testSummary);
      
      // Verify
      expect(score).toBeGreaterThan(90);
    });
    
    test('should create a performance tracker', () => {
      // Create performance tracker
      const performanceTracker = new testUtils.PerformanceTracker();
      
      // Start and end an operation
      performanceTracker.startOperation('test-operation');
      performanceTracker.endOperation();
      
      // Get metrics
      const metrics = performanceTracker.getMetrics();
      
      // Verify
      expect(metrics).toHaveProperty('test-operation');
      expect(metrics.test-operation).toBeGreaterThan(0);
    });
  });
});