/**
 * Types for GitHub integration
 */

import { Octokit } from '@octokit/rest';
import { RestEndpointMethodTypes } from '@octokit/plugin-rest-endpoint-methods';

/**
 * GitHub client options
 */
export interface GitHubClientOptions {
  /**
   * API token
   */
  token?: string;
  
  /**
   * Owner
   */
  owner?: string;
  
  /**
   * Repository
   */
  repo?: string;
  
  /**
   * Base URL
   */
  baseUrl?: string;
  
  /**
   * Timeout in milliseconds
   */
  timeout?: number;
}

/**
 * GitHub repository
 */
export type GitHubRepository = RestEndpointMethodTypes['repos']['get']['response']['data'];

/**
 * GitHub branch
 */
export type GitHubBranch = RestEndpointMethodTypes['repos']['getBranch']['response']['data'];

/**
 * GitHub commit
 */
export type GitHubCommit = RestEndpointMethodTypes['repos']['getCommit']['response']['data'];

/**
 * GitHub pull request
 */
export type GitHubPullRequest = RestEndpointMethodTypes['pulls']['get']['response']['data'];

/**
 * GitHub issue
 */
export type GitHubIssue = RestEndpointMethodTypes['issues']['get']['response']['data'];

/**
 * GitHub workflow
 */
export type GitHubWorkflow = RestEndpointMethodTypes['actions']['getWorkflow']['response']['data'];

/**
 * GitHub workflow run
 */
export type GitHubWorkflowRun = RestEndpointMethodTypes['actions']['getWorkflowRun']['response']['data'];

/**
 * GitHub release
 */
export type GitHubRelease = RestEndpointMethodTypes['repos']['getRelease']['response']['data'];

/**
 * GitHub tag
 */
export type GitHubTag = RestEndpointMethodTypes['repos']['getTag']['response']['data'];

/**
 * GitHub check run
 */
export type GitHubCheckRun = RestEndpointMethodTypes['checks']['get']['response']['data'];

/**
 * GitHub check suite
 */
export type GitHubCheckSuite = RestEndpointMethodTypes['checks']['getSuite']['response']['data'];

/**
 * GitHub API options
 */
export interface GitHubAPIOptions {
  /**
   * API token
   */
  token?: string;
  
  /**
   * Owner
   */
  owner?: string;
  
  /**
   * Repository
   */
  repo?: string;
  
  /**
   * Base URL
   */
  baseUrl?: string;
  
  /**
   * Timeout in milliseconds
   */
  timeout?: number;
}

/**
 * GitHub actions options
 */
export interface GitHubActionsOptions {
  /**
   * API token
   */
  token?: string;
  
  /**
   * Owner
   */
  owner?: string;
  
  /**
   * Repository
   */
  repo?: string;
  
  /**
   * Base URL
   */
  baseUrl?: string;
  
  /**
   * Timeout in milliseconds
   */
  timeout?: number;
}

/**
 * GitHub pages options
 */
export interface GitHubPagesOptions {
  /**
   * API token
   */
  token?: string;
  
  /**
   * Owner
   */
  owner?: string;
  
  /**
   * Repository
   */
  repo?: string;
  
  /**
   * Base URL
   */
  baseUrl?: string;
  
  /**
   * Timeout in milliseconds
   */
  timeout?: number;
}

/**
 * GitHub releases options
 */
export interface GitHubReleasesOptions {
  /**
   * API token
   */
  token?: string;
  
  /**
   * Owner
   */
  owner?: string;
  
  /**
   * Repository
   */
  repo?: string;
  
  /**
   * Base URL
   */
  baseUrl?: string;
  
  /**
   * Timeout in milliseconds
   */
  timeout?: number;
}

/**
 * GitHub workflow run status
 */
export enum GitHubWorkflowRunStatus {
  /**
   * Queued
   */
  QUEUED = 'queued',
  
  /**
   * In progress
   */
  IN_PROGRESS = 'in_progress',
  
  /**
   * Completed
   */
  COMPLETED = 'completed'
}

/**
 * GitHub workflow run conclusion
 */
export enum GitHubWorkflowRunConclusion {
  /**
   * Success
   */
  SUCCESS = 'success',
  
  /**
   * Failure
   */
  FAILURE = 'failure',
  
  /**
   * Neutral
   */
  NEUTRAL = 'neutral',
  
  /**
   * Cancelled
   */
  CANCELLED = 'cancelled',
  
  /**
   * Skipped
   */
  SKIPPED = 'skipped',
  
  /**
   * Timed out
   */
  TIMED_OUT = 'timed_out',
  
  /**
   * Action required
   */
  ACTION_REQUIRED = 'action_required'
}

/**
 * GitHub check run status
 */
export enum GitHubCheckRunStatus {
  /**
   * Queued
   */
  QUEUED = 'queued',
  
  /**
   * In progress
   */
  IN_PROGRESS = 'in_progress',
  
  /**
   * Completed
   */
  COMPLETED = 'completed'
}

/**
 * GitHub check run conclusion
 */
export enum GitHubCheckRunConclusion {
  /**
   * Success
   */
  SUCCESS = 'success',
  
  /**
   * Failure
   */
  FAILURE = 'failure',
  
  /**
   * Neutral
   */
  NEUTRAL = 'neutral',
  
  /**
   * Cancelled
   */
  CANCELLED = 'cancelled',
  
  /**
   * Skipped
   */
  SKIPPED = 'skipped',
  
  /**
   * Timed out
   */
  TIMED_OUT = 'timed_out',
  
  /**
   * Action required
   */
  ACTION_REQUIRED = 'action_required'
}

/**
 * GitHub issue state
 */
export enum GitHubIssueState {
  /**
   * Open
   */
  OPEN = 'open',
  
  /**
   * Closed
   */
  CLOSED = 'closed',
  
  /**
   * All
   */
  ALL = 'all'
}

/**
 * GitHub pull request state
 */
export enum GitHubPullRequestState {
  /**
   * Open
   */
  OPEN = 'open',
  
  /**
   * Closed
   */
  CLOSED = 'closed',
  
  /**
   * All
   */
  ALL = 'all'
}

/**
 * GitHub pull request merge method
 */
export enum GitHubPullRequestMergeMethod {
  /**
   * Merge
   */
  MERGE = 'merge',
  
  /**
   * Squash
   */
  SQUASH = 'squash',
  
  /**
   * Rebase
   */
  REBASE = 'rebase'
}

/**
 * GitHub release type
 */
export enum GitHubReleaseType {
  /**
   * Draft
   */
  DRAFT = 'draft',
  
  /**
   * Prerelease
   */
  PRERELEASE = 'prerelease',
  
  /**
   * Release
   */
  RELEASE = 'release'
}

/**
 * GitHub pages build status
 */
export enum GitHubPagesBuildStatus {
  /**
   * Building
   */
  BUILDING = 'building',
  
  /**
   * Built
   */
  BUILT = 'built',
  
  /**
   * Errored
   */
  ERRORED = 'errored'
}

/**
 * GitHub workflow run filter
 */
export interface GitHubWorkflowRunFilter {
  /**
   * Branch
   */
  branch?: string;
  
  /**
   * Event
   */
  event?: string;
  
  /**
   * Status
   */
  status?: GitHubWorkflowRunStatus;
  
  /**
   * Created
   */
  created?: string;
  
  /**
   * Actor
   */
  actor?: string;
  
  /**
   * Head SHA
   */
  headSha?: string;
}

/**
 * GitHub check run filter
 */
export interface GitHubCheckRunFilter {
  /**
   * Check name
   */
  checkName?: string;
  
  /**
   * Status
   */
  status?: GitHubCheckRunStatus;
  
  /**
   * Filter
   */
  filter?: 'latest' | 'all';
}

/**
 * GitHub issue filter
 */
export interface GitHubIssueFilter {
  /**
   * State
   */
  state?: GitHubIssueState;
  
  /**
   * Labels
   */
  labels?: string[];
  
  /**
   * Assignee
   */
  assignee?: string;
  
  /**
   * Creator
   */
  creator?: string;
  
  /**
   * Mentioned
   */
  mentioned?: string;
  
  /**
   * Since
   */
  since?: string;
  
  /**
   * Sort
   */
  sort?: 'created' | 'updated' | 'comments';
  
  /**
   * Direction
   */
  direction?: 'asc' | 'desc';
}

/**
 * GitHub pull request filter
 */
export interface GitHubPullRequestFilter {
  /**
   * State
   */
  state?: GitHubPullRequestState;
  
  /**
   * Head
   */
  head?: string;
  
  /**
   * Base
   */
  base?: string;
  
  /**
   * Sort
   */
  sort?: 'created' | 'updated' | 'popularity' | 'long-running';
  
  /**
   * Direction
   */
  direction?: 'asc' | 'desc';
}

/**
 * GitHub release filter
 */
export interface GitHubReleaseFilter {
  /**
   * Type
   */
  type?: GitHubReleaseType;
  
  /**
   * Sort
   */
  sort?: 'created' | 'updated';
  
  /**
   * Direction
   */
  direction?: 'asc' | 'desc';
}
