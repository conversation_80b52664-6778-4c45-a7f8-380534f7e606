/**
 * Simplified File Operations End-to-End Test
 *
 * This is a simplified version of our file operations end-to-end test that doesn't rely on the MCP or self-healing functionality.
 * It follows the Arrange-Act-Assert (AAA) pattern for test structure.
 *
 * The AAA pattern consists of three main sections:
 * - Arrange: Set up the test environment and prerequisites
 * - Act: Perform the action being tested
 * - Assert: Verify the expected outcome
 */

const { test, expect } = require('@playwright/test');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Create a simple screenshot utility
const screenshot = {
  takeScreenshot: async (page, options) => {
    const { testName, action, fullPage = false } = options;

    // Create screenshots directory if it doesn't exist
    const screenshotDir = path.join(process.cwd(), 'screenshots');
    if (!fs.existsSync(screenshotDir)) {
      fs.mkdirSync(screenshotDir, { recursive: true });
    }

    // Create a dated directory for today's screenshots
    const dateDir = path.join(screenshotDir, new Date().toISOString().split('T')[0]);
    if (!fs.existsSync(dateDir)) {
      fs.mkdirSync(dateDir, { recursive: true });
    }

    // Create a directory for this specific test
    const testDir = path.join(dateDir, testName);
    if (!fs.existsSync(testDir)) {
      fs.mkdirSync(testDir, { recursive: true });
    }

    // Create a timestamp for the filename
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');

    // Build the filename
    const filename = `${timestamp}_${action}.png`;
    const screenshotPath = path.join(testDir, filename);

    // Take the screenshot
    await page.screenshot({
      path: screenshotPath,
      fullPage
    });

    console.log(`Screenshot saved: ${screenshotPath}`);
    return screenshotPath;
  },

  takeErrorScreenshot: async (page, options) => {
    const { testName, error, fullPage = true } = options;

    const errorDescription = error ? `${error.name}-${error.message.substring(0, 20).replace(/[^a-zA-Z0-9]/g, '_')}` : 'unknown-error';

    return screenshot.takeScreenshot(page, {
      testName,
      action: `error-${errorDescription}`,
      fullPage
    });
  }
};

// Performance tracking utility
class PerformanceTracker {
  constructor() {
    this.metrics = {
      executionTime: {
        start: null,
        end: null,
        duration: null
      },
      operations: []
    };
  }

  start() {
    this.metrics.executionTime.start = Date.now();
    return this;
  }

  stop() {
    this.metrics.executionTime.end = Date.now();
    this.metrics.executionTime.duration = this.metrics.executionTime.end - this.metrics.executionTime.start;
    return this.getMetrics();
  }

  trackOperation(operation) {
    const { name, type, duration } = operation;

    this.metrics.operations.push({
      name,
      type,
      duration,
      timestamp: Date.now()
    });

    return this;
  }

  getMetrics() {
    return this.metrics;
  }
}

// Helper functions
async function login(page) {
  const email = process.env.EMAIL || '<EMAIL>';
  const password = process.env.PASSWORD || 'vhc!tGK289IS&';
  const url = process.env.URL || 'https://app.lidostaging.com';

  // Navigate to the login page
  await page.goto(url);

  // Fill in login credentials and submit
  await page.fill('[data-test-id="SignInEmail"]', email);
  await page.fill('[data-test-id="SignInPassword"]', password);
  await page.locator(':text("Log in with email")').click();

  // Wait for navigation to complete
  await page.waitForNavigation();

  // Verify successful login
  await expect(page).not.toHaveURL(/login/);
  await expect(page.locator('div[class*="FilesTable__Wrapper"]')).toBeVisible();
}

async function navigateToFilesPage(page) {
  try {
    // Try to find and click a "Files" link or button if we're not already on the files page
    await page.click('a:has-text("Files")').catch(() => {
      // If "Files" link not found, try clicking a back button
      return page.click('button[aria-label="Back"]');
    });

    // Wait for the file list to load
    await page.waitForTimeout(1000);
  } catch {
    // Continue with the test even if navigation fails
    // We're likely already on the files list page
  }

  // Wait for the files table to appear
  await page.waitForSelector('div[class*="FilesTable"]', { timeout: 5000 });
}

async function createFile(page, options = {}) {
  // Default file name is "untitled"
  const fileName = options.fileName || "untitled";

  // Count existing files with the same name before creating a new one
  const existingCount1 = await page.locator(`span[class*="FileTitle"]:has-text("${fileName}")`).count();
  const existingCount2 = await page.locator(`div[class*="styled_FileName"]:has-text("${fileName}"), div.jHdmZQ:has-text("${fileName}")`).count();
  const existingCount = Math.max(existingCount1, existingCount2);

  // Click on the "New file" button
  await page.click('div[class*="pages__NewFileButton"]');

  // Wait for the file to be created
  await page.waitForTimeout(1000);

  // Check if we need to click on the file to open it
  // First check if we're already in the file editor
  const fileViewerCount = await page.locator('div[class*="FileViewer"]').count();
  const isInFileEditor = fileViewerCount > 0;

  if (!isInFileEditor) {
    // We're not in the file editor, so we need to click on the file to open it
    try {
      // Try multiple selectors to find the file
      await page.locator(`span[class*="FileTitle"]:has-text("${fileName}")`).first().click()
        .catch(() => {
          return page.locator(`div[class*="styled_FileName"]:has-text("${fileName}"), div.jHdmZQ:has-text("${fileName}")`).first().click();
        });

      // Wait for the file to open
      await page.waitForTimeout(1000);
    } catch {
      // Continue with the test even if we can't click on the file
    }
  }

  return fileName;
}

async function deleteFile(page, options = {}) {
  // Default file name is "untitled"
  const fileName = options.fileName || "untitled";

  // Navigate back to the main menu if needed
  try {
    await page.click('button[aria-label="Back"], a[href="/"]');
    await page.waitForTimeout(1000);
  } catch {
    // If that fails, try direct navigation
    await page.goto(process.env.URL || 'https://app.lidostaging.com');
  }

  // Wait for the files table to appear
  await page.waitForSelector('div[class*="FilesTable"]', { timeout: 5000 });

  // Get all files with the specified name using multiple selectors
  const files1 = page.locator(`span[class*="FileTitle"]:has-text("${fileName}")`);
  const files2 = page.locator(`div[class*="styled_FileName"]:has-text("${fileName}"), div.jHdmZQ:has-text("${fileName}")`);

  // Try to click the more options menu using different approaches
  try {
    // First approach: Using the file title element and ancestor
    await files1.first().locator('xpath=./ancestor::tr').locator('button[aria-label="More"]').click({ timeout: 5000 });
  } catch {
    try {
      // Second approach: Using the exact class from the HTML
      await page.locator('button.HomePageFileMoreOptionsMenu__MenuButton-sc-13mscgm-2').first().click({ timeout: 5000 });
    } catch {
      try {
        // Third approach: Using a more generic selector
        await page.locator('button:has-text("...")').first().click({ timeout: 5000 });
      } catch {
        // Fourth approach: Find any button that might be a more options button
        const buttons = await page.locator('button').all();

        // Click the first button that contains "..." or is at the end of a row
        let buttonClicked = false;
        for (let i = 0; i < buttons.length; i++) {
          const buttonText = await buttons[i].textContent();
          if (buttonText.includes('...')) {
            await buttons[i].click();
            buttonClicked = true;
            break;
          }
        }

        if (!buttonClicked && buttons.length > 0) {
          // If we couldn't find a button with "...", just click the last button
          await buttons[buttons.length - 1].click();
        }
      }
    }
  }

  // Wait for the menu to appear
  await page.waitForTimeout(1000);

  // Click the delete option using multiple selectors
  try {
    // First try with text
    await page.click('text=Delete', { timeout: 5000 });
  } catch {
    try {
      // Second try with the exact class from the HTML
      await page.locator('div.HomePageFileMoreOptionsMenu__MenuItemWrapper-sc-13mscgm-1:has(svg[data-test-id="trash-icon"])').first().click({ timeout: 5000 });
    } catch {
      // Third try with a more generic approach
      await page.locator('div[role="menuitem"]:has-text("Delete")').click({ timeout: 5000 });
    }
  }

  // Wait for the confirmation dialog
  try {
    await page.waitForSelector('text=Are you sure you want to delete', { timeout: 5000 });
  } catch {
    // Continue anyway, the dialog might be there but with different text
  }

  // Confirm deletion by clicking "Yes, I'm sure"
  try {
    // First try with text
    await page.click('text=Yes, I\'m sure', { timeout: 5000 });
  } catch {
    try {
      // Second try with the class from the HTML
      await page.locator('div[class*="styled_ConfirmButtonBasic"]').click({ timeout: 5000 });
    } catch {
      // Third try with a more generic approach
      await page.locator('div[role="dialog"] button:nth-child(2)').click({ timeout: 5000 });
    }
  }

  // Wait for the deletion to complete
  await page.waitForTimeout(1000);
}

test.describe('File Operations', () => {
  // Before each test, log in
  test.beforeEach(async ({ page }) => {
    await login(page);
  });

  /**
   * Test: Create a new file
   * Purpose: Verify that users can create a new file
   * Input: None
   * Expected: New file is created
   *
   * This test follows the AAA pattern:
   * - Arrange: Navigate to the files page and count existing files
   * - Act: Create a new file
   * - Assert: Verify the file was created by checking the file count
   */
  test('should create a new file', async ({ page }) => {
    // Performance tracking
    const performanceTracker = new PerformanceTracker().start();

    try {
      // ARRANGE: Navigate to the files page and count existing files
      await navigateToFilesPage(page);

      // Take a screenshot before creating a file
      await screenshot.takeScreenshot(page, {
        testName: 'create-file',
        action: 'before-create-file',
        fullPage: true
      });

      // Count existing "untitled" files before creating a new one
      const existingUntitledCount1 = await page.locator('span[class*="FileTitle"]:has-text("untitled")').count();
      const existingUntitledCount2 = await page.locator('div[class*="styled_FileName"]:has-text("untitled"), div.jHdmZQ:has-text("untitled")').count();
      const existingUntitledCount = Math.max(existingUntitledCount1, existingUntitledCount2);

      // ACT: Create a new file
      const fileName = await createFile(page);

      // Track the operation
      performanceTracker.trackOperation({
        name: 'Create File',
        type: 'file-operation',
        duration: 1000 // Placeholder value
      });

      // Navigate back to the main menu to see the file list
      try {
        await page.click('button[aria-label="Back"], a[href="/"]');
        await page.waitForTimeout(1000);
      } catch {
        // If that fails, try direct navigation
        await page.goto(process.env.URL || 'https://app.lidostaging.com');
      }

      // Wait for the files table to appear
      await page.waitForSelector('div[class*="FilesTable"]', { timeout: 5000 });

      // Take a screenshot after creating a file
      await screenshot.takeScreenshot(page, {
        testName: 'create-file',
        action: 'after-create-file',
        fullPage: true
      });

      // ASSERT: Verify the file was created
      // After creating a new file, there should be one more "untitled" file than before
      const newUntitledCount1 = await page.locator('span[class*="FileTitle"]:has-text("untitled")').count();
      const newUntitledCount2 = await page.locator('div[class*="styled_FileName"]:has-text("untitled"), div.jHdmZQ:has-text("untitled")').count();
      const newUntitledCount = Math.max(newUntitledCount1, newUntitledCount2);

      expect(newUntitledCount).toBeGreaterThan(existingUntitledCount);
      expect(newUntitledCount).toBe(existingUntitledCount + 1);

      // Clean up: Delete the file we just created
      await deleteFile(page, { fileName });
    } catch (error) {
      // Take a screenshot on error
      await screenshot.takeErrorScreenshot(page, {
        testName: 'create-file',
        error
      });

      throw error;
    } finally {
      // Stop performance tracking
      const metrics = performanceTracker.stop();
      console.log('Performance metrics:', JSON.stringify(metrics, null, 2));
    }
  });
});
