/**
 * Linear users for Linear integration
 */

import { LinearClient, User, UserConnection } from '@linear/sdk';
import { LinearUsersOptions, LinearUserFilter } from './types';
import { EventBus, EventType } from '@qawolf/core';

/**
 * Linear users
 */
export class LinearUsers {
  /**
   * Linear client
   */
  private client: LinearClient;
  
  /**
   * Event bus
   */
  private eventBus: EventBus;
  
  /**
   * Constructor
   * @param options Linear users options
   */
  constructor(options: LinearUsersOptions = {}) {
    if (!options.client && !options.apiKey) {
      throw new Error('Linear client or API key is required');
    }
    
    if (options.client) {
      this.client = options.client;
    } else {
      this.client = new LinearClient({
        apiKey: options.apiKey
      });
    }
    
    this.eventBus = EventBus.getInstance();
  }
  
  /**
   * Get users
   * @param filter User filter
   * @returns Users
   */
  async getUsers(filter?: LinearUserFilter): Promise<User[]> {
    try {
      // Build filter
      const queryFilter: Record<string, any> = {};
      
      if (filter?.id) {
        queryFilter.id = { eq: filter.id };
      }
      
      if (filter?.name) {
        queryFilter.name = { contains: filter.name };
      }
      
      if (filter?.email) {
        queryFilter.email = { eq: filter.email };
      }
      
      if (filter?.displayName) {
        queryFilter.displayName = { contains: filter.displayName };
      }
      
      if (filter?.active !== undefined) {
        queryFilter.active = { eq: filter.active };
      }
      
      if (filter?.admin !== undefined) {
        queryFilter.admin = { eq: filter.admin };
      }
      
      if (filter?.teams && filter.teams.length > 0) {
        queryFilter.teams = { id: { in: filter.teams } };
      }
      
      if (filter?.createdAfter) {
        queryFilter.createdAt = { ...queryFilter.createdAt, gt: filter.createdAfter };
      }
      
      if (filter?.createdBefore) {
        queryFilter.createdAt = { ...queryFilter.createdAt, lt: filter.createdBefore };
      }
      
      if (filter?.updatedAfter) {
        queryFilter.updatedAt = { ...queryFilter.updatedAt, gt: filter.updatedAfter };
      }
      
      if (filter?.updatedBefore) {
        queryFilter.updatedAt = { ...queryFilter.updatedAt, lt: filter.updatedBefore };
      }
      
      // Build order by
      const orderBy = filter?.sortBy ? `${filter.sortBy}_${filter.sortOrder || 'ASC'}` : undefined;
      
      // Get users
      const users = await this.client.users({
        filter: queryFilter,
        orderBy
      });
      
      // Get all users
      const allUsers = await this.getAllUsers(users);
      
      // Emit event
      this.eventBus.emit(EventType.LINEAR_USERS_GET_USERS, {
        users: allUsers
      });
      
      return allUsers;
    } catch (error) {
      this.handleError(error, 'Failed to get users');
      return [];
    }
  }
  
  /**
   * Get user by ID
   * @param id User ID
   * @returns User
   */
  async getUserById(id: string): Promise<User | null> {
    try {
      const user = await this.client.user(id);
      
      // Emit event
      this.eventBus.emit(EventType.LINEAR_USERS_GET_USER_BY_ID, {
        user
      });
      
      return user;
    } catch (error) {
      this.handleError(error, `Failed to get user with ID ${id}`);
      return null;
    }
  }
  
  /**
   * Get user by email
   * @param email User email
   * @returns User
   */
  async getUserByEmail(email: string): Promise<User | null> {
    try {
      const users = await this.getUsers({
        email
      });
      
      if (users.length === 0) {
        return null;
      }
      
      // Emit event
      this.eventBus.emit(EventType.LINEAR_USERS_GET_USER_BY_EMAIL, {
        user: users[0]
      });
      
      return users[0];
    } catch (error) {
      this.handleError(error, `Failed to get user with email ${email}`);
      return null;
    }
  }
  
  /**
   * Get current user
   * @returns User
   */
  async getCurrentUser(): Promise<User | null> {
    try {
      const viewer = await this.client.viewer;
      
      // Emit event
      this.eventBus.emit(EventType.LINEAR_USERS_GET_CURRENT_USER, {
        user: viewer
      });
      
      return viewer;
    } catch (error) {
      this.handleError(error, 'Failed to get current user');
      return null;
    }
  }
  
  /**
   * Get all users
   * @param users User connection
   * @returns Users
   */
  private async getAllUsers(users: UserConnection): Promise<User[]> {
    const allUsers: User[] = [];
    let currentPage = users;
    
    while (true) {
      const nodes = await currentPage.nodes;
      allUsers.push(...nodes);
      
      if (!(await currentPage.hasNextPage)) {
        break;
      }
      
      currentPage = await currentPage.next();
    }
    
    return allUsers;
  }
  
  /**
   * Handle error
   * @param error Error
   * @param message Error message
   */
  private handleError(error: any, message: string): void {
    console.error(message, error);
    
    // Emit event
    this.eventBus.emit(EventType.LINEAR_USERS_ERROR, {
      error,
      message
    });
  }
}

/**
 * Create Linear users
 * @param options Linear users options
 * @returns Linear users
 */
export function createLinearUsers(options: LinearUsersOptions = {}): LinearUsers {
  return new LinearUsers(options);
}
