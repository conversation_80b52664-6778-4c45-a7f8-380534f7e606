/**
 * Configuration module for QA Wolf Metrics Framework
 */

import * as dotenv from 'dotenv';
import * as fs from 'fs';
import * as path from 'path';

// Load environment variables from .env file
dotenv.config();

/**
 * Configuration interface
 */
export interface Config {
  // General configuration
  environment: string;
  logLevel: string;
  
  // QA Wolf configuration
  qaWolfApiKey: string;
  qaWolfTeamId: string;
  qaWolfApiUrl: string;
  
  // Testmo configuration
  testmoApiKey: string;
  testmoUrl: string;
  testmoProjectId: string;
  
  // GitHub configuration
  githubToken: string;
  githubRepository: string;
  
  // Linear configuration
  linearApiKey: string;
  
  // Test configuration
  testTimeout: number;
  testRetries: number;
  testConcurrency: number;
  
  // Screenshot configuration
  screenshotDir: string;
  screenshotMaxAge: number;
  
  // Report configuration
  reportDir: string;
}

/**
 * Default configuration
 */
const defaultConfig: Config = {
  // General configuration
  environment: 'development',
  logLevel: 'info',
  
  // QA <PERSON> configuration
  qaWolfApiKey: '',
  qaWolfTeamId: 'clux0gjs50sb3ak01fnh7wvja',
  qaWolfApiUrl: 'https://app.qawolf.com/api/ci',
  
  // Testmo configuration
  testmoApiKey: '',
  testmoUrl: 'https://your-instance.testmo.net',
  testmoProjectId: '1',
  
  // GitHub configuration
  githubToken: '',
  githubRepository: '',
  
  // Linear configuration
  linearApiKey: '',
  
  // Test configuration
  testTimeout: 60000,
  testRetries: 2,
  testConcurrency: 1,
  
  // Screenshot configuration
  screenshotDir: './screenshots',
  screenshotMaxAge: 7,
  
  // Report configuration
  reportDir: './reports'
};

/**
 * Load configuration from environment variables
 * @returns Configuration object
 */
export function loadConfig(): Config {
  const config: Config = { ...defaultConfig };
  
  // General configuration
  config.environment = process.env.ENVIRONMENT || config.environment;
  config.logLevel = process.env.LOG_LEVEL || config.logLevel;
  
  // QA Wolf configuration
  config.qaWolfApiKey = process.env.QA_WOLF_API_KEY || config.qaWolfApiKey;
  config.qaWolfTeamId = process.env.QA_WOLF_TEAM_ID || config.qaWolfTeamId;
  config.qaWolfApiUrl = process.env.QA_WOLF_API_URL || config.qaWolfApiUrl;
  
  // Testmo configuration
  config.testmoApiKey = process.env.TESTMO_API_KEY || config.testmoApiKey;
  config.testmoUrl = process.env.TESTMO_URL || config.testmoUrl;
  config.testmoProjectId = process.env.TESTMO_PROJECT_ID || config.testmoProjectId;
  
  // GitHub configuration
  config.githubToken = process.env.GITHUB_TOKEN || process.env.GITHUB_API_TOKEN || config.githubToken;
  config.githubRepository = process.env.GITHUB_REPOSITORY || config.githubRepository;
  
  // Linear configuration
  config.linearApiKey = process.env.LINEAR_API_KEY || config.linearApiKey;
  
  // Test configuration
  config.testTimeout = parseInt(process.env.TEST_TIMEOUT || config.testTimeout.toString(), 10);
  config.testRetries = parseInt(process.env.TEST_RETRIES || config.testRetries.toString(), 10);
  config.testConcurrency = parseInt(process.env.TEST_CONCURRENCY || config.testConcurrency.toString(), 10);
  
  // Screenshot configuration
  config.screenshotDir = process.env.SCREENSHOT_DIR || config.screenshotDir;
  config.screenshotMaxAge = parseInt(process.env.SCREENSHOT_MAX_AGE || config.screenshotMaxAge.toString(), 10);
  
  // Report configuration
  config.reportDir = process.env.REPORT_DIR || config.reportDir;
  
  return config;
}

/**
 * Get configuration
 * @returns Configuration object
 */
export function getConfig(): Config {
  return loadConfig();
}

/**
 * Validate configuration
 * @param config Configuration object
 * @returns Validation result
 */
export function validateConfig(config: Config): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // Validate QA Wolf configuration
  if (!config.qaWolfApiKey && process.env.NODE_ENV === 'production') {
    errors.push('QA Wolf API key is required in production environment');
  }
  
  // Validate Testmo configuration
  if (!config.testmoApiKey && process.env.NODE_ENV === 'production') {
    errors.push('Testmo API key is required in production environment');
  }
  
  // Validate GitHub configuration
  if (!config.githubToken && process.env.NODE_ENV === 'production') {
    errors.push('GitHub token is required in production environment');
  }
  
  // Validate Linear configuration
  if (!config.linearApiKey && process.env.NODE_ENV === 'production') {
    errors.push('Linear API key is required in production environment');
  }
  
  return {
    valid: errors.length === 0,
    errors
  };
}

/**
 * Configuration class
 */
export class Configuration {
  private static instance: Configuration;
  private config: Config;
  
  private constructor() {
    this.config = loadConfig();
  }
  
  /**
   * Get configuration instance
   * @returns Configuration instance
   */
  public static getInstance(): Configuration {
    if (!Configuration.instance) {
      Configuration.instance = new Configuration();
    }
    
    return Configuration.instance;
  }
  
  /**
   * Get configuration
   * @returns Configuration object
   */
  public getConfig(): Config {
    return this.config;
  }
  
  /**
   * Validate configuration
   * @returns Validation result
   */
  public validate(): { valid: boolean; errors: string[] } {
    return validateConfig(this.config);
  }
  
  /**
   * Reload configuration
   */
  public reload(): void {
    this.config = loadConfig();
  }
}

// Export default instance
export default Configuration.getInstance();
