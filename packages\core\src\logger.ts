/**
 * Logger module for QA Wolf Metrics Framework
 */

import * as winston from 'winston';
import { getConfig } from './config';

// Get configuration
const config = getConfig();

/**
 * Create logger
 * @param module Module name
 * @returns Winston logger
 */
export function createLogger(module: string): winston.Logger {
  return winston.createLogger({
    level: config.logLevel,
    format: winston.format.combine(
      winston.format.timestamp(),
      winston.format.printf(({ level, message, timestamp, ...meta }) => {
        return `${timestamp} [${level}] [${module}]: ${message} ${Object.keys(meta).length ? JSON.stringify(meta) : ''}`;
      })
    ),
    transports: [
      new winston.transports.Console(),
      new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
      new winston.transports.File({ filename: 'logs/combined.log' })
    ]
  });
}

/**
 * Logger class
 */
export class Logger {
  private static instance: Logger;
  private loggers: Map<string, winston.Logger> = new Map();
  
  private constructor() {}
  
  /**
   * Get logger instance
   * @returns Logger instance
   */
  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    
    return Logger.instance;
  }
  
  /**
   * Get logger for module
   * @param module Module name
   * @returns Winston logger
   */
  public getLogger(module: string): winston.Logger {
    if (!this.loggers.has(module)) {
      this.loggers.set(module, createLogger(module));
    }
    
    return this.loggers.get(module)!;
  }
}

// Export default instance
export default Logger.getInstance();
