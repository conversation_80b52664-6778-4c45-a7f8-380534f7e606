/**
 * MCP Configuration
 * 
 * This module provides functions for managing MCP configuration.
 */

/**
 * Create default MCP configuration
 * @returns {Object} - Default configuration
 */
function createDefaultConfig() {
  return {
    // General
    enabled: true,
    
    // Playwright MCP
    autoStartPlaywrightMcp: true,
    playwrightMcpPort: 8932,
    playwrightMcpConfigPath: null, // Will use default if null
    headless: false,
    
    // Selector optimization
    generateFallbacks: true,
    prioritizeTestIds: true,
    
    // Tool selection
    tokenOptimized: true,
    performanceOptimized: false,
    
    // Reporting
    reportFormat: 'markdown',
    includeTimestamps: true,
    includeTokenUsage: true,
    
    // Logging
    logLevel: 'info', // 'debug', 'info', 'warn', 'error'
    
    // Advanced
    retryCount: 3,
    retryDelay: 1000,
    timeout: 30000
  };
}

/**
 * Validate MCP configuration
 * @param {Object} config - Configuration to validate
 * @throws {Error} - If configuration is invalid
 */
function validateConfig(config) {
  if (!config) {
    throw new Error('Configuration is required');
  }
  
  if (typeof config !== 'object') {
    throw new Error('Configuration must be an object');
  }
  
  // Validate required fields
  if (config.enabled !== undefined && typeof config.enabled !== 'boolean') {
    throw new Error('config.enabled must be a boolean');
  }
  
  if (config.autoStartPlaywrightMcp !== undefined && typeof config.autoStartPlaywrightMcp !== 'boolean') {
    throw new Error('config.autoStartPlaywrightMcp must be a boolean');
  }
  
  if (config.playwrightMcpPort !== undefined && typeof config.playwrightMcpPort !== 'number') {
    throw new Error('config.playwrightMcpPort must be a number');
  }
  
  if (config.headless !== undefined && typeof config.headless !== 'boolean') {
    throw new Error('config.headless must be a boolean');
  }
  
  if (config.generateFallbacks !== undefined && typeof config.generateFallbacks !== 'boolean') {
    throw new Error('config.generateFallbacks must be a boolean');
  }
  
  if (config.prioritizeTestIds !== undefined && typeof config.prioritizeTestIds !== 'boolean') {
    throw new Error('config.prioritizeTestIds must be a boolean');
  }
  
  if (config.tokenOptimized !== undefined && typeof config.tokenOptimized !== 'boolean') {
    throw new Error('config.tokenOptimized must be a boolean');
  }
  
  if (config.performanceOptimized !== undefined && typeof config.performanceOptimized !== 'boolean') {
    throw new Error('config.performanceOptimized must be a boolean');
  }
  
  if (config.reportFormat !== undefined && typeof config.reportFormat !== 'string') {
    throw new Error('config.reportFormat must be a string');
  }
  
  if (config.includeTimestamps !== undefined && typeof config.includeTimestamps !== 'boolean') {
    throw new Error('config.includeTimestamps must be a boolean');
  }
  
  if (config.includeTokenUsage !== undefined && typeof config.includeTokenUsage !== 'boolean') {
    throw new Error('config.includeTokenUsage must be a boolean');
  }
  
  if (config.logLevel !== undefined && typeof config.logLevel !== 'string') {
    throw new Error('config.logLevel must be a string');
  }
  
  if (config.retryCount !== undefined && typeof config.retryCount !== 'number') {
    throw new Error('config.retryCount must be a number');
  }
  
  if (config.retryDelay !== undefined && typeof config.retryDelay !== 'number') {
    throw new Error('config.retryDelay must be a number');
  }
  
  if (config.timeout !== undefined && typeof config.timeout !== 'number') {
    throw new Error('config.timeout must be a number');
  }
}

/**
 * Merge configuration objects
 * @param {Object} baseConfig - Base configuration
 * @param {Object} overrides - Configuration overrides
 * @returns {Object} - Merged configuration
 */
function mergeConfigs(baseConfig, overrides) {
  if (!baseConfig) {
    throw new Error('Base configuration is required');
  }
  
  if (!overrides) {
    return { ...baseConfig };
  }
  
  return {
    ...baseConfig,
    ...overrides
  };
}

module.exports = {
  createDefaultConfig,
  validateConfig,
  mergeConfigs
};